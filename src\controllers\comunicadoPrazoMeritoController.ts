import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { chatGuruLogger } from '../utils/logger';

// Importar do módulo refatorado 
import { 
  atualizarElegibilidadePorEtapaCRM,
  verificarEEnviarComunicados,
  iniciarVerificacaoDiariaComunicados
} from '../services/comunicados';

const prisma = new PrismaClient();

/**
 * Controle para receber webhooks do CRM com atualizações de etapas
 * de processos e atualizar a elegibilidade para comunicados.
 * Define se um processo é elegível para receber comunicados automáticos.
 */
export const webhookEtapaCRM = async (
  req: Request, 
  res: Response, 
  next: NextFunction
): Promise<void> => {
  try {
    const { crmId, etapaNome } = req.body;
    
    if (!crmId || !etapaNome) {
      res.status(400).json({ error: '<PERSON><PERSON> obrigatórios não fornecidos (crmId e etapaNome são necessários)' });
      return;
    }
    
    // Atualizar elegibilidade com base na etapa do CRM
    const resultado = await atualizarElegibilidadePorEtapaCRM(Number(crmId), etapaNome);
    
    chatGuruLogger.info(`Webhook de atualização de etapa processado para crmId ${crmId}`, {
      crmId,
      etapaNome,
      resultado
    });
    
    res.status(200).json({ 
      success: true,
      message: 'Elegibilidade do processo atualizada com sucesso',
      data: resultado
    });
  } catch (error) {
    chatGuruLogger.error(`Erro ao processar webhook de etapa do CRM: ${error instanceof Error ? error.message : 'Erro desconhecido'}`, { error });
    next(error);
  }
};

/**
 * Controle para verificar comunicados pendentes e enviá-los
 */
export const verificarEEnviarComunicadosController = async (
  req: Request, 
  res: Response, 
  next: NextFunction
): Promise<void> => {
  try {
    chatGuruLogger.info('Iniciando verificação manual de comunicados de prazo...');
    
    const resultado = await verificarEEnviarComunicados();
    
    res.status(200).json({
      success: true,
      message: 'Verificação de comunicados concluída com sucesso',
      data: resultado
    });
  } catch (error) {
    chatGuruLogger.error(`Erro ao verificar e enviar comunicados: ${error instanceof Error ? error.message : 'Erro desconhecido'}`, { error });
    next(error);
  }
};

/**
 * Inicializa o agendamento para verificação diária de comunicados
 */
export const iniciarVerificacaoDiaria = async (
  req: Request, 
  res: Response, 
  next: NextFunction
): Promise<void> => {
  try {
    const expressaoCron = req.body.expressaoCron || '0 9 * * *'; // Padrão: todos os dias às 9h
    
    chatGuruLogger.info(`Iniciando agendamento de verificação diária com expressão: ${expressaoCron}`);
    
    const job = iniciarVerificacaoDiariaComunicados(expressaoCron);
    
    res.status(200).json({
      success: true,
      message: 'Agendamento de verificação diária iniciado com sucesso',
      data: {
        expressaoCron,
        proximaExecucao: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // Estimativa simples
      }
    });
  } catch (error) {
    chatGuruLogger.error(`Erro ao iniciar verificação diária: ${error instanceof Error ? error.message : 'Erro desconhecido'}`, { error });
    next(error);
  }
}; 