from PyPDF2 import PdfReader
import requests
import logging
from tqdm import tqdm
import os

def get_last_revista_number():
    url = "https://api-v2-rgsys.registrese.app.br/revistas/buscaUltimaRevista"
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        data = response.json()
        return data.get('revista', {}).get('numero')
    except requests.exceptions.Timeout:
        logging.error("A requisição expirou (Timeout)")
        return None
    except requests.exceptions.ConnectionError:
        logging.error("Erro de conexão")
        return None
    except requests.RequestException as e:

        return None


def download_next_revista():
    last_revista_number = get_last_revista_number() - 1
    print(f"O último Revista: {last_revista_number}.")
    if last_revista_number is None:
        print("Não foi possível obter o número da última revista.")
        return None

    next_revista_number = last_revista_number + 1
    print(f"Baixando a revista {next_revista_number}.")
    url = f"https://revistas.inpi.gov.br/pdf/Marcas{next_revista_number}.pdf"
    local_filename = f"Marcas{next_revista_number}.pdf"

    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()

        # Tamanho total do arquivo para a barra de progresso
        total_size_in_bytes = int(response.headers.get('content-length', 0))
        block_size = 8192  # 8 Kibibytes
        progress_bar = tqdm(total=total_size_in_bytes,
                            unit='iB', unit_scale=True)

        with open(local_filename, 'wb') as pdf_file:
            for chunk in response.iter_content(chunk_size=block_size):
                progress_bar.update(len(chunk))
                pdf_file.write(chunk)

        progress_bar.close()

        # Verifica se o download foi concluído corretamente
        if total_size_in_bytes != 0 and progress_bar.n != total_size_in_bytes:
            print("Erro no download: Tamanho do arquivo não corresponde ao esperado.")
            return None

        return local_filename

    except requests.exceptions.HTTPError as http_err:
        print(f"Erro HTTP ao tentar baixar a revista: {http_err}")
    except requests.exceptions.RequestException as req_err:
        print(f"Erro ao fazer a requisição: {req_err}")
    except Exception as err:
        print(f"Um erro inesperado ocorreu: {err}")

    return None


def is_pdf_valid(filepath, remove_if_invalid=True):
    """
    Verifica se o PDF é válido.
    
    Args:
        filepath (str): Caminho para o arquivo PDF.
        remove_if_invalid (bool): Se True, remove o arquivo se inválido.
    
    Returns:
        bool: True se o PDF for válido, False caso contrário.
    """
    try:
        with open(filepath, 'rb') as f:
            pdf = PdfReader(f)
            if pdf.is_encrypted:
                logging.warning(f"O PDF está criptografado: {filepath}")
                if remove_if_invalid:
                    os.remove(filepath)
                    logging.info(
                        f"Arquivo PDF criptografado removido: {filepath}")
                return False
            if len(pdf.pages) > 0:
                return True
            else:
                logging.warning(f"O PDF não contém páginas: {filepath}")
                if remove_if_invalid:
                    os.remove(filepath)
                    logging.info(
                        f"Arquivo PDF sem páginas removido: {filepath}")
                return False
    except Exception as e:
        logging.error("Erro ao validar PDF ({filepath}): {e}")
        if remove_if_invalid and os.path.exists(filepath):
            try:
                os.remove(filepath)
                logging.info(
                    f"Arquivo PDF inválido removido devido a erro: {filepath}")
            except Exception as remove_err:
                logging.error(f"Erro ao remover arquivo PDF inválido ({filepath}): {remove_err}")
        return False
