-- AlterEnum
ALTER TYPE "ComunicadoTipo" ADD VALUE 'ATUALIZACAO_PRAZO';

-- CreateTable
CREATE TABLE "ComunicadoPrazoMerito" (
    "id" TEXT NOT NULL,
    "crmId" INTEGER NOT NULL,
    "processoId" TEXT NOT NULL,
    "dialogId" TEXT NOT NULL,
    "prazoEmMeses" INTEGER,
    "dataEnvio" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "success" BOOLEAN NOT NULL DEFAULT true,
    "errorMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ComunicadoPrazoMerito_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ComunicadoPrazoMerito_crmId_idx" ON "ComunicadoPrazoMerito"("crmId");

-- CreateIndex
CREATE INDEX "ComunicadoPrazoMerito_processoId_idx" ON "ComunicadoPrazoMerito"("processoId");

-- CreateIndex
CREATE INDEX "ComunicadoPrazoMerito_dataEnvio_idx" ON "ComunicadoPrazoMerito"("dataEnvio");

-- CreateIndex
CREATE UNIQUE INDEX "ComunicadoPrazoMerito_crmId_processoId_dialogId_key" ON "ComunicadoPrazoMerito"("crmId", "processoId", "dialogId");

-- AddForeignKey
ALTER TABLE "ComunicadoPrazoMerito" ADD CONSTRAINT "ComunicadoPrazoMerito_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
