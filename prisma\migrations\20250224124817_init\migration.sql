-- CreateEnum
CREATE TYPE "ComunicadoTipo" AS ENUM ('CRM_MOVIMENTACAO', 'DESPACHO');

-- CreateTable
CREATE TABLE "RPI" (
    "id" TEXT NOT NULL,
    "numero" INTEGER NOT NULL,
    "dataPublicacao" TIMESTAMP(3) NOT NULL,
    "pdfProcessado" BOOLEAN NOT NULL DEFAULT false,
    "xmlProcessado" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "RPI_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Processo" (
    "id" TEXT NOT NULL,
    "numero" TEXT NOT NULL,
    "dataDeposito" TIMESTAMP(3),
    "dataConcessao" TIMESTAMP(3),
    "dataVigencia" TIMESTAMP(3),
    "dataMerito" TIMESTAMP(3),
    "diasCorridosMerito" INTEGER,
    "dataMeritoEstimada" TIMESTAMP(3),
    "diasAteMerito" INTEGER,
    "oposicao" BOOLEAN NOT NULL DEFAULT false,
    "dataOposicao" TIMESTAMP(3),
    "sobrestamento" BOOLEAN NOT NULL DEFAULT false,
    "dataSobrestamento" TIMESTAMP(3),
    "exigencia" BOOLEAN NOT NULL DEFAULT false,
    "dataExigencia" TIMESTAMP(3),
    "procuradorId" TEXT,
    "monitorado" BOOLEAN NOT NULL DEFAULT false,
    "inicioVigencia" TIMESTAMP(3),
    "prorrogacaoOrdinaria" TEXT,
    "prorrogacaoExtraordinaria" TEXT,
    "apostila" TEXT,
    "clienteId" INTEGER,
    "marcaId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Processo_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Procurador" (
    "id" TEXT NOT NULL,
    "nome" TEXT NOT NULL,

    CONSTRAINT "Procurador_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Titular" (
    "id" TEXT NOT NULL,
    "processoId" TEXT NOT NULL,
    "nomeRazaoSocial" TEXT NOT NULL,
    "pais" TEXT,
    "uf" TEXT,
    "rpiId" TEXT,

    CONSTRAINT "Titular_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Despacho" (
    "id" TEXT NOT NULL,
    "processoId" TEXT NOT NULL,
    "rpiId" TEXT NOT NULL,
    "codigo" TEXT NOT NULL,
    "nome" TEXT,
    "textoComplementar" TEXT,
    "detalhesDespachoId" TEXT,

    CONSTRAINT "Despacho_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProtocoloDespacho" (
    "id" TEXT NOT NULL,
    "despachoId" TEXT NOT NULL,
    "numero" TEXT NOT NULL,
    "data" TIMESTAMP(3) NOT NULL,
    "codigoServico" TEXT NOT NULL,
    "requerenteNomeRazaoSocial" TEXT NOT NULL,
    "requerentePais" TEXT,
    "requerenteUf" TEXT,
    "procuradorId" TEXT,

    CONSTRAINT "ProtocoloDespacho_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Sobrestador" (
    "id" TEXT NOT NULL,
    "processoId" TEXT NOT NULL,
    "referenciaProcessual" TEXT,
    "marca" TEXT,

    CONSTRAINT "Sobrestador_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Marca" (
    "id" TEXT NOT NULL,
    "processoId" TEXT NOT NULL,
    "nome" TEXT,
    "apresentacao" TEXT,
    "natureza" TEXT,

    CONSTRAINT "Marca_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "NCL" (
    "id" TEXT NOT NULL,
    "marcaId" TEXT NOT NULL,
    "codigo" TEXT,
    "especificacao" TEXT,
    "status" TEXT,
    "edicao" TEXT,

    CONSTRAINT "NCL_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CFE" (
    "id" TEXT NOT NULL,
    "marcaId" TEXT NOT NULL,
    "codigo" TEXT,
    "edicao" TEXT,

    CONSTRAINT "CFE_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProcuradorMonitorado" (
    "id" TEXT NOT NULL,
    "nome" TEXT NOT NULL,

    CONSTRAINT "ProcuradorMonitorado_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProcessoInteresse" (
    "id" TEXT NOT NULL,
    "processoId" TEXT NOT NULL,
    "interessado" TEXT,
    "observacao" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProcessoInteresse_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DetalhesDespacho" (
    "id" TEXT NOT NULL,
    "codigo" TEXT NOT NULL,
    "nome" TEXT NOT NULL,
    "acaoCumprir" TEXT,
    "abreviacao" TEXT,
    "statusRelacionado" TEXT,
    "comum" BOOLEAN NOT NULL DEFAULT false,
    "prazoACumprir" DOUBLE PRECISION,
    "notificado" BOOLEAN NOT NULL DEFAULT false,
    "cumprido" BOOLEAN NOT NULL DEFAULT false,
    "observacao" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DetalhesDespacho_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ContatoCliente" (
    "id" SERIAL NOT NULL,
    "clienteId" INTEGER NOT NULL,
    "endereco" TEXT,
    "telefone" TEXT,
    "telefoneSegundario" TEXT,
    "email" TEXT,
    "cidade" TEXT,
    "estado" TEXT,
    "cep" TEXT,

    CONSTRAINT "ContatoCliente_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Cliente" (
    "id" SERIAL NOT NULL,
    "identificador" TEXT,
    "crmId" INTEGER,
    "crmLeadIds" INTEGER[],
    "crmStageId" TEXT,
    "tempoNaEtapa" TEXT,
    "camposPersonalizados" JSONB,
    "nome" TEXT,
    "tipoDeDocumento" TEXT,
    "numeroDocumento" TEXT,
    "nomeDaMarca" TEXT,

    CONSTRAINT "Cliente_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Comunicado" (
    "id" TEXT NOT NULL,
    "clienteId" INTEGER NOT NULL,
    "tipo" "ComunicadoTipo" NOT NULL,
    "titulo" TEXT NOT NULL,
    "mensagem" TEXT NOT NULL,
    "enviadoEm" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processoId" TEXT,
    "despachoId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Comunicado_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CrmProcessoControle" (
    "id" TEXT NOT NULL,
    "crmId" INTEGER NOT NULL,
    "processoId" TEXT NOT NULL,
    "automove" BOOLEAN NOT NULL DEFAULT false,
    "blockmove" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CrmProcessoControle_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "RPI_numero_key" ON "RPI"("numero");

-- CreateIndex
CREATE INDEX "RPI_numero_idx" ON "RPI"("numero");

-- CreateIndex
CREATE INDEX "RPI_dataPublicacao_idx" ON "RPI"("dataPublicacao");

-- CreateIndex
CREATE UNIQUE INDEX "Processo_numero_key" ON "Processo"("numero");

-- CreateIndex
CREATE INDEX "Processo_numero_idx" ON "Processo"("numero");

-- CreateIndex
CREATE INDEX "Processo_dataDeposito_idx" ON "Processo"("dataDeposito");

-- CreateIndex
CREATE INDEX "Processo_dataConcessao_idx" ON "Processo"("dataConcessao");

-- CreateIndex
CREATE INDEX "Processo_dataVigencia_idx" ON "Processo"("dataVigencia");

-- CreateIndex
CREATE INDEX "Processo_dataMerito_idx" ON "Processo"("dataMerito");

-- CreateIndex
CREATE INDEX "Processo_dataMeritoEstimada_idx" ON "Processo"("dataMeritoEstimada");

-- CreateIndex
CREATE INDEX "Processo_procuradorId_idx" ON "Processo"("procuradorId");

-- CreateIndex
CREATE INDEX "Processo_clienteId_idx" ON "Processo"("clienteId");

-- CreateIndex
CREATE INDEX "Processo_marcaId_idx" ON "Processo"("marcaId");

-- CreateIndex
CREATE UNIQUE INDEX "Procurador_nome_key" ON "Procurador"("nome");

-- CreateIndex
CREATE INDEX "Procurador_nome_idx" ON "Procurador"("nome");

-- CreateIndex
CREATE INDEX "Titular_processoId_idx" ON "Titular"("processoId");

-- CreateIndex
CREATE INDEX "Titular_nomeRazaoSocial_idx" ON "Titular"("nomeRazaoSocial");

-- CreateIndex
CREATE INDEX "Despacho_processoId_idx" ON "Despacho"("processoId");

-- CreateIndex
CREATE INDEX "Despacho_codigo_idx" ON "Despacho"("codigo");

-- CreateIndex
CREATE INDEX "Despacho_rpiId_idx" ON "Despacho"("rpiId");

-- CreateIndex
CREATE INDEX "Despacho_nome_idx" ON "Despacho"("nome");

-- CreateIndex
CREATE INDEX "ProtocoloDespacho_despachoId_idx" ON "ProtocoloDespacho"("despachoId");

-- CreateIndex
CREATE INDEX "ProtocoloDespacho_numero_idx" ON "ProtocoloDespacho"("numero");

-- CreateIndex
CREATE INDEX "ProtocoloDespacho_data_idx" ON "ProtocoloDespacho"("data");

-- CreateIndex
CREATE INDEX "Sobrestador_processoId_idx" ON "Sobrestador"("processoId");

-- CreateIndex
CREATE INDEX "Sobrestador_referenciaProcessual_idx" ON "Sobrestador"("referenciaProcessual");

-- CreateIndex
CREATE INDEX "Sobrestador_marca_idx" ON "Sobrestador"("marca");

-- CreateIndex
CREATE UNIQUE INDEX "Marca_processoId_key" ON "Marca"("processoId");

-- CreateIndex
CREATE INDEX "Marca_processoId_idx" ON "Marca"("processoId");

-- CreateIndex
CREATE INDEX "Marca_nome_idx" ON "Marca"("nome");

-- CreateIndex
CREATE INDEX "Marca_natureza_idx" ON "Marca"("natureza");

-- CreateIndex
CREATE INDEX "NCL_marcaId_idx" ON "NCL"("marcaId");

-- CreateIndex
CREATE INDEX "CFE_marcaId_idx" ON "CFE"("marcaId");

-- CreateIndex
CREATE UNIQUE INDEX "ProcuradorMonitorado_nome_key" ON "ProcuradorMonitorado"("nome");

-- CreateIndex
CREATE UNIQUE INDEX "ProcessoInteresse_processoId_key" ON "ProcessoInteresse"("processoId");

-- CreateIndex
CREATE UNIQUE INDEX "DetalhesDespacho_codigo_key" ON "DetalhesDespacho"("codigo");

-- CreateIndex
CREATE INDEX "DetalhesDespacho_codigo_idx" ON "DetalhesDespacho"("codigo");

-- CreateIndex
CREATE INDEX "DetalhesDespacho_nome_idx" ON "DetalhesDespacho"("nome");

-- CreateIndex
CREATE UNIQUE INDEX "Cliente_crmId_key" ON "Cliente"("crmId");

-- CreateIndex
CREATE INDEX "Cliente_crmId_idx" ON "Cliente"("crmId");

-- CreateIndex
CREATE INDEX "Cliente_identificador_idx" ON "Cliente"("identificador");

-- CreateIndex
CREATE INDEX "CrmProcessoControle_crmId_idx" ON "CrmProcessoControle"("crmId");

-- CreateIndex
CREATE INDEX "CrmProcessoControle_processoId_idx" ON "CrmProcessoControle"("processoId");

-- CreateIndex
CREATE UNIQUE INDEX "CrmProcessoControle_crmId_processoId_key" ON "CrmProcessoControle"("crmId", "processoId");

-- AddForeignKey
ALTER TABLE "Processo" ADD CONSTRAINT "Processo_procuradorId_fkey" FOREIGN KEY ("procuradorId") REFERENCES "Procurador"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Processo" ADD CONSTRAINT "Processo_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Titular" ADD CONSTRAINT "Titular_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Titular" ADD CONSTRAINT "Titular_rpiId_fkey" FOREIGN KEY ("rpiId") REFERENCES "RPI"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Despacho" ADD CONSTRAINT "Despacho_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Despacho" ADD CONSTRAINT "Despacho_rpiId_fkey" FOREIGN KEY ("rpiId") REFERENCES "RPI"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Despacho" ADD CONSTRAINT "Despacho_detalhesDespachoId_fkey" FOREIGN KEY ("detalhesDespachoId") REFERENCES "DetalhesDespacho"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProtocoloDespacho" ADD CONSTRAINT "ProtocoloDespacho_despachoId_fkey" FOREIGN KEY ("despachoId") REFERENCES "Despacho"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProtocoloDespacho" ADD CONSTRAINT "ProtocoloDespacho_procuradorId_fkey" FOREIGN KEY ("procuradorId") REFERENCES "Procurador"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Sobrestador" ADD CONSTRAINT "Sobrestador_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Marca" ADD CONSTRAINT "Marca_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NCL" ADD CONSTRAINT "NCL_marcaId_fkey" FOREIGN KEY ("marcaId") REFERENCES "Marca"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CFE" ADD CONSTRAINT "CFE_marcaId_fkey" FOREIGN KEY ("marcaId") REFERENCES "Marca"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProcessoInteresse" ADD CONSTRAINT "ProcessoInteresse_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContatoCliente" ADD CONSTRAINT "ContatoCliente_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Comunicado" ADD CONSTRAINT "Comunicado_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Comunicado" ADD CONSTRAINT "Comunicado_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Comunicado" ADD CONSTRAINT "Comunicado_despachoId_fkey" FOREIGN KEY ("despachoId") REFERENCES "Despacho"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CrmProcessoControle" ADD CONSTRAINT "CrmProcessoControle_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
