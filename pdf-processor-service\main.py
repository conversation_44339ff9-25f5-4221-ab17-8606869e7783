"""
Microserviço Python FastAPI para extração de imagens de PDFs de protocolo
Responsável por extrair logos de marca de PDFs do INPI
"""

from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.responses import JSONResponse
import fitz  # PyMuPDF
import re
import os
import base64
from typing import Optional, Dict, Any
import uvicorn
from pathlib import Path
import logging
import sys
from PIL import Image
import io

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('pdf_processor.log')
    ]
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="PDF Processor Service",
    description="Microserviço para extração de imagens de marca de PDFs de protocolo",
    version="1.0.0"
)

# Configurações
IMAGES_DIR = Path("extracted_images")
IMAGES_DIR.mkdir(exist_ok=True)

def extract_process_number(pdf_document) -> Optional[str]:
    """
    Extrai o número do processo do PDF
    """
    try:
        # Buscar nas primeiras páginas
        for page_num in range(min(3, pdf_document.page_count)):
            page = pdf_document[page_num]
            text = page.get_text()
            
            # Padrão: "Número do Processo: XXXXXXXXX"
            pattern = r"Número\s+do\s+Processo:\s*(\d+)"
            match = re.search(pattern, text, re.IGNORECASE)
            
            if match:
                logger.info(f"Número do processo encontrado: {match.group(1)}")
                return match.group(1)
        
        logger.warning("Número do processo não encontrado")
        return None
    except Exception as e:
        logger.error(f"Erro ao extrair número do processo: {e}")
        return None

def extract_elemento_nominativo(pdf_document) -> Optional[str]:
    """
    Extrai o elemento nominativo (nome da marca) do PDF
    """
    try:
        # Buscar nas primeiras páginas
        for page_num in range(min(3, pdf_document.page_count)):
            page = pdf_document[page_num]
            text = page.get_text()
            
            logger.info(f"Processando página {page_num + 1} para elemento nominativo...")
            
            # Dividir texto em linhas
            lines = text.split('\n')
            
            # Procurar "Elemento Nominativo:" e pegar a linha anterior
            for i, line in enumerate(lines):
                line_clean = line.strip()
                logger.info(f"   Linha {i}: '{line_clean}'")
                
                if re.search(r"Elemento\s+Nominativo", line_clean, re.IGNORECASE):
                    logger.info(f"Encontrou 'Elemento Nominativo' na linha {i}")
                    
                    # Verificar linha anterior
                    if i > 0:
                        linha_anterior = lines[i-1].strip()
                        logger.info(f"   Linha anterior: '{linha_anterior}'")
                        
                        # Verificar se a linha anterior não está vazia e não é texto estrutural
                        if (linha_anterior and 
                            len(linha_anterior) > 0 and
                            not re.search(r'(Apresentação|Natureza|Produto|serviço|Mista):', linha_anterior, re.IGNORECASE)):
                            
                            # Retornar o elemento nominativo original (sem limpeza)
                            logger.info(f"Elemento nominativo encontrado: '{linha_anterior}'")
                            return linha_anterior
        
        logger.warning("Elemento nominativo não encontrado")
        return None
    except Exception as e:
        logger.error(f"Erro ao extrair elemento nominativo: {e}")
        return None

def extract_titulares_data(pdf_document) -> list:
    """
    Extrai dados dos titulares/requerentes do PDF
    """
    try:
        titulares = []
        
        # Buscar nas primeiras páginas onde geralmente estão os dados dos requerentes
        for page_num in range(min(5, pdf_document.page_count)):
            page = pdf_document[page_num]
            text = page.get_text()
            
            logger.info(f"Processando página {page_num + 1} para dados dos titulares...")
            
            # Dividir texto em linhas
            lines = text.split('\n')
            
            # Procurar seção "Dados do(s) requerente(s)"
            section_found = False
            for i, line in enumerate(lines):
                line_clean = line.strip()
                
                if re.search(r"Dados\s+do\(s\)\s+requerente\(s\)", line_clean, re.IGNORECASE):
                    logger.info(f"Encontrou seção 'Dados do(s) requerente(s)' na linha {i}")
                    section_found = True
                    
                    # Processar a partir da próxima linha após a linha divisória
                    start_index = i + 1
                    
                    # Pular linha divisória se existir
                    while start_index < len(lines) and lines[start_index].strip() in ['', '----------', '─' * 10]:
                        start_index += 1
                    
                    # Processar dados dos titulares
                    titulares = parse_titulares_section(lines[start_index:])
                    break
            
            if section_found:
                break
        
        if not titulares:
            logger.warning("Nenhum titular encontrado no PDF")
        else:
            logger.info(f"Total de {len(titulares)} titular(es) encontrado(s)")
        
        return titulares
        
    except Exception as e:
        logger.error(f"Erro ao extrair dados dos titulares: {e}")
        return []

def parse_titulares_section(lines: list) -> list:
    """
    Analisa a seção de titulares e extrai dados de cada um
    Usa uma abordagem mais robusta para lidar com texto desordenado
    """
    titulares = []
    
    logger.info(f"Analisando {len(lines)} linhas da seção de titulares...")
    
    # Primeiro, vamos mapear todos os dados encontrados
    all_data = []
    
    try:
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Pular linhas vazias
            if not line:
                i += 1
                continue
            
            logger.info(f"   Linha {i}: '{line}'")
            
            # Parar se chegou em seções definitivamente não-titulares
            if re.search(r"(Dados\s+da\s+Marca|Apresentação\s+da\s+Marca|Produto|Serviço)", line, re.IGNORECASE):
                logger.info(f"Encontrou seção não-titular: '{line}' - parando parsing")
                break
            
            # Pular campos internos
            if re.search(r"(Natureza\s+Jurídica|Tipo\s+de\s+Pessoa|e-mail:)", line, re.IGNORECASE):
                logger.info(f"   Pulando campo interno: '{line}'")
                i += 1
                continue
            
            # Mapear dados encontrados
            data_item = {'line_num': i, 'content': line, 'type': 'unknown'}
            
            if line.lower().startswith('nome:'):
                nome = line[5:].strip()
                if nome:
                    data_item['type'] = 'nome'
                    data_item['value'] = nome
                    logger.info(f"   Mapeado Nome: {nome}")
                
            elif re.search(r"CPF/CNPJ/N[úu]mero\s+INPI:", line, re.IGNORECASE):
                documento = re.sub(r"CPF/CNPJ/N[úu]mero\s+INPI:\s*", "", line, flags=re.IGNORECASE).strip()
                if documento:
                    data_item['type'] = 'documento'
                    data_item['value'] = documento
                    logger.info(f"   Mapeado Documento: {documento}")
                
            elif line.lower().startswith('endereço:'):
                endereco = line[9:].strip()
                if endereco:
                    data_item['type'] = 'endereco'
                    data_item['value'] = endereco
                    logger.info(f"   Mapeado Endereço: {endereco}")
                
            elif line.lower().startswith('cidade:'):
                cidade = line[7:].strip()
                if cidade:
                    data_item['type'] = 'cidade'
                    data_item['value'] = cidade
                    logger.info(f"   Mapeado Cidade: {cidade}")
                
            elif line.lower().startswith('estado:'):
                estado = line[7:].strip()
                if estado:
                    data_item['type'] = 'uf'
                    data_item['value'] = estado
                    logger.info(f"   Mapeado Estado: {estado}")
                
            elif line.lower().startswith('cep:'):
                cep = line[4:].strip()
                if cep:
                    data_item['type'] = 'cep'
                    data_item['value'] = cep
                    logger.info(f"   Mapeado CEP: {cep}")
                
            elif line.lower().startswith('pais:') or line.lower().startswith('país:'):
                pais = re.sub(r"Pa[íi]s:\s*", "", line, flags=re.IGNORECASE).strip()
                if pais:
                    data_item['type'] = 'pais'
                    data_item['value'] = pais
                    logger.info(f"   Mapeado País: {pais}")
            
            # Detectar valores soltos que podem ser de campos anteriores
            elif re.match(r'^[0-9]{11}$|^[0-9]{14}$|^[0-9]{8,15}$', line):
                # Possível CPF/CNPJ solto
                data_item['type'] = 'documento_solto'
                data_item['value'] = line
                logger.info(f"   Possível documento solto: {line}")
                
            elif re.match(r'^[0-9]{5}-?[0-9]{3}$', line):
                # Possível CEP solto
                data_item['type'] = 'cep_solto'
                data_item['value'] = line
                logger.info(f"   Possível CEP solto: {line}")
                
            elif line in ['Brasil', 'Argentina', 'Estados Unidos', 'Chile', 'Uruguai']:
                # Possível país solto
                data_item['type'] = 'pais_solto'
                data_item['value'] = line
                logger.info(f"   Possível país solto: {line}")
                
            elif line in ['SP', 'RJ', 'MG', 'RS', 'PR', 'SC', 'BA', 'GO', 'PE', 'CE', 'PA', 'MA', 'PB', 'ES', 'PI', 'AL', 'RN', 'MT', 'MS', 'DF', 'SE', 'RO', 'AC', 'AM', 'RR', 'AP', 'TO']:
                # Possível estado solto
                data_item['type'] = 'uf_solto'
                data_item['value'] = line
                logger.info(f"   Possível estado solto: {line}")
                
            elif line in ['São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Salvador', 'Brasília', 'Fortaleza', 'Manaus', 'Curitiba', 'Recife', 'Goiânia', 'Belém', 'Porto Alegre', 'Guarulhos', 'Campinas', 'São Luís', 'São Gonçalo', 'Maceió', 'Duque de Caxias', 'Campo Grande', 'Natal', 'Teresina', 'São Bernardo do Campo', 'Nova Iguaçu', 'João Pessoa', 'Santo André', 'Osasco']:
                # Possível cidade solto
                data_item['type'] = 'cidade_solto'
                data_item['value'] = line
                logger.info(f"   Possível cidade solto: {line}")
            
            all_data.append(data_item)
            i += 1
        
        # Agora vamos reconstruir os titulares a partir dos dados mapeados
        logger.info(f"Reconstruindo titulares a partir de {len(all_data)} itens mapeados...")
        
        current_titular = {}
        
        for item in all_data:
            if item['type'] == 'nome' and 'value' in item:
                # Salvar titular anterior se existir
                if current_titular and 'nome' in current_titular:
                    titulares.append(current_titular.copy())
                    logger.info(f"Titular {len(titulares)} reconstruído: {current_titular['nome']}")
                
                # Iniciar novo titular
                current_titular = {'nome': item['value']}
                logger.info(f"   Novo titular iniciado: {item['value']}")
                
            elif 'value' in item and current_titular:
                # Adicionar dados ao titular atual
                if item['type'] == 'documento':
                    current_titular['numeroDocumento'] = item['value']
                elif item['type'] == 'endereco':
                    current_titular['endereco'] = item['value']
                elif item['type'] == 'cidade':
                    current_titular['cidade'] = item['value']
                elif item['type'] == 'uf':
                    current_titular['uf'] = item['value']
                elif item['type'] == 'cep':
                    current_titular['cep'] = item['value']
                elif item['type'] == 'pais':
                    current_titular['pais'] = item['value']
                # Tentar preencher campos vazios com valores soltos
                elif item['type'] == 'documento_solto' and 'numeroDocumento' not in current_titular:
                    current_titular['numeroDocumento'] = item['value']
                    logger.info(f"   Documento solto atribuído: {item['value']}")
                elif item['type'] == 'cep_solto' and 'cep' not in current_titular:
                    current_titular['cep'] = item['value']
                    logger.info(f"   CEP solto atribuído: {item['value']}")
                elif item['type'] == 'pais_solto' and 'pais' not in current_titular:
                    current_titular['pais'] = item['value']
                    logger.info(f"   País solto atribuído: {item['value']}")
                elif item['type'] == 'uf_solto' and 'uf' not in current_titular:
                    current_titular['uf'] = item['value']
                    logger.info(f"   Estado solto atribuído: {item['value']}")
                elif item['type'] == 'cidade_solto' and 'cidade' not in current_titular:
                    current_titular['cidade'] = item['value']
                    logger.info(f"   Cidade solto atribuído: {item['value']}")
        
        # Adicionar último titular se existir
        if current_titular and 'nome' in current_titular:
            titulares.append(current_titular.copy())
            logger.info(f"Último titular reconstruído: {current_titular['nome']}")
        
    except Exception as e:
        logger.error(f"Erro ao analisar seção de titulares: {e}")
    
    return titulares

def resize_logo_with_quality(image_bytes: bytes, target_size: tuple = (300, 300)) -> bytes:
    """
    Redimensiona a logo mantendo alta qualidade
    """
    try:
        # Abrir imagem do buffer
        original_image = Image.open(io.BytesIO(image_bytes))
        
        logger.info(f"Imagem original: {original_image.size} modo: {original_image.mode}")
        
        # Usar LANCZOS para máxima qualidade (melhor algoritmo para downscale)
        resized_image = original_image.resize(target_size, Image.Resampling.LANCZOS)
        
        # Salvar com alta qualidade
        output_buffer = io.BytesIO()
        resized_image.save(
            output_buffer,
            format='JPEG',
            quality=95,  # Alta qualidade
            optimize=True  # Otimização automática
        )
        
        resized_bytes = output_buffer.getvalue()
        logger.info(f"Logo redimensionada: {target_size} ({len(resized_bytes)} bytes)")
        
        return resized_bytes
        
    except Exception as e:
        logger.error(f"Erro ao redimensionar logo: {e}")
        return image_bytes  # Retorna original em caso de erro

def verify_image_digital_marca(pdf_document) -> bool:
    """
    Verifica se o PDF contém o texto "Imagem Digital da Marca"
    """
    try:
        for page_num in range(pdf_document.page_count):
            page = pdf_document[page_num]
            text = page.get_text().lower()
            
            if "imagem digital da marca" in text:
                logger.info("Texto 'Imagem Digital da Marca' encontrado")
                return True
        
        logger.warning("Texto 'Imagem Digital da Marca' não encontrado")
        return False
    except Exception as e:
        logger.error(f"Erro ao verificar texto: {e}")
        return False

def extract_logo_image(pdf_document, process_number: str) -> Optional[Dict[str, Any]]:
    """
    Extrai a imagem da logo (JPEG 1200x1200) do PDF
    """
    try:
        logger.info(f"Procurando logo para processo {process_number}...")
        
        for page_num in range(pdf_document.page_count):
            page = pdf_document[page_num]
            image_list = page.get_images()
            
            logger.info(f"Página {page_num + 1}: {len(image_list)} imagens encontradas")
            
            for img_index, img in enumerate(image_list):
                # Extrair informações da imagem
                xref = img[0]
                
                try:
                    # Obter dados da imagem
                    base_image = pdf_document.extract_image(xref)
                    image_bytes = base_image["image"]
                    image_ext = base_image["ext"]
                    width = base_image["width"]
                    height = base_image["height"]
                    
                    logger.info(f"   Imagem {img_index + 1}: {width}x{height} {image_ext.upper()}")
                    
                    # Verificar se é a logo (JPEG 1200x1200)
                    if (image_ext.lower() in ['jpg', 'jpeg'] and 
                        width == 1200 and height == 1200):
                        
                        logger.info(f"LOGO ENCONTRADA! {width}x{height} {image_ext.upper()}")
                        
                        # Redimensionar para 300x300 mantendo qualidade
                        resized_bytes = resize_logo_with_quality(image_bytes, (300, 300))
                        
                        # Converter para base64 para resposta (não salvar arquivo aqui)
                        image_b64 = base64.b64encode(resized_bytes).decode()
                        
                        # Nome sugerido para o arquivo
                        image_filename = f"{process_number}.jpg"
                        
                        logger.info(f"Logo processada: {image_filename} (300x300)")
                        
                        return {
                            "found": True,
                            "filename": image_filename,
                            "width": 300,  # Tamanho fixo
                            "height": 300,  # Tamanho fixo
                            "original_width": width,  # Tamanho original
                            "original_height": height,  # Tamanho original
                            "format": "jpg",
                            "size_bytes": len(resized_bytes),
                            "original_size_bytes": len(image_bytes),
                            "page": page_num + 1,
                            "image_data": image_b64
                        }
                        
                except Exception as img_error:
                    logger.error(f"Erro ao processar imagem {img_index}: {img_error}")
                    continue
        
        logger.warning("Logo JPEG 1200x1200 não encontrada")
        return None
        
    except Exception as e:
        logger.error(f"Erro na extração de imagem: {e}")
        return None

@app.on_event("startup")
async def startup_event():
    logger.info("PDF Processor Service iniciado!")
    logger.info(f"Diretório de imagens: {IMAGES_DIR.absolute()}")

@app.get("/")
async def root():
    return {"message": "PDF Processor Service - Microserviço para extração de imagens de marca"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "pdf-processor"}

@app.post("/extract-metadata")
async def extract_metadata(file: UploadFile = File(...)):
    """
    Extrai apenas os metadados do PDF (número do processo e elemento nominativo)
    """
    try:
        # Verificar se é PDF
        if not file.content_type == "application/pdf":
            raise HTTPException(
                status_code=400, 
                detail="Arquivo deve ser um PDF"
            )
        
        # Ler o arquivo
        pdf_bytes = await file.read()
        
        logger.info(f"Extraindo metadados do PDF: {file.filename}")
        
        # Abrir PDF com PyMuPDF
        pdf_document = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Extrair número do processo
        process_number = extract_process_number(pdf_document)
        
        # Extrair elemento nominativo
        elemento_nominativo = extract_elemento_nominativo(pdf_document)
        
        pdf_document.close()
        
        return {
            "success": True,
            "data": {
                "process_number": process_number,
                "elemento_nominativo": elemento_nominativo,
                "suggested_filename": f"{process_number}-{elemento_nominativo}.pdf" if process_number and elemento_nominativo else None
            }
        }
        
    except Exception as e:
        logger.error(f"Erro ao extrair metadados: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Erro interno do servidor: {str(e)}"
            }
        )

@app.post("/extract-complete-data")
async def extract_complete_data(file: UploadFile = File(...)):
    """
    Extrai dados completos do PDF: metadados + titulares
    """
    try:
        # Verificar se é PDF
        if not file.content_type == "application/pdf":
            raise HTTPException(
                status_code=400, 
                detail="Arquivo deve ser um PDF"
            )
        
        # Ler o arquivo
        pdf_bytes = await file.read()
        
        logger.info(f"Extraindo dados completos do PDF: {file.filename}")
        
        # Abrir PDF com PyMuPDF
        pdf_document = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        # Extrair metadados
        process_number = extract_process_number(pdf_document)
        elemento_nominativo = extract_elemento_nominativo(pdf_document)
        
        # Extrair dados dos titulares
        titulares = extract_titulares_data(pdf_document)
        
        pdf_document.close()
        
        logger.info(f"Extração completa concluída:")
        logger.info(f"  - Processo: {process_number}")
        logger.info(f"  - Elemento Nominativo: {elemento_nominativo}")
        logger.info(f"  - Titulares encontrados: {len(titulares)}")
        
        return {
            "success": True,
            "data": {
                "process_number": process_number,
                "elemento_nominativo": elemento_nominativo,
                "titulares": titulares,
                "total_titulares": len(titulares),
                "suggested_filename": f"{process_number}-{elemento_nominativo}.pdf" if process_number and elemento_nominativo else None
            }
        }
        
    except Exception as e:
        logger.error(f"Erro ao extrair dados completos: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Erro interno do servidor: {str(e)}"
            }
        )

@app.post("/extract-logo")
async def extract_logo(file: UploadFile = File(...)):
    """
    Extrai a logo da marca de um PDF de protocolo
    Logo é automaticamente redimensionada para 300x300px
    """
    try:
        # Verificar se é PDF
        if not file.content_type == "application/pdf":
            raise HTTPException(
                status_code=400, 
                detail="Arquivo deve ser um PDF"
            )
        
        # Ler o arquivo
        pdf_bytes = await file.read()
        
        logger.info(f"Processando PDF: {file.filename} ({len(pdf_bytes)} bytes)")
        
        # Abrir PDF com PyMuPDF
        pdf_document = fitz.open(stream=pdf_bytes, filetype="pdf")
        
        logger.info(f"PDF carregado: {pdf_document.page_count} páginas")
        
        # 1. Extrair número do processo
        process_number = extract_process_number(pdf_document)
        if not process_number:
            pdf_document.close()
            return JSONResponse(
                status_code=422,
                content={
                    "success": False,
                    "error": "Número do processo não encontrado no PDF"
                }
            )
        
        # 2. Verificar texto "Imagem Digital da Marca"
        has_marca_text = verify_image_digital_marca(pdf_document)
        if not has_marca_text:
            pdf_document.close()
            return JSONResponse(
                status_code=422,
                content={
                    "success": False,
                    "error": "Texto 'Imagem Digital da Marca' não encontrado",
                    "process_number": process_number
                }
            )
        
        # 3. Extrair logo
        logo_result = extract_logo_image(pdf_document, process_number)
        
        pdf_document.close()
        
        if logo_result:
            logger.info(f"Sucesso! Logo extraída para processo {process_number}")
            return {
                "success": True,
                "message": "Logo extraída com sucesso!",
                "process_number": process_number,
                "logo": logo_result
            }
        else:
            return JSONResponse(
                status_code=422,
                content={
                    "success": False,
                    "error": "Logo JPEG 1200x1200 não encontrada no PDF",
                    "process_number": process_number
                }
            )
        
    except Exception as e:
        logger.error(f"Erro geral: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Erro interno do servidor: {str(e)}"
            }
        )

if __name__ == "__main__":
    # Configurações para produção
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    
    logger.info(f"Iniciando PDF Processor Service em {host}:{port}")
    
    uvicorn.run(
        "main:app", 
        host=host, 
        port=port, 
        reload=False,  # Desabilitar reload em produção
        log_level="info",
        access_log=True
    ) 