import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

const prisma = new PrismaClient();

// Array com todas as RPIs a serem analisadas
const RPIS_ANALISE = Array.from({length: 30}, (_, i) => 2800 + i); // Gera array de 2800 a 2829

interface AnaliseTempo {
  tempoTotal: number;
  tempoMinimo: number;
  tempoMaximo: number;
  processosAnalisados: number;
  tempos: number[];
  detalhesProcessos: string[];
}

interface ResultadoAnalise {
  semDespachoIntermediario: AnaliseTempo;
  comSobrestamento: AnaliseTempo;
  comExigencia: AnaliseTempo;
  outros: AnaliseTempo;
}

function inicializarAnalise(): AnaliseTempo {
  return {
    tempoTotal: 0,
    tempoMinimo: Number.MAX_VALUE,
    tempoMaximo: 0,
    processosAnalisados: 0,
    tempos: [],
    detalhesProcessos: []
  };
}

function calcularEstatisticas(analise: AnaliseTempo) {
  if (analise.processosAnalisados === 0) return null;

  const media = analise.tempoTotal / analise.processosAnalisados;
  
  const somaDiferencasQuadrado = analise.tempos.reduce((acc, tempo) => {
    const diferenca = tempo - media;
    return acc + (diferenca * diferenca);
  }, 0);
  
  const desvioPadrao = Math.sqrt(somaDiferencasQuadrado / analise.processosAnalisados);
  
  analise.tempos.sort((a, b) => a - b);
  const mediana = analise.processosAnalisados % 2 === 0
    ? (analise.tempos[analise.processosAnalisados/2 - 1] + analise.tempos[analise.processosAnalisados/2]) / 2
    : analise.tempos[Math.floor(analise.processosAnalisados/2)];

  return {
    media,
    desvioPadrao,
    mediana,
    minimo: analise.tempoMinimo,
    maximo: analise.tempoMaximo
  };
}

function gerarDistribuicaoTempos(analise: AnaliseTempo, intervalos: number[]): string {
  let resultado = '';
  let temposAnteriores = 0;
  
  for (const intervalo of intervalos) {
    const quantidade = analise.tempos.filter(t => t > temposAnteriores && t <= intervalo).length;
    const percentual = ((quantidade / analise.processosAnalisados) * 100).toFixed(2);
    resultado += `${temposAnteriores + 1} a ${intervalo} dias: ${quantidade} processos (${percentual}%)\n`;
    temposAnteriores = intervalo;
  }
  
  const quantidadeRestante = analise.tempos.filter(t => t > intervalos[intervalos.length - 1]).length;
  const percentualRestante = ((quantidadeRestante / analise.processosAnalisados) * 100).toFixed(2);
  resultado += `Mais de ${intervalos[intervalos.length - 1]} dias: ${quantidadeRestante} processos (${percentualRestante}%)\n`;
  
  return resultado;
}

/**
 * Analisa o tempo entre a "Notificação de recurso" (contra indeferimento)
 * e a decisão subsequente ("Recurso provido" ou "Recurso não provido")
 * Filtrando por decisões nas RPIs 2800 a 2829.
 */
async function analisarTempoRecursoDecisao() {
  try {
    console.log('Iniciando análise do tempo entre notificação de recurso e decisão...');
    console.log(`Filtrando por decisões de recurso nas RPIs ${RPIS_ANALISE[0]} a ${RPIS_ANALISE[RPIS_ANALISE.length-1]}\n`);

    let conteudoArquivo = 'ANÁLISE DO TEMPO ENTRE NOTIFICAÇÃO DE RECURSO (CONTRA INDEFERIMENTO) E DECISÃO\n';
    conteudoArquivo += `RPIs da Decisão Analisadas: ${RPIS_ANALISE[0]} a ${RPIS_ANALISE[RPIS_ANALISE.length-1]}\n`;
    conteudoArquivo += '='.repeat(80) + '\n\n';

    const processos = await prisma.processo.findMany({
      where: {
        despachos: {
          some: {
            nome: { contains: 'Notificação de recurso', mode: 'insensitive' },
            textoComplementar: { contains: 'Recurso contra indeferimento', mode: 'insensitive' }
          }
        },
        AND: {
          despachos: {
            some: {
              AND: [
                {
                  OR: [
                    { nome: { contains: 'Recurso provido', mode: 'insensitive' } },
                    { nome: { contains: 'Recurso não provido', mode: 'insensitive' } }
                  ]
                },
                {
                  rpi: {
                    numero: {
                      in: RPIS_ANALISE
                    }
                  }
                }
              ]
            }
          }
        }
      },
      include: {
        despachos: {
          include: {
            rpi: true
          },
          orderBy: {
            rpi: {
              dataPublicacao: 'asc'
            }
          }
        }
      }
    });

    console.log(`Encontrados ${processos.length} processos com notificação de recurso e decisão nas RPIs ${RPIS_ANALISE[0]}-${RPIS_ANALISE[RPIS_ANALISE.length-1]}.`);
    conteudoArquivo += `Total de processos encontrados: ${processos.length}\n\n`;

    const resultado: ResultadoAnalise = {
      semDespachoIntermediario: inicializarAnalise(),
      comSobrestamento: inicializarAnalise(),
      comExigencia: inicializarAnalise(),
      outros: inicializarAnalise()
    };

    for (const processo of processos) {
      const indexNotificacao = processo.despachos.findIndex(d => 
        d.nome?.toLowerCase().includes('notificação de recurso') &&
        d.textoComplementar?.toLowerCase().includes('recurso contra indeferimento')
      );

      if (indexNotificacao === -1) continue;

      const indexDecisao = processo.despachos.findIndex((d, index) => 
        index > indexNotificacao &&
        (d.nome?.toLowerCase().includes('recurso provido') || 
         d.nome?.toLowerCase().includes('recurso não provido')) &&
        RPIS_ANALISE.includes(d.rpi?.numero || 0)
      );

      if (indexDecisao !== -1) {
        const notificacao = processo.despachos[indexNotificacao];
        const decisao = processo.despachos[indexDecisao];

        if (notificacao?.rpi?.dataPublicacao && 
            decisao?.rpi?.dataPublicacao && 
            RPIS_ANALISE.includes(decisao.rpi.numero)) {
          
          const dataNotificacao = new Date(notificacao.rpi.dataPublicacao);
          const dataDecisao = new Date(decisao.rpi.dataPublicacao);
          
          if (dataDecisao <= dataNotificacao) continue;

          const diffTime = Math.abs(dataDecisao.getTime() - dataNotificacao.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          // Verificar despachos intermediários
          const despachosIntermediarios = processo.despachos.filter((d, index) => 
            index > indexNotificacao && 
            index < indexDecisao &&
            d.nome !== notificacao.nome &&
            d.nome !== decisao.nome
          );

          const temSobrestamento = despachosIntermediarios.some(d => 
            d.nome?.toLowerCase().includes('sobrestamento')
          );

          const temExigencia = despachosIntermediarios.some(d => 
            d.nome?.toLowerCase().includes('exigência')
          );

          let categoria: keyof ResultadoAnalise;
          let categoriaTexto: string;

          if (despachosIntermediarios.length === 0) {
            categoria = 'semDespachoIntermediario';
            categoriaTexto = 'Sem despachos intermediários';
          } else if (temSobrestamento) {
            categoria = 'comSobrestamento';
            categoriaTexto = 'Com sobrestamento';
          } else if (temExigencia) {
            categoria = 'comExigencia';
            categoriaTexto = 'Com exigência';
          } else {
            categoria = 'outros';
            categoriaTexto = 'Com outros despachos intermediários';
          }

          const analise = resultado[categoria];
          analise.tempos.push(diffDays);
          analise.tempoTotal += diffDays;
          analise.tempoMinimo = Math.min(analise.tempoMinimo, diffDays);
          analise.tempoMaximo = Math.max(analise.tempoMaximo, diffDays);
          analise.processosAnalisados++;

          const despachosIntermText = despachosIntermediarios.length > 0 
            ? '\n' + despachosIntermediarios.map(d => 
                `  - ${d.nome} (${new Date(d.rpi.dataPublicacao).toLocaleDateString('pt-BR')})`
              ).join('\n')
            : ' Nenhum';

          analise.detalhesProcessos.push(
            `Processo: ${processo.numero}\n` +
            `Categoria: ${categoriaTexto}\n` +
            `Data Notificação Recurso: ${dataNotificacao.toLocaleDateString('pt-BR')}\n` +
            `Decisão: ${decisao.nome}\n` +
            `RPI Decisão: ${decisao.rpi.numero}\n` +
            `Data Decisão: ${dataDecisao.toLocaleDateString('pt-BR')}\n` +
            `Tempo decorrido: ${diffDays} dias\n` +
            `Despachos intermediários:${despachosIntermText}\n`
          );
        }
      }
    }

    const totalProcessosAnalisados = Object.values(resultado).reduce(
      (total, analise) => total + analise.processosAnalisados, 
      0
    );

    if (totalProcessosAnalisados === 0) {
      const mensagem = `Nenhum processo encontrado com os critérios para análise (decisão nas RPIs ${RPIS_ANALISE[0]}-${RPIS_ANALISE[RPIS_ANALISE.length-1]}).`;
      console.log(mensagem);
      conteudoArquivo += mensagem;
      const nomeArquivoZero = `analise_recurso_decisao_rpi${RPIS_ANALISE[0]}_${RPIS_ANALISE[RPIS_ANALISE.length-1]}_${new Date().toISOString().split('T')[0]}.txt`;
      fs.writeFileSync(nomeArquivoZero, conteudoArquivo);
      console.log(`Arquivo de análise (sem resultados) salvo em: ${nomeArquivoZero}`);
      return;
    }

    // Adicionar resultados por categoria
    const categorias = {
      semDespachoIntermediario: 'PROCESSOS SEM DESPACHOS INTERMEDIÁRIOS',
      comSobrestamento: 'PROCESSOS COM SOBRESTAMENTO',
      comExigencia: 'PROCESSOS COM EXIGÊNCIA',
      outros: 'PROCESSOS COM OUTROS DESPACHOS INTERMEDIÁRIOS'
    };

    for (const [key, titulo] of Object.entries(categorias)) {
      const analise = resultado[key as keyof ResultadoAnalise];
      if (analise.processosAnalisados === 0) continue;

      const stats = calcularEstatisticas(analise);
      if (!stats) continue;

      conteudoArquivo += `\n${titulo}\n`;
      conteudoArquivo += '='.repeat(titulo.length) + '\n\n';
      conteudoArquivo += 'RESULTADOS ESTATÍSTICOS\n';
      conteudoArquivo += '-'.repeat(30) + '\n';
      conteudoArquivo += `Total de processos: ${analise.processosAnalisados}\n`;
      conteudoArquivo += `Tempo médio: ${stats.media.toFixed(2)} dias\n`;
      conteudoArquivo += `Desvio padrão: ${stats.desvioPadrao.toFixed(2)} dias\n`;
      conteudoArquivo += `Mediana: ${stats.mediana.toFixed(2)} dias\n`;
      conteudoArquivo += `Tempo mínimo: ${stats.minimo} dias\n`;
      conteudoArquivo += `Tempo máximo: ${stats.maximo} dias\n\n`;

      conteudoArquivo += 'DISTRIBUIÇÃO DOS TEMPOS\n';
      conteudoArquivo += '-'.repeat(30) + '\n';
      conteudoArquivo += gerarDistribuicaoTempos(analise, [30, 60, 90, 120, 180, 365, 730, 1095]);
      conteudoArquivo += '\n';

      conteudoArquivo += 'DETALHES DOS PROCESSOS\n';
      conteudoArquivo += '-'.repeat(30) + '\n';
      analise.detalhesProcessos.forEach((detalhe, index) => {
        conteudoArquivo += detalhe + (index < analise.detalhesProcessos.length - 1 ? '\n' : '');
      });
      conteudoArquivo += '\n';
    }

    // Salvar arquivo
    const nomeArquivo = `analise_recurso_decisao_rpi${RPIS_ANALISE[0]}_${RPIS_ANALISE[RPIS_ANALISE.length-1]}_${new Date().toISOString().split('T')[0]}.txt`;
    fs.writeFileSync(nomeArquivo, conteudoArquivo);
    
    console.log(`\nAnálise completa! Resultados salvos em: ${nomeArquivo}`);
    console.log(`Total de processos analisados: ${totalProcessosAnalisados}`);
    
    // Mostrar resumo por categoria no console
    for (const [key, titulo] of Object.entries(categorias)) {
      const analise = resultado[key as keyof ResultadoAnalise];
      if (analise.processosAnalisados === 0) continue;

      const stats = calcularEstatisticas(analise);
      if (!stats) continue;

      console.log(`\n${titulo}:`);
      console.log(`Processos: ${analise.processosAnalisados}`);
      console.log(`Tempo médio: ${stats.media.toFixed(2)} dias`);
    }

  } catch (error) {
    console.error('Erro ao analisar tempo entre notificação de recurso e decisão:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar a análise
analisarTempoRecursoDecisao(); 