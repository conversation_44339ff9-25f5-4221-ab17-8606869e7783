import axios from "axios";
import querystring from "querystring";

export const dialogoMapping: Record<
  string,
  { idEtapa: number; dialogoChatguru: string; estagioProcesso: string }
> = {
  Monitoramento: {
    idEtapa: 341596,
    dialogoChatguru: "663e65dfa00a441168e435f4",
    estagioProcesso: "Protocolado",
  },
  "Informar publicação": {
    idEtapa: 354223,
    dialogoChatguru: "663bcfcfe6f36a039fb28842",
    estagioProcesso: "Publicado",
  },
  "Sem oposição": {
    idEtapa: 525591,
    dialogoChatguru: "663bd085bd2c0af29b50cc3c",
    estagioProcesso: "Fim do prazo de oposição",
  },
  "Recebeu oposição": {
    idEtapa: 341599,
    dialogoChatguru: "663bd09be6f36a039fb288c0",
    estagioProcesso: "Fim do prazo de oposição",
  },
  Sobrestamentos: {
    idEtapa: 468316,
    dialogoChatguru: "663bd0ab3cf86f6eda9fb85f",
    estagioProcesso: "Sobrestado",
  },
  "Exigência INPI": {
    idEtapa: 341600,
    dialogoChatguru: "663bd10a6d078022570907bf",
    estagioProcesso: "Com exigência",
  },
  "faltam 25 meses": {
    idEtapa: 525414,
    dialogoChatguru: "663bd122e368858fcb298162",
    estagioProcesso: "faltam 25 meses",
  },
  "faltam 24 meses": {
    idEtapa: 525415,
    dialogoChatguru: "663bd12d0c4f43f53c4b73d1",
    estagioProcesso: "faltam 24 meses",
  },
  "faltam 23 meses": {
    idEtapa: 525416,
    dialogoChatguru: "663bd13eccb8e028af53d825",
    estagioProcesso: "faltam 23 meses",
  },
  "faltam 22 meses": {
    idEtapa: 525417,
    dialogoChatguru: "663bd1509fc460955d1a2621",
    estagioProcesso: "faltam 22 meses",
  },
  "faltam 21 meses": {
    idEtapa: 525418,
    dialogoChatguru: "663bd155e85d57333a439f9e",
    estagioProcesso: "faltam 21 meses",
  },
  "faltam 20 meses": {
    idEtapa: 525419,
    dialogoChatguru: "663bd1583dd65e4fa0247126",
    estagioProcesso: "faltam 20 meses",
  },
  "faltam 19 meses": {
    idEtapa: 525420,
    dialogoChatguru: "663bd15c174eed33ca6901c6",
    estagioProcesso: "faltam 19 meses",
  },
  "faltam 18 meses": {
    idEtapa: 525421,
    dialogoChatguru: "663bd15f6d078022570907e4",
    estagioProcesso: "faltam 18 meses",
  },
  "faltam 17 meses": {
    idEtapa: 525422,
    dialogoChatguru: "663bd163174eed33ca6901cb",
    estagioProcesso: "faltam 17 meses",
  },
  "faltam 16 meses": {
    idEtapa: 525423,
    dialogoChatguru: "663bd1661d532ed9fdcf708e",
    estagioProcesso: "faltam 16 meses",
  },
  "faltam 15 meses": {
    idEtapa: 525424,
    dialogoChatguru: "663bd16a9fc460955d1a262c",
    estagioProcesso: "faltam 15 meses",
  },
  "faltam 14 meses": {
    idEtapa: 525425,
    dialogoChatguru: "663bd1709fc460955d1a2631",
    estagioProcesso: "faltam 14 meses",
  },
  "faltam 13 meses": {
    idEtapa: 525426,
    dialogoChatguru: "663bd192e85d57333a439fb5",
    estagioProcesso: "faltam 13 meses",
  },
  "faltam 12 meses": {
    idEtapa: 525427,
    dialogoChatguru: "663bd196c18179af28bb7164",
    estagioProcesso: "faltam 12 meses",
  },
  "faltam 11 meses": {
    idEtapa: 525428,
    dialogoChatguru: "663bd19a85e092493b14cc93",
    estagioProcesso: "faltam 11 meses",
  },
  "faltam 10 meses": {
    idEtapa: 525429,
    dialogoChatguru: "663bd19e11814ad84f67ac50",
    estagioProcesso: "faltam 10 meses",
  },
  "faltam 9 meses": {
    idEtapa: 525430,
    dialogoChatguru: "663bd1a1e368858fcb29819e",
    estagioProcesso: "faltam 9 meses",
  },
  "faltam 8 meses": {
    idEtapa: 525431,
    dialogoChatguru: "663bd1a53dd65e4fa0247139",
    estagioProcesso: "faltam 8 meses",
  },
  "faltam 7 meses": {
    idEtapa: 525432,
    dialogoChatguru: "663bd1a948d55cb1f3d3909d",
    estagioProcesso: "faltam 7 meses",
  },
  "faltam 6 meses": {
    idEtapa: 525433,
    dialogoChatguru: "663bd1ac27b4bb4e5ca48434",
    estagioProcesso: "faltam 6 meses",
  },
  "faltam 5 meses": {
    idEtapa: 525434,
    dialogoChatguru: "663bd1b0cac64aa1c6b66e41",
    estagioProcesso: "faltam 5 meses",
  },
  "faltam 4 meses": {
    idEtapa: 525435,
    dialogoChatguru: "663bd1d53cf86f6eda9fb8d6",
    estagioProcesso: "faltam 4 meses",
  },
  "faltam 3 meses": {
    idEtapa: 525436,
    dialogoChatguru: "663bd1db993ea10503a4186c",
    estagioProcesso: "faltam 3 meses",
  },
  "faltam 2 meses": {
    idEtapa: 525437,
    dialogoChatguru: "663bd1df48d55cb1f3d390ab",
    estagioProcesso: "faltam 2 meses",
  },
  "falta cerca de 1 mês": {
    idEtapa: 525438,
    dialogoChatguru: "663bd1e411814ad84f67ac5b",
    estagioProcesso: "falta cerca de 1 mês",
  },
  "Falta pouco": {
    idEtapa: 525439,
    dialogoChatguru: "663bd1eca55f16dee773f2a0",
    estagioProcesso: "Falta pouco",
  },
  Atrasado: {
    idEtapa: 525440,
    dialogoChatguru: "663bd1f19f6b71f310b50b62",
    estagioProcesso: "Atrasado",
  },
  Deferido: {
    idEtapa: 341602,
    dialogoChatguru: "663bd20ce85d57333a439fd7",
    estagioProcesso: "Deferido",
  },
  Indeferido: {
    idEtapa: 341603,
    dialogoChatguru: "663bd2106d07802257090812",
    estagioProcesso: "Indeferido",
  },
};

export async function executeDialog(
  dialogId: string,
  chatNumber: string
): Promise<any> {
  console.log("📱 Número original recebido:", chatNumber);

  // Remover caracteres especiais e espaços
  let cleanNumber = chatNumber.replace(/\D/g, "");
  console.log("📱 Número após limpeza:", cleanNumber);

  // Garantir que o número tenha o formato correto (*************)
  if (!cleanNumber.startsWith("55")) {
    cleanNumber = "55" + cleanNumber;
  }
  console.log("📱 Número com código do país:", cleanNumber);

  const key = process.env.CHATGURU_API_KEY;
  const accountId = process.env.CHATGURU_ACCOUNT_ID;
  const phoneId = process.env.CHATGURU_PHONE_ID;
  const action = "dialog_execute";

  if (!key || !accountId || !phoneId) {
    console.error("❌ Credenciais do Chatguru não configuradas");
    throw new Error("Credenciais do Chatguru não configuradas");
  }

  const sendRequest = async (
    formattedNumber: string,
    retryCount: number = 0
  ): Promise<any> => {
    try {
      console.log(
        "📤 Tentando enviar requisição para número:",
        formattedNumber
      );

      const chatGuruData = {
        key,
        account_id: accountId,
        phone_id: phoneId,
        dialog_id: dialogId,
        chat_number: formattedNumber,
        action,
      };

      console.log("🔑 Dados da requisição:", {
        ...chatGuruData,
        key: "***",
        account_id: "***",
        phone_id: "***",
      });

      const formData = querystring.stringify(chatGuruData);
      console.log(
        "📦 Body da requisição:",
        formData.replace(/(key=|account_id=|phone_id=)[^&]+/g, "$1***")
      );

      const response = await axios.post(
        "https://s16.chatguru.app/api/v1",
        formData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      console.log(
        `📥 Resposta do Chatguru (${response.status}):`,
        response.data
      );
      console.log(
        "✅ Diálogo executado com sucesso para o número:",
        formattedNumber
      );

      return response.data;
    } catch (error: any) {
      if (error.response && error.response.status === 400 && retryCount < 2) {
        console.log(
          `❌ Erro 400 ao executar diálogo para ${formattedNumber} (tentativa ${
            retryCount + 1
          } de 2)`
        );
        const reformattedChatNumber = reformatChatNumber(formattedNumber);
        if (reformattedChatNumber !== formattedNumber) {
          console.log(
            "🔄 Tentando com número reformatado:",
            reformattedChatNumber
          );
          return await sendRequest(reformattedChatNumber, retryCount + 1);
        }
      }
      console.error(
        "❌ Erro na requisição ao Chatguru:",
        error.response?.data || error.message
      );
      throw error;
    }
  };

  return await sendRequest(cleanNumber);
}

function reformatChatNumber(chatNumber: string): string {
  if (!chatNumber || typeof chatNumber !== "string") {
    throw new Error("Número de chat inválido");
  }

  const countryCode = chatNumber.slice(0, 2); // Código do país
  const areaCode = chatNumber.slice(2, 4); // Código de área
  let phoneNumber = chatNumber.slice(4);

  if (phoneNumber.length === 9) {
    phoneNumber = phoneNumber.slice(1);
  } else if (phoneNumber.length === 8) {
    phoneNumber = "9" + phoneNumber;
  }

  return countryCode + areaCode + phoneNumber;
}

export async function updateChatGuruCustomField(
  chatNumber: string,
  customFieldVariable: string, // Ex: 'cpf_cnpj' ou a variável do seu campo no ChatGuru
  customFieldValue: string
): Promise<any> {
  console.log(
    `🔄 Iniciando atualização de campo personalizado para: ${chatNumber}, Campo: ${customFieldVariable}, Valor: ${customFieldValue}`
  );

  // Remover caracteres especiais e espaços
  let cleanNumber = chatNumber.replace(/\D/g, "");
  console.log("📱 Número após limpeza para atualização:", cleanNumber);

  // Garantir que o número tenha o formato correto (*************)
  if (!cleanNumber.startsWith("55")) {
    cleanNumber = "55" + cleanNumber;
  }
  console.log("📱 Número com código do país para atualização:", cleanNumber);

  const key = process.env.CHATGURU_API_KEY;
  const accountId = process.env.CHATGURU_ACCOUNT_ID;
  const phoneId = process.env.CHATGURU_PHONE_ID;
  const action = "chat_update_custom_fields";

  if (!key || !accountId || !phoneId) {
    console.error("❌ Credenciais do Chatguru não configuradas para atualização de campo.");
    throw new Error("Credenciais do Chatguru não configuradas");
  }

  const sendRequest = async (
    formattedNumber: string,
    retryCount: number = 0
  ): Promise<any> => {
    try {
      console.log(
        `📤 Tentando atualizar campo para número: ${formattedNumber} (tentativa ${retryCount + 1})`
      );

      const chatGuruData: any = {
        key,
        account_id: accountId,
        phone_id: phoneId,
        chat_number: formattedNumber,
        action,
      };
      // Adiciona o campo personalizado dinamicamente
      chatGuruData[`field__${customFieldVariable}`] = customFieldValue;

      console.log("🔑 Dados da requisição para atualização:", {
        ...chatGuruData,
        key: "***",
        account_id: "***",
        phone_id: "***",
      });

      const formData = querystring.stringify(chatGuruData);
      console.log(
        "📦 Body da requisição para atualização:",
        formData.replace(/(key=|account_id=|phone_id=)[^&]+/g, "$1***")
      );

      const response = await axios.post(
        process.env.CHATGURU_API_ENDPOINT || "https://s16.chatguru.app/api/v1", // Usar variável de ambiente para endpoint
        formData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      console.log(
        `📥 Resposta do Chatguru para atualização (${response.status}):`,
        response.data
      );
      console.log(
        "✅ Campo personalizado atualizado com sucesso para o número:",
        formattedNumber
      );

      return response.data;
    } catch (error: any) {
      if (error.response && error.response.status === 400 && retryCount < 2) { // Mantendo a lógica de 2 tentativas (total)
        console.warn(
          `⚠️ Erro 400 ao atualizar campo para ${formattedNumber} (tentativa ${retryCount + 1})`,
          error.response?.data || error.message
        );
        const reformattedChatNumber = reformatChatNumber(formattedNumber); // Reutilizando sua função de reformatar
        if (reformattedChatNumber !== formattedNumber) {
          console.log(
            "🔄 Tentando com número reformatado para atualização:",
            reformattedChatNumber
          );
          return await sendRequest(reformattedChatNumber, retryCount + 1);
        }
      }
      console.error(
        "❌ Erro na requisição ao Chatguru para atualização de campo:",
        error.response?.data || error.message
      );
      throw error; // Propaga o erro se não for tratado ou após retentativas
    }
  };

  return await sendRequest(cleanNumber);
}
