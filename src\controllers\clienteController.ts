import { Request, Response } from 'express'; // Exemplo para Express.js
import { getIdentificadorPorNumeroProcesso } from '../services/clienteService';

/**
 * Controlador para lidar com a requisição GET /cliente/identificador-por-processo/:numeroProcesso
 */
export async function handleGetIdentificadorPorNumeroProcesso(req: Request, res: Response) {
  const { numeroProcesso } = req.params;

  if (!numeroProcesso) {
    return res.status(400).json({ error: 'Parâmetro numeroProcesso não fornecido na URL.' });
  }

  try {
    const resultado = await getIdentificadorPorNumeroProcesso(numeroProcesso as string);

    if (resultado.error) {
      // Determinar o status code baseado no tipo de erro
      if (resultado.error.includes('não encontrado')) {
        return res.status(404).json(resultado);
      }
      if (resultado.error.includes('não fornecido ou inválido')) {
        return res.status(400).json(resultado);
      } // Outros erros de lógica de serviço podem ser 400 ou 404 dependendo do caso
        // Erros como "não possui um identificador válido" ou "não está associado a nenhum cliente" podem ser 404
        // se considerarmos que o recurso final (identificador válido) não foi encontrado.
      if (resultado.error.includes('não possui um identificador válido') || resultado.error.includes('não está associado a nenhum cliente')) {
        return res.status(404).json(resultado);
      }
      // Erro interno genérico
      return res.status(500).json(resultado);
    }

    return res.status(200).json(resultado);

  } catch (error) {
    // Este catch é para erros inesperados na camada do controlador ou no serviço que não foram tratados
    console.error(`Erro inesperado no controlador ao buscar identificador para o processo "${numeroProcesso}":`, error);
    return res.status(500).json({ error: 'Erro interno do servidor ao processar a solicitação.' });
  }
} 