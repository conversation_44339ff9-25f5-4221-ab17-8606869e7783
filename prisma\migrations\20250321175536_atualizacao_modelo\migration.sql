-- CreateEnum
CREATE TYPE "ComunicadoStatus" AS ENUM ('PENDENTE', 'EM_PROCESSAMENTO', 'ENVIADO', 'FALHA', 'CANCELADO');

-- AlterTable
ALTER TABLE "ComunicadoPrazoMerito" ADD COLUMN     "detalhesErro" JSONB,
ADD COLUMN     "status" "ComunicadoStatus" NOT NULL DEFAULT 'PENDENTE',
ADD COLUMN     "tentativas" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "ultimaTentativa" TIMESTAMP(3),
ALTER COLUMN "success" SET DEFAULT false;

-- CreateTable
CREATE TABLE "HistoricoComunicadoPrazoMerito" (
    "id" TEXT NOT NULL,
    "comunicadoPrazoMeritoId" TEXT NOT NULL,
    "dataTentativa" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" "ComunicadoStatus" NOT NULL,
    "success" BOOLEAN NOT NULL,
    "errorMessage" TEXT,
    "detalhesErro" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "HistoricoComunicadoPrazoMerito_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "HistoricoComunicadoPrazoMerito_comunicadoPrazoMeritoId_idx" ON "HistoricoComunicadoPrazoMerito"("comunicadoPrazoMeritoId");

-- CreateIndex
CREATE INDEX "HistoricoComunicadoPrazoMerito_dataTentativa_idx" ON "HistoricoComunicadoPrazoMerito"("dataTentativa");

-- CreateIndex
CREATE INDEX "HistoricoComunicadoPrazoMerito_status_idx" ON "HistoricoComunicadoPrazoMerito"("status");

-- CreateIndex
CREATE INDEX "ComunicadoPrazoMerito_status_idx" ON "ComunicadoPrazoMerito"("status");

-- AddForeignKey
ALTER TABLE "HistoricoComunicadoPrazoMerito" ADD CONSTRAINT "HistoricoComunicadoPrazoMerito_comunicadoPrazoMeritoId_fkey" FOREIGN KEY ("comunicadoPrazoMeritoId") REFERENCES "ComunicadoPrazoMerito"("id") ON DELETE CASCADE ON UPDATE CASCADE;
