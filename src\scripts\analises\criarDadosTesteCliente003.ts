import { PrismaClient, Procurador } from '@prisma/client';
import cliProgress from 'cli-progress';

const prisma = new PrismaClient();

// --- DADOS DO CLIENTE ---
const identificadorClienteTeste = '00000003';
const numeroDocumentoCliente = '00000003'; // Corrigido para seguir a sequência
const nomeCliente = `Cliente Datas (${identificadorClienteTeste})`;
const nomeProcurador = "REGISTRE-SE LTDA.";
// -----------------------

// --- Estrutura para definir os processos e despachos com datas ---
interface DespachoInfo {
  nome: string;
  dataPublicacao: string; // Formato DD/MM/YYYY
  textoComplementar?: string;
}

interface ProcessoInfo {
  numeroSufixo: number; // Para gerar o número do processo
  dataDeposito: string; // Formato DD/MM/YYYY
  taxaPagaOverride?: boolean; // Para definir taxaConcessaoPaga
  despachos: DespachoInfo[];
}

// --- DEFINIÇÃO DOS PROCESSOS ESPECÍFICOS ---
const processosEspecificos: ProcessoInfo[] = [
  {
    numeroSufixo: 1,
    dataDeposito: '10/03/2025',
    despachos: [
      { nome: "Publicação de pedido de registro para oposição (exame formal concluído)", dataPublicacao: '25/03/2025' },
    ]
  },
  {
    numeroSufixo: 2,
    dataDeposito: '07/01/2025',
    despachos: [
      { nome: "Publicação de pedido de registro para oposição (exame formal concluído)", dataPublicacao: '28/01/2025' },
    ]
  },
  {
    numeroSufixo: 3,
    dataDeposito: '29/11/2024',
    despachos: [
      { nome: "Publicação de pedido de registro para oposição (exame formal concluído)", dataPublicacao: '17/12/2024' },
      { nome: "Notificação de oposição", dataPublicacao: '01/04/2025' },
    ]
  },
  {
    numeroSufixo: 4,
    dataDeposito: '13/09/2023',
    despachos: [
      { nome: "Publicação de pedido de registro para oposição (exame formal concluído)", dataPublicacao: '10/10/2023' },
      { nome: "Deferimento do pedido", dataPublicacao: '01/04/2025' },
    ]
  },
  {
    numeroSufixo: 5,
    dataDeposito: '13/06/2023',
    taxaPagaOverride: true,
    despachos: [
      { nome: "Publicação de pedido de registro para oposição (exame formal concluído)", dataPublicacao: '10/07/2023' },
      { nome: "Deferimento do pedido", dataPublicacao: '27/01/2025' },
    ]
  },
  {
    numeroSufixo: 6, // Renomeado de 5b para 6
    dataDeposito: '13/06/2023',
    taxaPagaOverride: false,
    despachos: [
      { nome: "Publicação de pedido de registro para oposição (exame formal concluído)", dataPublicacao: '10/07/2023' },
      { nome: "Deferimento do pedido", dataPublicacao: '27/01/2025' },
    ]
  },
  {
    numeroSufixo: 7, // Renomeado de 6 para 7
    dataDeposito: '06/09/2023',
    despachos: [
      { nome: "Publicação de pedido de registro para oposição (exame formal concluído)", dataPublicacao: '26/09/2023' },
      { nome: "Indeferimento do pedido", dataPublicacao: '01/04/2025' },
    ]
  },
  {
    numeroSufixo: 8, // Renomeado de 7 para 8
    dataDeposito: '02/04/2022',
    despachos: [
      { nome: "Publicação de pedido de registro para oposição (exame formal concluído)", dataPublicacao: '19/04/2022' },
      { nome: "Indeferimento do pedido", dataPublicacao: '09/05/2023' },
      { nome: "Notificação de recurso", dataPublicacao: '04/07/2023', textoComplementar: "Recurso contra indeferimento." },
      { nome: "Recurso provido (decisão reformada para: Deferimento)", dataPublicacao: '24/12/2024' },
    ]
  },
  {
    numeroSufixo: 9, // Renomeado de 8 para 9
    dataDeposito: '18/12/2018',
    despachos: [
      { nome: "Publicação de pedido de registro para oposição (exame formal concluído)", dataPublicacao: '22/01/2019' },
      { nome: "Exigência de mérito", dataPublicacao: '30/07/2019' },
      { nome: "Deferimento do pedido", dataPublicacao: '29/10/2019' },
      { nome: "Arquivamento definitivo de pedido de registro por falta de pagamento da concessão", dataPublicacao: '03/03/2020' },
    ]
  },
  {
    numeroSufixo: 10, // Renomeado de 9 para 10
    dataDeposito: '19/10/2018',
    despachos: [
      { nome: "Publicação de pedido de registro para oposição (exame formal concluído)", dataPublicacao: '21/11/2018' },
      { nome: "Deferimento do pedido", dataPublicacao: '11/06/2019' },
      { nome: "Concessão de registro", dataPublicacao: '24/09/2019' },
      { nome: "Notificação de instauração de processo de nulidade a requerimento", dataPublicacao: '14/04/2020' },
      { nome: "Requerimento não provido (mantida a concessão)", dataPublicacao: '12/09/2023' },
    ]
  },
  {
    numeroSufixo: 11, // Renomeado de 10 para 11
    dataDeposito: '23/02/2023',
    despachos: [
        { nome: "Publicação de pedido de registro para oposição (exame formal concluído)", dataPublicacao: '14/03/2023' },
        { nome: "Deferimento do pedido", dataPublicacao: '02/07/2024' },
        { nome: "Concessão de registro", dataPublicacao: '30/07/2024' },
    ]
  }
];
// -------------------------------------------

/**
 * Converte string DD/MM/YYYY para Date object.
 */
function parseDateString(dateStr: string): Date {
  const [day, month, year] = dateStr.split('/').map(Number);
  // Month is 0-indexed in JavaScript Date
  return new Date(year, month - 1, day);
}


/**
 * Cria uma abreviação mais única para um nome de despacho.
 */
function abreviarDespacho(nome: string | null): string {
  // ... (função mantida da versão anterior)
  if (!nome) return 'N/A';
  const overrides: { [key: string]: string } = {
    'Deferimento do pedido': 'DefPed',
    'Deferimento da petição': 'DefPet',
    'Indeferimento do pedido': 'IndPed',
    'Indeferimento da petição': 'IndPet',
    'Publicação de pedido de registro para oposição': 'PubOpo',
    'Publicação de pedido de registro para oposição (exame formal concluído)': 'PubOpoEx',
    'Notificação de oposição': 'NotOpo',
    'Notificação de recurso': 'NotRec',
    'Notificação de caducidade': 'NotCad',
    'Concessão de registro': 'ConcReg',
    'Arquivamento definitivo de pedido de registro por falta de pagamento da concessão': 'ArqPagConc',
    'Arquivamento definitivo de pedido de registro por falta de cumprimento de exigência de mérito': 'ArqExiMer',
    'Exigência de mérito': 'ExiMer',
    'Exigência formal': 'ExiFor',
    'Sobrestamento do exame de mérito': 'SobExaMer',
    'Recurso não provido (decisão mantida)': 'RecNeg',
    'Recurso provido (decisão reformada para: Deferimento)': 'RecDef',
    'Notificação de instauração de processo de nulidade a requerimento': 'NotNulReq',
    'Requerimento não provido (mantida a concessão)': 'ReqNeg',
    'Ato de prejudicar petição': 'AtoPrejPet',
    'Decisão de não conhecer da petição': 'DecNaoConPet',
  };
  if (overrides[nome]) { return overrides[nome]; }
  const palavras = nome.split(' ');
  const primeiraPalavra = palavras[0] || '';
  const abreviacao = primeiraPalavra.substring(0, 3);
  const palavrasIgnoradas = ['de', 'da', 'do', 'a', 'o', 'em', 'para', 'por'];
  let segundaSigla = '';
  for (let i = 1; i < palavras.length; i++) {
    if (!palavrasIgnoradas.includes(palavras[i].toLowerCase())) {
      segundaSigla = palavras[i].substring(0, 1);
      break;
    }
  }
  return (abreviacao + segundaSigla).substring(0, 4);
}

/**
 * Cria um nome de marca resumido baseado na sequência de despachos.
 */
function criarNomeMarcaResumido(sequencia: DespachoInfo[], limite = 50): string {
  // ... (função mantida da versão anterior, mas adaptada para DespachoInfo)
  if (!sequencia || sequencia.length === 0) {
    return 'Fluxo Vazio'.substring(0, limite);
  }
  const abreviacoes = sequencia.map(d => abreviarDespacho(d.nome));
  const nomeCompletoAbrev = abreviacoes.join('-');
  let nome = `Fluxo(${sequencia.length}): ${nomeCompletoAbrev}`;
  if (nome.length > limite) {
    const primeiraAbrev = abreviacoes[0] || 'Inicio';
    const ultimaAbrev = abreviacoes[abreviacoes.length - 1] || 'Fim';
    nome = `Fluxo(${sequencia.length}): ${primeiraAbrev}...${ultimaAbrev}`;
  }
  return nome.substring(0, limite);
}


/**
 * Função principal para criar o cliente específico 003 e seus processos.
 */
async function criarDadosCliente003() {
  let rpiCounter = 0; // Contador para números de RPI únicos
  try {
    console.log(`Iniciando criação de dados para cliente: ${identificadorClienteTeste}`);

    // 1. Encontrar ou Criar Procurador
    let procurador = await prisma.procurador.findUnique({ where: { nome: nomeProcurador } });
    if (!procurador) {
      console.log(`Criando procurador: ${nomeProcurador}`);
      procurador = await prisma.procurador.create({ data: { nome: nomeProcurador } });
    }
    const procuradorId = procurador.id;
    console.log(`Procurador ${nomeProcurador} encontrado/criado com ID: ${procuradorId}`);

    // 2. Criar ou encontrar o Cliente
    let cliente = await prisma.cliente.findFirst({
      where: { identificador: identificadorClienteTeste }
    });
    if (!cliente) {
      console.log(`Criando novo cliente com identificador: ${identificadorClienteTeste}`);
      cliente = await prisma.cliente.create({
        data: {
          identificador: identificadorClienteTeste,
          nome: nomeCliente,
          tipoDeDocumento: 'TESTE',
          numeroDocumento: numeroDocumentoCliente
        }
      });
    } else {
      console.log(`Cliente ${identificadorClienteTeste} encontrado com ID: ${cliente.id}`);
    }
    const clienteId = cliente.id;

    // 3. Configurar barra de progresso
    const barraProgresso = new cliProgress.SingleBar({
      format: 'Criando processos |{bar}| {percentage}% | {value}/{total} Processos',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });
    barraProgresso.start(processosEspecificos.length, 0);

    // 4. Criar processos e despachos
    let processosCriados = 0;
    for (const processoInfo of processosEspecificos) {
      const numeroProcesso = `TESTE-${identificadorClienteTeste}-${processoInfo.numeroSufixo}`.padStart(15, '0');
      const nomeMarca = criarNomeMarcaResumido(processoInfo.despachos);
      const dataDeposito = parseDateString(processoInfo.dataDeposito);
      const taxaPaga = processoInfo.taxaPagaOverride ?? false;

      try {
        const processo = await prisma.processo.create({
          data: {
            numero: numeroProcesso,
            clienteId: clienteId,
            procuradorId: procuradorId, // Associar procurador
            dataDeposito: dataDeposito,
            taxaConcessaoPaga: taxaPaga,
            monitorado: true,
            marca: {
              create: {
                nome: nomeMarca,
                apresentacao: 'Nominativa',
                natureza: 'Produto'
              }
            }
          }
        });

        // Criar RPI e Despacho para cada item na sequência
        for (const despachoInfo of processoInfo.despachos) {
          const dataPublicacaoRpi = parseDateString(despachoInfo.dataPublicacao);
          
          // Criar uma RPI única para este despacho/data
          const rpi = await prisma.rPI.create({
            data: {
              numero: ********* + rpiCounter++, // Número único fictício
              dataPublicacao: dataPublicacaoRpi
            }
          });

          // Criar o despacho associado à RPI e ao Processo
          await prisma.despacho.create({
            data: {
              processoId: processo.id,
              rpiId: rpi.id,
              codigo: 'FLUXO_DT3', // Código específico
              nome: despachoInfo.nome,
              textoComplementar: despachoInfo.textoComplementar // Adiciona se existir
            }
          });
        }

        processosCriados++;
      } catch (error: any) {
        if (error.code === 'P2002' && error.meta?.target?.includes('numero')) {
          console.warn(`\nAviso: Processo ${numeroProcesso} já existe. Pulando.`);
        } else {
          console.error(`\nErro ao criar processo ${numeroProcesso}:`, error);
        }
      }
      barraProgresso.increment();
    }

    barraProgresso.stop();
    console.log(`\nCriação concluída. ${processosCriados} processos criados para o cliente ${identificadorClienteTeste}.`);

  } catch (error: any) {
    console.error('Erro ao criar dados de teste para cliente 003:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script
criarDadosCliente003(); 