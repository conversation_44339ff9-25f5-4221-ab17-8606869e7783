import { readFileSync, writeFileSync } from 'fs';

/**
 * Lê o arquivo de análise e extrai os fluxos únicos de despachos.
 * @param caminhoArquivo O caminho para o arquivo JSON de análise.
 * @param caminhoSaida O caminho para salvar o arquivo com os fluxos únicos.
 */
function extrairFluxosUnicos(caminhoArquivo: string, caminhoSaida: string) {
  try {
    console.log(`Lendo arquivo de análise: ${caminhoArquivo}`);
    const dadosCrus = readFileSync(caminhoArquivo, 'utf-8');
    const dadosAnalise = JSON.parse(dadosCrus);

    if (!dadosAnalise.fluxosUnicos || !Array.isArray(dadosAnalise.fluxosUnicos)) {
      throw new Error('Formato inválido do arquivo de análise. Chave "fluxosUnicos" não encontrada ou não é um array.');
    }

    console.log(`Total de fluxos encontrados: ${dadosAnalise.fluxosUnicos.length}`);

    // Usar um Set para garantir a unicidade dos fluxos
    const fluxosSet = new Set<string>();
    const fluxosUnicosProcessados: string[][] = [];

    for (const fluxo of dadosAnalise.fluxosUnicos) {
      const fluxoString = JSON.stringify(fluxo.sequenciaDespachos);
      if (!fluxosSet.has(fluxoString)) {
        fluxosSet.add(fluxoString);
        fluxosUnicosProcessados.push(fluxo.sequenciaDespachos);
      }
    }

    console.log(`Total de fluxos únicos extraídos: ${fluxosUnicosProcessados.length}`);

    // Salvar os fluxos únicos em um novo arquivo JSON
    writeFileSync(caminhoSaida, JSON.stringify(fluxosUnicosProcessados, null, 2));
    console.log(`Fluxos únicos salvos em: ${caminhoSaida}`);

    return fluxosUnicosProcessados;

  } catch (error: any) {
    console.error('Erro ao extrair fluxos únicos:', error.message);
    process.exit(1);
  }
}

// Exemplo de uso: Chamar com o caminho do seu arquivo de análise
const arquivoAnalise = 'src/scripts/analises/analise-fluxos-2025-04-05T14-13-32-673Z.json';
const arquivoSaida = 'src/scripts/analises/fluxos-unicos.json';

extrairFluxosUnicos(arquivoAnalise, arquivoSaida); 