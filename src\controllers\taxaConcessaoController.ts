import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const atualizarTaxaConcessao = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.body;

    if (!id) {
      return res.status(400).json({ 
        success: false, 
        message: 'O ID do CRM é obrigatório' 
      });
    }

    // Buscar o cliente pelo crmId
    const cliente = await prisma.cliente.findUnique({
      where: { crmId: parseInt(id) },
      include: {
        processos: true
      }
    });

    if (!cliente) {
      return res.status(404).json({ 
        success: false, 
        message: 'Cliente não encontrado' 
      });
    }

    // Atualizar todos os processos do cliente
    if (cliente.processos.length > 0) {
      await prisma.processo.updateMany({
        where: {
          id: {
            in: cliente.processos.map(p => p.id)
          }
        },
        data: {
          taxaConcessaoPaga: true
        }
      });

      return res.status(200).json({
        success: true,
        message: `Taxa de concessão atualizada com sucesso para ${cliente.processos.length} processo(s)`,
        processosAtualizados: cliente.processos.length
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Cliente encontrado, mas não possui processos para atualizar',
      processosAtualizados: 0
    });

  } catch (error) {
    console.error('Erro ao atualizar taxa de concessão:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro interno ao processar a requisição'
    });
  }
}; 