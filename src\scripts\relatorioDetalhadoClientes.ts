import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import cliProgress from 'cli-progress';

const prisma = new PrismaClient();

// Interface para clareza dos dados que vamos buscar
interface ClienteParaRelatorio {
  nome: string | null;
  numeroDocumento: string | null;
  identificador: string | null;
  contatos: Array<{ email: string | null }>; // Pegaremos o primeiro email
  nomeDaMarca: string | null; // Campo direto do cliente
  processos: Array<{ marca: { nome: string | null } | null }>; // Para buscar nome da marca nos processos
}

async function gerarRelatorioDetalhadoClientes() {
  console.log("🔍 Buscando dados detalhados dos clientes válidos para o relatório...");

  try {
    const todosClientes: ClienteParaRelatorio[] = await prisma.cliente.findMany({
      where: {
        NOT: {
          identificador: null, // Exclui clientes com identificador nulo
        },
        // E exclui clientes com o identificador padrão de "inválido"
        // Se você também quiser garantir que tenha 10 dígitos, 
        // essa verificação de "não ser o placeholder" já cobre muitos casos.
        // Para uma checagem estrita de 10 dígitos, precisaríamos filtrar em memória após a query
        // ou garantir que identificadores não-10-dígitos sejam sempre null ou "0000000000".
        AND: [
          {
            identificador: {
              not: "0000000000", 
            }
          }
        ]
      },
      select: {
        nome: true,
        numeroDocumento: true,
        identificador: true, // Já sabemos que não será nulo ou "0000000000"
        contatos: {
          select: {
            email: true,
          },
          orderBy: {
            id: 'asc',
          },
          take: 1,
        },
        nomeDaMarca: true,
        processos: {
          select: {
            marca: {
              select: {
                nome: true,
              },
            },
          },
        },
      },
    });

    console.log(`📊 Encontrados ${todosClientes.length} clientes válidos para processar.`);

    if (todosClientes.length === 0) {
      console.log("Nenhum cliente encontrado para gerar o relatório.");
      return;
    }

    const progressBar = new cliProgress.SingleBar({
      format: "Gerando CSV Detalhado |{bar}| {percentage}% || {value}/{total} Clientes",
      barCompleteChar: "\u2588",
      barIncompleteChar: "\u2591",
      hideCursor: true,
    });
    progressBar.start(todosClientes.length, 0);

    const cabecalho = [
      'nome_cliente',
      'numero_documento',
      'identificador',
      'email_principal',
      'nome_marca_final'
    ];

    const linhasCsv = todosClientes.map(cliente => {
      const emailPrincipal = cliente.contatos[0]?.email || '';
      
      let nomeMarcaFinal = cliente.nomeDaMarca;
      // Se nomeDaMarca do cliente estiver vazio ou for o placeholder, tentar buscar nos processos
      if (!nomeMarcaFinal || nomeMarcaFinal === "Marca não informada") {
        for (const processo of cliente.processos) {
          if (processo.marca?.nome) {
            nomeMarcaFinal = processo.marca.nome;
            break; // Pega o primeiro nome de marca encontrado nos processos
          }
        }
      }
      // Se ainda assim estiver vazio, garantir que seja uma string vazia para o CSV
      if (!nomeMarcaFinal || nomeMarcaFinal === "Marca não informada") {
        nomeMarcaFinal = '';
      }

      progressBar.increment();

      return [
        `"${cliente.nome || ''}"`,
        `"${(cliente.numeroDocumento && cliente.numeroDocumento !== "CPF não informado") ? cliente.numeroDocumento : ''}"`,
        `"${cliente.identificador || ''}"`,
        `"${emailPrincipal}"`,
        `"${nomeMarcaFinal}"`
      ].join(',');
    });

    progressBar.stop();

    const conteudoCsv = [cabecalho.join(','), ...linhasCsv].join('\n');
    const nomeArquivo = `relatorio-detalhado-clientes-${new Date().toISOString().slice(0, 10)}-${Date.now()}.csv`;
    
    fs.writeFileSync(nomeArquivo, conteudoCsv, 'utf8');
    
    console.log(`\n✅ Arquivo CSV Detalhado gerado: ${nomeArquivo}`);
    console.log(`📂 Localização: ${process.cwd()}/${nomeArquivo}`);

  } catch (error) {
    console.error("❌ Erro ao gerar relatório detalhado:", error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  gerarRelatorioDetalhadoClientes().catch(error => {
    console.error("Erro ao executar o relatório detalhado:", error);
    process.exit(1);
  });
}

export { gerarRelatorioDetalhadoClientes }; 