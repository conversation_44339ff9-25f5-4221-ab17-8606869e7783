import express from "express";
import procuradorMonitoradoRoutes from './routes/procuradorMonitoradoRoutes';
import estatisticasRoutes from './routes/estatisticasRoutes';
import processoRoutes from "./routes/processoRoutes";
import meritoRoutes from "./routes/meritoRoutes";
import { iniciarAtualizacaoEstimativas } from './services/atualizacaoEstimativasMerito';
import comunicadoPrazoMeritoRoutes from './routes/comunicadoPrazoMeritoRoutes';
import dashboardRoutes from './routes/dashboard';
import clientesRoutes from './routes/clientesRoutes';
import taxaConcessaoRoutes from './routes/taxaConcessaoRoutes';
import procuradoresAnaliseRoutes from './routes/analises/procuradoresRoutes';
import crmWebhookRoutes from './routes/crmWebhookRoutes';
import protocoloRoutes from './routes/protocoloRoutes';
import filaProtocoloRoutes from './routes/filaProtocoloRoutes';
import comunicadoRoutes from './routes/comunicadoRoutes';
import cors from 'cors';
import marcaRoutes from "./routes/marcaRoutes";


const app = express();

//app.use(cors());
// Middleware para leitura de JSON
app.use(express.json());

// Rotas
app.use('/api/procuradores-monitorados', procuradorMonitoradoRoutes);
app.use('/api/estatisticas', estatisticasRoutes);
app.use('/api/processos', processoRoutes);
app.use('/api/merito', meritoRoutes);
app.use('/api/comunicados-prazo-merito', comunicadoPrazoMeritoRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/clientes', clientesRoutes);
app.use('/api/crm/webhook', comunicadoPrazoMeritoRoutes);
app.use('/api/taxa-concessao', taxaConcessaoRoutes); //enpoint para atualizar a taxa de concessão
app.use('/api/analises/procuradores', procuradoresAnaliseRoutes);
app.use('/api/crm/webhook', crmWebhookRoutes);
app.use('/api/protocolo', protocoloRoutes);
app.use('/api/fila-protocolo', filaProtocoloRoutes);
app.use('/api/comunicados', comunicadoRoutes);
app.use('/api/marca', marcaRoutes);

// Inicia os serviços agendados
iniciarAtualizacaoEstimativas();
//iniciarVerificacaoDiariaComunicados();

export default app;
