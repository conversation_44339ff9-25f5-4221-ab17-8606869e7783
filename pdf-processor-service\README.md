# PDF Processor Service 🐍

Microserviço Python FastAPI para extração de imagens de marca de PDFs de protocolo do INPI.

## 🎯 Funcionalidades

- ✅ **Extração de número do processo** do PDF
- ✅ **Verificação do texto** "Imagem Digital da Marca"
- ✅ **Extração específica** de logos JPEG 1200x1200
- ✅ **API REST** com FastAPI
- ✅ **Logs detalhados** do processamento
- ✅ **Containerização** com Docker

## 🚀 Execução Local

### Pré-requisitos
- Python 3.11+
- pip

### Instalação
```bash
cd pdf-processor-service
pip install -r requirements.txt
python main.py
```

O serviço estará disponível em:
- **API**: http://localhost:8000
- **Documentação**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 🐳 Execução com Docker

```bash
cd pdf-processor-service
docker build -t pdf-processor .
docker run -p 8000:8000 pdf-processor
```

## 📡 API Endpoints

### POST /extract-logo
Extrai a logo da marca de um PDF de protocolo.

**Request:**
```bash
curl -X POST "http://localhost:8000/extract-logo" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@protocolo.pdf"
```

**Response Success:**
```json
{
  "success": true,
  "message": "Logo extraída com sucesso!",
  "process_number": "939194767",
  "logo": {
    "found": true,
    "filename": "939194767.jpg",
    "width": 1200,
    "height": 1200,
    "format": "jpeg",
    "size_bytes": 45678,
    "page": 2,
    "image_data": "base64_encoded_image..."
  }
}
```

**Response Error:**
```json
{
  "success": false,
  "error": "Logo JPEG 1200x1200 não encontrada no PDF",
  "process_number": "939194767"
}
```

### GET /health
Health check do serviço.

```json
{
  "status": "healthy",
  "service": "pdf-processor"
}
```

## 🔧 Integração com Node.js

```typescript
// Node.js - Chamada para o microserviço
const formData = new FormData();
formData.append('file', pdfFile);

const response = await fetch('http://localhost:8000/extract-logo', {
  method: 'POST',
  body: formData
});

const result = await response.json();

if (result.success) {
  console.log(`Logo extraída: ${result.logo.filename}`);
  // Salvar imagem decodificada
  const imageBuffer = Buffer.from(result.logo.image_data, 'base64');
  fs.writeFileSync(`uploads/logos/${result.logo.filename}`, imageBuffer);
}
```

## 📦 Bibliotecas Utilizadas

- **FastAPI**: Framework web moderno para Python
- **PyMuPDF (fitz)**: Biblioteca poderosa para manipulação de PDFs
- **Uvicorn**: Servidor ASGI de alta performance
- **Pillow**: Manipulação de imagens (se necessário)

## 🎯 Vantagens sobre Node.js

- ✅ **PyMuPDF é nativo** - acesso direto aos objetos PDF
- ✅ **Extração perfeita** de imagens embarcadas
- ✅ **Performance superior** para processamento de PDF
- ✅ **Logs claros** e debugging fácil
- ✅ **Escalabilidade** independente do Node.js

## 🔄 Deploy

O microserviço pode ser executado:
- **Localmente** para desenvolvimento
- **Docker** para produção
- **Kubernetes** para alta disponibilidade
- **Heroku/Railway** para deploy simples 