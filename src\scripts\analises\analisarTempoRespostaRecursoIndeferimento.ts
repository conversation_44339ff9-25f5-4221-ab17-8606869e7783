import { PrismaClient, <PERSON>o, Despacho, RPI } from "@prisma/client";
import { writeFileSync } from "fs";
import cliProgress from "cli-progress";
import { differenceInDays, isValid } from "date-fns";

const prisma = new PrismaClient({
  log: ["warn", "error"],
});

const DESPACHO_NOTIFICACAO_RECURSO = "Notificação de recurso";
const DESPACHOS_ANALISE_RECURSO_NOMES = new Set([
  "Recurso não provido (decisão mantida)",
  "Recurso provido (decisão reformada para: Deferimento)",
  "Recurso provido (decisão reformada para: Deferimento parcial)",
  "Recurso provido parcialmente (decisão reformada para: Deferimento parcial)",
  "Recurso provido parcialmente (outros)",
]);

const NUMERO_ULTIMAS_RPIS_CONSIDERAR = 4;
const TAMANHO_LOTE = 100; // Ajuste conforme necessário

interface DespachoInfoComData {
  nome: string;
  codigo: string | null;
  dataPublicacaoRPI: Date;
  numeroRPI: number | null;
}

interface ResultadoTempoRecurso {
  numeroProcesso: string;
  despachoNotificacaoRecurso: DespachoInfoComData;
  despachoAnaliseRecurso: DespachoInfoComData;
  tempoRespostaDias: number;
}

type ProcessoComDespachosCompletos = Processo & {
  despachos: (Despacho & { rpi: RPI | null })[];
};

async function getUltimasRpiIds(count: number): Promise<string[]> {
  const rpis = await prisma.rPI.findMany({
    where: {
   numero:{
    in:[2833,2834,2835,2836]
   }
    },
    orderBy: { numero: "desc" }, // Ou dataPublicacao, dependendo da preferência
    take: count,
    select: { id: true },
  });
  if (rpis.length < count) {
    console.warn(`Esperava ${count} RPIs, mas encontrou apenas ${rpis.length}. Usando as encontradas.`);
  }
  return rpis.map(rpi => rpi.id);
}

async function analisarTempoRespostaRecursoIndeferimento() {
  console.log("Iniciando análise do tempo de resposta a recursos (Notificação -> Análise)...");
  console.log(`Considerando análises de recurso nas últimas ${NUMERO_ULTIMAS_RPIS_CONSIDERAR} RPIs.`);

  const ultimasRpiIds = await getUltimasRpiIds(NUMERO_ULTIMAS_RPIS_CONSIDERAR);
  if (ultimasRpiIds.length === 0) {
    console.error("Nenhuma RPI encontrada para análise. Encerrando.");
    return;
  }
  console.log(`IDs das últimas ${ultimasRpiIds.length} RPIs para filtro: ${ultimasRpiIds.join(", ")}`);

  const resultados: ResultadoTempoRecurso[] = [];
  let processedCount = 0;

  const whereClause = {
    despachos: {
      some: {
        nome: { in: Array.from(DESPACHOS_ANALISE_RECURSO_NOMES) },
        rpiId: { in: ultimasRpiIds },
      },
    },
  };

  console.log("Contando processos com análise de recurso nas RPIs selecionadas...");
  const totalProcessosParaAnalisar = await prisma.processo.count({ where: whereClause });

  if (totalProcessosParaAnalisar === 0) {
    console.log("Nenhum processo encontrado com os critérios especificados.");
    return;
  }
  console.log(`Total de processos a serem analisados: ${totalProcessosParaAnalisar}`);

  const barraProgresso = new cliProgress.SingleBar({}, cliProgress.Presets.shades_classic);
  barraProgresso.start(totalProcessosParaAnalisar, 0, { speed: "N/A" });

  while (processedCount < totalProcessosParaAnalisar) {
    const processosLote = await prisma.processo.findMany({
      where: whereClause,
      include: {
        despachos: {
          include: { rpi: true },
          orderBy: { rpi: { dataPublicacao: "asc" } },
        },
      },
      skip: processedCount,
      take: TAMANHO_LOTE,
    });

    if (processosLote.length === 0) {
      console.warn("\nNenhum processo encontrado no lote atual, encerrando.");
      break;
    }

    for (const processo of processosLote as ProcessoComDespachosCompletos[]) {
      const despachosOrdenados = processo.despachos;
      for (let i = 0; i < despachosOrdenados.length; i++) {
        const despachoAtual = despachosOrdenados[i];

        if (
          despachoAtual.nome &&
          DESPACHOS_ANALISE_RECURSO_NOMES.has(despachoAtual.nome) &&
          despachoAtual.rpiId &&
          ultimasRpiIds.includes(despachoAtual.rpiId) &&
          despachoAtual.rpi?.dataPublicacao && isValid(despachoAtual.rpi.dataPublicacao)
        ) {
          const despachoAnaliseRecurso: DespachoInfoComData = {
            nome: despachoAtual.nome,
            codigo: despachoAtual.codigo,
            dataPublicacaoRPI: despachoAtual.rpi.dataPublicacao,
            numeroRPI: despachoAtual.rpi.numero
          };

          // Procurar a notificação de recurso anterior
          for (let j = i - 1; j >= 0; j--) {
            const despachoAnterior = despachosOrdenados[j];
            if (
              despachoAnterior.nome === DESPACHO_NOTIFICACAO_RECURSO &&
              despachoAnterior.rpi?.dataPublicacao && isValid(despachoAnterior.rpi.dataPublicacao)
            ) {
              const despachoNotificacao: DespachoInfoComData = {
                nome: despachoAnterior.nome,
                codigo: despachoAnterior.codigo,
                dataPublicacaoRPI: despachoAnterior.rpi.dataPublicacao,
                numeroRPI: despachoAnterior.rpi.numero
              };

              const tempoDias = differenceInDays(
                despachoAnaliseRecurso.dataPublicacaoRPI,
                despachoNotificacao.dataPublicacaoRPI
              );
              
              if (tempoDias >=0) { // Garante que a análise é posterior à notificação
                resultados.push({
                  numeroProcesso: processo.numero,
                  despachoNotificacaoRecurso: despachoNotificacao,
                  despachoAnaliseRecurso,
                  tempoRespostaDias: tempoDias,
                });
              }
              break; // Encontrou a notificação de recurso mais recente antes desta análise
            }
          }
        }
      }
    }
    processedCount += processosLote.length;
    barraProgresso.update(processedCount);
  }

  barraProgresso.stop();
  console.log("\nAnálise de lotes concluída.");

  if (resultados.length === 0) {
    console.log("Nenhum par de notificação de recurso/análise de recurso válido encontrado para calcular o tempo.");
    return;
  }

  // Calcular estatísticas
  const tempos = resultados.map(r => r.tempoRespostaDias);
  const mediaTempo = tempos.reduce((sum, t) => sum + t, 0) / tempos.length;
  tempos.sort((a, b) => a - b);
  const medianaTempo = tempos.length % 2 === 0
      ? (tempos[tempos.length / 2 - 1] + tempos[tempos.length / 2]) / 2
      : tempos[Math.floor(tempos.length / 2)];
  const minTempo = tempos[0];
  const maxTempo = tempos[tempos.length - 1];

  console.log("\n--- Estatísticas do Tempo de Resposta a Recursos ---");
  console.log(`Total de pares analisados: ${resultados.length}`);
  console.log(`Tempo Médio: ${mediaTempo.toFixed(2)} dias`);
  console.log(`Tempo Mediano: ${medianaTempo.toFixed(2)} dias`);
  console.log(`Tempo Mínimo: ${minTempo} dias`);
  console.log(`Tempo Máximo: ${maxTempo} dias`);

  const dataHora = new Date().toISOString().replace(/[:.]/g, "-");
  const nomeArquivo = `analise-tempo-notificacao-recurso-analise-${dataHora}.json`;
  try {
    writeFileSync(nomeArquivo, JSON.stringify({ 
        estatisticas: {
            totalParesAnalisados: resultados.length,
            tempoMedioDias: mediaTempo.toFixed(2),
            tempoMedianoDias: medianaTempo.toFixed(2),
            tempoMinimoDias: minTempo,
            tempoMaximoDias: maxTempo,
            numeroUltimasRpisConsideradas: NUMERO_ULTIMAS_RPIS_CONSIDERAR,
            rpiIdsConsideradas: ultimasRpiIds
        },
        detalhes: resultados 
    }, null, 2));
    console.log(`\nResultados detalhados salvos em: ${nomeArquivo}`);
  } catch (error) {
    console.error("\nErro ao salvar arquivo JSON:", error);
  }
}

async function main() {
  try {
    await analisarTempoRespostaRecursoIndeferimento();
  } catch (error) {
    console.error("\nErro na execução principal:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log("\nConexão com o banco de dados fechada.");
  }
}

if (require.main === module) {
  main();
} 