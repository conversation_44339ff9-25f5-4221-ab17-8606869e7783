import { PrismaClient } from '@prisma/client';

// Inicializar o cliente Prisma
const prisma = new PrismaClient();

// Tipos para a resposta
export type ProcessoTimeline = {
  etapa: string;
  data: Date | null;
  descricao: string;
  detalhes?: Record<string, any>;
};

export type ProcessoResponse = {
  numero: string;
  statusAtual: string;
  timeline: ProcessoTimeline[];
};

export type ClienteProcessosResponse = {
  idc: string;
  nome: string | null;
  totalProcessos: number;
  processos: ProcessoResponse[];
};

/**
 * Determina o status atual do processo com base em seus campos
 */
function determinarStatusProcesso(processo: any): string {
  // Verificar se já tem mérito (deferido ou indeferido)
  if (processo.dataMerito) {
    return processo.despachos.some((d: any) => d.nome?.includes('Deferimento')) ? 'Deferido' : 'Indeferido';
  }
  
  // Verificar se está em fase de oposição
  const despachoPublicacao = processo.despachos.find((d: any) => 
    d.nome?.includes('Publicação de pedido de registro para oposição')
  );
  
  if (despachoPublicacao) {
    const dataPublicacao = new Date(despachoPublicacao.rpi.dataPublicacao);
    const hoje = new Date();
    const diasDesdePublicacao = Math.ceil(
      (hoje.getTime() - dataPublicacao.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    // Se passaram menos de 60 dias desde a publicação, está em fase de oposição
    if (diasDesdePublicacao <= 60) {
      return 'Em fase de oposição';
    }
  }
  
  // Verificar oposição
  if (processo.oposicao) {
    return 'Em análise pós-oposição';
  }
  
  // Verificar sobrestamento
  if (processo.sobrestamento) {
    return 'Sobrestado';
  }
  
  // Verificar exigência
  if (processo.exigencia) {
    return 'Em exigência';
  }
  
  // Se tem data estimada de mérito e passou a fase de oposição (60 dias após publicação)
  if (processo.dataMeritoEstimada) {
    // Cálculo de dias restantes até o mérito
    const hoje = new Date();
    const dataEstimada = new Date(processo.dataMeritoEstimada);
    const diasRestantes = Math.ceil(
      (dataEstimada.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (diasRestantes <= 0) {
      return 'Mérito pendente (análise em andamento)';
    }
    
    // Converter dias para meses (aproximadamente)
    const mesesRestantes = Math.floor(diasRestantes / 30);
    
    if (mesesRestantes <= 1) {
      return 'Vencendo - Mérito em breve';
    }
    
    return `Aguardando mérito - Estimado em ${mesesRestantes} meses`;
  }
  
  // Se não se encaixa em nenhum dos estados acima
  return 'Em análise inicial';
}

/**
 * Gera a timeline completa do processo
 */
function gerarTimelineProcesso(processo: any): ProcessoTimeline[] {
  const timeline: ProcessoTimeline[] = [];
  
  // 1. Depósito/Protocolo do pedido (sempre a primeira etapa)
  if (processo.dataDeposito) {
    timeline.push({
      etapa: 'protocolo',
      data: processo.dataDeposito,
      descricao: 'Protocolo do pedido',
    });
  }
  
  // 2. Publicação de pedido para oposição (exame formal concluído)
  const despachoPublicacao = processo.despachos.find((d: any) => 
    d.nome?.includes('Publicação de pedido de registro para oposição')
  );
  
  if (despachoPublicacao) {
    timeline.push({
      etapa: 'publicacao',
      data: despachoPublicacao.rpi.dataPublicacao,
      descricao: 'Publicação de pedido de registro para oposição (exame formal concluído)',
      detalhes: {
        rpi: despachoPublicacao.rpi.numero,
        nome: despachoPublicacao.nome
      }
    });
    
    // 3. Fase de oposição (60 dias após a publicação)
    const dataFimOposicao = new Date(despachoPublicacao.rpi.dataPublicacao);
    dataFimOposicao.setDate(dataFimOposicao.getDate() + 60);
    
    // Verificar se houve oposição
    if (processo.oposicao) {
      // 3.1 Oposição apresentada
      timeline.push({
        etapa: 'oposicao',
        data: processo.dataOposicao || dataFimOposicao,
        descricao: 'Oposição apresentada',
      });
      
      // 3.2 Eventos pós-oposição (todos os despachos depois da oposição, exceto o mérito)
      const dataOposicao = processo.dataOposicao || dataFimOposicao;
      const despachosAposOposicao = processo.despachos
        .filter((d: any) => {
          const dataDespacho = new Date(d.rpi.dataPublicacao);
          return dataDespacho > dataOposicao && 
                 !d.nome?.includes('Deferimento') && 
                 !d.nome?.includes('Indeferimento');
        })
        .sort((a: any, b: any) => 
          new Date(a.rpi.dataPublicacao).getTime() - new Date(b.rpi.dataPublicacao).getTime()
        );
      
      // Adicionar cada despacho pós-oposição à timeline
      despachosAposOposicao.forEach((despacho: any) => {
        timeline.push({
          etapa: 'pos_oposicao',
          data: despacho.rpi.dataPublicacao,
          descricao: `Pós-oposição: ${despacho.nome || 'Despacho publicado'}`,
          detalhes: {
            codigo: despacho.codigo,
            nome: despacho.nome,
            rpi: despacho.rpi.numero
          }
        });
      });
    } else {
      // 3.3 Sem oposição - Fase de oposição finalizada
      const hoje = new Date();
      
      // Somente adicionar essa entrada se já terminou o prazo de oposição
      if (hoje > dataFimOposicao) {
        timeline.push({
          etapa: 'fim_oposicao',
          data: dataFimOposicao,
          descricao: 'Fase de oposição concluída sem oposições',
        });
        
        // 3.4 Entrada na fase de aguardando mérito
        timeline.push({
          etapa: 'aguardando_merito',
          data: dataFimOposicao,
          descricao: 'Aguardando análise de mérito',
        });
      }
    }
  }
  
  // 4. Resultado final (Deferido ou Indeferido)
  const despachoMerito = processo.despachos.find((d: any) => 
    d.nome?.includes('Deferimento') || d.nome?.includes('Indeferimento')
  );
  
  if (despachoMerito) {
    timeline.push({
      etapa: 'merito',
      data: despachoMerito.rpi.dataPublicacao,
      descricao: despachoMerito.nome?.includes('Deferimento') ? 'Deferido' : 'Indeferido',
      detalhes: {
        codigo: despachoMerito.codigo,
        nome: despachoMerito.nome
      }
    });
    
    // 5. Certificado (se deferido)
    if (despachoMerito.nome?.includes('Deferimento')) {
      const despachoConcessao = processo.despachos.find((d: any) => 
        d.nome?.includes('Concessão de registro')
      );
      
      if (despachoConcessao) {
        timeline.push({
          etapa: 'concessao',
          data: despachoConcessao.rpi.dataPublicacao,
          descricao: 'Emissão do certificado de registro',
          detalhes: {
            validadeDez: processo.dataVigencia ? new Date(new Date(processo.dataVigencia).setFullYear(new Date(processo.dataVigencia).getFullYear() + 10)) : null
          }
        });
      }
    }
  } else if (processo.dataMeritoEstimada) {
    // Se ainda não tem mérito, adicionar estimativa
    const hoje = new Date();
    const dataEstimada = new Date(processo.dataMeritoEstimada);
    // Somente adicionar a previsão se for no futuro
    if (dataEstimada > hoje) {
      timeline.push({
        etapa: 'merito_estimado',
        data: processo.dataMeritoEstimada,
        descricao: 'Previsão para análise de mérito',
      });
    }
  }
  
  // Ordenar a timeline por data
  return timeline.sort((a, b) => {
    if (!a.data) return 1;
    if (!b.data) return -1;
    return new Date(a.data).getTime() - new Date(b.data).getTime();
  });
}

/**
 * Obtém cliente e seus processos por IDC (últimos 8 dígitos do telefone)
 */
export async function obterClienteProcessosPorIdc(
  idc: string
): Promise<ClienteProcessosResponse | null> {
  try {
    // Buscar o cliente pelo IDC (últimos 8 dígitos do telefone)
    const cliente = await prisma.cliente.findFirst({
      where: {
        OR: [
          // Procurar pelo IDC como identificador
          { identificador: idc },
          // Ou procurar por telefones que terminem com o IDC
          {
            contatos: {
              some: {
                OR: [
                  { telefone: { endsWith: idc } },
                  { telefoneSegundario: { endsWith: idc } }
                ]
              }
            }
          }
        ]
      },
      include: {
        contatos: true
      }
    });

    if (!cliente) {
      return null;
    }

    // Buscar todos os processos do cliente
    const processos = await prisma.processo.findMany({
      where: {
        clienteId: cliente.id
      },
      include: {
        marca: {
          include: {
            ncl: true,
            cfe: true
          }
        },
        despachos: {
          include: {
            rpi: true
          },
          orderBy: {
            rpi: {
              dataPublicacao: 'asc'
            }
          }
        },
        titulares: true
      },
      orderBy: {
        dataDeposito: 'desc'
      }
    });

    // Transformar os processos para o formato esperado
    const processosFormatados: ProcessoResponse[] = processos.map(processo => {
      return {
        numero: processo.numero,
        statusAtual: determinarStatusProcesso(processo),
        timeline: gerarTimelineProcesso(processo)
      };
    });

    // Montar a resposta final
    return {
      idc,
      nome: cliente.nome,
      totalProcessos: processos.length,
      processos: processosFormatados
    };
  } catch (error) {
    console.error("Erro ao obter cliente e processos por IDC:", error);
    throw error;
  }
} 