import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
import fs from "fs";
import path from "path";
import { fetchDeals, fetchPersons, fetchCompanies, fetchPipelines, fetchStages } from "../utils/fetchCrmData.utils";
import { Logger } from "../services/logger.service";

dotenv.config();

const prisma = new PrismaClient();

interface DadosEnriquecidos {
  totalOportunidades: number;
  arquivosGerados: string[];
  tempoExecucao: number;
  estatisticas: {
    comPerson: number;
    comCompany: number;
    comPipeline: number;
    comStage: number;
    comCustomFields: number;
  };
}

interface OportunidadeCompleta {
  // Dados da oportunidade
  id: number;
  name: string;
  value: number;
  stage_id: number;
  pipeline_id: number;
  person_id?: number;
  company_id?: number;
  owner_id?: number;
  created_at: string;
  updated_at: string;
  expected_close_date?: string;
  customFields?: any[];
  tags?: any[];
  
  // Dados enriquecidos
  pipeline?: any;
  stage?: any;
  person?: any;
  company?: any;
  owner?: any;
  
  // Metadados de processamento
  processedAt: string;
  batchNumber: number;
}

async function criarEstruturaPastas(): Promise<string> {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  const basePath = path.join(process.cwd(), 'dados-crm-extraidos', timestamp);
  
  // Criar pastas necessárias
  const pastas = [
    basePath,
    path.join(basePath, 'oportunidades-completas'),
    path.join(basePath, 'metadados'),
    path.join(basePath, 'logs')
  ];
  
  for (const pasta of pastas) {
    if (!fs.existsSync(pasta)) {
      fs.mkdirSync(pasta, { recursive: true });
    }
  }
  
  return basePath;
}

async function buscarDadosComplementares() {
  Logger.info("🔍 Buscando dados complementares do CRM...");
  
  const dadosComplementares = {
    persons: new Map(),
    companies: new Map(),
    pipelines: new Map(),
    stages: new Map()
  };
  
  try {
    // Buscar persons
    Logger.info("📞 Buscando persons...");
    const persons = await fetchPersons();
    persons.forEach(person => {
      dadosComplementares.persons.set(person.id, person);
    });
    Logger.success(`✅ ${persons.length} persons carregados`);
    
    // Buscar companies
    Logger.info("🏢 Buscando companies...");
    const companies = await fetchCompanies();
    companies.forEach(company => {
      dadosComplementares.companies.set(company.id, company);
    });
    Logger.success(`✅ ${companies.length} companies carregados`);
    
    // Buscar pipelines
    Logger.info("🔄 Buscando pipelines...");
    const pipelines = await fetchPipelines();
    pipelines.forEach(pipeline => {
      dadosComplementares.pipelines.set(pipeline.id, pipeline);
    });
    Logger.success(`✅ ${pipelines.length} pipelines carregados`);
    
    // Buscar stages
    Logger.info("📊 Buscando stages...");
    const stages = await fetchStages();
    stages.forEach(stage => {
      dadosComplementares.stages.set(stage.id, stage);
    });
    Logger.success(`✅ ${stages.length} stages carregados`);
    
    return dadosComplementares;
    
  } catch (error: any) {
    Logger.error("❌ Erro ao buscar dados complementares:", error.message);
    throw error;
  }
}

function enriquecerOportunidade(
  oportunidade: any, 
  dadosComplementares: any, 
  batchNumber: number
): OportunidadeCompleta {
  
  const person = dadosComplementares.persons.get(oportunidade.person_id) || null;
  const company = dadosComplementares.companies.get(oportunidade.company_id) || null;
  const pipeline = dadosComplementares.pipelines.get(oportunidade.pipeline_id) || null;
  const stage = dadosComplementares.stages.get(oportunidade.stage_id) || null;
  
  return {
    // Dados originais da oportunidade
    id: oportunidade.id,
    name: oportunidade.name,
    value: oportunidade.value || 0,
    stage_id: oportunidade.stage_id,
    pipeline_id: oportunidade.pipeline_id,
    person_id: oportunidade.person_id,
    company_id: oportunidade.company_id,
    owner_id: oportunidade.owner_id,
    created_at: oportunidade.created_at,
    updated_at: oportunidade.updated_at,
    expected_close_date: oportunidade.expected_close_date,
    customFields: oportunidade.customFields || [],
    tags: oportunidade.tags || [],
    
    // Dados enriquecidos
    pipeline,
    stage,
    person,
    company,
    
    // Metadados
    processedAt: new Date().toISOString(),
    batchNumber
  };
}

async function salvarOportunidadesEmLotes(
  oportunidades: any[], 
  dadosComplementares: any, 
  basePath: string
): Promise<string[]> {
  const LOTE_SIZE = 1000;
  const totalLotes = Math.ceil(oportunidades.length / LOTE_SIZE);
  const arquivosGerados: string[] = [];
  
  Logger.info(`📦 Dividindo ${oportunidades.length} oportunidades em ${totalLotes} lotes de ${LOTE_SIZE}`);
  
  for (let i = 0; i < totalLotes; i++) {
    const inicio = i * LOTE_SIZE;
    const fim = Math.min(inicio + LOTE_SIZE, oportunidades.length);
    const loteAtual = oportunidades.slice(inicio, fim);
    const numeroBatch = i + 1;
    
    Logger.info(`📄 Processando lote ${numeroBatch}/${totalLotes} (${loteAtual.length} oportunidades)...`);
    
    // Enriquecer oportunidades do lote
    const oportunidadesEnriquecidas = loteAtual.map(oportunidade => 
      enriquecerOportunidade(oportunidade, dadosComplementares, numeroBatch)
    );
    
    // Salvar arquivo JSON
    const nomeArquivo = `oportunidades-lote-${numeroBatch.toString().padStart(3, '0')}.json`;
    const caminhoArquivo = path.join(basePath, 'oportunidades-completas', nomeArquivo);
    
    const dadosParaSalvar = {
      metadata: {
        loteNumero: numeroBatch,
        totalLotes,
        quantidadeOportunidades: oportunidadesEnriquecidas.length,
        intervaloIds: {
          inicio: oportunidadesEnriquecidas[0]?.id || null,
          fim: oportunidadesEnriquecidas[oportunidadesEnriquecidas.length - 1]?.id || null
        },
        processadoEm: new Date().toISOString()
      },
      oportunidades: oportunidadesEnriquecidas
    };
    
    fs.writeFileSync(caminhoArquivo, JSON.stringify(dadosParaSalvar, null, 2), 'utf8');
    arquivosGerados.push(nomeArquivo);
    
    Logger.success(`✅ Lote ${numeroBatch} salvo: ${nomeArquivo}`);
  }
  
  return arquivosGerados;
}

function gerarEstatisticas(oportunidades: any[]): any {
  const stats = {
    comPerson: 0,
    comCompany: 0,
    comPipeline: 0,
    comStage: 0,
    comCustomFields: 0
  };
  
  oportunidades.forEach(oportunidade => {
    if (oportunidade.person) stats.comPerson++;
    if (oportunidade.company) stats.comCompany++;
    if (oportunidade.pipeline) stats.comPipeline++;
    if (oportunidade.stage) stats.comStage++;
    if (oportunidade.customFields && oportunidade.customFields.length > 0) stats.comCustomFields++;
  });
  
  return stats;
}

async function salvarMetadados(
  basePath: string, 
  dados: DadosEnriquecidos, 
  dadosComplementares: any
): Promise<void> {
  
  // Metadados da execução
  const metadados = {
    execucao: {
      dataHora: new Date().toISOString(),
      tempoExecucaoSegundos: dados.tempoExecucao,
      totalOportunidades: dados.totalOportunidades,
      arquivosGerados: dados.arquivosGerados,
      estatisticas: dados.estatisticas
    },
    dadosComplementares: {
      totalPersons: dadosComplementares.persons.size,
      totalCompanies: dadosComplementares.companies.size,
      totalPipelines: dadosComplementares.pipelines.size,
      totalStages: dadosComplementares.stages.size
    },
    estrutura: {
      loteSize: 1000,
      totalLotes: dados.arquivosGerados.length,
      formatoArquivos: "JSON com metadata + array de oportunidades"
    }
  };
  
  // Salvar metadados gerais
  fs.writeFileSync(
    path.join(basePath, 'metadados', 'execucao.json'), 
    JSON.stringify(metadados, null, 2), 
    'utf8'
  );
  
  // Salvar mapeamentos para referência futura
  const mapeamentos = {
    pipelines: Array.from(dadosComplementares.pipelines.values()),
    stages: Array.from(dadosComplementares.stages.values())
  };
  
  fs.writeFileSync(
    path.join(basePath, 'metadados', 'mapeamentos.json'), 
    JSON.stringify(mapeamentos, null, 2), 
    'utf8'
  );
  
  // Relatório resumo em TXT
  const resumo = [
    '='.repeat(80),
    'EXTRAÇÃO DE DADOS CRM - OPORTUNIDADES COMPLETAS',
    '='.repeat(80),
    `Data da execução: ${new Date().toLocaleString('pt-BR')}`,
    `Tempo de execução: ${dados.tempoExecucao.toFixed(2)} segundos`,
    '',
    'RESUMO DOS DADOS:',
    `📊 Total de oportunidades: ${dados.totalOportunidades.toLocaleString('pt-BR')}`,
    `📁 Arquivos JSON gerados: ${dados.arquivosGerados.length}`,
    `📦 Tamanho do lote: 1.000 oportunidades por arquivo`,
    '',
    'DADOS COMPLEMENTARES CARREGADOS:',
    `👥 Persons: ${dadosComplementares.persons.size.toLocaleString('pt-BR')}`,
    `🏢 Companies: ${dadosComplementares.companies.size.toLocaleString('pt-BR')}`,
    `🔄 Pipelines: ${dadosComplementares.pipelines.size.toLocaleString('pt-BR')}`,
    `📊 Stages: ${dadosComplementares.stages.size.toLocaleString('pt-BR')}`,
    '',
    'ESTATÍSTICAS DE ENRIQUECIMENTO:',
    `👥 Com person vinculado: ${dados.estatisticas.comPerson.toLocaleString('pt-BR')} (${((dados.estatisticas.comPerson / dados.totalOportunidades) * 100).toFixed(1)}%)`,
    `🏢 Com company vinculado: ${dados.estatisticas.comCompany.toLocaleString('pt-BR')} (${((dados.estatisticas.comCompany / dados.totalOportunidades) * 100).toFixed(1)}%)`,
    `🔄 Com pipeline vinculado: ${dados.estatisticas.comPipeline.toLocaleString('pt-BR')} (${((dados.estatisticas.comPipeline / dados.totalOportunidades) * 100).toFixed(1)}%)`,
    `📊 Com stage vinculado: ${dados.estatisticas.comStage.toLocaleString('pt-BR')} (${((dados.estatisticas.comStage / dados.totalOportunidades) * 100).toFixed(1)}%)`,
    `🏷️ Com custom fields: ${dados.estatisticas.comCustomFields.toLocaleString('pt-BR')} (${((dados.estatisticas.comCustomFields / dados.totalOportunidades) * 100).toFixed(1)}%)`,
    '',
    'ESTRUTURA DOS ARQUIVOS:',
    `📂 Base: dados-crm-extraidos/[timestamp]/`,
    `   ├── oportunidades-completas/ (${dados.arquivosGerados.length} arquivos JSON)`,
    `   ├── metadados/ (execucao.json, mapeamentos.json)`,
    `   └── logs/ (logs de execução)`,
    '',
    'PRÓXIMOS PASSOS SUGERIDOS:',
    `1. Analisar distribuição de pipelines e stages`,
    `2. Validar qualidade dos dados enriquecidos`,
    `3. Criar scripts para processar JSONs em lotes`,
    `4. Implementar pipeline de criação de clientes baseado nos dados`,
    '',
    '='.repeat(80),
    ''
  ].join('\n');
  
  fs.writeFileSync(
    path.join(basePath, 'resumo-execucao.txt'), 
    resumo, 
    'utf8'
  );
}

async function orquestrarExtracaoCrm(): Promise<DadosEnriquecidos> {
  const startTime = Date.now();
  
  try {
    Logger.section("🚀 ORQUESTRAÇÃO DE EXTRAÇÃO CRM");
    Logger.info("Iniciando extração completa de dados do CRM...");
    
    // 1. Criar estrutura de pastas
    Logger.info("📁 Criando estrutura de pastas...");
    const basePath = await criarEstruturaPastas();
    Logger.success(`✅ Pastas criadas em: ${basePath}`);
    
    // 2. Buscar dados complementares (persons, companies, etc.)
    const dadosComplementares = await buscarDadosComplementares();
    
    // 3. Buscar oportunidades
    Logger.info("🎯 Buscando oportunidades do CRM...");
    const oportunidades = await fetchDeals();
    Logger.success(`✅ ${oportunidades.length.toLocaleString('pt-BR')} oportunidades encontradas`);
    
    if (oportunidades.length === 0) {
      throw new Error("Nenhuma oportunidade encontrada no CRM");
    }
    
    // 4. Salvar oportunidades em lotes
    Logger.info("💾 Salvando oportunidades em arquivos JSON...");
    const arquivosGerados = await salvarOportunidadesEmLotes(
      oportunidades, 
      dadosComplementares, 
      basePath
    );
    
    // 5. Gerar estatísticas (para isso, precisamos carregar uma amostra das oportunidades enriquecidas)
    Logger.info("📊 Gerando estatísticas...");
    const amostraEnriquecida = oportunidades.slice(0, 100).map(oportunidade => 
      enriquecerOportunidade(oportunidade, dadosComplementares, 1)
    );
    const estatisticas = gerarEstatisticas(amostraEnriquecida);
    
    // Projetar estatísticas para o total
    const estatisticasFinais = {
      comPerson: Math.round((estatisticas.comPerson / 100) * oportunidades.length),
      comCompany: Math.round((estatisticas.comCompany / 100) * oportunidades.length),
      comPipeline: Math.round((estatisticas.comPipeline / 100) * oportunidades.length),
      comStage: Math.round((estatisticas.comStage / 100) * oportunidades.length),
      comCustomFields: Math.round((estatisticas.comCustomFields / 100) * oportunidades.length)
    };
    
    const endTime = Date.now();
    const tempoExecucao = (endTime - startTime) / 1000;
    
    const resultado: DadosEnriquecidos = {
      totalOportunidades: oportunidades.length,
      arquivosGerados,
      tempoExecucao,
      estatisticas: estatisticasFinais
    };
    
    // 6. Salvar metadados
    Logger.info("📋 Salvando metadados e relatórios...");
    await salvarMetadados(basePath, resultado, dadosComplementares);
    
    return resultado;
    
  } catch (error: any) {
    Logger.error("❌ Erro durante a orquestração:", error.message);
    throw error;
  }
}

// Função principal
async function main() {
  Logger.section("🎯 EXTRAÇÃO COMPLETA DE DADOS CRM");
  
  try {
    const resultado = await orquestrarExtracaoCrm();
    
    Logger.success("\n✅ EXTRAÇÃO CONCLUÍDA COM SUCESSO!");
    Logger.info("=".repeat(60));
    Logger.info(`📊 Total de oportunidades: ${resultado.totalOportunidades.toLocaleString('pt-BR')}`);
    Logger.info(`📁 Arquivos JSON gerados: ${resultado.arquivosGerados.length}`);
    Logger.info(`⏱️ Tempo de execução: ${resultado.tempoExecucao.toFixed(2)} segundos`);
    
    Logger.info("\n📈 ESTATÍSTICAS DE ENRIQUECIMENTO:");
    Logger.info(`👥 Com person: ${resultado.estatisticas.comPerson.toLocaleString('pt-BR')} (${((resultado.estatisticas.comPerson / resultado.totalOportunidades) * 100).toFixed(1)}%)`);
    Logger.info(`🏢 Com company: ${resultado.estatisticas.comCompany.toLocaleString('pt-BR')} (${((resultado.estatisticas.comCompany / resultado.totalOportunidades) * 100).toFixed(1)}%)`);
    Logger.info(`🔄 Com pipeline: ${resultado.estatisticas.comPipeline.toLocaleString('pt-BR')} (${((resultado.estatisticas.comPipeline / resultado.totalOportunidades) * 100).toFixed(1)}%)`);
    Logger.info(`📊 Com stage: ${resultado.estatisticas.comStage.toLocaleString('pt-BR')} (${((resultado.estatisticas.comStage / resultado.totalOportunidades) * 100).toFixed(1)}%)`);
    
    Logger.info("\n💡 PRÓXIMOS PASSOS RECOMENDADOS:");
    Logger.info("1. Validar qualidade dos dados nos arquivos JSON");
    Logger.info("2. Criar script para analisar distribuição por pipeline");
    Logger.info("3. Implementar lógica de criação de clientes baseada nos dados");
    Logger.info("4. Configurar pipeline automático de sincronização");
    
    console.log(`\n📂 Dados salvos em: dados-crm-extraidos/`);
    
  } catch (error: any) {
    Logger.error("❌ Erro durante a execução:", error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  main();
}

export { orquestrarExtracaoCrm }; 