import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
import fs from "fs";
import path from "path";
import { Logger } from "../services/logger.service";

dotenv.config();

const prisma = new PrismaClient();

interface OportunidadeCrm {
  id: number;
  created_at: string;
  customFields?: any[];
  person?: {
    id: number;
    name?: string;
    cpf?: string;
    contactPhones?: Array<{
      phone: string;
      is_main: number;
    }>;
    contactEmails?: Array<{
      email: string;
      is_main: number;
    }>;
  };
  company?: {
    id: number;
    name?: string;
    cnpj?: string;
    contactPhones?: Array<{
      phone: string;
      is_main: number;
    }>;
    contactEmails?: Array<{
      email: string;
      is_main: number;
    }>;
  };
}

interface ClienteProcessado {
  identificador: string;
  oportunidades: OportunidadeCrm[];
  oportunidadePrincipal: OportunidadeCrm;
  telefone: string;
  nome: string;
  numeroDocumento?: string;
  tipoDocumento?: string;
  numerosProcesso: string[];
  emails: string[];
  telefonesSecundarios: string[];
}

interface ResultadoProcessamento {
  clientesCriados: number;
  processosVinculados: number;
  titularesVinculados: number;
  oportunidadesProcessadas: number;
  oportunidadesIgnoradas: number;
  detalhes: Array<{
    clienteNome: string;
    identificador: string;
    numeroDocumento: string;
    qtdProcessos: number;
  }>;
}

// Função corrigida para extrair identificador do telefone
function extrairIdentificadorTelefone(telefone: string): string {
  if (!telefone || telefone === "NoPhone") {
    return "0000000000";
  }

  const numeroLimpo = telefone.replace(/\D/g, '');
  
  if (!numeroLimpo) {
    return "0000000000";
  }

  let numeroProcessado = numeroLimpo;

  // Lista de DDDs válidos no Brasil
  const dddsValidos = new Set([
    11, 12, 13, 14, 15, 16, 17, 18, 19, // SP
    21, 22, 24, // RJ/ES
    27, 28, // ES
    31, 32, 33, 34, 35, 37, 38, // MG
    41, 42, 43, 44, 45, 46, // PR
    47, 48, 49, // SC
    51, 53, 54, 55, // RS
    61, // DF/GO
    62, 64, // GO
    63, // TO
    65, 66, // MT
    67, // MS
    68, // AC
    69, // RO
    71, 73, 74, 75, 77, // BA
    79, // SE
    81, 87, // PE
    82, // AL
    83, // PB
    84, // RN
    85, 88, // CE
    86, 89, // PI
    91, 93, 94, // PA
    92, 97, // AM
    95, // RR
    96, // AP
    98, 99 // MA
  ]);

  // CORREÇÃO: Detectar duplo "55" (país + DDD)
  if (numeroProcessado.startsWith('5555') && numeroProcessado.length >= 12) {
    // Remove primeiro "55" (código do país), mantém segundo "55" (DDD)
    numeroProcessado = numeroProcessado.substring(2);
  } else if (numeroProcessado.startsWith('55') && numeroProcessado.length >= 12) {
    // Verifica se após remover 55, temos um número válido brasileiro
    const semPrefixo = numeroProcessado.substring(2);
    
    if (semPrefixo.length === 10 || semPrefixo.length === 11) {
      const possibleDDD = parseInt(semPrefixo.substring(0, 2));
      if (dddsValidos.has(possibleDDD)) {
        numeroProcessado = semPrefixo;
      }
    }
  }

  // Se ainda não temos pelo menos 10 dígitos, preenche com zeros à direita
  if (numeroProcessado.length < 10) {
    numeroProcessado = numeroProcessado.padEnd(10, '0');
  }

  // Extrai DDD (primeiros 2 dígitos) e últimos 8 dígitos
  const ddd = numeroProcessado.substring(0, 2);
  const ultimosOitoDigitos = numeroProcessado.slice(-8);
  
  return ddd + ultimosOitoDigitos;
}

// Função para extrair números de processo
function extrairNumerosProcesso(oportunidade: OportunidadeCrm): string[] {
  const numeroProcessoField = oportunidade.customFields?.find((field: any) => field.id === 194250);
  if (!numeroProcessoField?.value) return [];
  
  const matches = numeroProcessoField.value.match(/\b\d{9}\b/g);
  return matches ? [...new Set(matches as string[])] : [];
}

// Função para obter melhor telefone
function obterMelhorTelefone(oportunidade: OportunidadeCrm): string | null {
  // Prioridade 1: Person com is_main = 1
  if (oportunidade.person?.contactPhones) {
    const principal = oportunidade.person.contactPhones.find(p => p.is_main === 1);
    if (principal?.phone && principal.phone.replace(/\D/g, '').length >= 10) {
      return principal.phone;
    }
    
    // Prioridade 2: Primeiro telefone válido do person
    const primeiro = oportunidade.person.contactPhones.find(p => p.phone && p.phone.replace(/\D/g, '').length >= 10);
    if (primeiro?.phone) {
      return primeiro.phone;
    }
  }

  // Prioridade 3: Company
  if (oportunidade.company?.contactPhones) {
    const telefoneCompany = oportunidade.company.contactPhones.find(p => p.phone && p.phone.replace(/\D/g, '').length >= 10);
    if (telefoneCompany?.phone) {
      return telefoneCompany.phone;
    }
  }

  return null;
}

// Função para obter documento
function obterDocumento(oportunidade: OportunidadeCrm): { documento?: string; tipo?: string } {
  if (oportunidade.person?.cpf) {
    return { documento: oportunidade.person.cpf, tipo: 'CPF' };
  }
  
  if (oportunidade.company?.cnpj) {
    return { documento: oportunidade.company.cnpj, tipo: 'CNPJ' };
  }
  
  return {};
}

// Função para carregar todos os dados CRM
async function carregarDadosCrm(): Promise<OportunidadeCrm[]> {
  Logger.section("📥 CARREGAMENTO DE DADOS CRM");
  
  const pastaPrincipal = path.join(process.cwd(), 'dados-crm-extraidos');
  
  if (!fs.existsSync(pastaPrincipal)) {
    throw new Error("Pasta 'dados-crm-extraidos' não encontrada.");
  }
  
  const subpastas = fs.readdirSync(pastaPrincipal, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name)
    .sort()
    .reverse();
  
  if (subpastas.length === 0) {
    throw new Error("Nenhuma subpasta encontrada em 'dados-crm-extraidos'.");
  }
  
  const pastaBase = path.join(pastaPrincipal, subpastas[0]);
  const pastaOportunidades = path.join(pastaBase, 'oportunidades-completas');
  
  Logger.info(`📂 Usando pasta: ${subpastas[0]}`);
  
  const arquivos = fs.readdirSync(pastaOportunidades)
    .filter(arquivo => arquivo.endsWith('.json'))
    .map(arquivo => path.join(pastaOportunidades, arquivo))
    .sort();
  
  Logger.info(`📁 ${arquivos.length} arquivos JSON encontrados`);
  
  const todasOportunidades: OportunidadeCrm[] = [];
  
  for (let i = 0; i < arquivos.length; i++) {
    const arquivo = arquivos[i];
    const nomeArquivo = path.basename(arquivo);
    
    try {
      const conteudo = fs.readFileSync(arquivo, 'utf8');
      const dados = JSON.parse(conteudo);
      
      if (dados.oportunidades && Array.isArray(dados.oportunidades)) {
        todasOportunidades.push(...dados.oportunidades);
      }
      
      if ((i + 1) % 10 === 0) {
        Logger.info(`✅ Processados ${i + 1}/${arquivos.length} arquivos`);
      }
      
    } catch (error: any) {
      Logger.error(`❌ Erro ao processar ${nomeArquivo}: ${error.message}`);
    }
  }
  
  Logger.success(`🎯 Total carregado: ${todasOportunidades.length} oportunidades`);
  return todasOportunidades;
}

// Função principal de processamento
async function processarDadosCrm(): Promise<ResultadoProcessamento> {
  Logger.section("🔄 PROCESSAMENTO DOS DADOS CRM");
  
  const oportunidades = await carregarDadosCrm();
  const clientesMap = new Map<string, ClienteProcessado>();
  let oportunidadesIgnoradas = 0;
  
  Logger.info("🔍 Agrupando oportunidades por telefone...");
  
  for (let i = 0; i < oportunidades.length; i++) {
    const oportunidade = oportunidades[i];
    
    // Progresso a cada 100
    if ((i + 1) % 100 === 0) {
      Logger.info(`📊 Processando: ${i + 1}/${oportunidades.length} oportunidades`);
    }
    
    // Obter telefone válido
    const telefone = obterMelhorTelefone(oportunidade);
    if (!telefone) {
      oportunidadesIgnoradas++;
      continue;
    }
    
    const identificador = extrairIdentificadorTelefone(telefone);
    const { documento, tipo } = obterDocumento(oportunidade);
    const numerosProcesso = extrairNumerosProcesso(oportunidade);
    
    // Se não tem documento próprio, verifica se os processos têm titulares com documento
    if (!documento && numerosProcesso.length === 0) {
      oportunidadesIgnoradas++;
      continue;
    }
    
    // Agrupar por identificador
    if (!clientesMap.has(identificador)) {
      clientesMap.set(identificador, {
        identificador,
        oportunidades: [],
        oportunidadePrincipal: oportunidade,
        telefone,
        nome: oportunidade.person?.name || oportunidade.company?.name || `Cliente ${identificador}`,
        numeroDocumento: documento,
        tipoDocumento: tipo,
        numerosProcesso: [...numerosProcesso],
        emails: [],
        telefonesSecundarios: []
      });
    } else {
      const cliente = clientesMap.get(identificador)!;
      cliente.oportunidades.push(oportunidade);
      cliente.numerosProcesso.push(...numerosProcesso);
      
      // Atualizar para oportunidade mais antiga
      if (new Date(oportunidade.created_at) < new Date(cliente.oportunidadePrincipal.created_at)) {
        cliente.oportunidadePrincipal = oportunidade;
        cliente.nome = oportunidade.person?.name || oportunidade.company?.name || cliente.nome;
        if (!cliente.numeroDocumento && documento) {
          cliente.numeroDocumento = documento;
          cliente.tipoDocumento = tipo;
        }
      }
    }
    
    // Adicionar oportunidade ao grupo
    clientesMap.get(identificador)!.oportunidades.push(oportunidade);
  }
  
  Logger.success(`✅ Agrupamento concluído: ${clientesMap.size} clientes únicos identificados`);
  Logger.info(`❌ Oportunidades ignoradas: ${oportunidadesIgnoradas}`);
  
  return await criarClientesNoBanco(Array.from(clientesMap.values()));
}

// Função para criar clientes no banco
async function criarClientesNoBanco(clientesProcessados: ClienteProcessado[]): Promise<ResultadoProcessamento> {
  Logger.section("💾 CRIAÇÃO DE CLIENTES NO BANCO");
  
  const resultado: ResultadoProcessamento = {
    clientesCriados: 0,
    processosVinculados: 0,
    titularesVinculados: 0,
    oportunidadesProcessadas: 0,
    oportunidadesIgnoradas: 0,
    detalhes: []
  };
  
  for (let i = 0; i < clientesProcessados.length; i++) {
    const clienteData = clientesProcessados[i];
    
    if ((i + 1) % 100 === 0) {
      Logger.info(`💾 Criando clientes: ${i + 1}/${clientesProcessados.length}`);
    }
    
    try {
      // Verificar se tem documento próprio ou de titular
      let temDocumento = !!clienteData.numeroDocumento;
      
      if (!temDocumento && clienteData.numerosProcesso.length > 0) {
        // Verificar se algum processo tem titular com documento
        const titularesComDocumento = await prisma.titular.findFirst({
          where: {
            AND: [
              { processoId: { in: clienteData.numerosProcesso } },
              { numeroDocumento: { not: null } }
            ]
          }
        });
        
        temDocumento = !!titularesComDocumento;
      }
      
      if (!temDocumento) {
        continue; // Ignora cliente sem documento
      }
      
      // Criar cliente
      const cliente = await prisma.cliente.create({
        data: {
          identificador: clienteData.identificador,
          crmId: clienteData.oportunidadePrincipal.id,
          crmLeadIds: clienteData.oportunidades.map(o => o.id),
          nome: clienteData.nome,
          numeroDocumento: clienteData.numeroDocumento,
          tipoDeDocumento: clienteData.tipoDocumento,
          camposPersonalizados: clienteData.oportunidadePrincipal.customFields || undefined
        }
      });
      
      resultado.clientesCriados++;
      
      // Criar contatos
      await criarContatosCliente(cliente.id, clienteData);
      
      // Vincular processos
      const processosVinculados = await vincularProcessos(cliente.id, clienteData.numerosProcesso);
      resultado.processosVinculados += processosVinculados;
      
      // Vincular titulares
      const titularesVinculados = await vincularTitulares(cliente.id, clienteData.numerosProcesso);
      resultado.titularesVinculados += titularesVinculados;
      
      resultado.detalhes.push({
        clienteNome: clienteData.nome,
        identificador: clienteData.identificador,
        numeroDocumento: clienteData.numeroDocumento || 'SEM_DOCUMENTO_PROPRIO',
        qtdProcessos: processosVinculados
      });
      
    } catch (error: any) {
      Logger.error(`❌ Erro ao criar cliente ${clienteData.identificador}: ${error.message}`);
    }
  }
  
  return resultado;
}

// Função para criar contatos do cliente
async function criarContatosCliente(clienteId: number, clienteData: ClienteProcessado): Promise<void> {
  const oportunidade = clienteData.oportunidadePrincipal;
  
  // Contato principal (person ou company)
  const emailPrincipal = oportunidade.person?.contactEmails?.[0]?.email || 
                        oportunidade.company?.contactEmails?.[0]?.email;
  
  await prisma.contatoCliente.create({
    data: {
      clienteId,
      telefone: clienteData.telefone,
      email: emailPrincipal
    }
  });
  
  // Contato adicional se company tem dados diferentes
  if (oportunidade.person && oportunidade.company) {
    const telefoneCompany = oportunidade.company.contactPhones?.[0]?.phone;
    const emailCompany = oportunidade.company.contactEmails?.[0]?.email;
    
    if (telefoneCompany && telefoneCompany !== clienteData.telefone) {
      await prisma.contatoCliente.create({
        data: {
          clienteId,
          telefoneSegundario: telefoneCompany,
          email: emailCompany
        }
      });
    }
  }
}

// Função para vincular processos
async function vincularProcessos(clienteId: number, numerosProcesso: string[]): Promise<number> {
  if (numerosProcesso.length === 0) return 0;
  
  const processosExistentes = await prisma.processo.findMany({
    where: { numero: { in: numerosProcesso } }
  });
  
  for (const processo of processosExistentes) {
    await prisma.processo.update({
      where: { id: processo.id },
      data: { clienteId }
    });
  }
  
  return processosExistentes.length;
}

// Função para vincular titulares
async function vincularTitulares(clienteId: number, numerosProcesso: string[]): Promise<number> {
  if (numerosProcesso.length === 0) return 0;
  
  const result = await prisma.titular.updateMany({
    where: { processoId: { in: numerosProcesso } },
    data: { clienteId }
  });
  
  return result.count;
}

// Função para gerar relatório final
async function gerarRelatorioFinal(resultado: ResultadoProcessamento): Promise<void> {
  Logger.section("📊 GERAÇÃO DE RELATÓRIO FINAL");
  
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  
  // Relatório TXT
  const relatorioTxt = [
    '='.repeat(80),
    'RELATÓRIO DE CRIAÇÃO DE CLIENTES CRM',
    '='.repeat(80),
    `Data de execução: ${new Date().toLocaleString('pt-BR')}`,
    '',
    'RESUMO GERAL:',
    `✅ Clientes criados: ${resultado.clientesCriados.toLocaleString('pt-BR')}`,
    `🔗 Processos vinculados: ${resultado.processosVinculados.toLocaleString('pt-BR')}`,
    `👥 Titulares vinculados: ${resultado.titularesVinculados.toLocaleString('pt-BR')}`,
    '',
    '='.repeat(80)
  ].join('\n');
  
  fs.writeFileSync(`relatorio-clientes-crm-${timestamp}.txt`, relatorioTxt, 'utf8');
  
  // Relatório CSV
  const csvContent = [
    'cliente_nome,identificador,numero_documento,qtd_processos',
    ...resultado.detalhes.map(d => 
      `"${d.clienteNome}","${d.identificador}","${d.numeroDocumento}","${d.qtdProcessos}"`
    )
  ].join('\n');
  
  fs.writeFileSync(`clientes-criados-${timestamp}.csv`, csvContent, 'utf8');
  
  Logger.success(`📁 Relatórios salvos: relatorio-clientes-crm-${timestamp}.txt`);
  Logger.success(`📁 CSV salvo: clientes-criados-${timestamp}.csv`);
}

// Função principal
async function main() {
  try {
    Logger.section("🚀 CRIAÇÃO DE CLIENTES A PARTIR DO CRM");
    
    const resultado = await processarDadosCrm();
    await gerarRelatorioFinal(resultado);
    
    Logger.success("✅ Processo concluído com sucesso!");
    
  } catch (error: any) {
    Logger.error("❌ Erro durante a execução:", error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  main();
}

export { main as criarClientesCrmCompleto }; 