import { PrismaClient, TipoAcesso } from '@prisma/client';
import cliProgress from 'cli-progress';

const prisma = new PrismaClient();

/**
 * Script para popular as permissões iniciais do sistema
 * 1. Criar acessos PROPRIETARIO para todos os processos que já têm clienteId
 * 2. Criar acessos CORINGA para o cliente 13 em todos os processos da REGISTRE-SE
 */
async function popularPermissoesIniciais() {
  try {
    console.log('🚀 Iniciando população de permissões iniciais...\n');

    // 1. Buscar todos os processos que já têm cliente associado
    console.log('📋 Buscando processos com clientes existentes...');
    const processosComCliente = await prisma.processo.findMany({
      where: {
        clienteId: { not: null }
      },
      select: {
        id: true,
        numero: true,
        clienteId: true
      }
    });

    console.log(`   Encontrados ${processosComCliente.length} processos com clientes\n`);

    // 2. Buscar processos da REGISTRE-SE LTDA.
    console.log('📋 Buscando processos da REGISTRE-SE LTDA...');
    const processosRegistreSe = await prisma.processo.findMany({
      where: {
        procurador: {
          nome: {
            contains: "REGISTRE-SE LTDA",
            mode: "insensitive"
          }
        }
      },
      select: {
        id: true,
        numero: true,
        clienteId: true
      }
    });

    console.log(`   Encontrados ${processosRegistreSe.length} processos da REGISTRE-SE LTDA\n`);

    // 3. Verificar se cliente 13 existe
    const cliente13 = await prisma.cliente.findFirst({
      where: { identificador: '00000013' }
    });

    if (!cliente13) {
      console.log('⚠️  Cliente coringa (00000013) não encontrado. Criando...');
      await prisma.cliente.create({
        data: {
          identificador: '00000013',
          nome: 'Cliente Coringa (REGISTRE-SE)',
          tipoDeDocumento: 'CORINGA',
          numeroDocumento: '00000013'
        }
      });
      console.log('   ✅ Cliente coringa criado\n');
    }

    const clienteCoringa = await prisma.cliente.findFirst({
      where: { identificador: '00000013' }
    });

    if (!clienteCoringa) {
      throw new Error('Falha ao criar/encontrar cliente coringa');
    }

    // 4. Preparar dados para inserção
    const permissoesParaInserir: Array<{
      clienteId: number;
      processoId: string;
      tipoAcesso: TipoAcesso;
      observacoes: string;
    }> = [];

    // 4.1. Adicionar permissões PROPRIETARIO para processos existentes
    for (const processo of processosComCliente) {
      if (processo.clienteId) {
        permissoesParaInserir.push({
          clienteId: processo.clienteId,
          processoId: processo.id,
          tipoAcesso: TipoAcesso.PROPRIETARIO,
          observacoes: 'Migração inicial - cliente proprietário existente'
        });
      }
    }

    // 4.2. Adicionar permissões CORINGA para cliente 13
    for (const processo of processosRegistreSe) {
      permissoesParaInserir.push({
        clienteId: clienteCoringa.id,
        processoId: processo.id,
        tipoAcesso: TipoAcesso.CORINGA,
        observacoes: 'Cliente coringa - acesso total REGISTRE-SE LTDA'
      });
    }

    // 5. Remover duplicatas (caso processo já tenha permissão do mesmo cliente)
    const permissoesUnicas = permissoesParaInserir.filter((permissao, index, array) => {
      return array.findIndex(p => 
        p.clienteId === permissao.clienteId && p.processoId === permissao.processoId
      ) === index;
    });

    console.log(`📊 Permissões a serem criadas:`);
    console.log(`   • ${permissoesUnicas.filter(p => p.tipoAcesso === 'PROPRIETARIO').length} acessos PROPRIETARIO`);
    console.log(`   • ${permissoesUnicas.filter(p => p.tipoAcesso === 'CORINGA').length} acessos CORINGA`);
    console.log(`   • Total: ${permissoesUnicas.length} permissões\n`);

    if (permissoesUnicas.length === 0) {
      console.log('✅ Nenhuma permissão nova para criar.');
      return;
    }

    // 6. Configurar barra de progresso
    const barraProgresso = new cliProgress.SingleBar({
      format: 'Criando permissões |{bar}| {percentage}% | {value}/{total}',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });
    barraProgresso.start(permissoesUnicas.length, 0);

    // 7. Inserir permissões em lotes para performance
    const BATCH_SIZE = 100;
    let totalCriadas = 0;

    for (let i = 0; i < permissoesUnicas.length; i += BATCH_SIZE) {
      const lote = permissoesUnicas.slice(i, i + BATCH_SIZE);
      
      try {
        await prisma.clienteProcessoAcesso.createMany({
          data: lote,
          skipDuplicates: true // Evita erro se já existir
        });
        
        totalCriadas += lote.length;
        barraProgresso.update(Math.min(i + BATCH_SIZE, permissoesUnicas.length));
      } catch (error: any) {
        console.error(`\nErro no lote ${Math.floor(i / BATCH_SIZE) + 1}:`, error.message);
      }
    }

    barraProgresso.stop();

    // 8. Verificar resultados
    const totalPermissoesCriadas = await prisma.clienteProcessoAcesso.count();
    
    console.log('\n🎉 POPULAÇÃO DE PERMISSÕES CONCLUÍDA!');
    console.log('📊 Resultados:');
    console.log(`   • ${totalCriadas} permissões processadas`);
    console.log(`   • ${totalPermissoesCriadas} permissões total no sistema`);
    console.log(`   • Cliente coringa (ID: ${clienteCoringa.id}) configurado\n`);

    // 9. Relatório por tipo de acesso
    const relatorioTipos = await prisma.clienteProcessoAcesso.groupBy({
      by: ['tipoAcesso'],
      _count: { id: true }
    });

    console.log('📈 Distribuição por tipo de acesso:');
    for (const tipo of relatorioTipos) {
      console.log(`   • ${tipo.tipoAcesso}: ${tipo._count.id} permissões`);
    }

  } catch (error: any) {
    console.error('\n❌ Erro ao popular permissões iniciais:', error.message);
    if (error.code) {
      console.error('Código do erro:', error.code);
    }
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script
console.log('🏗️ Sistema de Permissões - População Inicial');
console.log('═══════════════════════════════════════════\n');
popularPermissoesIniciais(); 