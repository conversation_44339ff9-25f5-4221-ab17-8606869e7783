import { Request, Response } from 'express';
import { criarInfoProtocolo, obterListaProtocolos } from '../utils/protocoloUtils';
import { extrairImagemMarca, verificarMicroservico, extrairMetadados, extrairDadosCompletos } from '../utils/pdfProcessorClient';
import { PrismaClient } from '@prisma/client';
import path from 'path';
import fs from 'fs';
import { BATCH_CONFIG } from '../middleware/protocoloUploadMiddleware';

const prisma = new PrismaClient();

/**
 * Cria resposta de sucesso para upload
 */
const criarRespostaSucessoUpload = (protocoloInfo: any) => ({
  success: true,
  message: 'Protocolo PDF recebido e salvo com sucesso!',
  data: {
    protocolo: protocoloInfo,
    nextSteps: [
      'Extração de imagem será processada pelo microserviço Python',
      'Armazenamento no banco de dados'
    ]
  }
});

/**
 * Cria resposta de erro padrão
 */
const criarRespostaErro = (message: string, error?: string) => ({
  success: false,
  message,
  ...(error && { error })
});

/**
 * Upload e salva um arquivo PDF de protocolo
 * Extrai metadados, renomeia arquivo e processa imagem
 */
export const uploadProtocolo = async (req: Request, res: Response): Promise<any> => {
  try {
    // Verificar se o arquivo foi enviado
    if (!req.file) {
      return res.status(400).json(
        criarRespostaErro('Nenhum arquivo PDF de protocolo foi enviado', 'FILE_NOT_PROVIDED')
      );
    }
    console.log('🔍 Arquivo recebido:', req.file);
    const arquivoTemporario = req.file.path;
    const diretorioProtocolos = path.dirname(arquivoTemporario);

    // Log do protocolo salvo temporariamente
    console.log('📄 Protocolo PDF recebido:', req.file.filename);

    // Verificar se microserviço está online
    const microservicoOnline = await verificarMicroservico();
    if (!microservicoOnline) {
      console.log('⚠️ Microserviço Python offline - PDF salvo mas não processado');
      return res.status(200).json({
        success: true,
        message: 'PDF salvo, mas microserviço Python está offline',
        data: {
          numeroProcesso: null,
          elementoNominativo: null,
          nomeArquivo: req.file.filename,
          logoExtraida: false,
          dataProcessamento: new Date().toISOString(),
          urls: {
            downloadPdf: null,
            logoImagem: null
          },
          warning: 'Processamento de metadados e extração de imagem não foram executados',
          detalhes: {
            protocolo: criarInfoProtocolo(req.file)
          }
        }
      });
    }

    // 1. Extrair metadados (número do processo + elemento nominativo)
    console.log('🔍 Extraindo metadados do PDF...');
    const metadados = await extrairMetadados(arquivoTemporario);

    // 1.5. Extrair dados completos incluindo titulares
    console.log('👥 Extraindo dados dos titulares do PDF...');
    let dadosTitulares: any = null;
    try {
      const dadosCompletos = await extrairDadosCompletos(arquivoTemporario);

      if (dadosCompletos && dadosCompletos.titulares) {
        dadosTitulares = dadosCompletos.titulares;
        console.log(`✅ Extraídos dados de ${dadosTitulares.length} titular(es)`);
      }
    } catch (error) {
      console.log('⚠️ Erro ao extrair dados dos titulares:', error);
      // Não falhar o upload se a extração dos titulares falhar
    }

    let novoNomeArquivo: string;
    let caminhoFinal: string;

    if (metadados && metadados.process_number) {
      // 🔧 CORRIGIDO: Aceitar processo mesmo sem elemento nominativo (marcas figurativas)
      const elementoNominativo = metadados.elemento_nominativo || null;
      const elementoNominativoLimpo = elementoNominativo ? 
        elementoNominativo.replace(/[^\w\-_\.]/g, '_') : 
        'MARCA_FIGURATIVA';
      
      // Criar nome do arquivo com metadados extraídos
      novoNomeArquivo = `${metadados.process_number}-${elementoNominativoLimpo}.pdf`;
      caminhoFinal = path.join(diretorioProtocolos, novoNomeArquivo);

      // Renomear arquivo
      fs.renameSync(arquivoTemporario, caminhoFinal);
      console.log(`✅ Arquivo renomeado: ${novoNomeArquivo}`);
      if (elementoNominativo) {
        console.log(`📝 Elemento nominativo original: "${elementoNominativo}"`);
        console.log(`📝 Para nome de arquivo: "${elementoNominativoLimpo}"`);
      } else {
        console.log(`📝 Marca figurativa - sem elemento nominativo`);
      }
    } else {
      // Se não conseguiu extrair número do processo, manter nome original
      console.log('⚠️ Não foi possível extrair número do processo - mantendo nome original');
      novoNomeArquivo = req.file.filename;
      caminhoFinal = arquivoTemporario;
    }

    // 2. Chamar microserviço Python para extrair imagem
    console.log('🐍 Processando extração de imagem...');
    const resultadoExtracao = await extrairImagemMarca(caminhoFinal);

    // Criar informações do protocolo final
    const protocoloInfo = {
      originalName: req.file.originalname,
      fileName: novoNomeArquivo,
      filePath: caminhoFinal,
      size: req.file.size,
      mimetype: req.file.mimetype,
      uploadDate: new Date().toISOString(),
      metadados: metadados || null
    };

    // Construir URLs para o frontend
    const numeroProcesso = metadados?.process_number;
    const elementoNominativo = metadados?.elemento_nominativo;
    const baseUrl = process.env.API_BASE_URL || `${req.protocol}://${req.get('host')}`;
    
    const urls = {
      downloadPdf: numeroProcesso ? `${baseUrl}/api/protocolo/download/${numeroProcesso}` : null,
      logoImagem: numeroProcesso ? `${baseUrl}/api/protocolo/logo/${numeroProcesso}` : null
    };

    // Se o número do processo foi extraído, adicionar à fila de processamento
    let processamentoId: string | null = null;
    if (numeroProcesso) {
      try {
        const processamento = await prisma.processamentoProtocolo.create({
          data: {
            numeroProcesso: numeroProcesso,
            elementoNominativo: elementoNominativo, // Pode ser null para marcas figurativas
            nomeArquivoPdf: novoNomeArquivo,
            dadosTitulares: dadosTitulares,
            status: 'PENDENTE'
          }
        });
        processamentoId = processamento.id;
        console.log(`📋 Protocolo ${numeroProcesso} adicionado à fila de processamento com ID: ${processamentoId}`);
        if (!elementoNominativo) {
          console.log(`📋 Marca figurativa - será processada normalmente sem elemento nominativo`);
        }
      } catch (filaError) {
        // Log do erro mas não falhar o upload
        console.error('Erro ao adicionar protocolo à fila:', filaError);
      }
    }

    // Resposta de sucesso com resultado completo
    return res.status(200).json({
      success: true,
      message: 'Protocolo processado com sucesso!',
      data: {
        // Informações principais para o frontend
        numeroProcesso: numeroProcesso || null,
        elementoNominativo: elementoNominativo || null,
        nomeArquivo: novoNomeArquivo,
        logoExtraida: resultadoExtracao?.success || false,
        dataProcessamento: new Date().toISOString(),
        
        // ID do processamento para acompanhamento
        processamentoId: processamentoId,
        
        // URLs diretas para uso no frontend
        urls: urls,
        
        // URLs de acompanhamento
        acompanhamento: {
          statusUrl: processamentoId ? `${baseUrl}/api/fila-protocolo/status/${numeroProcesso}` : null,
          filaAtivaUrl: `${baseUrl}/api/fila-protocolo/fila-ativa`,
          recentesUrl: `${baseUrl}/api/fila-protocolo/recentes`
        },
        
        // Detalhes completos (para debug/logs)
        detalhes: {
          protocolo: protocoloInfo,
          imagemMarca: resultadoExtracao
        }
      }
    });

  } catch (error) {
    console.error('Erro no upload do protocolo:', error);
    
    return res.status(500).json(
      criarRespostaErro(
        'Erro interno do servidor ao processar o protocolo',
        error instanceof Error ? error.message : 'Erro desconhecido'
      )
    );
  }
};

/**
 * 🆕 Lista processamentos de protocolos do banco de dados com filtros avançados
 * Substitui a antiga função que só listava arquivos do filesystem
 */
export const listarProtocolos = async (req: Request, res: Response): Promise<any> => {
  try {
    // Parâmetros de filtro da query string
    const {
      status,           // PENDENTE, PROCESSANDO, SUCESSO, FALHA_CRM, etc
      agrupamento,      // cliente, data, status, none
      limite = '50',    // número de registros
      pagina = '1',     // página atual
      cliente,          // ID ou nome do cliente
      dataInicio,       // YYYY-MM-DD
      dataFim,          // YYYY-MM-DD
      busca            // busca por número de processo ou elemento nominativo
    } = req.query;

    const limiteNum = parseInt(limite as string);
    const paginaNum = parseInt(pagina as string);
    const offset = (paginaNum - 1) * limiteNum;

    // Construir filtros WHERE dinâmicos
    const filtros: any = {};
    
    if (status) {
      if (status === 'FALHA') {
        // Buscar qualquer tipo de falha
        filtros.status = {
          in: ['FALHA_CRM', 'FALHA_CLIENTE', 'FALHA_PROCESSO', 'FALHA_LINK', 'FALHA_CHATGURU', 'FALHA_GERAL']
        };
      } else {
        filtros.status = status;
      }
    }

    if (dataInicio || dataFim) {
      filtros.criadoEm = {};
      if (dataInicio) filtros.criadoEm.gte = new Date(dataInicio as string);
      if (dataFim) {
        const dataFimDate = new Date(dataFim as string);
        dataFimDate.setHours(23, 59, 59, 999); // Fim do dia
        filtros.criadoEm.lte = dataFimDate;
      }
    }

    if (busca) {
      filtros.OR = [
        { numeroProcesso: { contains: busca as string } },
        { elementoNominativo: { contains: busca as string, mode: 'insensitive' } }
      ];
    }

    // Buscar processamentos com dados relacionados
    const processamentos = await prisma.processamentoProtocolo.findMany({
      where: filtros,
      include: {
        cliente: {
          select: {
            id: true,
            nome: true,
            identificador: true,
            autoLoginUrl: true
          }
        },
        processo: {
          select: {
            id: true,
            numero: true,
            marca: {
              select: {
                nome: true,
                apresentacao: true
              }
            }
          }
        }
      },
      orderBy: { criadoEm: 'desc' },
      skip: offset,
      take: limiteNum
    });

    // Contar total para paginação
    const total = await prisma.processamentoProtocolo.count({
      where: filtros
    });

    // Filtrar por cliente se especificado
    let processamentosFiltrados = processamentos;
    if (cliente) {
      processamentosFiltrados = processamentos.filter(p => 
        p.cliente?.id.toString() === cliente || 
        (p.cliente?.nome && p.cliente.nome.toLowerCase().includes((cliente as string).toLowerCase()))
      );
    }

    const baseUrl = process.env.API_BASE_URL || `${req.protocol}://${req.get('host')}`;

    // RESPOSTA SEM AGRUPAMENTO (padrão)
    if (!agrupamento || agrupamento === 'none') {
      const dados = processamentosFiltrados.map(p => ({
        id: p.id,
        numeroProcesso: p.numeroProcesso,
        elementoNominativo: p.elementoNominativo,
        status: p.status,
        tentativas: p.tentativas,
        criadoEm: p.criadoEm,
        processadoEm: p.processadoEm,
        ultimoErro: p.ultimoErro,
        
        // Dados do cliente
        cliente: p.cliente ? {
          id: p.cliente.id,
          nome: p.cliente.nome,
          identificador: p.cliente.identificador,
          autoLoginUrl: p.cliente.autoLoginUrl
        } : null,
        
        // Dados do processo/marca
        processo: p.processo ? {
          numero: p.processo.numero,
          marca: p.processo.marca?.nome,
          apresentacao: p.processo.marca?.apresentacao
        } : null,
        
        // URLs úteis
        urls: {
          downloadPdf: `${baseUrl}/api/protocolo/download/${p.numeroProcesso}`,
          logoImagem: `${baseUrl}/api/protocolo/logo/${p.numeroProcesso}`,
          status: `${baseUrl}/api/fila-protocolo/status/${p.numeroProcesso}`
        },
        
        // Indicadores visuais
        indicadores: {
          clienteCriado: p.clienteCriado,
          processoVinculado: p.processoVinculado,
          linkGerado: p.linkGerado,
          chatguruAtualizado: p.chatguruAtualizado
        }
      }));

      return res.json({
        success: true,
        message: `${dados.length} processamento(s) encontrado(s)`,
        data: dados,
        paginacao: {
          total,
          pagina: paginaNum,
          limite: limiteNum,
          totalPaginas: Math.ceil(total / limiteNum)
        },
        filtros: {
          status: status || 'todos',
          periodo: dataInicio && dataFim ? `${dataInicio} a ${dataFim}` : 'todos',
          busca: busca || null
        }
      });
    }

    // RESPOSTA COM AGRUPAMENTO
    if (agrupamento === 'cliente') {
      const grupos: any = {};
      
      for (const p of processamentosFiltrados) {
        const clienteKey = p.cliente ? `cliente-${p.cliente.id}` : 'sem-cliente';
        const clienteNome = p.cliente?.nome || 'Protocolos sem cliente';
        
        if (!grupos[clienteKey]) {
          grupos[clienteKey] = {
            cliente: p.cliente ? {
              id: p.cliente.id,
              nome: p.cliente.nome,
              identificador: p.cliente.identificador,
              autoLoginUrl: p.cliente.autoLoginUrl
            } : null,
            protocolos: [],
            resumo: {
              total: 0,
              pendentes: 0,
              processando: 0,
              sucessos: 0,
              falhas: 0,
              marcas: new Set()
            }
          };
        }
        
        grupos[clienteKey].protocolos.push({
          id: p.id,
          numeroProcesso: p.numeroProcesso,
          elementoNominativo: p.elementoNominativo,
          status: p.status,
          criadoEm: p.criadoEm,
          processadoEm: p.processadoEm,
          marca: p.processo?.marca?.nome,
          urls: {
            downloadPdf: `${baseUrl}/api/protocolo/download/${p.numeroProcesso}`,
            logoImagem: `${baseUrl}/api/protocolo/logo/${p.numeroProcesso}`
          }
        });
        
        // Atualizar resumo
        grupos[clienteKey].resumo.total++;
        if (p.status === 'PENDENTE') grupos[clienteKey].resumo.pendentes++;
        else if (p.status === 'PROCESSANDO') grupos[clienteKey].resumo.processando++;
        else if (p.status === 'SUCESSO') grupos[clienteKey].resumo.sucessos++;
        else if (p.status.startsWith('FALHA_')) grupos[clienteKey].resumo.falhas++;
        
        if (p.processo?.marca?.nome) {
          grupos[clienteKey].resumo.marcas.add(p.processo.marca.nome);
        }
      }
      
      // Converter Set de marcas para array
      Object.values(grupos).forEach((grupo: any) => {
        grupo.resumo.marcas = Array.from(grupo.resumo.marcas);
        grupo.resumo.marcasTexto = grupo.resumo.marcas.join(' / ');
      });

      return res.json({
        success: true,
        message: `${Object.keys(grupos).length} cliente(s) com processamentos`,
        data: grupos,
        agrupamento: 'cliente',
        paginacao: {
          total,
          pagina: paginaNum,
          limite: limiteNum,
          totalPaginas: Math.ceil(total / limiteNum)
        }
      });
    }

    if (agrupamento === 'status') {
      const grupos: any = {
        'PENDENTE': { processamentos: [], count: 0 },
        'PROCESSANDO': { processamentos: [], count: 0 },
        'SUCESSO': { processamentos: [], count: 0 },
        'FALHAS': { processamentos: [], count: 0 }
      };
      
      processamentosFiltrados.forEach(p => {
        const dadosProcessamento = {
          id: p.id,
          numeroProcesso: p.numeroProcesso,
          elementoNominativo: p.elementoNominativo,
          status: p.status,
          cliente: p.cliente?.nome,
          marca: p.processo?.marca?.nome,
          criadoEm: p.criadoEm
        };
        
        if (p.status === 'PENDENTE') {
          grupos.PENDENTE.processamentos.push(dadosProcessamento);
          grupos.PENDENTE.count++;
        } else if (p.status === 'PROCESSANDO') {
          grupos.PROCESSANDO.processamentos.push(dadosProcessamento);
          grupos.PROCESSANDO.count++;
        } else if (p.status === 'SUCESSO') {
          grupos.SUCESSO.processamentos.push(dadosProcessamento);
          grupos.SUCESSO.count++;
        } else if (p.status.startsWith('FALHA_')) {
          grupos.FALHAS.processamentos.push(dadosProcessamento);
          grupos.FALHAS.count++;
        }
      });

      return res.json({
        success: true,
        message: 'Processamentos agrupados por status',
        data: grupos,
        agrupamento: 'status'
      });
    }

    if (agrupamento === 'data') {
      const grupos: any = {};
      
      processamentosFiltrados.forEach(p => {
        const data = p.criadoEm.toISOString().split('T')[0]; // YYYY-MM-DD
        
        if (!grupos[data]) {
          grupos[data] = {
            data,
            processamentos: [],
            resumo: { total: 0, sucessos: 0, falhas: 0 }
          };
        }
        
        grupos[data].processamentos.push({
          id: p.id,
          numeroProcesso: p.numeroProcesso,
          elementoNominativo: p.elementoNominativo,
          status: p.status,
          cliente: p.cliente?.nome,
          marca: p.processo?.marca?.nome
        });
        
        grupos[data].resumo.total++;
        if (p.status === 'SUCESSO') grupos[data].resumo.sucessos++;
        else if (p.status.startsWith('FALHA_')) grupos[data].resumo.falhas++;
      });

      return res.json({
        success: true,
        message: `Processamentos agrupados por data`,
        data: grupos,
        agrupamento: 'data'
      });
    }

  } catch (error) {
    console.error('Erro ao listar processamentos:', error);
    
    return res.status(500).json(
      criarRespostaErro(
        'Erro ao listar processamentos de protocolos',
        error instanceof Error ? error.message : 'Erro desconhecido'
      )
    );
  }
};

/**
 * Download do protocolo PDF pelo número do processo
 */
export const downloadProtocolo = async (req: Request, res: Response): Promise<any> => {
  try {
    const { numeroProcesso } = req.params;

    if (!numeroProcesso) {
      return res.status(400).json(
        criarRespostaErro('Número do processo é obrigatório')
      );
    }

    // Buscar arquivo PDF com nome que contenha o número do processo
    const protocolosDir = path.join(process.cwd(), 'uploads', 'protocolos');
    
    if (!fs.existsSync(protocolosDir)) {
      return res.status(404).json(
        criarRespostaErro('Diretório de protocolos não encontrado')
      );
    }

    // Listar arquivos e procurar por arquivo que contenha o número do processo
    const arquivos = fs.readdirSync(protocolosDir);
    const arquivoEncontrado = arquivos.find(arquivo => {
      // Verificar se o arquivo contém o número do processo e é PDF
      return arquivo.includes(numeroProcesso) && path.extname(arquivo).toLowerCase() === '.pdf';
    });

    if (!arquivoEncontrado) {
      return res.status(404).json(
        criarRespostaErro(`Protocolo não encontrado para o processo ${numeroProcesso}`)
      );
    }

    const caminhoArquivo = path.join(protocolosDir, arquivoEncontrado);

    // Verificar se arquivo existe
    if (!fs.existsSync(caminhoArquivo)) {
      return res.status(404).json(
        criarRespostaErro('Arquivo de protocolo não encontrado no sistema')
      );
    }

    // Configurar headers para download
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="protocolo-${numeroProcesso}.pdf"`);

    // Enviar arquivo
    res.sendFile(path.resolve(caminhoArquivo));

  } catch (error) {
    console.error('Erro ao fazer download do protocolo:', error);
    
    return res.status(500).json(
      criarRespostaErro(
        'Erro interno do servidor ao baixar protocolo',
        error instanceof Error ? error.message : 'Erro desconhecido'
      )
    );
  }
};

/**
 * Download da logo da marca pelo número do processo
 */
export const downloadLogo = async (req: Request, res: Response): Promise<any> => {
  try {
    const { numeroProcesso } = req.params;

    if (!numeroProcesso) {
      return res.status(400).json(
        criarRespostaErro('Número do processo é obrigatório')
      );
    }

    // Buscar logo com nome baseado no número do processo
    const imagensDir = path.join(process.cwd(), 'uploads', 'imagens-marca');
    
    if (!fs.existsSync(imagensDir)) {
      return res.status(404).json(
        criarRespostaErro('Diretório de imagens não encontrado')
      );
    }

    // Extensões de imagem suportadas
    const extensoesSuportadas = ['jpg', 'jpeg', 'png', 'webp', 'gif'];
    
    let caminhoLogo: string | null = null;
    let extensaoEncontrada: string | null = null;

    // Procurar arquivo de logo com diferentes extensões
    for (const ext of extensoesSuportadas) {
      const nomeArquivoLogo = `${numeroProcesso}.${ext}`;
      const caminhoTeste = path.join(imagensDir, nomeArquivoLogo);
      
      if (fs.existsSync(caminhoTeste)) {
        caminhoLogo = caminhoTeste;
        extensaoEncontrada = ext;
        break;
      }
    }

    // Verificar se arquivo de logo foi encontrado
    if (!caminhoLogo || !extensaoEncontrada) {
      return res.status(404).json(
        criarRespostaErro(`Logo não encontrada para o processo ${numeroProcesso}. Extensões procuradas: ${extensoesSuportadas.join(', ')}`)
      );
    }

    // Definir Content-Type baseado na extensão
    const contentTypes: { [key: string]: string } = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'webp': 'image/webp',
      'gif': 'image/gif'
    };

    // Configurar headers para exibição de imagem
    res.setHeader('Content-Type', contentTypes[extensaoEncontrada] || 'image/jpeg');
    res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache por 24h
    res.setHeader('Accept-Ranges', 'bytes');

    // Enviar arquivo para exibição
    res.sendFile(path.resolve(caminhoLogo));

  } catch (error) {
    console.error('Erro ao fazer download da logo:', error);
    
    return res.status(500).json(
      criarRespostaErro(
        'Erro interno do servidor ao baixar logo',
        error instanceof Error ? error.message : 'Erro desconhecido'
      )
    );
  }
};

// 🆕 NOVA FUNCIONALIDADE: Upload em lote com processamento otimizado
export const uploadProtocoloBatch = async (req: Request, res: Response): Promise<any> => {
  const inicioProcessamento = Date.now();
  console.log('\n[BATCH] ========== INICIANDO UPLOAD DE PROTOCOLOS ==========');
  
  try {
    // Verificar se arquivos foram enviados
    const files = req.files as Express.Multer.File[];
    if (!files || files.length === 0) {
      return res.status(400).json(
        criarRespostaErro('Nenhum arquivo PDF foi enviado no batch', 'NO_FILES_PROVIDED')
      );
    }

    console.log(`[BATCH] Recebidos ${files.length} arquivo(s) PDF`);

    // Validação de limites
    if (files.length > BATCH_CONFIG.MAX_FILES_PER_BATCH) {
      return res.status(400).json(
        criarRespostaErro(
          `Máximo de ${BATCH_CONFIG.MAX_FILES_PER_BATCH} arquivos por batch. Recebidos: ${files.length}`,
          'BATCH_SIZE_EXCEEDED'
        )
      );
    }

    // Verificar se microserviço está online
    const microservicoOnline = await verificarMicroservico();
    if (!microservicoOnline) {
      console.log('[BATCH] Microserviço Python offline - interrompendo');
      return res.status(200).json({
        success: false,
        message: 'Microserviço Python está offline',
        data: {
          totalArquivos: files.length,
          processadosComSucesso: 0,
          comErros: files.length,
          warning: 'Todos os arquivos foram salvos mas não processados devido ao microserviço offline'
        }
      });
    }

    // Preparar estruturas de controle
    interface ProtocoloProcessado {
      arquivo: string;
      numeroProcesso?: string;
      elementoNominativo?: string;
      success: boolean;
      error?: string;
      processamentoId?: string;
      dadosTitulares?: any;
    }

    const protocolosProcessados: ProtocoloProcessado[] = [];
    const baseUrl = process.env.API_BASE_URL || `${req.protocol}://${req.get('host')}`;

    // FASE 1: Processar cada arquivo individualmente (extração de metadados)
    console.log('[BATCH] FASE 1: Extraindo metadados de todos os arquivos...');
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`[BATCH] Arquivo ${i + 1}/${files.length}: ${file.originalname}`);
      
      try {
        const arquivoTemporario = file.path;
        const diretorioProtocolos = path.dirname(arquivoTemporario);

        // Extrair metadados
        const metadados = await extrairMetadados(arquivoTemporario);

        // Extrair dados dos titulares
        let dadosTitulares: any = null;
        try {
          const dadosCompletos = await extrairDadosCompletos(arquivoTemporario);
          if (dadosCompletos && dadosCompletos.titulares) {
            dadosTitulares = dadosCompletos.titulares;
            console.log(`[BATCH] Extraídos dados de ${dadosTitulares.length} titular(es)`);
          }
        } catch (error) {
          console.log(`[BATCH] Erro ao extrair dados dos titulares: ${error}`);
        }

        let novoNomeArquivo: string;
        let caminhoFinal: string;

        if (metadados && metadados.process_number) {
          // 🔧 CORRIGIDO: Aceitar processo mesmo sem elemento nominativo (marcas figurativas)
          const elementoNominativo = metadados.elemento_nominativo || null;
          const elementoNominativoLimpo = elementoNominativo ? 
            elementoNominativo.replace(/[^\w\-_\.]/g, '_') : 
            'MARCA_FIGURATIVA';
          
          // Renomear arquivo com metadados
          novoNomeArquivo = `${metadados.process_number}-${elementoNominativoLimpo}.pdf`;
          caminhoFinal = path.join(diretorioProtocolos, novoNomeArquivo);
          fs.renameSync(arquivoTemporario, caminhoFinal);
          console.log(`[BATCH] Arquivo renomeado: ${novoNomeArquivo}`);
          if (elementoNominativo) {
            console.log(`[BATCH] Elemento nominativo original: "${elementoNominativo}"`);
            console.log(`[BATCH] Para nome de arquivo: "${elementoNominativoLimpo}"`);
          } else {
            console.log(`[BATCH] Marca figurativa - sem elemento nominativo`);
          }
        } else {
          console.log('[BATCH] Metadados não extraídos - mantendo nome original');
          novoNomeArquivo = file.filename;
          caminhoFinal = arquivoTemporario;
        }

        // Extrair imagem da marca
        console.log('[BATCH] Extraindo imagem da marca...');
        const resultadoExtracao = await extrairImagemMarca(caminhoFinal);

        protocolosProcessados.push({
          arquivo: file.originalname,
          numeroProcesso: metadados?.process_number,
          elementoNominativo: metadados?.elemento_nominativo,
          dadosTitulares,
          success: true
        });

        console.log(`[BATCH] Arquivo processado com sucesso`);

      } catch (error) {
        console.log(`[BATCH] Erro no arquivo ${file.originalname}: ${error}`);
        protocolosProcessados.push({
          arquivo: file.originalname,
          success: false,
          error: error instanceof Error ? error.message : 'Erro desconhecido'
        });
      }

      // Delay entre processamentos para não sobrecarregar o microserviço
      if (i < files.length - 1) {
        await new Promise(resolve => setTimeout(resolve, BATCH_CONFIG.DELAY_BETWEEN_EXTRACTIONS));
      }
    }

    // FASE 2: Adicionar protocolos válidos à fila
    console.log('[BATCH] FASE 2: Adicionando protocolos à fila de processamento...');
    
    // 🔧 CORRIGIDO: Aceitar protocolos apenas com numeroProcesso válido
    // elementoNominativo pode ser null para marcas figurativas
    const protocolosValidos = protocolosProcessados.filter(p => 
      p.success && p.numeroProcesso
    );

    for (const protocolo of protocolosValidos) {
      try {
        // 🔧 CORRIGIDO: Tratar elementoNominativo como opcional (marcas figurativas)
        const elementoNominativo = protocolo.elementoNominativo || null;
        const elementoNominativoLimpo = elementoNominativo ? 
          elementoNominativo.replace(/[^\w\-_\.]/g, '_') : 
          'MARCA_FIGURATIVA';
        
        const processamento = await prisma.processamentoProtocolo.create({
          data: {
            numeroProcesso: protocolo.numeroProcesso!,
            elementoNominativo: elementoNominativo, // Pode ser null para marcas figurativas
            nomeArquivoPdf: `${protocolo.numeroProcesso}-${elementoNominativoLimpo}.pdf`, // Nome do arquivo com fallback
            dadosTitulares: protocolo.dadosTitulares,
            status: 'PENDENTE'
          }
        });
        
        protocolo.processamentoId = processamento.id;
        console.log(`[BATCH] Protocolo ${protocolo.numeroProcesso} adicionado à fila (ID: ${processamento.id})`);
        
      } catch (filaError) {
        console.log(`[BATCH] Erro ao adicionar protocolo ${protocolo.numeroProcesso} à fila: ${filaError}`);
        protocolo.error = 'Erro ao adicionar à fila de processamento';
      }
    }

    // FASE 3: Processamento imediato da fila (OBJETIVO 3)
    console.log('[BATCH] FASE 3: Processando fila imediatamente...');
    
    let resultadoProcessamento: any = null;
    
    try {
      // 🆕 Usar novo processamento com agrupamento otimizado
      const { processarFilaComAgrupamento } = await import('../services/processamentoProtocoloService');
      resultadoProcessamento = await processarFilaComAgrupamento();
      console.log('[BATCH] Fila processada com agrupamento com sucesso!');
      console.log(`[BATCH] Grupos processados: ${resultadoProcessamento.gruposProcessados}/${resultadoProcessamento.totalGrupos}`);
    } catch (processingError) {
      console.log(`[BATCH] Erro no processamento da fila: ${processingError}`);
      // Não quebrar o fluxo - o cron job pegará depois
    }

    // FASE 4: Preparar resposta detalhada agrupada por cliente
    const tempoTotal = Date.now() - inicioProcessamento;
    const processadosComSucesso = protocolosProcessados.filter(p => p.success).length;
    const comErros = protocolosProcessados.filter(p => !p.success).length;

    console.log(`[BATCH] ========== CONCLUÍDO EM ${tempoTotal}ms ==========`);
    console.log(`[BATCH] Resumo: ${processadosComSucesso} sucessos, ${comErros} erros`);

    // 🆕 Criar resposta agrupada por cliente (OBJETIVO 4)
    const resumoGrupos: any = {};
    
    if (resultadoProcessamento && resultadoProcessamento.resultados) {
      console.log('\n📋 Criando resumo agrupado por cliente...');
      
      for (const resultado of resultadoProcessamento.resultados) {
        resumoGrupos[resultado.clienteKey] = {
          clienteNome: resultado.clienteNome,
          protocolosTotal: resultado.protocolosTotal,
          protocolosProcessados: resultado.protocolosProcessados,
          protocolosComErro: resultado.protocolosComErro,
          clienteCriado: resultado.clienteCriado,
          clienteAtualizado: resultado.clienteAtualizado,
          linkGerado: resultado.linkGerado,
          chatguruAtualizado: resultado.chatguruAtualizado,
          personsAtualizadas: resultado.personsAtualizadas,
          marcasEncontradas: resultado.marcasEncontradas,
          sucesso: resultado.sucesso,
          tempoProcessamento: `${resultado.tempoProcessamento}ms`,
          erros: resultado.erros
        };
      }
      
      console.log(`✅ Resumo criado para ${Object.keys(resumoGrupos).length} cliente(s)`);
    }

    // Resposta estruturada para o frontend
    return res.status(200).json({
      success: true,
      message: `Batch processado! ${processadosComSucesso} arquivo(s) com sucesso, ${comErros} com erro(s)`,
      tempoProcessamento: `${tempoTotal}ms`,
      data: {
        resumo: {
          totalArquivos: files.length,
          processadosComSucesso,
          comErros,
          tempoTotalMs: tempoTotal,
          // 🆕 Dados do agrupamento
          agrupamento: resultadoProcessamento ? {
            totalGrupos: resultadoProcessamento.totalGrupos,
            gruposProcessados: resultadoProcessamento.gruposProcessados,
            gruposComErro: resultadoProcessamento.gruposComErro,
            tempoProcessamentoMs: resultadoProcessamento.tempoTotal
          } : null
        },
        protocolos: protocolosProcessados.map(p => ({
          arquivo: Buffer.from(p.arquivo, 'binary').toString('utf8'),
          numeroProcesso: p.numeroProcesso || null,
          elementoNominativo: p.elementoNominativo || null,
          success: p.success,
          processamentoId: p.processamentoId || null,
          error: p.error || null,
          urls: p.numeroProcesso ? {
            downloadPdf: `${baseUrl}/api/protocolo/download/${p.numeroProcesso}`,
            logoImagem: `${baseUrl}/api/protocolo/logo/${p.numeroProcesso}`,
            status: p.processamentoId ? `${baseUrl}/api/fila-protocolo/status/${p.numeroProcesso}` : null
          } : null
        })),
        // 🆕 Resumo agrupado por cliente (OBJETIVO 4)
        grupos: resumoGrupos,
        acompanhamento: {
          filaAtivaUrl: `${baseUrl}/api/fila-protocolo/fila-ativa`,
          recentesUrl: `${baseUrl}/api/fila-protocolo/recentes`
        }
      }
    });

  } catch (error) {
    const tempoTotal = Date.now() - inicioProcessamento;
    console.error(`❌ ========== ERRO NO BATCH APÓS ${tempoTotal}ms ==========`, error);
    
    return res.status(500).json(
      criarRespostaErro(
        'Erro interno do servidor ao processar batch de protocolos',
        error instanceof Error ? error.message : 'Erro desconhecido'
      )
    );
  }
}; 