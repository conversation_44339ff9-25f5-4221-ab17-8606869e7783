FROM python:3.11-slim

# Instalar dependências do sistema para PyMuPDF
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Definir diretório de trabalho
WORKDIR /app

# Copiar requirements e instalar dependências Python
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copiar código fonte
COPY main.py .

# Criar diretório para imagens extraídas
RUN mkdir -p extracted_images

# Expor porta
EXPOSE 8000

# Comando para executar
CMD ["python", "main.py"] 