import fs from 'fs';
import path from 'path';
import FormData from 'form-data';
import axios from 'axios';

/**
 * Configurações do microserviço Python
 */
const PDF_PROCESSOR_URL = process.env.PDF_PROCESSOR_URL || 'http://localhost:8000';

interface LogoExtractionResult {
  success: boolean;
  message?: string;
  process_number?: string;
  logo?: {
    found: boolean;
    filename: string;
    path: string;
    width: number;
    height: number;
    format: string;
    size_bytes: number;
    page: number;
    image_data: string; // base64
  };
  error?: string;
}

/**
 * Extrai metadados do PDF (número do processo e elemento nominativo)
 */
export const extrairMetadados = async (caminhoArquivoPdf: string): Promise<any> => {
  try {
    console.log(`🔍 Extraindo metadados do PDF: ${caminhoArquivoPdf}`);

    // Verificar se arquivo existe
    if (!fs.existsSync(caminhoArquivoPdf)) {
      throw new Error(`Arquivo PDF não encontrado: ${caminhoArquivoPdf}`);
    }

    // Criar FormData para envio
    const formData = new FormData();
    const fileStream = fs.createReadStream(caminhoArquivoPdf);
    const fileName = path.basename(caminhoArquivoPdf);
    
    formData.append('file', fileStream, {
      filename: fileName,
      contentType: 'application/pdf'
    });

    // Fazer chamada para o microserviço
    const response = await axios.post(`${PDF_PROCESSOR_URL}/extract-metadata`, formData, {
      headers: formData.getHeaders(),
      timeout: 15000, // 15 segundos
      validateStatus: () => true
    });

    const result = response.data;

    if (response.status !== 200 || !result.success) {
      console.log(`⚠️ Erro ao extrair metadados: ${result.error}`);
      return null;
    }

    console.log(`✅ Metadados extraídos:`);
    console.log(`   📊 Processo: ${result.data.process_number}`);
    console.log(`   🏷️  Elemento: ${result.data.elemento_nominativo}`);
    console.log(`   📄 Sugestão: ${result.data.suggested_filename}`);

    return result.data;

  } catch (error) {
    console.error('❌ Erro ao extrair metadados:', error);
    return null;
  }
};

/**
 * Extrai dados completos do PDF (metadados + titulares)
 */
export const extrairDadosCompletos = async (caminhoArquivoPdf: string): Promise<any> => {
  try {
    console.log(`🔍 Extraindo dados completos do PDF: ${caminhoArquivoPdf}`);

    // Verificar se arquivo existe
    if (!fs.existsSync(caminhoArquivoPdf)) {
      throw new Error(`Arquivo PDF não encontrado: ${caminhoArquivoPdf}`);
    }

    // Criar FormData para envio
    const formData = new FormData();
    const fileStream = fs.createReadStream(caminhoArquivoPdf);
    const fileName = path.basename(caminhoArquivoPdf);
    
    formData.append('file', fileStream, {
      filename: fileName,
      contentType: 'application/pdf'
    });

    // Fazer chamada para o microserviço
    const response = await axios.post(`${PDF_PROCESSOR_URL}/extract-complete-data`, formData, {
      headers: formData.getHeaders(),
      timeout: 20000, // 20 segundos
      validateStatus: () => true
    });

    const result = response.data;

    if (response.status !== 200 || !result.success) {
      console.log(`⚠️ Erro ao extrair dados completos: ${result.error}`);
      return null;
    }

    console.log(`✅ Dados completos extraídos:`);
    console.log(`   📊 Processo: ${result.data.process_number}`);
    console.log(`   🏷️  Elemento: ${result.data.elemento_nominativo}`);
    console.log(`   👥 Titulares: ${result.data.total_titulares}`);
    console.log(`   📄 Sugestão: ${result.data.suggested_filename}`);

    // Log detalhado dos titulares
    if (result.data.titulares && result.data.titulares.length > 0) {
      console.log(`📋 Detalhes dos titulares:`);
      result.data.titulares.forEach((titular: any, index: number) => {
        console.log(`   ${index + 1}. ${titular.nome || 'Nome não informado'}`);
        console.log(`      📄 Doc: ${titular.numeroDocumento || 'N/A'}`);
        console.log(`      🌍 País: ${titular.pais || 'N/A'}`);
        console.log(`      📍 UF: ${titular.uf || 'N/A'}`);
      });
    }

    return result.data;

  } catch (error) {
    console.error('❌ Erro ao extrair dados completos:', error);
    return null;
  }
};

/**
 * Chama o microserviço Python para extrair logo do PDF
 */
export const extrairImagemMarca = async (caminhoArquivoPdf: string): Promise<LogoExtractionResult> => {
  try {
    console.log(`🐍 Chamando microserviço Python para: ${caminhoArquivoPdf}`);

    // Verificar se arquivo existe
    if (!fs.existsSync(caminhoArquivoPdf)) {
      throw new Error(`Arquivo PDF não encontrado: ${caminhoArquivoPdf}`);
    }

    // Criar FormData para envio
    const formData = new FormData();
    const fileStream = fs.createReadStream(caminhoArquivoPdf);
    const fileName = path.basename(caminhoArquivoPdf);
    
    formData.append('file', fileStream, {
      filename: fileName,
      contentType: 'application/pdf'
    });

    // Fazer chamada para o microserviço
    const response = await axios.post(`${PDF_PROCESSOR_URL}/extract-logo`, formData, {
      headers: formData.getHeaders(),
      timeout: 30000, // 30 segundos
      validateStatus: () => true // Não lançar erro para status HTTP
    });

    const result = response.data as LogoExtractionResult;

    if (response.status !== 200) {
      console.log(`⚠️ Microserviço retornou erro (${response.status}): ${result.error}`);
      return result;
    }

    if (result.success && result.logo) {
      console.log(`✅ Logo extraída com sucesso: ${result.logo.filename}`);
      console.log(`   📐 Dimensões: ${result.logo.width}x${result.logo.height}`);
      console.log(`   📄 Página: ${result.logo.page}`);
      console.log(`   💾 Tamanho: ${result.logo.size_bytes} bytes`);

      // Salvar imagem no diretório de imagens local
      if (result.logo.image_data) {
        await salvarImagemLocal(result.logo.image_data, result.process_number!);
      }
    } else {
      console.log(`❌ Falha na extração: ${result.error}`);
    }

    return result;

  } catch (error) {
    console.error('❌ Erro ao chamar microserviço Python:', error);
    
    return {
      success: false,
      error: `Erro na comunicação com microserviço: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    };
  }
};

/**
 * Salva a imagem extraída no diretório local de imagens
 */
const salvarImagemLocal = async (imageBase64: string, numeroProcesso: string): Promise<string> => {
  try {
    // Criar diretório se não existir
    const diretorioImagens = path.join(process.cwd(), 'uploads', 'imagens-marca');
    if (!fs.existsSync(diretorioImagens)) {
      fs.mkdirSync(diretorioImagens, { recursive: true });
    }

    // Decodificar base64 e salvar
    const imageBuffer = Buffer.from(imageBase64, 'base64');
    const nomeArquivo = `${numeroProcesso}.jpg`;
    const caminhoCompleto = path.join(diretorioImagens, nomeArquivo);

    fs.writeFileSync(caminhoCompleto, imageBuffer);

    console.log(`💾 Imagem salva localmente: ${caminhoCompleto}`);
    return caminhoCompleto;

  } catch (error) {
    console.error('❌ Erro ao salvar imagem local:', error);
    throw error;
  }
};

/**
 * Verifica se o microserviço Python está funcionando
 */
export const verificarMicroservico = async (): Promise<boolean> => {
  try {
    const response = await axios.get(`${PDF_PROCESSOR_URL}/health`, {
      timeout: 5000,
      validateStatus: () => true // Não lançar erro para status HTTP
    });

    const result = response.data;
    
    if (response.status === 200 && result.status === 'healthy') {
      console.log('✅ Microserviço Python está online');
      return true;
    } else {
      console.log('⚠️ Microserviço Python não está saudável');
      return false;
    }

  } catch (error) {
    console.log('❌ Microserviço Python não está acessível:', error);
    return false;
  }
}; 