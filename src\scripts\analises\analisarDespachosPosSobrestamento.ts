import { PrismaClient, <PERSON>o, Despacho, RPI } from "@prisma/client";
import { writeFileSync } from "fs";
import cliProgress from "cli-progress";

const prisma = new PrismaClient({
  log: ["warn", "error"],
});

// Nomes dos despachos de sobrestamento a serem analisados
const DESPACHOS_SOBRESTAMENTO_NOMES = [
  "Sobrestamento do exame de mérito",
  "Sobrestamento do exame de mérito (em petição)",
  "Sobrestamento da instrução técnica",
  // Adicionar outros nomes ou códigos se necessário
];

// Interface para armazenar os resultados da contagem
interface ContagemDespachos {
  [nomeDespacho: string]: {
    nome: string;
    contagem: number;
  };
}

type ProcessoComDespachosOrdenados = Processo & {
  despachos: (Despacho & { rpi: RPI | null })[]; // RPI pode ser null em alguns casos de dados mais antigos
};

async function analisarDespachosPosSobrestamento() {
  console.log("Iniciando análise de despachos após sobrestamento...");
  console.log("Despachos de sobrestamento monitorados:", DESPACHOS_SOBRESTAMENTO_NOMES.join(', '));

  const contagemDespachosSeguintes: ContagemDespachos = {};
  let processosComSobrestamentoConsiderados = 0;
  let totalOcorrenciasSobrestamento = 0;
  let processedCount = 0;
  const TAMANHO_LOTE = 1000; // Ajuste conforme necessário

  const whereClause = {
    despachos: {
      some: {
        nome: {
          in: DESPACHOS_SOBRESTAMENTO_NOMES,
        },
      },
    },
  };

  console.log("Contando o total de processos com despachos de sobrestamento...");
  const totalProcessosParaAnalisar = await prisma.processo.count({ where: whereClause });

  if (totalProcessosParaAnalisar === 0) {
    console.log("Nenhum processo encontrado com os despachos de sobrestamento especificados.");
    return;
  }
  console.log(`Total de processos a serem analisados: ${totalProcessosParaAnalisar}`);

  const barraProgresso = new cliProgress.SingleBar(
    {
      format:
        "Analisando processos |{bar}| {percentage}% | {value}/{total} Processos",
      barCompleteChar: "\u2588",
      barIncompleteChar: "\u2591",
      hideCursor: true,
    },
    cliProgress.Presets.shades_classic
  );

  barraProgresso.start(totalProcessosParaAnalisar, 0);

  while (processedCount < totalProcessosParaAnalisar) {
    const processosLote = await prisma.processo.findMany({
      where: whereClause,
      include: {
        despachos: {
          include: {
            rpi: true,
          },
          orderBy: {
            rpi: {
              dataPublicacao: "asc",
            },
          },
        },
      },
      skip: processedCount,
      take: TAMANHO_LOTE,
    });

    if (processosLote.length === 0) {
      // Segurança caso a contagem mude durante o processo ou erro na lógica de paginação
      console.warn("Nenhum processo encontrado no lote atual, encerrando a análise de lotes.");
      break;
    }

    for (const processo of processosLote as ProcessoComDespachosOrdenados[]) {
      let encontrouSobrestamentoNoProcesso = false;
      for (let i = 0; i < processo.despachos.length; i++) {
        const despachoAtual = processo.despachos[i];

        if (despachoAtual.nome && DESPACHOS_SOBRESTAMENTO_NOMES.includes(despachoAtual.nome)) {
          totalOcorrenciasSobrestamento++;
          if (!encontrouSobrestamentoNoProcesso) {
              processosComSobrestamentoConsiderados++;
              encontrouSobrestamentoNoProcesso = true;
          }

          if (i + 1 < processo.despachos.length) {
            const despachoSeguinte = processo.despachos[i + 1];
            const nomeDespachoSeguinte = despachoSeguinte.nome;

            if (nomeDespachoSeguinte) {
              if (!contagemDespachosSeguintes[nomeDespachoSeguinte]) {
                contagemDespachosSeguintes[nomeDespachoSeguinte] = {
                  nome: nomeDespachoSeguinte,
                  contagem: 0,
                };
              }
              contagemDespachosSeguintes[nomeDespachoSeguinte].contagem++;
            } else {
              const chaveSemNome = "DESPACHO_SEGUINTE_SEM_NOME";
              if (!contagemDespachosSeguintes[chaveSemNome]) {
                  contagemDespachosSeguintes[chaveSemNome] = {
                      nome: chaveSemNome,
                      contagem: 0
                  };
              }
              contagemDespachosSeguintes[chaveSemNome].contagem++;
            }
          } else {
            const chaveFimFluxo = "FIM_DO_FLUXO_APOS_SOBRESTAMENTO";
              if (!contagemDespachosSeguintes[chaveFimFluxo]) {
                  contagemDespachosSeguintes[chaveFimFluxo] = {
                      nome: chaveFimFluxo,
                      contagem: 0
                  };
              }
              contagemDespachosSeguintes[chaveFimFluxo].contagem++;
          }
        }
      }
    }
    processedCount += processosLote.length;
    barraProgresso.update(processedCount);
  }

  barraProgresso.stop();

  console.log("\n--- Resultados da Análise ---");
  console.log(`Total de processos que tiveram ao menos um sobrestamento considerado: ${processosComSobrestamentoConsiderados}`);
  console.log(`Total de ocorrências de despachos de sobrestamento encontradas: ${totalOcorrenciasSobrestamento}`);

  const resultadosOrdenados = Object.values(contagemDespachosSeguintes).sort(
    (a, b) => b.contagem - a.contagem
  );

  console.log("\nDespachos mais comuns após um sobrestamento:");
  if (resultadosOrdenados.length === 0) {
    console.log("Nenhum despacho seguinte encontrado.");
  } else {
    resultadosOrdenados.forEach((resultado) => {
      console.log(`- ${resultado.nome}: ${resultado.contagem} ocorrências`);
    });

    const dataHora = new Date().toISOString().replace(/[:.]/g, "-");
    const nomeArquivo = `analise-pos-sobrestamento-${dataHora}.json`;
    try {
        writeFileSync(nomeArquivo, JSON.stringify({
            totalProcessosComSobrestamentoConsiderados: processosComSobrestamentoConsiderados,
            totalOcorrenciasSobrestamento: totalOcorrenciasSobrestamento,
            despachosMonitorados: DESPACHOS_SOBRESTAMENTO_NOMES,
            frequenciaDespachosSeguintes: resultadosOrdenados
        }, null, 2));
        console.log(`\nResultados detalhados salvos em: ${nomeArquivo}`);
    } catch (error) {
        console.error("\nErro ao salvar arquivo JSON:", error);
    }
  }
}

async function main() {
  try {
    await analisarDespachosPosSobrestamento();
  } catch (error) {
    console.error("\nErro na execução principal:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log("\nConexão com o banco de dados fechada.");
  }
}

if (require.main === module) {
  main();
} 