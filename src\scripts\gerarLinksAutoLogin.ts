import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';
import dotenv from 'dotenv';
const prisma = new PrismaClient();
dotenv.config();
// Configurações de criptografia (seguindo as instruções)
const ALGORITHM = 'aes-256-cbc';
const JWT_SECRET = process.env.JWT_SECRET!;

const KEY = crypto.createHash('sha256').update(JWT_SECRET).digest();
const BASE_URL = 'https://cliente.registre.se';

// Função para gerar código curto de 6 caracteres
function generateShortCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Função para verificar se código já existe
async function isShortCodeUnique(shortCode: string): Promise<boolean> {
  const existing = await prisma.shortUrl.findUnique({
    where: { shortCode }
  });
  return !existing;
}

// Função para gerar código único
async function generateUniqueShortCode(): Promise<string> {
  let shortCode: string;
  let attempts = 0;
  const maxAttempts = 10;
  
  do {
    shortCode = generateShortCode();
    attempts++;
    
    if (attempts >= maxAttempts) {
      throw new Error('Não foi possível gerar um código único após várias tentativas');
    }
  } while (!(await isShortCodeUnique(shortCode)));
  
  return shortCode;
}

// Função para criptografar credenciais
function encryptCredentials(identificador: string, numeroDocumento: string): string {
  try {
    // Pegar últimos 3 dígitos do documento como senha (somente números)
    const apenasNumeros = numeroDocumento.replace(/\D/g, ''); // Remove tudo que não é dígito
    const senha = apenasNumeros.slice(-3);
    
    // Criar payload das credenciais
    const payload = {
      identificador,
      senha,
      timestamp: Date.now()
    };
    
    console.log('🔍 Backend - Criptografando:', { identificador, senha: senha.substring(0, 1) + '**' });
    
    // Criptografar usando AES-256-CBC
    const iv = crypto.randomBytes(16); // 16 bytes = 128 bits
    const cipher = crypto.createCipheriv(ALGORITHM, KEY, iv);
    
    console.log('🔍 Backend - IV gerado (buffer):', iv);
    console.log('🔍 Backend - IV length (bytes):', iv.length);
    
    // CRÍTICO: IV deve ser convertido para HEX (não base64)
    const ivHex = iv.toString('hex');
    console.log('🔍 Backend - IV em HEX:', ivHex);
    console.log('🔍 Backend - IV hex length:', ivHex.length); // Deve ser 32
    
    if (ivHex.length !== 32) {
      throw new Error(`IV hex tem tamanho inválido: ${ivHex.length}, esperado: 32`);
    }
    
    // CRÍTICO: Dados criptografados também em HEX (não base64)
    let encrypted = cipher.update(JSON.stringify(payload), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    console.log('🔍 Backend - Dados criptografados (hex):', encrypted.substring(0, 30) + '...');
    console.log('🔍 Backend - Encrypted length:', encrypted.length);
    
    // IMPORTANTE: Concatenar IV(hex) + ':' + dados(hex)
    const combined = ivHex + ':' + encrypted;
    console.log('🔍 Backend - Combined (IV:encrypted):', combined.substring(0, 50) + '...');
    console.log('🔍 Backend - Combined length:', combined.length);
    
    // Converter combined string para base64url
    const base64 = Buffer.from(combined, 'utf8').toString('base64');
    const result = base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    
    console.log('✅ Backend - Token final gerado:', result.substring(0, 20) + '...');
    console.log('🔍 Backend - Token length:', result.length);
    
    // TESTE: Verificar se pode decodificar corretamente
    const testDecode = Buffer.from(base64, 'base64').toString('utf8');
    const [testIV, testEncrypted] = testDecode.split(':');
    console.log('🧪 Backend - Test decode IV length:', testIV?.length); // Deve ser 32
    console.log('🧪 Backend - Test decode IV:', testIV?.substring(0, 10) + '...');
    
    if (testIV?.length !== 32) {
      throw new Error(`ERRO: IV decodificado tem tamanho ${testIV?.length}, esperado: 32`);
    }
    
    return result;
    
  } catch (error) {
    console.error('❌ Backend - Erro na criptografia:', error);
    throw new Error('Falha na criptografia das credenciais: ' + (error as Error).message);
  }
}

async function gerarLinksAutoLogin() {
  console.log('🚀 Iniciando geração de links de auto-login...\n');
  
  let stats = {
    clientesAnalisados: 0,
    clientesValidos: 0,
    linksGerados: 0,
    linksAtualizados: 0,
    erros: 0,
    invalidos: {
      semProcessos: 0,
      semIdentificador: 0,
      semNumeroDocumento: 0,
      semContatos: 0,
      semTelefone: 0
    }
  };

  try {
    // Buscar todos os clientes com suas relações
    console.log('📊 Buscando clientes...');
    const clientes = await prisma.cliente.findMany({
      include: {
        processos: true,
        contatos: true,
        urlsCurtas: true
      }
    });

    console.log(`✅ Encontrados ${clientes.length} clientes para análise\n`);
    stats.clientesAnalisados = clientes.length;

    for (const cliente of clientes) {
      console.log(`🔍 Analisando cliente ID: ${cliente.id} - ${cliente.nome || 'Sem nome'}`);
      
      // Validação 1: Cliente deve ter processos
      if (!cliente.processos || cliente.processos.length === 0) {
        console.log(`   ❌ Sem processos vinculados`);
        stats.invalidos.semProcessos++;
        continue;
      }

      // Validação 2: Cliente deve ter identificador
      if (!cliente.identificador) {
        console.log(`   ❌ Sem identificador`);
        stats.invalidos.semIdentificador++;
        continue;
      }

      // Validação 3: Cliente deve ter numeroDocumento
      if (!cliente.numeroDocumento) {
        console.log(`   ❌ Sem número de documento`);
        stats.invalidos.semNumeroDocumento++;
        continue;
      }

      // Validação 4: Cliente deve ter contatos
      if (!cliente.contatos || cliente.contatos.length === 0) {
        console.log(`   ❌ Sem contatos vinculados`);
        stats.invalidos.semContatos++;
        continue;
      }

      // Validação 5: Pelo menos um contato deve ter telefone
      const temTelefone = cliente.contatos.some(contato => 
        contato.telefone || contato.telefoneSegundario
      );
      
      if (!temTelefone) {
        console.log(`   ❌ Nenhum contato possui telefone`);
        stats.invalidos.semTelefone++;
        continue;
      }

      console.log(`   ✅ Cliente válido (${cliente.processos.length} processo(s), ${cliente.contatos.length} contato(s))`);
      stats.clientesValidos++;

      try {
        // Gerar token criptografado
        const longToken = encryptCredentials(cliente.identificador, cliente.numeroDocumento);
        
        // Gerar código curto único
        const shortCode = await generateUniqueShortCode();
        
        // URL final
        const autoLoginUrl = `${BASE_URL}/${shortCode}`;

        // Verificar se já existe link para este cliente
        const existingShortUrl = await prisma.shortUrl.findFirst({
          where: { clienteId: cliente.id }
        });

        if (existingShortUrl) {
          // Atualizar link existente
          await prisma.shortUrl.update({
            where: { id: existingShortUrl.id },
            data: {
              shortCode,
              longToken,
              usageCount: 0,
              isActive: true
            }
          });
          console.log(`   🔄 Link atualizado: ${autoLoginUrl}`);
          stats.linksAtualizados++;
        } else {
          // Criar novo link
          await prisma.shortUrl.create({
            data: {
              shortCode,
              longToken,
              clienteId: cliente.id,
              expiresAt: null, // Links permanentes
              usageCount: 0,
              isActive: true
            }
          });
          console.log(`   🆕 Link criado: ${autoLoginUrl}`);
          stats.linksGerados++;
        }

        // Atualizar campo autoLoginUrl do cliente
        await prisma.cliente.update({
          where: { id: cliente.id },
          data: { autoLoginUrl }
        });

        console.log(`   ✅ Cliente atualizado com sucesso\n`);

      } catch (error) {
        console.error(`   ❌ Erro ao processar cliente ${cliente.id}:`, error);
        stats.erros++;
      }
    }

  } catch (error) {
    console.error('❌ Erro geral na execução:', error);
    stats.erros++;
  } finally {
    await prisma.$disconnect();
  }

  // Relatório final
  console.log('\n' + '='.repeat(60));
  console.log('📊 RELATÓRIO FINAL - GERAÇÃO DE LINKS AUTO-LOGIN');
  console.log('='.repeat(60));
  console.log(`📈 Clientes analisados: ${stats.clientesAnalisados}`);
  console.log(`✅ Clientes válidos: ${stats.clientesValidos}`);
  console.log(`🆕 Links criados: ${stats.linksGerados}`);
  console.log(`🔄 Links atualizados: ${stats.linksAtualizados}`);
  console.log(`🔗 Total de links processados: ${stats.linksGerados + stats.linksAtualizados}`);
  console.log(`❌ Erros: ${stats.erros}`);
  
  console.log('\n📋 MOTIVOS DE INVALIDAÇÃO:');
  console.log(`   • Sem processos: ${stats.invalidos.semProcessos}`);
  console.log(`   • Sem identificador: ${stats.invalidos.semIdentificador}`);
  console.log(`   • Sem número documento: ${stats.invalidos.semNumeroDocumento}`);
  console.log(`   • Sem contatos: ${stats.invalidos.semContatos}`);
  console.log(`   • Sem telefone: ${stats.invalidos.semTelefone}`);
  
  const totalInvalidos = Object.values(stats.invalidos).reduce((a, b) => a + b, 0);
  console.log(`   📊 Total inválidos: ${totalInvalidos}`);
  
  console.log('\n🎯 Taxa de sucesso: ' + 
    `${((stats.clientesValidos / stats.clientesAnalisados) * 100).toFixed(1)}%`);
  
  if (stats.erros === 0 && stats.clientesValidos > 0) {
    console.log('\n🎉 Operação concluída com sucesso!');
  } else if (stats.erros > 0) {
    console.log('\n⚠️  Operação concluída com alguns erros. Verifique os logs acima.');
  } else {
    console.log('\n⚠️  Nenhum cliente válido encontrado para processamento.');
  }
  
  console.log('='.repeat(60));
}

// Executar o script
if (require.main === module) {
  gerarLinksAutoLogin()
    .catch(console.error)
    .finally(() => process.exit());
}

export { gerarLinksAutoLogin }; 