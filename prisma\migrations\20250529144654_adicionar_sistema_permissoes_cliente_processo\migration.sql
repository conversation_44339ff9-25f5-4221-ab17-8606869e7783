-- CreateEnum
CREATE TYPE "TipoAcesso" AS ENUM ('PROPRIETAR<PERSON>', 'VISUALIZACA<PERSON>', 'CORING<PERSON>', 'PARC<PERSON><PERSON>', 'AUDITORI<PERSON>', 'TEMPORARIO');

-- CreateTable
CREATE TABLE "ClienteProcessoAcesso" (
    "id" TEXT NOT NULL,
    "clienteId" INTEGER NOT NULL,
    "processoId" TEXT NOT NULL,
    "tipoAcesso" "TipoAcesso" NOT NULL DEFAULT 'VISUALIZACAO',
    "concedidoPor" TEXT,
    "dataConcessao" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dataExpiracao" TIMESTAMP(3),
    "ativo" BOOLEAN NOT NULL DEFAULT true,
    "observacoes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ClienteProcessoAcesso_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ClienteProcessoAcesso_clienteId_ativo_idx" ON "ClienteProcessoAcesso"("clienteId", "ativo");

-- CreateIndex
CREATE INDEX "ClienteProcessoAcesso_processoId_ativo_idx" ON "ClienteProcessoAcesso"("processoId", "ativo");

-- CreateIndex
CREATE INDEX "ClienteProcessoAcesso_tipoAcesso_idx" ON "ClienteProcessoAcesso"("tipoAcesso");

-- CreateIndex
CREATE INDEX "ClienteProcessoAcesso_dataExpiracao_idx" ON "ClienteProcessoAcesso"("dataExpiracao");

-- CreateIndex
CREATE UNIQUE INDEX "ClienteProcessoAcesso_clienteId_processoId_key" ON "ClienteProcessoAcesso"("clienteId", "processoId");

-- AddForeignKey
ALTER TABLE "ClienteProcessoAcesso" ADD CONSTRAINT "ClienteProcessoAcesso_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClienteProcessoAcesso" ADD CONSTRAINT "ClienteProcessoAcesso_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE CASCADE ON UPDATE CASCADE;
