-- CreateEnum
CREATE TYPE "StatusComunicadoElegivel" AS ENUM ('PENDENTE', 'PROCESSANDO', 'ENVIADO', 'FALHA', 'IGNORADO');

-- CreateTable
CREATE TABLE "DespachoElegivel" (
    "id" TEXT NOT NULL,
    "processoId" TEXT NOT NULL,
    "clienteId" INTEGER NOT NULL,
    "despachoId" TEXT NOT NULL,
    "statusComunicado" "StatusComunicadoElegivel" NOT NULL DEFAULT 'PENDENTE',
    "dataRegistro" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dataProcessamento" TIMESTAMP(3),

    CONSTRAINT "DespachoElegivel_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "DespachoElegivel_statusComunicado_idx" ON "DespachoElegivel"("statusComunicado");

-- CreateIndex
CREATE INDEX "DespachoElegivel_dataRegistro_idx" ON "DespachoElegivel"("dataRegistro");

-- CreateIndex
CREATE INDEX "DespachoElegivel_clienteId_idx" ON "DespachoElegivel"("clienteId");

-- CreateIndex
CREATE UNIQUE INDEX "DespachoElegivel_processoId_despachoId_key" ON "DespachoElegivel"("processoId", "despachoId");

-- AddForeignKey
ALTER TABLE "DespachoElegivel" ADD CONSTRAINT "DespachoElegivel_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DespachoElegivel" ADD CONSTRAINT "DespachoElegivel_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DespachoElegivel" ADD CONSTRAINT "DespachoElegivel_despachoId_fkey" FOREIGN KEY ("despachoId") REFERENCES "Despacho"("id") ON DELETE CASCADE ON UPDATE CASCADE;
