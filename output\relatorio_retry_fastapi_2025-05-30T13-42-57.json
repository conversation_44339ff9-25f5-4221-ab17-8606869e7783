{"totalProcessosMonitorados": 2751, "logosJaExistentes": 1233, "processosSemLogo": ["822115689", "827894619", "840317581", "840341873", "903063239", "903063280", "903063352", "903063360", "903063417", "903063441", "903063476", "903063549", "903063565", "903063611", "903063662", "903727331", "903866943", "904213544", "904905594", "904905640", "912288647", "913011240", "913011371", "913878111", "916350100", "917065867", "921671180", "924722550", "924856386", "926440047", "926498770", "926909584", "926916076", "926943820", "927067870", "927206293", "927308797", "927916339", "928268942", "928404366", "928583686", "928618340", "928618510", "928618609", "928618749", "928618897", "928696731", "928697495", "928784096", "929105362", "929118120", "929386442", "929386736", "929386884", "929386949", "929885724", "929885775", "930109627", "930109864", "930110080", "930110200", "930164520", "930164652", "930164792", "930267680", "930465385", "930465571", "930465628", "930465741", "930465849", "930796608", "930817788", "930829450", "930829719", "930829891", "930830075", "930885350", "930885481", "930924754", "930924843", "930925017", "930955544", "930955684", "931002257", "931002346", "931057221", "931220980", "931385733", "931583420", "932027490", "932248446", "932334504", "932616372", "932616534", "932616666", "932753680", "933237391", "933237430", "933237529", "933267207", "933267355", "933531494", "934141703", "934284180", "934286000", "934297509", "934317925", "934328080", "934362580", "934362661", "934362726", "934433062", "934433259", "934433402", "934482144", "934482152", "934482160", "934551251", "934571651", "934571660", "934650713", "934654638", "934654662", "934666806", "934690693", "934718148", "934734151", "934734232", "934734372", "934775699", "934775737", "934789118", "934815682", "934835780", "934841691", "934841799", "934841900", "934842043", "934842108", "934842191", "934860521", "934860866", "934860939", "934877564", "934931275", "934942790", "934942854", "934942900", "934960054", "934960119", "934987513", "934996474", "935024263", "935044906", "935057579", "935083421", "935178457", "935178465", "935230726", "935254820", "935258280", "935274243", "935288473", "935310274", "935338772", "935338918", "935339221", "935371745", "935371877", "935384022", "935384111", "935384308", "935384383", "935436367", "935436456", "935447385", "935464794", "935464824", "935472614", "935472983", "935485716", "935485732", "935489053", "935489150", "935490124", "935490256", "935511911", "935512039", "935532641", "935536892", "935545506", "935545581", "935546138", "935550666", "935551000", "935551620", "935558926", "935575243", "935576819", "935583980", "935584099", "935584218", "935584579", "935587446", "935587527", "935593632", "935594280", "935594345", "935595651", "935607137", "935607820", "935633286", "935633367", "935643060", "935653287", "935653325", "935654054", "935657959", "935661352", "935667342", "935667792", "935680101", "935681493", "935691405", "935691499", "935705929", "935705945", "935705961", "935706011", "935706119", "935714359", "935720022", "935720146", "935725547", "935739548", "935743790", "935750177", "935753087", "935753168", "935754440", "935765794", "935765905", "935765948", "935774556", "935775200", "935775269", "935783431", "935783490", "935793950", "935819312", "935819339", "935821767", "935826068", "935826211", "935826408", "935826980", "935827927", "935828311", "935832955", "935833005", "935836411", "935849475", "935850597", "935850678", "935850740", "935856420", "935857567", "935862102", "935862250", "935862773", "935863451", "935876634", "935876685", "935899979", "935900055", "935905618", "935905715", "935918221", "935920765", "935933360", "935933506", "935943013", "935943544", "935960112", "935967516", "935967923", "935968164", "935968377", "935968768", "935985174", "935985204", "935985220", "935985263", "935985638", "935986405", "935986510", "935988602", "935989455", "935989501", "936004142", "936004207", "936004525", "936004550", "936004630", "936006307", "936006366", "936006447", "936008660", "936012498", "936013192", "936015721", "936022884", "936022906", "936022949", "936025778", "936025859", "936027045", "936027177", "936045639", "936051272", "936051574", "936062959", "936063190", "936064250", "936064340", "936071540", "936071613", "936078766", "936078782", "936078804", "936078812", "936078855", "936078995", "936079150", "936081635", "936097418", "936097531", "936099550", "936106689", "936108770", "936108940", "936109998", "936118830", "936122722", "936122803", "936122854", "936123230", "936123303", "936123443", "936123478", "936129816", "936130091", "936131233", "936131624", "936143355", "936145765", "936158840", "936159090", "936160780", "936160853", "936162317", "936162384", "936162473", "936162902", "936164654", "936172886", "936172940", "936173009", "936178302", "936178450", "936178558", "936184310", "936185112", "936185805", "936186070", "936201258", "936213671", "936213833", "936218746", "936220465", "936242850", "936242868", "936242876", "936250500", "936251298", "936251417", "936252189", "936252537", "936254920", "936265990", "936273356", "936273461", "936279591", "936280352", "936280395", "936280794", "936290099", "936297964", "936301546", "936301570", "936302968", "936302984", "936303417", "936303450", "936306300", "936306335", "936306700", "936321806", "936339438", "936341351", "936351012", "936352361", "936352434", "936352973", "936355310", "936355344", "936358343", "936368330", "936368420", "936368535", "936368632", "936379545", "936379570", "936385960", "936386290", "936396059", "936396067", "936398035", "936398183", "936398256", "936402113", "936402148", "936402180", "936402598", "936407239", "936423064", "936441208", "936441852", "936441860", "936441992", "936442018", "936442050", "936442107", "936442115", "936443006", "936454741", "936455080", "936455136", "936455195", "936459859", "936459972", "936460016", "936461845", "936477407", "936477431", "936484560", "936486740", "936501898", "936503343", "936504161", "936505222", "936505281", "936510846", "936510862", "936518618", "936532971", "936533021", "936540729", "936540745", "936564997", "936565020", "936565055", "936568160", "936581816", "936581824", "936581832", "936583355", "936583525", "936583711", "936595205", "936595310", "936609869", "936611995", "936612584", "936625198", "936632992", "936637480", "936637552", "936647876", "936647930", "936648813", "936648899", "936649712", "936653329", "936657197", "936662212", "936662590", "936663979", "936664355", "936671653", "936671718", "936676060", "936676566", "936676671", "936676744", "936683619", "936683716", "936687576", "936695331", "936695340", "936702273", "936702389", "936717653", "936717963", "936718137", "936723734", "936739142", "936740175", "936740329", "936740906", "936746262", "936751339", "936758635", "936760443", "936765534", "936769823", "936769840", "936769904", "936769912", "936769955", "936769963", "936779896", "936781874", "936782374", "936798033", "936798122", "936807210", "936809728", "936810351", "936811013", "936812117", "936812141", "936822937", "936823402", "936824220", "936829770", "936832061", "936832258", "936845910", "936849410", "936849452", "936849690", "936859571", "936868600", "936868783", "936868937", "936869577", "936869640", "936875690", "936882280", "936887850", "936894601", "936898976", "936898984", "936898992", "936899000", "936899042", "936910224", "936910429", "936924640", "936932937", "936934298", "936934360", "936934689", "936937203", "936950935", "936950943", "936950978", "936952598", "936952644", "936952687", "936952717", "936952768", "936956992", "936960442", "936963824", "936964626", "936973455", "936988908", "937012432", "937015601", "937016063", "937026581", "937032409", "937046914", "937046930", "937047465", "937047490", "937047538", "937048453", "937048755", "937055336", "937070009", "937070750", "937070793", "937072079", "937072540", "937096172", "937096180", "937096210", "937096318", "937096334", "937096369", "937105732", "937105996", "937113840", "937120260", "937121665", "937122955", "937123242", "937123633", "937130818", "937130826", "937130834", "937130850", "937133159", "937133248", "937133272", "937149780", "937149802", "937149837", "937149845", "937151513", "937162795", "937162884", "937163619", "937164810", "937165867", "937165930", "937170518", "937170526", "937176036", "937176230", "937176303", "937176613", "937186961", "937186970", "937186988", "937187011", "937187046", "937187070", "937187097", "937187119", "937187127", "937187844", "937187860", "937187917", "937187941", "937191841", "937191930", "937192023", "937192244", "937193860", "937193917", "937195405", "937206806", "937206890", "937206903", "937210242", "937215929", "937219541", "937219630", "937222070", "937229237", "937229288", "937229300", "937229393", "937230359", "937230375", "937230529", "937230766", "937230847", "937231100", "937236039", "937236330", "937236900", "937237230", "937237620", "937253383", "937257443", "937257826", "937258709", "937260509", "937261270", "937261343", "937265829", "937265853", "937266108", "937266280", "937266337", "937266590", "937267309", "937267376", "937268313", "937269409", "937272531", "937272604", "937272680", "937272876", "937273678", "937273805", "937274720", "937275328", "937276090", "937277037", "937277126", "937277843", "937277894", "937277975", "937282529", "937285501", "937286532", "937286591", "937286664", "937289310", "937289337", "937300152", "937300160", "937300195", "937300217", "937301078", "937301256", "937301337", "937315494", "937315508", "937315532", "937315591", "937315621", "937315680", "937315745", "937323012", "937331970", "937331988", "937331996", "937332003", "937332011", "937339717", "937340448", "937340820", "937341371", "937341690", "937351032", "937351040", "937351067", "937351075", "937351121", "937351148", "937351229", "937351261", "937358584", "937358657", "937369250", "937369276", "937369306", "937369349", "937369390", "937369403", "937369489", "937370207", "937375535", "937375608", "937375659", "937376035", "937376132", "937376582", "937376884", "937377740", "937379824", "937391433", "937391450", "937398381", "937403504", "937403598", "937404179", "937410950", "937410969", "937410993", "937411027", "937411175", "937414077", "937428647", "937428663", "937428671", "937428701", "937428736", "937428744", "937428779", "937428817", "937428868", "937429465", "937429511", "937435082", "937435236", "937435449", "937436186", "937436224", "937440507", "937447773", "937447781", "937447811", "937447820", "937447846", "937447854", "937459089", "937460184", "937460265", "937462098", "937462667", "937467022", "937467049", "937467057", "937467081", "937468169", "937473200", "937475297", "937475424", "937475475", "937475521", "937477230", "937477559", "937502898", "937548642", "937548715", "937548812", "937548863", "937548910", "937548979", "937560979", "937561452", "937562130", "937562521", "937562815", "937563145", "937563420", "937566179", "937577260", "937577278", "937577294", "937577324", "937577332", "937577340", "937577359", "937577375", "937585890", "937586021", "937586366", "937586480", "937587923", "937587990", "937588024", "937588148", "937595527", "937595616", "937595659", "937595748", "937596795", "937597325", "937601810", "937602108", "937604950", "937604992", "937605310", "937606782", "937606839", "937606863", "937606910", "937610771", "937611514", "937611522", "937612910", "937612979", "937613371", "937613584", "937614130", "937627704", "937628263", "937633372", "937634743", "937636550", "937636584", "937641170", "937644811", "937661317", "937661414", "937662089", "937664375", "937669466", "937685224", "937689823", "937690139", "937693790", "937710733", "937710784", "937720526", "937724602", "937724874", "937725536", "937729213", "937729221", "937729230", "937729248", "937729442", "937745227", "937747238", "937751782", "937751847", "937752576", "937754080", "937761362", "937768790", "937772763", "937777960", "937778133", "937778141", "937778508", "937778532", "937787264", "937787310", "937789992", "937790117", "937790850", "937791423", "937794171", "937799564", "937800066", "937809918", "937818429", "937818500", "937819387", "937819468", "937819549", "937820415", "937821802", "937822205", "937822841", "937823538", "937826472", "937826570", "937826596", "937836591", "937836885", "937837792", "937839469", "937844322", "937853780", "937864978", "937865125", "937865141", "937865389", "937866636", "937866679", "937866725", "937866776", "937880957", "937881414", "937889504", "937889954", "937890430", "937891339", "937901938", "937901970", "937902004", "937903167", "937903221", "937904201", "937908720", "937909637", "937912620", "937912913", "937924415", "937925349", "937925926", "937926655", "937926760", "937937754", "937947024", "937947032", "937947040", "937947067", "937947075", "937947164", "937947172", "937947180", "937947199", "937947210", "937947245", "937947350", "937947385", "937947407", "937947415", "937947458", "937947466", "937947490", "937947539", "937947563", "937947580", "937947598", "937947601", "937947644", "937949124", "937949132", "937949159", "937949175", "937949183", "937954829", "937954900", "937955256", "937955647", "937956163", "937957186", "937957240", "937959146", "937959499", "937960705", "937964891", "937969672", "937969729", "937969990", "937970026", "937981362", "937981370", "937981389", "937981397", "937981427", "937981443", "937981451", "937981478", "937988030", "937988065", "937989754", "938000969", "938002325", "938018850", "938018957", "938019651", "938027000", "938027980", "938028022", "938028430", "938028499", "938030469", "938032941", "938033026", "938041800", "938049496", "938050427", "938050516", "938053043", "938054660", "938054759", "938054864", "938065947", "938069748", "938072196", "938085310", "938085395", "938096788", "938096818", "938096907", "938096915", "938096923", "938102990", "938103636", "938103695", "938110802", "938111434", "938122169", "938122223", "938122754", "938123920", "938125389", "938125443", "938134752", "938134760", "938134981", "938134990", "938135066", "938135074", "938139495", "938146033", "938147757", "938147838", "938148516", "938171500", "938173154", "938173170", "938178202", "938178334", "938178539", "938184890", "938190873", "938190890", "938190911", "938190946", "938190989", "938202901", "938210327", "938210394", "938210416", "938215523", "938217879", "938220560", "938253697", "938253956", "938265083", "938265091", "938265105", "938265130", "938265148", "938265164", "938265237", "938278525", "938286188", "938286242", "938295101", "938295829", "938300938", "938300946", "938300954", "938301683", "938301780", "938302051", "938302175", "938303236", "938308831", "938309587", "938309838", "938309935", "938313711", "938326678", "938336053", "938343700", "938344862", "938352598", "938352687", "938354434", "938354485", "938360450", "938361350", "938362003", "938362070", "938364200", "938365576", "938365819", "938370243", "938375784", "938375911", "938376098", "938381695", "938382411", "938382799", "938385712", "938393154", "938399853", "938399993", "938400622", "938408445", "938410512", "938410520", "938411063", "938411640", "938413198", "938413309", "938421875", "938422510", "938427369", "938434926", "938435213", "938435582", "938435884", "938436260", "938436678", "938437429", "938438484", "938441965", "938442058", "938442252", "938444832", "938444840", "938446363", "938453173", "938453912", "938457764", "938463853", "938463870", "938471139", "938471236", "938472305", "938472739", "938473174", "938474693", "938480308", "938480561", "938481398", "938481410", "938481452", "938481460", "938481487", "938481517", "938481541", "938481550", "938485288", "938489291", "938497227", "938498878", "938499009", "938501054", "938501070", "938501089", "938501097", "938504010", "938508296", "938508733", "938508776", "938508814", "938514440", "938515810", "938517392", "938520890", "938520911", "938520970", "938520997", "938532987", "938536168", "938537571", "938540050", "938540068", "938540084", "938540262", "938540270", "938546570", "938554417", "938556134", "938558943", "938558951", "938558960", "938558978", "938558986", "938559001", "938559060", "938559680", "938570935", "938571052", "938571133", "938575961", "938576097", "938576429", "938577484", "938577522", "938577557", "938577565", "938577590", "938577611", "938577883", "938577956", "938587692", "938587765", "938595180", "938595296", "938595458", "938595571", "938597710", "938597752", "938609483", "938609530", "938616013", "938616048", "938618768", "938621246", "938621505", "938624601", "938624679", "938625195", "938626990", "938635190", "938637053", "938644580", "938655906", "938655914", "938655930", "938655949", "938655981", "938656015", "938656058", "938656066", "938656074", "938656090", "938658565", "938665979", "938667076", "938667157", "938670360", "938670670", "938672517", "938673785", "938673831", "938673882", "938673939", "938674030", "938674129", "938675184", "938675192", "938675621", "938675648", "938681770", "938682598", "938692720", "938696009", "938704613", "938704664", "938705539", "938708473", "938709062", "938710451", "938710621", "938710915", "938711261", "938711296", "938711458", "938711695", "938711733", "938714309", "938714864", "938728806", "938728881", "938728962", "938729039", "938732447", "938732471", "938732560", "938732579", "938742418", "938755722", "938755730", "938755757", "938755765", "938767186", "938767267", "938767402", "938768247", "938769316", "938770128", "938770489", "938774000", "938775294", "938783815", "938788760", "938789015", "938789538", "938789600", "938790048", "938790226", "938792423", "938792431", "938792440", "938792458", "938792466", "938792580", "938800884", "938806319", "938807870", "938808524", "938812319", "938812378", "938814788", "938815202", "938815580", "938818929", "938819518", "938819542", "938821350", "938822780", "938823124", "938823183", "938841602", "938845829", "938849697", "938850628", "938854046", "938854240", "938854526", "938854593", "938856278", "938856294", "938856340", "938856367", "938856421", "938856588", "938871307", "938873458", "938873466", "938873474", "938873784", "938881043", "938881256", "938882473", "938892240", "938894153", "938894943", "938895141", "938896539", "938897071", "938900498", "938900951", "938901036", "938901915", "938901982", "938907077", "938907298", "938907590", "938907816", "938908197", "938917064", "938917153", "938917250", "938917269", "938917277", "938931920", "938931997", "938932136", "938932217", "938933582", "938933590", "938933604", "938933620", "938940198", "938940333", "938942514", "938946226", "938947168", "938947940", "938950290", "938951882", "938951998", "938952250", "938952315", "938952366", "938952447", "938954369", "938954385", "938954415", "938954423", "938962604", "938965557", "938966006", "938966057", "938966316", "938966340", "938969978", "938970739", "938970810", "938970879", "938971506", "938971980", "938974220", "938980564", "938982443", "938985191", "938985760", "938990110", "938990179", "938998650", "939002191", "939007010", "939007509", "939012847", "939026490", "939026562", "939026902", "939027135", "939037319", "939038471", "939040620", "939041146", "939041448", "939041600", "939041936", "939042169", "939046253", "939046393", "939046440", "939049589", "939055139", "939055430", "939055899", "939055945", "939056755", "939057174", "939058812", "939058910", "939058987", "939059266", "939059576", "939060957", "939061040", "939063042", "939063174", "939065207", "939065304", "939070138", "939071592", "939076942", "939090864", "939090880", "939091348", "939091810", "939092271", "939105578"], "fastapiDisponivel": true, "processosEnviadosParaFastApi": 0, "tempoExecucao": "0.66s", "erros": ["Erro ao enviar para FastAPI: FastAPI retornou status 202: Accepted"], "fastapiResposta": {"success": false, "message": "FastAPI retornou status 202: Accepted"}}