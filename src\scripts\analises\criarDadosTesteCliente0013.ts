import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// --- DADOS DO CLIENTE ALVO ---
const identificadorClienteAlvo = '00000013';
const numeroDocumentoClienteAlvo = '00000013'; // Pode ser igual ao identificador ou um CNPJ/CPF real
const nomeClienteAlvo = `TESTER (${identificadorClienteAlvo})`;
// -----------------------

/**
 * Função principal para criar o cliente alvo, se ele não existir.
 */
async function criarClienteAlvoSeNaoExistir() {
  try {
    console.log(`Iniciando verificação/criação do cliente ${identificadorClienteAlvo}.`);

    // 1. Verificar se o Cliente Alvo já existe
    let clienteAlvo = await prisma.cliente.findFirst({
      where: { identificador: identificadorClienteAlvo }
    });

    if (!clienteAlvo) {
      console.log(`Cliente com identificador: ${identificadorClienteAlvo} não encontrado. Criando novo cliente...`);
      clienteAlvo = await prisma.cliente.create({
        data: {
          identificador: identificadorClienteAlvo,
          nome: nomeClienteAlvo,
          tipoDeDocumento: 'TESTE_CRIACAO_SCRIPT', // Tipo de documento para identificar a origem
          numeroDocumento: numeroDocumentoClienteAlvo,
          // Outros campos opcionais podem ser adicionados aqui conforme necessário
        }
      });
      console.log(`Cliente ${clienteAlvo.nome} (ID: ${clienteAlvo.id}) criado com sucesso.`);
    } else {
      console.log(`Cliente ${clienteAlvo.nome} (ID: ${clienteAlvo.id}) com identificador ${identificadorClienteAlvo} já existe. Nenhuma ação necessária.`);
    }

  } catch (error: any) {
    console.error(`Erro ao verificar/criar o cliente ${identificadorClienteAlvo}:`, error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script
criarClienteAlvoSeNaoExistir(); 