import { Request<PERSON><PERSON><PERSON> } from 'express';
import prisma from '../dbClient';

export const cadastrarProcuradorMonitorado: RequestHandler = async (req, res) => {
  try {
    const { nome } = req.body;

    // Validação básica
    if (!nome || typeof nome !== 'string') {
      res.status(400).json({ error: 'Nome do procurador é obrigatório e deve ser uma string' });
      return;
    }

    // Verifica se já existe um procurador com o mesmo nome
    const procuradorExistente = await prisma.procuradorMonitorado.findUnique({
      where: { nome }
    });

    if (procuradorExistente) {
      res.status(409).json({ error: 'Procurador já cadastrado' });
      return;
    }

    // Cria o novo procurador monitorado
    const procurador = await prisma.procuradorMonitorado.create({
      data: { nome }
    });

    res.status(201).json({
      message: 'Procurador monitorado cadastrado com sucesso',
      data: procurador
    });

  } catch (error) {
    console.error('Erro ao cadastrar procurador monitorado:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
};

export const listarProcuradoresMonitorados: RequestHandler = async (_req, res) => {
  try {
    const procuradores = await prisma.procuradorMonitorado.findMany();
    res.json(procuradores);
  } catch (error) {
    console.error('Erro ao listar procuradores monitorados:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
};

export const deletarProcuradorMonitorado: RequestHandler = async (req, res) => {
  try {
    const { id } = req.params;

    const procurador = await prisma.procuradorMonitorado.delete({
      where: { id }
    });

    res.json({
      message: 'Procurador monitorado removido com sucesso',
      data: procurador
    });
  } catch (error) {
    console.error('Erro ao deletar procurador monitorado:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
}; 
export const listarProcessosPorProcurador: RequestHandler = async(req, res ) =>{
  try {
    const { nome } = req.query;

    if (!nome || typeof nome !== 'string') {
      res.status(400).json({ error: 'Nome do procurador é obrigatório' });
      return;
    }

    const processos = await prisma.processo.findMany({
      where: {
        procurador: {
          nome: {
            equals: nome,
            mode: 'insensitive'
          }
        }
      },
      include: {
        titulares: true,
        despachos: {
          include: {
            rpi: true
          }
        }
      }
    });

    if (!processos.length) {
      res.status(404).json({ 
        message: 'Nenhum processo encontrado para este procurador'
      });
      return;
    }

    res.json({
      message: 'Processos encontrados com sucesso',
      data: processos.length
    });

  } catch (error) {
    console.error('Erro ao buscar processos do procurador:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
}