import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente do .env na raiz do projeto (se existir)
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

const prisma = new PrismaClient();

// --- Configurações ---
const NOME_DESPACHO_ALVO = "Deferimento do pedido";
const NOME_PROCURADOR_FILTRO = "REGISTRE-SE LTDA";
const SCRAPING_SERVICE_URL = process.env.SCRAPING_SERVICE_URL || 'http://127.0.0.1:8000';
const RPI_NUMERO_MAXIMO = 2834; // RPI máxima a considerar (opcional, pode ser removido se filtrar por data)
// --- Fim <PERSON>figu<PERSON> ---

/**
 * Valida e parseia uma string de data no formato YYYY-MM-DD.
 */
function parseDateArgument(dateStr: string | undefined): Date | null {
    if (!dateStr) return null;
    const dateRegex = /^(\d{4})-(\d{2})-(\d{2})$/;
    if (!dateRegex.test(dateStr)) return null;
    const date = new Date(`${dateStr}T00:00:00.000Z`); // Usar UTC para evitar problemas de fuso
    if (isNaN(date.getTime())) return null;
    return date;
}

async function encontrarEEnviarDeferidos(startDate: Date, endDate: Date) {
    // Ajustar endDate para incluir o dia inteiro (até 23:59:59.999Z)
    const inclusiveEndDate = new Date(endDate);
    inclusiveEndDate.setUTCDate(inclusiveEndDate.getUTCDate() + 1);
    inclusiveEndDate.setUTCMilliseconds(inclusiveEndDate.getUTCMilliseconds() - 1);

    console.log(`[${new Date().toISOString()}] Iniciando busca por processos com último despacho '${NOME_DESPACHO_ALVO}'...`);
    console.log(`  -> Intervalo de data de publicação da RPI do deferimento: ${startDate.toISOString().split('T')[0]} a ${endDate.toISOString().split('T')[0]}`);

    try {
        // 1. Buscar despachos de deferimento que atendem aos critérios iniciais E ao intervalo de datas
        const despachosDeferimento = await prisma.despacho.findMany({
            where: {
                nome: NOME_DESPACHO_ALVO,
                // Filtro da RPI associada a ESTE despacho
                rpi: {
                    numero: {
                        lte: RPI_NUMERO_MAXIMO // Mantido por precaução, mas o filtro de data é o principal
                    },
                    // *** NOVO: Filtro pela data de publicação da RPI do deferimento ***
                    dataPublicacao: {
                        gte: startDate,
                        lte: inclusiveEndDate, // Usar a data final inclusiva
                    }
                },
                // Filtros do Processo associado (inalterados)
                processo: {
                    NOT: {
                        numero: {
                            startsWith: 'TESTE-'
                        }
                    },
                    OR: [
                        { monitorado: true },
                        {
                            procurador: {
                                nome: {
                                    contains: NOME_PROCURADOR_FILTRO,
                                    mode: 'insensitive'
                                }
                            }
                        }
                    ]
                }
            },
            include: {
                // Inclui a data de publicação da RPI deste despacho
                rpi: {
                    select: { dataPublicacao: true, numero: true } // Incluir número da RPI no log se útil
                },
                // Inclui o processo e TODOS os seus despachos ordenados pelo mais recente
                processo: {
                    select: {
                        id: true,
                        numero: true,
                        despachos: {
                            select: {
                                id: true,    // Campo do Despacho
                                nome: true,  // Campo do Despacho
                                rpi: {       // Relação RPI
                                    select: { // Selecionar campos específicos da RPI
                                        dataPublicacao: true,
                                        numero: true
                                    }
                                }
                            },
                            orderBy: {
                                rpi: {
                                    dataPublicacao: 'desc' // O mais recente primeiro
                                }
                            }
                        }
                    }
                }
            }
        });

        console.log(`[${new Date().toISOString()}] ${despachosDeferimento.length} despachos '${NOME_DESPACHO_ALVO}' encontrados no período especificado.`);

        // 2. Filtrar para garantir que o despacho de deferimento é o ÚLTIMO (lógica inalterada)
        const processosParaEnviar = new Set<string>();
        for (const deferimento of despachosDeferimento) {
            if (!deferimento.processo || !deferimento.processo.despachos || deferimento.processo.despachos.length === 0) {
                console.warn(`[${new Date().toISOString()}] Processo ${deferimento.processo?.numero || deferimento.processoId} associado ao despacho ${deferimento.id} (RPI ${deferimento.rpi.numero}) não tem despachos listados? Pulando.`);
                continue;
            }
            const ultimoDespachoDoProcesso = deferimento.processo.despachos[0];
            if (deferimento.id === ultimoDespachoDoProcesso.id) {
                processosParaEnviar.add(deferimento.processo.numero);
                 console.log(`  [OK] Processo ${deferimento.processo.numero} (RPI ${deferimento.rpi.numero}) confirmado. Último despacho é '${NOME_DESPACHO_ALVO}'.`);
            } else {
                 console.log(`  [DESCARTADO] Processo ${deferimento.processo.numero} (RPI ${deferimento.rpi.numero}). Último despacho é '${ultimoDespachoDoProcesso.nome}' (RPI ${ultimoDespachoDoProcesso.rpi.numero}), não '${NOME_DESPACHO_ALVO}'.`);
            }
        }

        const numerosProcesso = Array.from(processosParaEnviar);

        if (numerosProcesso.length === 0) {
            console.log(`[${new Date().toISOString()}] Nenhum processo final elegível encontrado após verificação do último despacho no período.`);
            return;
        }

        console.log(`[${new Date().toISOString()}] ${numerosProcesso.length} processo(s) finais elegíveis para scraping no período: ${numerosProcesso.join(', ')}`);

        // 3. Enviar para o serviço de scraping (lógica inalterada)
        const jobId = uuidv4();
        const payload = {
            processos: numerosProcesso,
            job_id: jobId
        };
        console.log(`[${new Date().toISOString()}] Enviando ${numerosProcesso.length} processo(s) para ${SCRAPING_SERVICE_URL}/scrape com job_id ${jobId}...`);
        try {
            const response = await axios.post(`${SCRAPING_SERVICE_URL}/scrape`, payload, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 30000
            });
            if (response.status === 200 || response.status === 202) {
                console.log(`[${new Date().toISOString()}] Job ${jobId} enviado com sucesso para o serviço de scraping. Status: ${response.status}`);
            } else {
                console.warn(`[${new Date().toISOString()}] Serviço de scraping retornou status inesperado ${response.status} para job ${jobId}. Corpo:`, response.data);
            }
        } catch (error: any) {
            console.error(`[${new Date().toISOString()}] Falha ao ENVIAR job ${jobId} para o serviço de scraping.`);
            if (axios.isAxiosError(error)) {
                if (error.response) {
                    console.error(` > Resposta do Serviço: Status ${error.response.status}`, error.response.data);
                } else if (error.request) {
                    console.error(` > Nenhuma resposta recebida do serviço.`);
                } else {
                    console.error(` > Erro na configuração do Axios:`, error.message);
                }
            } else {
                console.error(` > Erro não relacionado ao Axios:`, error);
            }
        }

    } catch (error) {
        console.error(`[${new Date().toISOString()}] Erro geral durante a busca e envio de processos deferidos:`, error);
    } finally {
        await prisma.$disconnect();
        console.log(`[${new Date().toISOString()}] Script finalizado. Conexão com DB fechada.`);
    }
}

// --- Execução do Script ---
const startDateArg = process.argv[2];
const endDateArg = process.argv[3];

const startDate = parseDateArgument(startDateArg);
const endDate = parseDateArgument(endDateArg);

if (!startDate || !endDate) {
    console.error('Erro: Datas de início e fim são obrigatórias.');
    console.error('Uso: ts-node src/scripts/enviarDeferidosParaScraping.ts YYYY-MM-DD YYYY-MM-DD');
    console.error('Exemplo: ts-node src/scripts/enviarDeferidosParaScraping.ts 2024-04-01 2024-04-29');
    process.exit(1);
}

encontrarEEnviarDeferidos(startDate, endDate)
    .catch((e) => {
        console.error('Erro fatal no script:', e);
        process.exit(1);
    }); 