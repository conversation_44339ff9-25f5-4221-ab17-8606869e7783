import { PrismaClient } from '@prisma/client';
import cliProgress from 'cli-progress';

const prisma = new PrismaClient();

// Configurações de performance ajustadas
const BATCH_SIZE_PROCESSOS = 50; // Processos por lote
const BATCH_SIZE_RPIS = 25; // RPIs por lote (menor para evitar timeout)
const MAX_CONCURRENT_BATCHES = 3; // Reduzido para evitar sobrecarga
const TRANSACTION_TIMEOUT = 30000; // 30 segundos timeout

// Identificadores dos clientes de teste criados pelos scripts
const CLIENTES_TESTE = ['00000001', '00000002', '00000003'];

// Prefixos dos números de processo de teste
const PREFIXOS_PROCESSO_TESTE = [
  'TESTE-00000001-',
  'TESTE-00000002-', 
  'TESTE-00000003-',
  'TESTE',
];

// Ranges de números de RPI fictícios criados pelos scripts
const RANGES_RPI_FICTICIOS = [
  { min: 999900000, max: 999999999 }, // criarDadosTesteFluxos.ts
  { min: 888800000, max: 888899999 }, // criarDadosTesteCliente002.ts
  { min: 777700000, max: 777799999 }  // criarDadosTesteCliente003.ts
];

// Códigos de despacho dos dados de teste
const CODIGOS_DESPACHO_TESTE = ['TESTE', 'FLUXO_ESP', 'FLUXO_DT3'];

/**
 * Divide um array em lotes de tamanho específico
 */
function dividirEmLotes<T>(array: T[], tamanhoBatch: number): T[][] {
  const lotes: T[][] = [];
  for (let i = 0; i < array.length; i += tamanhoBatch) {
    lotes.push(array.slice(i, i + tamanhoBatch));
  }
  return lotes;
}

/**
 * Processa um lote de processos de forma atômica
 */
async function processarLoteProcessos(loteProcessos: string[], numeroLote: number, totalLotes: number) {
  return await prisma.$transaction(async (tx) => {
    // 1. Remover ProtocoloDespacho
    await tx.protocoloDespacho.deleteMany({
      where: {
        despacho: {
          processoId: { in: loteProcessos }
        }
      }
    });

    // 2. Remover Despachos
    await tx.despacho.deleteMany({
      where: { processoId: { in: loteProcessos } }
    });

    // 3. Remover HistoricoComunicadoPrazoMerito e ComunicadoPrazoMerito
    await tx.historicoComunicadoPrazoMerito.deleteMany({
      where: {
        comunicado: {
          processoId: { in: loteProcessos }
        }
      }
    });
    await tx.comunicadoPrazoMerito.deleteMany({
      where: { processoId: { in: loteProcessos } }
    });

    // 4. Remover outros dados relacionados
    await tx.crmProcessoControle.deleteMany({
      where: { processoId: { in: loteProcessos } }
    });

    await tx.processoScrapingControl.deleteMany({
      where: { processoId: { in: loteProcessos } }
    });

    await tx.processoInteresse.deleteMany({
      where: { processoId: { in: loteProcessos } }
    });

    await tx.sobrestador.deleteMany({
      where: { processoId: { in: loteProcessos } }
    });

    await tx.titular.deleteMany({
      where: { processoId: { in: loteProcessos } }
    });

    // 5. Remover Marcas (NCL e CFE são removidos por cascade)
    await tx.marca.deleteMany({
      where: { processoId: { in: loteProcessos } }
    });

    // 6. Remover Processos
    await tx.processo.deleteMany({
      where: { id: { in: loteProcessos } }
    });

    return loteProcessos.length;
  }, {
    timeout: TRANSACTION_TIMEOUT
  });
}

/**
 * Processa um lote de RPIs de forma atômica com timeout maior
 */
async function processarLoteRPIs(loteRPIs: string[], numeroLote: number, totalLotes: number) {
  return await prisma.$transaction(async (tx) => {
    // 1. Remover Despachos órfãos que referenciam estas RPIs
    await tx.despacho.deleteMany({
      where: { rpiId: { in: loteRPIs } }
    });

    // 2. Remover Titulares que referenciam estas RPIs
    await tx.titular.deleteMany({
      where: { rpiId: { in: loteRPIs } }
    });

    // 3. Remover RPIs
    await tx.rPI.deleteMany({
      where: { id: { in: loteRPIs } }
    });

    return loteRPIs.length;
  }, {
    timeout: TRANSACTION_TIMEOUT
  });
}

/**
 * Executa operações em lotes paralelos com controle de concorrência
 */
async function executarLotesParalelos<T>(
  lotes: T[][],
  processarLote: (lote: T[], numeroLote: number, totalLotes: number) => Promise<number>,
  descricao: string
): Promise<number> {
  let totalProcessado = 0;
  const totalLotes = lotes.length;

  // Configurar barra de progresso específica
  const barraLote = new cliProgress.SingleBar({
    format: `${descricao} |{bar}| {percentage}% | {value}/{total} Lotes | Processados: {processados}`,
    barCompleteChar: '\u2588',
    barIncompleteChar: '\u2591',
    hideCursor: true
  });
  barraLote.start(totalLotes, 0, { processados: 0 });

  // Processar lotes em grupos com concorrência limitada
  for (let i = 0; i < lotes.length; i += MAX_CONCURRENT_BATCHES) {
    const lotesConcorrentes = lotes.slice(i, i + MAX_CONCURRENT_BATCHES);
    
    const promessas = lotesConcorrentes.map((lote, index) => 
      processarLote(lote, i + index + 1, totalLotes)
    );

    try {
      const resultados = await Promise.all(promessas);
      const processadosNesteCiclo = resultados.reduce((sum, count) => sum + count, 0);
      totalProcessado += processadosNesteCiclo;
      
      barraLote.increment(lotesConcorrentes.length, { 
        processados: totalProcessado 
      });
    } catch (error) {
      barraLote.stop();
      throw error;
    }
  }

  barraLote.stop();
  return totalProcessado;
}

/**
 * Função principal otimizada para remoção em lotes paralelos
 */
async function removerDadosTesteOtimizado() {
  try {
    console.log('🚀 Iniciando remoção otimizada de dados de teste...\n');

    // 1. Identificar todos os dados de teste
    console.log('📋 Identificando dados de teste...');
    
    const processosConditions = PREFIXOS_PROCESSO_TESTE.map(prefixo => ({
      numero: { startsWith: prefixo }
    }));

    const [processosTeste, rpisFicticias, clientesTeste] = await Promise.all([
      prisma.processo.findMany({
        where: { OR: processosConditions },
        select: { id: true, numero: true }
      }),
      prisma.rPI.findMany({
        where: { 
          OR: RANGES_RPI_FICTICIOS.map(range => ({
            numero: { gte: range.min, lte: range.max }
          }))
        },
        select: { id: true, numero: true }
      }),
      prisma.cliente.findMany({
        where: { identificador: { in: CLIENTES_TESTE } },
        select: { id: true, identificador: true, nome: true }
      })
    ]);

    console.log(`   • ${processosTeste.length} processos de teste`);
    console.log(`   • ${rpisFicticias.length} RPIs fictícias`);
    console.log(`   • ${clientesTeste.length} clientes de teste\n`);

    if (processosTeste.length === 0 && rpisFicticias.length === 0 && clientesTeste.length === 0) {
      console.log('✅ Nenhum dado de teste encontrado para remover.');
      return;
    }

    // Confirmar configuração de performance
    console.log('⚡ Configuração de Performance (Ajustada):');
    console.log(`   • Lote de processos: ${BATCH_SIZE_PROCESSOS} registros`);
    console.log(`   • Lote de RPIs: ${BATCH_SIZE_RPIS} registros`);
    console.log(`   • Lotes paralelos: ${MAX_CONCURRENT_BATCHES} simultâneos`);
    console.log(`   • Timeout de transação: ${TRANSACTION_TIMEOUT}ms\n`);

    let totalRemovido = 0;

    // 2. Processar remoção de processos em lotes paralelos
    if (processosTeste.length > 0) {
      console.log('🔄 Removendo processos e dados relacionados...');
      const processosIds = processosTeste.map(p => p.id);
      const lotesProcessos = dividirEmLotes(processosIds, BATCH_SIZE_PROCESSOS);
      
      const processadosProcessos = await executarLotesParalelos(
        lotesProcessos,
        processarLoteProcessos,
        'Processos'
      );
      totalRemovido += processadosProcessos;
      console.log(`   ✅ ${processadosProcessos} processos removidos\n`);
    }

    // 3. Processar remoção de RPIs órfãs em lotes paralelos (com lotes menores)
    if (rpisFicticias.length > 0) {
      console.log('🔄 Removendo RPIs órfãs...');
      const rpisIds = rpisFicticias.map(r => r.id);
      const lotesRPIs = dividirEmLotes(rpisIds, BATCH_SIZE_RPIS);
      
      const processadosRPIs = await executarLotesParalelos(
        lotesRPIs,
        processarLoteRPIs,
        'RPIs'
      );
      console.log(`   ✅ ${processadosRPIs} RPIs removidas\n`);
    }

    // 4. Remover clientes e contatos (operação rápida, sem necessidade de lotes)
    if (clientesTeste.length > 0) {
      console.log('🔄 Removendo clientes de teste...');
      const clientesIds = clientesTeste.map(c => c.id);
      
      await prisma.$transaction(async (tx) => {
        await tx.contatoCliente.deleteMany({
          where: { clienteId: { in: clientesIds } }
        });
        await tx.cliente.deleteMany({
          where: { id: { in: clientesIds } }
        });
      });
      
      console.log(`   ✅ ${clientesTeste.length} clientes removidos\n`);
    }

    // 5. Limpeza adicional por código de despacho
    console.log('🧹 Limpeza final de despachos órfãos por código...');
    const despachosOrfaos = await prisma.despacho.deleteMany({
      where: { codigo: { in: CODIGOS_DESPACHO_TESTE } }
    });
    console.log(`   ✅ ${despachosOrfaos.count} despachos órfãos removidos\n`);

    // Resumo final
    console.log('🎉 REMOÇÃO CONCLUÍDA COM SUCESSO!');
    console.log('📊 Resumo:');
    console.log(`   • ${processosTeste.length} processos de teste`);
    console.log(`   • ${rpisFicticias.length} RPIs fictícias`);
    console.log(`   • ${clientesTeste.length} clientes de teste`);
    console.log(`   • ${despachosOrfaos.count} despachos órfãos`);
    console.log(`   • Milhares de registros relacionados\n`);

    console.log('⚡ Performance melhorada com processamento em lotes paralelos!');

  } catch (error: any) {
    console.error('\n❌ Erro ao remover dados de teste:', error.message);
    if (error.code) {
      console.error('Código do erro:', error.code);
    }
    if (error.meta) {
      console.error('Detalhes:', error.meta);
    }
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script otimizado
console.log('🚀 Script de remoção OTIMIZADO iniciado...');
console.log('💡 Usando processamento em lotes paralelos para melhor performance\n');
removerDadosTesteOtimizado(); 