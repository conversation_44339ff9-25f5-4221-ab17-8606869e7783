import { PrismaClient } from '@prisma/client';
import fs from 'fs/promises';
import path from 'path';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente do .env na raiz do projeto (se existir)
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

const prisma = new PrismaClient();

// --- Configurações ---
const CODIGO_SERVICO_TAXA_CONCESSAO = "372";
const OUTPUT_DIR = path.resolve(__dirname, '../../reports'); // Pasta para salvar os relatórios
// --- Fim Configurações ---

interface ScrapingResult {
    numero_processo: string;
    situacao?: string; // Opcional
    peticoes?: Array<{ servico_cod?: string; [key: string]: any }>;
    // ... outras propriedades que podem existir no JSON
}

interface JsonResult {
    job_id: string;
    results: ScrapingResult[];
    errors?: any;
}

interface ReportRow {
    nomeMarca: string;
    numeroProcesso: string;
    taxaPaga: string; // "Sim" ou "Não"
    telefonesCliente: string;
}

/**
 * Formata a linha de dados para CSV, tratando aspas duplas internas.
 */
function formatCsvRow(row: ReportRow): string {
    const escapeCsv = (field: string | null | undefined): string => {
        if (field === null || field === undefined) {
            return '""';
        }
        // Escapa aspas duplas internas duplicando-as e envolve o campo em aspas
        const escaped = field.replace(/"/g, '""');
        return `"${escaped}"`;
    };

    return [
        escapeCsv(row.nomeMarca),
        escapeCsv(row.numeroProcesso),
        escapeCsv(row.taxaPaga),
        escapeCsv(row.telefonesCliente)
    ].join(',');
}

async function gerarRelatorio(jsonFilePath: string) {
    console.log(`[${new Date().toISOString()}] Iniciando geração de relatório para: ${jsonFilePath}`);

    let jsonData: JsonResult;
    try {
        const fileContent = await fs.readFile(jsonFilePath, 'utf-8');
        jsonData = JSON.parse(fileContent);
        if (!jsonData || !Array.isArray(jsonData.results)) {
            throw new Error('Estrutura do JSON inválida. Array "results" não encontrado.');
        }
        console.log(`[${new Date().toISOString()}] JSON lido e parseado com sucesso. Job ID: ${jsonData.job_id}. Processando ${jsonData.results.length} resultados.`);
    } catch (error: any) {
        console.error(`[${new Date().toISOString()}] Erro ao ler ou parsear o arquivo JSON: ${error.message}`);
        throw error; // Re-lança para parar a execução
    }

    const reportData: ReportRow[] = [];

    for (const result of jsonData.results) {
        const numeroProcesso = result.numero_processo;
        if (!numeroProcesso) {
            console.warn(`[${new Date().toISOString()}] Resultado sem 'numero_processo' encontrado no JSON. Pulando.`);
            continue;
        }

        console.log(`  -> Processando Processo: ${numeroProcesso}`);

        // Verificar se a taxa de concessão foi paga (pelo JSON)
        const taxaPagaJson = result.peticoes?.some(
            peticao => peticao.servico_cod === CODIGO_SERVICO_TAXA_CONCESSAO
        ) ?? false;

        let nomeMarca = 'N/A';
        let telefonesCliente = '';

        try {
            // Buscar dados adicionais no banco
            const processoDb = await prisma.processo.findUnique({
                where: { numero: numeroProcesso },
                select: {
                    id: true,
                    marca: { select: { nome: true } },
                    cliente: {
                        select: {
                            contatos: {
                                select: { telefone: true, telefoneSegundario: true }
                            }
                        }
                    }
                }
            });

            if (processoDb) {
                nomeMarca = processoDb.marca?.nome || 'Marca não encontrada';

                if (processoDb.cliente && processoDb.cliente.contatos) {
                    const phones = new Set<string>();
                    processoDb.cliente.contatos.forEach(contato => {
                        if (contato.telefone) phones.add(contato.telefone);
                        if (contato.telefoneSegundario) phones.add(contato.telefoneSegundario);
                    });
                    telefonesCliente = Array.from(phones).join(' / ');
                }
                console.log(`     Marca: ${nomeMarca}, Telefones: ${telefonesCliente || 'Nenhum'}`);
            } else {
                console.warn(`     Processo ${numeroProcesso} não encontrado no banco de dados.`);
                nomeMarca = 'Processo não encontrado no DB';
            }

        } catch (dbError: any) {
            console.error(`     Erro ao buscar dados no DB para processo ${numeroProcesso}: ${dbError.message}`);
            nomeMarca = 'Erro ao buscar no DB';
            // Continuar mesmo com erro no DB para incluir os dados do JSON
        }

        reportData.push({
            nomeMarca: nomeMarca,
            numeroProcesso: numeroProcesso,
            taxaPaga: taxaPagaJson ? 'Sim' : 'Não',
            telefonesCliente: telefonesCliente || 'Nenhum telefone encontrado'
        });
    }

    // Gerar conteúdo CSV
    const header = '"Nome da Marca","Número do Processo","Taxa de Concessão Paga?","Telefones do Cliente"';
    const csvRows = reportData.map(formatCsvRow);
    const csvContent = [header, ...csvRows].join('\n');

    // Salvar arquivo CSV
    try {
        await fs.mkdir(OUTPUT_DIR, { recursive: true }); // Garante que o diretório exista
        const baseName = path.basename(jsonFilePath, '.json');
        const outputCsvPath = path.join(OUTPUT_DIR, `${baseName}_relatorio_concessao.csv`);

        await fs.writeFile(outputCsvPath, csvContent, 'utf-8');
        console.log(`[${new Date().toISOString()}] Relatório salvo com sucesso em: ${outputCsvPath}`);

    } catch (writeError: any) {
        console.error(`[${new Date().toISOString()}] Erro ao salvar o arquivo CSV: ${writeError.message}`);
        throw writeError;
    }
}

// --- Execução do Script ---
const jsonFileArg = process.argv[2];

if (!jsonFileArg) {
    console.error('Erro: O caminho para o arquivo JSON de resultados é obrigatório.');
    console.error('Uso: ts-node src/scripts/gerarRelatorioConcessao.ts <caminho_para_o_arquivo.json>');
    console.error('Exemplo: ts-node src/scripts/gerarRelatorioConcessao.ts uploads/obtidoScraping/resultados_xxxx.json');
    process.exit(1);
}

const fullJsonPath = path.resolve(jsonFileArg);

gerarRelatorio(fullJsonPath)
    .catch((e) => {
        console.error('Erro fatal no script:', e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
        console.log(`[${new Date().toISOString()}] Script finalizado. Conexão com DB fechada.`);
    }); 