/*
  Warnings:

  - You are about to drop the `ClienteProcessoAcesso` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "ClienteProcessoAcesso" DROP CONSTRAINT "ClienteProcessoAcesso_clienteId_fkey";

-- DropFore<PERSON>Key
ALTER TABLE "ClienteProcessoAcesso" DROP CONSTRAINT "ClienteProcessoAcesso_processoId_fkey";

-- DropTable
DROP TABLE "ClienteProcessoAcesso";

-- DropEnum
DROP TYPE "TipoAcesso";

-- CreateTable
CREATE TABLE "SessionLog" (
    "id" TEXT NOT NULL,
    "clienteId" INTEGER NOT NULL,
    "identificador" TEXT NOT NULL,
    "loginAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "logoutAt" TIMESTAMP(3),
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "sessionDuration" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SessionLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProtocoloDownloadLog" (
    "id" TEXT NOT NULL,
    "clienteId" INTEGER NOT NULL,
    "processoId" TEXT NOT NULL,
    "numeroProcesso" TEXT NOT NULL,
    "downloadAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "fileSize" INTEGER,
    "success" BOOLEAN NOT NULL DEFAULT true,
    "errorMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProtocoloDownloadLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SessionLog_clienteId_idx" ON "SessionLog"("clienteId");

-- CreateIndex
CREATE INDEX "SessionLog_loginAt_idx" ON "SessionLog"("loginAt");

-- CreateIndex
CREATE INDEX "SessionLog_isActive_idx" ON "SessionLog"("isActive");

-- CreateIndex
CREATE INDEX "SessionLog_identificador_idx" ON "SessionLog"("identificador");

-- CreateIndex
CREATE INDEX "ProtocoloDownloadLog_clienteId_idx" ON "ProtocoloDownloadLog"("clienteId");

-- CreateIndex
CREATE INDEX "ProtocoloDownloadLog_processoId_idx" ON "ProtocoloDownloadLog"("processoId");

-- CreateIndex
CREATE INDEX "ProtocoloDownloadLog_downloadAt_idx" ON "ProtocoloDownloadLog"("downloadAt");

-- CreateIndex
CREATE INDEX "ProtocoloDownloadLog_numeroProcesso_idx" ON "ProtocoloDownloadLog"("numeroProcesso");

-- AddForeignKey
ALTER TABLE "SessionLog" ADD CONSTRAINT "SessionLog_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProtocoloDownloadLog" ADD CONSTRAINT "ProtocoloDownloadLog_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProtocoloDownloadLog" ADD CONSTRAINT "ProtocoloDownloadLog_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE CASCADE ON UPDATE CASCADE;
