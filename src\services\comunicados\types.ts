import { PrismaClient } from '@prisma/client';

export type DialogoInfo = {
  dialogId: string;
  estagioProcesso: string;
  idEtapa: number;
};

export type ElegibilidadeResult = {
  elegivel: boolean;
  prazoMeses: number;
  dialogoInfo?: DialogoInfo;
  motivoInelegibilidade?: string;
};

export type ElegibilidadeDespachoResult = {
  elegivel: boolean;
  motivo?: string;
};

export type AtualizacaoElegibilidadeResult = {
  success: boolean;
  processosAtualizados: number;
  mensagem: string;
};

export type ExecutarDialogoResult = {
  success: boolean;
  errorMessage?: string;
};

export type VerificarEnviarResult = { 
  processosVerificados: number;
  comunicadosEnviados: number;
  processosSobrestados: number;
  processosSemCliente: number;
  comunicadosPendentes: number;
  comunicadosEmProcessamento: number;
  comunicadosComFalha: number;
  sucessos: number;
  falhas: number;
};

export interface Processo {
  id: string;
  numero: string;
  dataMeritoEstimada?: Date | null;
  diasAteMeritoEstimada?: number | null;
  sobrestamento: boolean;
  clienteId?: number | null;
  elegivelParaComunicados?: boolean | null;
  dataElegibilidade?: Date | null;
  cliente?: {
    id: number;
    crmId?: number | null;
    nome?: string | null;
  } | null;
  despachos?: Array<{
    nome?: string | null;
    rpi: {
      dataPublicacao: Date;
    };
  }> | null;
}

// Instância compartilhada do Prisma
export const prisma = new PrismaClient(); 