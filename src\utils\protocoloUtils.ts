import path from 'path';
import fs from 'fs';

/**
 * Tipos para protocolo
 */
export interface ProtocoloFileInfo {
  originalName: string;
  fileName: string;
  filePath: string;
  size: number;
  mimetype: string;
  uploadDate: string;
}

export interface ProtocoloListItem {
  fileName: string;
  numeroProcesso: string | null;
  elementoNominativo: string | null;
  size: number;
  createdAt: Date;
  modifiedAt: Date;
  logoDisponivel: boolean;
  urls: {
    downloadPdf: string;
    logoImagem: string | null;
  };
}

/**
 * Cria informações do arquivo de protocolo
 */
export const criarInfoProtocolo = (file: Express.Multer.File): ProtocoloFileInfo => ({
  originalName: file.originalname,
  fileName: file.filename,
  filePath: file.path,
  size: file.size,
  mimetype: file.mimetype,
  uploadDate: new Date().toISOString()
});

/**
 * Obtém o diretório de protocolos
 */
export const obterDiretorioProtocolos = (): string => 
  path.join(process.cwd(), 'uploads', 'protocolos');

/**
 * Verifica se o diretório de protocolos existe
 */
export const diretorioProtocolosExiste = (): boolean => 
  fs.existsSync(obterDiretorioProtocolos());

/**
 * Lista arquivos PDF no diretório de protocolos
 */
export const listarArquivosPdf = (): string[] => {
  const uploadsDir = obterDiretorioProtocolos();
  
  if (!diretorioProtocolosExiste()) {
    return [];
  }

  return fs.readdirSync(uploadsDir)
    .filter(file => path.extname(file).toLowerCase() === '.pdf');
};

/**
 * Extrai número do processo e elemento nominativo do nome do arquivo
 */
const extrairDadosDoNomeArquivo = (fileName: string) => {
  // Remover extensão .pdf
  const nomeBase = fileName.replace(/\.pdf$/i, '');
  
  // Tentar extrair número do processo e elemento nominativo
  // Formato esperado: "numeroProcesso-elementoNominativo.pdf"
  const parteDividida = nomeBase.split('-');
  
  if (parteDividida.length >= 2) {
    const numeroProcesso = parteDividida[0];
    const elementoNominativo = parteDividida.slice(1).join('-'); // Junta de volta caso tenha mais hífens
    
    // Verificar se número do processo é válido (só números)
    if (/^\d+$/.test(numeroProcesso)) {
      return { numeroProcesso, elementoNominativo };
    }
  }
  
  return { numeroProcesso: null, elementoNominativo: null };
};

/**
 * Verifica se logo existe para um número de processo
 */
const verificarLogoExiste = (numeroProcesso: string | null): boolean => {
  if (!numeroProcesso) return false;
  
  const imagensDir = path.join(process.cwd(), 'uploads', 'imagens-marca');
  const extensoesSuportadas = ['jpg', 'jpeg', 'png', 'webp', 'gif'];
  
  for (const ext of extensoesSuportadas) {
    const caminhoLogo = path.join(imagensDir, `${numeroProcesso}.${ext}`);
    if (fs.existsSync(caminhoLogo)) {
      return true;
    }
  }
  
  return false;
};

/**
 * Cria informações de um arquivo de protocolo para listagem
 */
export const criarItemListaProtocolo = (fileName: string, baseUrl?: string): ProtocoloListItem => {
  const filePath = path.join(obterDiretorioProtocolos(), fileName);
  const stats = fs.statSync(filePath);
  
  // Extrair dados do nome do arquivo
  const { numeroProcesso, elementoNominativo } = extrairDadosDoNomeArquivo(fileName);
  
  // Verificar se logo existe
  const logoDisponivel = verificarLogoExiste(numeroProcesso);
  
  // Construir URLs (usar baseUrl padrão se não fornecida)
  const apiBase = baseUrl || 'https://api-v3-rgsys.registrese.app.br';
  
  return {
    fileName,
    numeroProcesso,
    elementoNominativo,
    size: stats.size,
    createdAt: stats.birthtime,
    modifiedAt: stats.mtime,
    logoDisponivel,
    urls: {
      downloadPdf: numeroProcesso 
        ? `${apiBase}/api/protocolo/download/${numeroProcesso}`
        : `${apiBase}/api/protocolo/download/${encodeURIComponent(fileName.replace('.pdf', ''))}`,
      logoImagem: logoDisponivel && numeroProcesso 
        ? `${apiBase}/api/protocolo/logo/${numeroProcesso}`
        : null
    }
  };
};

/**
 * Mapeia lista de arquivos para informações de protocolo
 */
export const mapearParaListaProtocolos = (arquivos: string[], baseUrl?: string): ProtocoloListItem[] =>
  arquivos.map(fileName => criarItemListaProtocolo(fileName, baseUrl));

/**
 * Obtém lista completa de protocolos
 */
export const obterListaProtocolos = (baseUrl?: string): ProtocoloListItem[] => {
  const arquivos = listarArquivosPdf();
  return mapearParaListaProtocolos(arquivos, baseUrl);
}; 