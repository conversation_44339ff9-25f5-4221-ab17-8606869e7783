import * as logger from '../../utils/logger';
import { Processo, VerificarEnviarResult, prisma } from './types';
import { verificarElegibilidadeComunicado } from './elegibilidade';
import { executarDialogo } from './chatguruApi';

/**
 * Verifica e envia comunicados automaticamente para processos elegíveis
 * @returns Resultado da verificação e envio de comunicados
 */
export async function verificarEEnviarComunicados(): Promise<VerificarEnviarResult> {
  const resultado: VerificarEnviarResult = {
    processosVerificados: 0,
    comunicadosEnviados: 0,
    processosSobrestados: 0,
    processosSemCliente: 0,
    comunicadosPendentes: 0,
    comunicadosEmProcessamento: 0,
    comunicadosComFalha: 0,
    sucessos: 0,
    falhas: 0
  };

  try {
    logger.chatGuruLogger.info('Iniciando verificação de processos para envio de comunicados');

    // Buscar processos potencialmente elegíveis (com data mérito estimada e cliente associado)
    const processos = await prisma.processo.findMany({
      where: {
        dataMeritoEstimada: { not: null },
        clienteId: { not: null },
        cliente: {
          crmId: { not: null }
        },
        elegivelParaComunicados: true
      },
      include: {
        cliente: true,
        despachos: {
          include: {
            rpi: true
          }
        }
      }
    });

    logger.chatGuruLogger.info(`Encontrados ${processos.length} processos para verificação de elegibilidade`);
    resultado.processosVerificados = processos.length;

    // Para cada processo, verifica a elegibilidade
    for (const processo of processos) {
      if (!processo.clienteId || !processo.cliente?.crmId) {
        resultado.processosSemCliente++;
        continue;
      }

      if (processo.sobrestamento) {
        resultado.processosSobrestados++;
        continue;
      }

      // Verificar elegibilidade baseado nas regras de negócio
      const { elegivel, prazoMeses, dialogoInfo } = await verificarElegibilidadeComunicado(processo as Processo);

      if (!elegivel || !dialogoInfo) {
        logger.chatGuruLogger.debug(`Processo ${processo.numero} não é elegível para comunicado`);
        continue;
      }

      // Verificar se já existe um comunicado pendente ou em processamento para este processo
      const comunicadoExistente = await prisma.comunicadoPrazoMerito.findFirst({
        where: {
          processoId: processo.id,
          OR: [
            { status: 'PENDENTE' },
            { status: 'EM_PROCESSAMENTO' }
          ]
        }
      });

      if (comunicadoExistente) {
        if (comunicadoExistente.status === 'PENDENTE') {
          resultado.comunicadosPendentes++;
        } else {
          resultado.comunicadosEmProcessamento++;
        }
        continue;
      }

      // Criar registro de comunicado
      const comunicado = await prisma.comunicadoPrazoMerito.create({
        data: {
          id: `${processo.cliente.crmId}-${processo.id}-${dialogoInfo.dialogId}-${Date.now()}`,
          crmId: processo.cliente.crmId,
          processoId: processo.id,
          dialogId: dialogoInfo.dialogId,
          updatedAt: new Date(),
          prazoEmMeses: prazoMeses,
          status: 'EM_PROCESSAMENTO',
          dataEnvio: new Date(),
          success: false
        }
      });

      // Executar diálogo no ChatGuru
      try {
        const crmId = processo.cliente?.crmId;
        
        if (!crmId) {
          throw new Error('Cliente sem ID no CRM');
        }
        
        // Chama a função executarDialogo com os 5 parâmetros necessários
        const result = await executarDialogo(
          crmId, 
          dialogoInfo.dialogId, 
          processo.id, 
          prazoMeses,
          dialogoInfo.estagioProcesso
        );

        // Atualizar status do comunicado
        await prisma.comunicadoPrazoMerito.update({
          where: { id: comunicado.id },
          data: {
            status: 'ENVIADO',
            success: result.success,
            errorMessage: result.errorMessage
          }
        });

        if (result.success) {
          resultado.sucessos++;
          resultado.comunicadosEnviados++;
          logger.chatGuruLogger.info(`Comunicado enviado com sucesso para o processo ${processo.numero}`);
        } else {
          resultado.falhas++;
          resultado.comunicadosComFalha++;
          logger.chatGuruLogger.error(`Falha ao enviar comunicado para o processo ${processo.numero}: ${result.errorMessage}`);
        }
      } catch (error) {
        resultado.falhas++;
        resultado.comunicadosComFalha++;
        
        // Atualizar status do comunicado com erro
        await prisma.comunicadoPrazoMerito.update({
          where: { id: comunicado.id },
          data: {
            status: 'FALHA',
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Erro desconhecido'
          }
        });
        
        logger.chatGuruLogger.error(`Erro ao processar comunicado para processo ${processo.numero}:`, error);
      }
    }

    logger.chatGuruLogger.info(`Verificação concluída: ${resultado.comunicadosEnviados} comunicados enviados, ${resultado.falhas} falhas`);
    return resultado;
  } catch (error) {
    logger.chatGuruLogger.error('Erro na verificação de comunicados:', error);
    throw error;
  }
}

/**
 * Inicia o trabalho de verificação diária de comunicados
 * @param cronExpression Expressão cron para a execução (padrão: todos os dias às 9h)
 * @returns O objeto cron criado
 */
export function iniciarVerificacaoDiariaComunicados(cronExpression = '0 9 * * *'): any {
  logger.chatGuruLogger.info(`Configurando verificação diária de comunicados: ${cronExpression}`);
  
  // Implementação usando node-cron
  const cron = require('node-cron');
  const job = cron.schedule(
    cronExpression,
    async function() {
      logger.chatGuruLogger.info('Iniciando tarefa agendada de verificação de comunicados');
      try {
        await verificarEEnviarComunicados();
        logger.chatGuruLogger.info('Tarefa agendada concluída com sucesso');
      } catch (error) {
        logger.chatGuruLogger.error('Erro na execução da tarefa agendada:', error);
      }
    },
    {
      scheduled: true,
      timezone: 'America/Sao_Paulo'
    }
  );
  
  logger.chatGuruLogger.info('Verificação diária de comunicados configurada com sucesso');
  return job;
} 