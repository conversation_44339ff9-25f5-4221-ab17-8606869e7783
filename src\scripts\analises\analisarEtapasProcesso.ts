import { PrismaClient } from '@prisma/client';
import { writeFileSync } from 'fs';
import cliProgress from 'cli-progress';

const prisma = new PrismaClient();

/**
 * Interface para representar um fluxo de processo
 */
interface FluxoProcesso {
  numeroProcesso: string;
  sequenciaDespachos: string[];
}

/**
 * Interface para armazenar os diferentes fluxos encontrados
 */
interface AnaliseFluxos {
  // Conjunto de todos os possíveis despachos encontrados
  despachosExistentes: Set<string>;
  // Diferentes fluxos encontrados
  fluxosEncontrados: FluxoProcesso[];
}

/**
 * Função para contar total de processos
 */
async function contarProcessos(): Promise<number> {
  return await prisma.processo.count();
}

/**
 * Função para buscar processos em lotes
 */
async function buscarLoteProcessos(skip: number, take: number) {
  return await prisma.processo.findMany({
    select: {
      numero: true,
      despachos: {
        select: {
          nome: true,
          rpi: {
            select: {
              dataPublicacao: true
            }
          }
        },
        orderBy: {
          rpi: {
            dataPublicacao: 'asc'
          }
        }
      }
    },
    skip,
    take
  });
}

/**
 * Função principal que analisa os diferentes fluxos de despachos
 */
async function analisarFluxosDespachos() {
  try {
    console.log('Iniciando análise dos fluxos de despachos...');

    // 1. Configurar tamanho do lote e contar total de processos
    const TAMANHO_LOTE = 20000; // Processar 10 mil por vez
    const totalProcessos = await contarProcessos();
    
    console.log(`Total de processos a analisar: ${totalProcessos}`);

    // 2. Configurar barra de progresso
    const barraProgresso = new cliProgress.SingleBar({
      format: 'Análise em andamento |{bar}| {percentage}% | {value}/{total} Processos | ETA: {eta}s',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });

    // 3. Inicializar estrutura para análise
    const analise: AnaliseFluxos = {
      despachosExistentes: new Set<string>(),
      fluxosEncontrados: []
    };

    // 4. Iniciar barra de progresso
    barraProgresso.start(totalProcessos, 0);
    
    // 5. Processar em lotes
    for (let offset = 0; offset < totalProcessos; offset += TAMANHO_LOTE) {
      const processos = await buscarLoteProcessos(offset, TAMANHO_LOTE);
      
      // Processar cada processo do lote
      for (const processo of processos) {
        if (processo.despachos.length > 0) {
          const sequenciaDespachos = processo.despachos
            .map(d => d.nome)
            .filter((nome): nome is string => nome !== null);

          // Adicionar despachos únicos ao conjunto
          sequenciaDespachos.forEach(nome => analise.despachosExistentes.add(nome));

          // Se encontramos menos de 1000 fluxos diferentes, continuamos armazenando
          if (analise.fluxosEncontrados.length < 1000) {
            const fluxoExistente = analise.fluxosEncontrados.find(
              f => JSON.stringify(f.sequenciaDespachos) === JSON.stringify(sequenciaDespachos)
            );

            if (!fluxoExistente) {
              analise.fluxosEncontrados.push({
                numeroProcesso: processo.numero,
                sequenciaDespachos
              });
            }
          }
        }
        
        // Atualizar barra de progresso
        barraProgresso.increment();
      }
    }

    // 6. Finalizar barra de progresso
    barraProgresso.stop();

    // 7. Organizar e salvar resultados
    const resultado = {
      totalProcessosAnalisados: totalProcessos,
      despachosExistentes: Array.from(analise.despachosExistentes).sort(),
      fluxosUnicos: analise.fluxosEncontrados
        .sort((a, b) => a.sequenciaDespachos.length - b.sequenciaDespachos.length)
    };

    const dataHora = new Date().toISOString().replace(/[:.]/g, '-');
    const nomeArquivo = `analise-fluxos-${dataHora}.json`;
    
    writeFileSync(
      nomeArquivo,
      JSON.stringify(resultado, null, 2)
    );

    // 8. Exibir resumo
    console.log('\n=== Resumo da Análise ===');
    console.log(`\nTotal de processos analisados: ${totalProcessos}`);
    console.log(`Total de tipos de despachos encontrados: ${resultado.despachosExistentes.length}`);
    console.log(`Total de fluxos únicos identificados: ${resultado.fluxosUnicos.length}`);
    
    console.log('\nPossíveis Despachos:');
    resultado.despachosExistentes.forEach(despacho => {
      console.log(`- ${despacho}`);
    });

    console.log('\nExemplos de Fluxos Únicos:');
    resultado.fluxosUnicos.slice(0, 10).forEach(fluxo => {
      console.log(`\nProcesso: ${fluxo.numeroProcesso}`);
      console.log('Sequência de despachos:');
      fluxo.sequenciaDespachos.forEach((despacho, index) => {
        console.log(`${index + 1}. ${despacho}`);
      });
    });

    console.log(`\nResultados completos salvos em: ${nomeArquivo}`);

    return resultado;

  } catch (error) {
    console.error('Erro durante a análise:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Função principal que executa o script
 */
async function main() {
  try {
    await analisarFluxosDespachos();
  } catch (error) {
    console.error('Erro ao executar o script:', error);
    process.exit(1);
  }
}

// Executar o script apenas se for chamado diretamente
if (require.main === module) {
  main();
}

export { analisarFluxosDespachos }; 