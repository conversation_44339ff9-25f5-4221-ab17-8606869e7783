import prisma from '../dbClient';
import { Prisma } from '@prisma/client';

// --- Nova Interface Única --- //
interface CitacaoInfo {
    tipo: 'despacho' | 'sobrestador';
    processoCitadorNumero: string;
    marcaNome: string | null;
    ncl: Array<{ codigo?: string | null; especificacao?: string | null }>; // Array de NCLs da marca
    cfe: Array<{ codigo?: string | null }>; // Array de CFEs da marca
    titularNome: string | null; // Nome do primeiro titular encontrado
    nomeDespachoCitador: string | null; // Nome do despacho (apenas se tipo='despacho')
    textoComplementarCitador: string | null; // Texto complementar (apenas se tipo='despacho')
    rpiNumero?: number;
    dataRpi?: Date;
}

// --- Nova Interface para o Frontend --- //
interface CitacaoDespachoFrontend {
    processo: string;
    marca: string | null;
    classe: string | null; // Apenas o código da primeira classe NCL
    titular: string | null;
    dataRpi: Date | null; // Data de publicação da RPI
    numeroRpi: number | null; // Número da RPI
    despacho: string | null;
    complemento: string | null;
}

// --- Funções de Serviço Atualizadas --- //

// Função para buscar citações em despachos (formato frontend)
export const buscarCitacoesEmDespachos = async (
    numeroProcessoBuscado: string
): Promise<CitacaoDespachoFrontend[]> => { // Retorna a nova interface
    try {
        const despachosCitando = await prisma.despacho.findMany({
            where: {
                textoComplementar: {
                    not: null,
                    contains: numeroProcessoBuscado,
                    mode: 'insensitive',
                },
                processo: {
                    numero: {
                        not: numeroProcessoBuscado,
                    },
                },
            },
            select: {
                nome: true, // Nome do despacho -> DESPACHO
                textoComplementar: true, // -> COMPLEMENTO
                rpi: {
                    select: {
                        numero: true, // <- Assumindo que você adicionou isso -> NUMERO (RPI)
                        dataPublicacao: true // -> DATA (RPI)
                    }
                },
                processo: {
                    select: {
                        numero: true, // -> PROCESSO
                        marca: {
                            select: {
                                nome: true, // -> MARCA
                                ncl: { // -> CLASSE (primeiro código)
                                    select: { codigo: true },
                                    take: 1 // Pega apenas a primeira NCL
                                }
                            }
                        },
                        titulares: { // -> TITULAR (primeiro nome)
                            select: { nomeRazaoSocial: true },
                            take: 1
                        }
                    }
                }
            },
            orderBy: {
                rpi: {
                    dataPublicacao: 'desc', // Ordena pela data da RPI
                },
            },
        });

        // Mapeia diretamente para o formato do frontend
        const resultado: CitacaoDespachoFrontend[] = despachosCitando.map(despacho => ({
            processo: despacho.processo.numero,
            marca: despacho.processo.marca?.nome ?? null,
            // Pega o código da primeira NCL, se existir
            classe: despacho.processo.marca?.ncl[0]?.codigo ?? null,
            titular: despacho.processo.titulares[0]?.nomeRazaoSocial ?? null,
            // Usa os dados da RPI diretamente
            dataRpi: despacho.rpi?.dataPublicacao ?? null,
            numeroRpi: despacho.rpi?.numero ?? null, // <- Assumindo que o campo 'numero' existe em RPI
            despacho: despacho.nome,
            complemento: despacho.textoComplementar,
        }));

        return resultado;

    } catch (error) {
        console.error("Erro ao buscar citações em despachos (formato frontend):", error);
        throw new Error('Falha ao buscar citações nos despachos.');
    }
};

// Função para buscar citações em sobrestadores (formato único)
export const buscarCitacoesEmSobrestadores = async (
    numeroProcessoBuscado: string
): Promise<CitacaoInfo[]> => {
    try {
        const sobrestadoresCitando = await prisma.sobrestador.findMany({
            where: {
                referenciaProcessual: numeroProcessoBuscado,
                processo: {
                    numero: {
                        not: numeroProcessoBuscado,
                    },
                },
            },
            select: { // Usar select para buscar apenas o necessário
                id: true,
                processo: { // Dados do processo citador
                    select: {
                        numero: true,
                        dataDeposito: true, // Para dataRelevante
                        createdAt: true, // Fallback para dataRelevante
                        marca: {
                            select: {
                                nome: true,
                                ncl: { select: { codigo: true, especificacao: true } },
                                cfe: { select: { codigo: true } }
                            }
                        },
                        titulares: {
                            select: { nomeRazaoSocial: true },
                            take: 1
                        }
                    }
                }
            },
             orderBy: {
                processo: {
                    dataDeposito: 'desc',
                },
            },
        });

         // Mapeia para o novo formato
         const resultado: CitacaoInfo[] = sobrestadoresCitando.map(sobrestador => ({
            tipo: 'sobrestador',
            processoCitadorNumero: sobrestador.processo.numero,
            marcaNome: sobrestador.processo.marca?.nome ?? null,
            ncl: sobrestador.processo.marca?.ncl ?? [],
            cfe: sobrestador.processo.marca?.cfe ?? [],
            titularNome: sobrestador.processo.titulares[0]?.nomeRazaoSocial ?? null,
            nomeDespachoCitador: null, // Não aplicável para sobrestador
            textoComplementarCitador: null, // Não aplicável para sobrestador   
            dataRelevante: sobrestador.processo.dataDeposito ?? sobrestador.processo.createdAt,
        }));

        return resultado;

    } catch (error) {
        console.error("Erro ao buscar citações em sobrestadores:", error);
        throw new Error('Falha ao buscar citações nos sobrestadores.');
    }
}; 