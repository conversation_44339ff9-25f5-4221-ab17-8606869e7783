import fs from 'fs';
import path from 'path';

// Configurações do logger
const LOG_DIR = path.join(process.cwd(), 'logs');
const CHATGURU_LOG_FILE = path.join(LOG_DIR, 'chatguru-integration.log');
const MAX_LOG_SIZE = 5 * 1024 * 1024; // 5MB

// Níveis de log
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR'
}

// Certifique-se de que o diretório de logs exista
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Função para rotacionar logs se necessário
function rotateLogFileIfNeeded(logFile: string): void {
  try {
    if (fs.existsSync(logFile)) {
      const stats = fs.statSync(logFile);
      if (stats.size > MAX_LOG_SIZE) {
        const timestamp = new Date().toISOString().replace(/:/g, '-');
        fs.renameSync(logFile, `${logFile}.${timestamp}`);
      }
    }
  } catch (error) {
    console.error('Erro ao rotacionar arquivo de log:', error);
  }
}

// Função principal de log
function logToFile(level: LogLevel, message: string, data?: any): void {
  try {
    rotateLogFileIfNeeded(CHATGURU_LOG_FILE);
    
    const timestamp = new Date().toISOString();
    const logData = data ? JSON.stringify(data, null, 2) : '';
    const logEntry = `[${timestamp}] [${level}] ${message}\n${logData ? logData + '\n' : ''}`;
    
    fs.appendFileSync(CHATGURU_LOG_FILE, logEntry);
    
    // Também loga no console
    if (level === LogLevel.ERROR) {
      console.error(`[ChatGuru Integration] ${message}`, data);
    } else {
      console.log(`[ChatGuru Integration] [${level}] ${message}`);
    }
  } catch (error) {
    console.error('Erro ao escrever no arquivo de log:', error);
  }
}

// API pública do logger
export const chatGuruLogger = {
  debug: (message: string, data?: any) => logToFile(LogLevel.DEBUG, message, data),
  info: (message: string, data?: any) => logToFile(LogLevel.INFO, message, data),
  warn: (message: string, data?: any) => logToFile(LogLevel.WARN, message, data),
  error: (message: string, data?: any) => logToFile(LogLevel.ERROR, message, data),
  
  // Métodos específicos para as funções de integração
  logCampoAtualizado: (crmId: number, campo: string, valor: string, sucesso: boolean) => {
    const message = sucesso 
      ? `Campo '${campo}' atualizado com sucesso para crmId ${crmId}`
      : `Falha ao atualizar campo '${campo}' para crmId ${crmId}`;
    
    logToFile(
      sucesso ? LogLevel.INFO : LogLevel.ERROR,
      message,
      { crmId, campo, valor, sucesso, timestamp: new Date().toISOString() }
    );
  },
  
  logDialogoExecutado: (crmId: number, dialogId: string, processoId: string, estagioProcesso: string, sucesso: boolean, erro?: string) => {
    const message = sucesso
      ? `Diálogo executado com sucesso para crmId ${crmId}`
      : `Falha ao executar diálogo para crmId ${crmId}: ${erro || 'Erro desconhecido'}`;
    
    logToFile(
      sucesso ? LogLevel.INFO : LogLevel.ERROR,
      message,
      { 
        crmId, 
        dialogId, 
        processoId, 
        estagioProcesso, 
        sucesso, 
        erro,
        timestamp: new Date().toISOString() 
      }
    );
  }
}; 