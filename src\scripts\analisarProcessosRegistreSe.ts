import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
import fs from "fs";

dotenv.config();

const prisma = new PrismaClient();

interface ProcessoAnalise {
  id: string;
  numero: string;
  procuradorNome: string;
  temCliente: boolean;
  clienteId?: number;
  clienteNome?: string;
}

async function analisarProcessosRegistreSe() {
  const startTime = Date.now();
  
  try {
    console.log("🔍 Analisando processos da REGISTRE-SE LTDA...\n");
    
    // Buscar todos os processos da REGISTRE-SE LTDA
    console.log("📡 Buscando processos da REGISTRE-SE LTDA...");
    
    const processosRegistreSe = await prisma.processo.findMany({
      where: {
        procurador: {
          nome: {
            contains: "REGISTRE-SE LTDA",
            mode: "insensitive"
          }
        }
      },
      include: {
        procurador: {
          select: {
            nome: true
          }
        },
        cliente: {
          select: {
            id: true,
            nome: true
          }
        }
      },
      orderBy: {
        numero: 'asc'
      }
    });
    
    console.log(`✅ Encontrados ${processosRegistreSe.length} processos da REGISTRE-SE LTDA`);
    
    // Analisar cada processo
    const processosAnalisados: ProcessoAnalise[] = [];
    const processosSemCliente: string[] = [];
    let processosComCliente = 0;
    
    for (const processo of processosRegistreSe) {
      const analise: ProcessoAnalise = {
        id: processo.id,
        numero: processo.numero,
        procuradorNome: processo.procurador?.nome || "N/A",
        temCliente: !!processo.cliente,
        clienteId: processo.cliente?.id,
        clienteNome: processo.cliente?.nome || undefined
      };
      
      processosAnalisados.push(analise);
      
      if (processo.cliente) {
        processosComCliente++;
      } else {
        processosSemCliente.push(processo.numero);
      }
    }
    
    // Gerar relatório
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    
    // Relatório resumo
    const resumo = [
      '='.repeat(60),
      'ANÁLISE DE PROCESSOS REGISTRE-SE LTDA',
      '='.repeat(60),
      `Data da análise: ${new Date().toLocaleString('pt-BR')}`,
      '',
      'RESUMO GERAL:',
      `• Total de processos encontrados: ${processosRegistreSe.length}`,
      `• Processos com cliente vinculado: ${processosComCliente}`,
      `• Processos sem cliente vinculado: ${processosSemCliente.length}`,
      '',
      'PERCENTUAIS:',
      `• Com cliente: ${((processosComCliente / processosRegistreSe.length) * 100).toFixed(1)}%`,
      `• Sem cliente: ${((processosSemCliente.length / processosRegistreSe.length) * 100).toFixed(1)}%`,
      '',
      '='.repeat(60),
      ''
    ].join('\n');
    
    // Salvar relatório resumo
    const nomeArquivoResumo = `analise-registre-se-resumo-${timestamp}.txt`;
    fs.writeFileSync(nomeArquivoResumo, resumo, 'utf8');
    
    // Gerar arquivo com processos sem cliente
    if (processosSemCliente.length > 0) {
      const conteudoProcessosSemCliente = [
        '# PROCESSOS REGISTRE-SE LTDA SEM CLIENTE VINCULADO',
        `# Total: ${processosSemCliente.length} processos`,
        `# Gerado em: ${new Date().toLocaleString('pt-BR')}`,
        '#',
        '# Lista de números de processos:',
        '',
        ...processosSemCliente
      ].join('\n');
      
      const nomeArquivoProcessos = `processos-registre-se-sem-cliente-${timestamp}.txt`;
      fs.writeFileSync(nomeArquivoProcessos, conteudoProcessosSemCliente, 'utf8');
      
      console.log(`📄 Arquivo gerado: ${nomeArquivoProcessos}`);
    }
    
    // Relatório final no console
    const endTime = Date.now();
    const executionTime = (endTime - startTime) / 1000;
    
    console.log("\n🎯 RESULTADOS DA ANÁLISE:");
    console.log("=".repeat(50));
    console.log(`📊 Total de processos REGISTRE-SE LTDA: ${processosRegistreSe.length}`);
    console.log(`✅ Processos com cliente vinculado: ${processosComCliente} (${((processosComCliente / processosRegistreSe.length) * 100).toFixed(1)}%)`);
    console.log(`❌ Processos sem cliente vinculado: ${processosSemCliente.length} (${((processosSemCliente.length / processosRegistreSe.length) * 100).toFixed(1)}%)`);
    console.log(`⏱️ Tempo de execução: ${executionTime.toFixed(2)} segundos`);
    
    console.log("\n📁 ARQUIVOS GERADOS:");
    console.log(`   📋 Relatório resumo: ${nomeArquivoResumo}`);
    
    if (processosSemCliente.length > 0) {
      console.log(`   📄 Processos sem cliente: processos-registre-se-sem-cliente-${timestamp}.txt`);
    }
    
    console.log(`\n📂 Localização: ${process.cwd()}/`);
    
    return {
      totalProcessos: processosRegistreSe.length,
      processosComCliente,
      processosSemCliente: processosSemCliente.length,
      listaProcessosSemCliente: processosSemCliente
    };
    
  } catch (error: any) {
    console.error("❌ Erro durante a análise:", error.message);
    if (error.stack) {
      console.error("Stack trace:", error.stack);
    }
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Função principal
async function main() {
  console.log("🚀 ANÁLISE DE PROCESSOS REGISTRE-SE LTDA\n");
  
  try {
    const resultado = await analisarProcessosRegistreSe();
    
    console.log("\n✅ Análise concluída com sucesso!");
    
    if (resultado.processosSemCliente > 0) {
      console.log(`\n💡 Próximos passos sugeridos:`);
      console.log(`   • Revisar os ${resultado.processosSemCliente} processos sem cliente`);
      console.log(`   • Executar script de importação do CRM para estes processos`);
      console.log(`   • Verificar se existem leads no CRM para estes números`);
    }
    
  } catch (error: any) {
    console.error("❌ Erro durante a execução:", error.message);
    process.exit(1);
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  main();
}

export { analisarProcessosRegistreSe }; 