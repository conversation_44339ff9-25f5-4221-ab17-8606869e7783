import { Request, Response, NextFunction } from 'express';
import { obterKanbanPorPrazo } from '../../services/dashboard/processosDashboardService';

/**
 * Controller para obter processos agrupados por meses restantes até o mérito para visualização em kanban
 */
export const getKanbanPorPrazo = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extrair parâmetros da query
    const { 
      clienteId, 
      tipoProcesso, 
      elegivelParaComunicados,
      limite 
    } = req.query;

    // Transformar parâmetros para os tipos corretos
    const filtros = {
      clienteId: clienteId ? Number(clienteId) : undefined,
      tipoProcesso: tipoProcesso as string | undefined,
      elegivelParaComunicados: elegivelParaComunicados 
        ? elegivelParaComunicados === 'true' 
        : undefined,
      limite: limite ? Number(limite) : undefined
    };

    // Chamar o serviço para obter os dados do kanban
    const kanbanData = await obterKanbanPorPrazo(filtros);

    // Retornar os dados
    res.status(200).json({
      success: true,
      data: kanbanData
    });
  } catch (error) {
    console.error('Erro ao obter processos para kanban por prazo:', error);
    next(error);
  }
}; 