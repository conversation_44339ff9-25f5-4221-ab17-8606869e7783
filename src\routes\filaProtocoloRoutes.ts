import { Router } from 'express';
import {
  obterEstatisticasFila,
  listarProtocolosFila,
  reprocessarProtocolo,
  removerProtocoloFila,
  buscarStatusProtocolo,
  listarProtocolosRecentes,
  verificarFilaAtiva
} from '../controllers/filaProtocoloController';

const router = Router();

/**
 * @route GET /api/fila-protocolo/estatisticas
 * @desc Obter estatísticas da fila de processamento
 * @access Public
 */
router.get('/estatisticas', obterEstatisticasFila);

/**
 * @route GET /api/fila-protocolo/listar
 * @desc Listar protocolos na fila com filtros
 * @access Public
 */
router.get('/listar', listarProtocolosFila);

/**
 * @route GET /api/fila-protocolo/status/:numeroProcesso
 * @desc Buscar status de processamento de um protocolo específico
 * @access Public
 */
router.get('/status/:numeroProcesso', buscarStatusProtocolo);

/**
 * @route GET /api/fila-protocolo/recentes
 * @desc Listar protocolos processados recentemente (últimas 24h)
 * @access Public
 */
router.get('/recentes', listarProtocolosRecentes);

/**
 * @route GET /api/fila-protocolo/fila-ativa
 * @desc Verificar se há protocolos pendentes ou em processamento
 * @access Public
 */
router.get('/fila-ativa', verificarFilaAtiva);

/**
 * @route POST /api/fila-protocolo/:id/reprocessar
 * @desc Reprocessar um protocolo específico
 * @access Public
 */
router.post('/:id/reprocessar', reprocessarProtocolo);

/**
 * @route DELETE /api/fila-protocolo/:id
 * @desc Remover um protocolo da fila
 * @access Public
 */
router.delete('/:id', removerProtocoloFila);

export default router; 