import { Request, Response, NextFunction } from 'express';
import { obterClienteProcessosPorIdc, getIdentificadorPorNumeroProcesso } from '../services/clienteService';

/**
 * Controller para obter cliente por IDC (últimos 8 dígitos do telefone) e seus processos
 */
export const getClienteProcessosByIdc = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Obter o IDC da rota
    const { idc } = req.params;

    if (!idc || idc.length < 8) {
      res.status(400).json({
        success: false,
        message: 'IDC inválido. Deve conter pelo menos 8 dígitos.'
      });
      return;
    }

    // Buscar cliente e processos pelo serviço
    const clienteProcessos = await obterClienteProcessosPorIdc(idc);

    if (!clienteProcessos) {
      res.status(404).json({
        success: false,
        message: 'Cliente não encontrado com o IDC fornecido.'
      });
      return;
    }

    // Retornar os dados
    res.status(200).json({
      success: true,
      data: clienteProcessos
    });
  } catch (error) {
    console.error('Erro ao obter cliente e processos por IDC:', error);
    next(error);
  }
};

/**
 * Controlador para lidar com a requisição GET /cliente/identificador-por-processo/:numeroProcesso
 */
export async function handleGetIdentificadorPorNumeroProcesso(req: Request, res: Response): Promise<any> {
  const { numeroProcesso } = req.params;

  if (!numeroProcesso) {
    return res.status(400).json({ error: 'Parâmetro numeroProcesso não fornecido na URL.' });
  }

  try {
    const resultado = await getIdentificadorPorNumeroProcesso(numeroProcesso as string);

    if (resultado.error) {
      // Determinar o status code baseado no tipo de erro
      if (resultado.error.includes('não encontrado')) {
        return res.status(404).json(resultado);
      }
      if (resultado.error.includes('não fornecido ou inválido')) {
        return res.status(400).json(resultado);
      }
      if (resultado.error.includes('não possui um identificador válido') || resultado.error.includes('não está associado a nenhum cliente')) {
        return res.status(404).json(resultado);
      }
      // Erro interno genérico
      return res.status(500).json(resultado);
    }

    return res.status(200).json(resultado);

  } catch (error) {
    // Este catch é para erros inesperados na camada do controlador ou no serviço que não foram tratados
    console.error(`Erro inesperado no controlador ao buscar identificador para o processo "${numeroProcesso}":`, error);
    return res.status(500).json({ error: 'Erro interno do servidor ao processar a solicitação.' });
  }
} 