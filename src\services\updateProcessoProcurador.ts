import { PrismaClient } from "@prisma/client";
import { readdir, readFile } from "fs/promises";
import path from "path";
import fs from "fs";

const prisma = new PrismaClient({
  // Aumentando o pool de conexões para suportar mais operações paralelas
  log: ['warn', 'error'],
});

const ERROR_LOG_FILE = path.join(__dirname, "procurador_update_error.log");
const BATCH_SIZE = 300; // Tamanho do lote para processamento em paralelo
const CONCURRENT_FILES = 2; // Número de arquivos processados simultaneamente
const MAX_RETRIES = 3;
const INITIAL_RETRY_DELAY = 1000;

// Função para registrar erros em um arquivo de log
async function logError(message: string): Promise<void> {
  const timestamp = new Date().toISOString();
  await fs.promises.appendFile(ERROR_LOG_FILE, `[${timestamp}] ${message}\n`);
}

// Função auxiliar para dividir array em chunks
function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

// Função de retry com backoff exponencial
async function retryOperation<T>(
  operation: () => Promise<T>,
  retries = MAX_RETRIES,
  delay = INITIAL_RETRY_DELAY
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    if (retries > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
      return retryOperation(operation, retries - 1, delay * 2);
    }
    throw error;
  }
}

// Função para extrair o número da RPI do nome do arquivo
function getRpiNumberFromFilename(filename: string): number | null {
  const match = filename.match(/merged_(\d+)\.json$/);
  return match ? parseInt(match[1]) : null;
}

// Função para atualizar um lote de processos de uma vez
async function updateProcessosBatch(updates: { numeroProcesso: string; nomeProcurador: string }[]): Promise<void> {
  try {
    // Primeiro, vamos fazer upsert de todos os procuradores únicos do lote
    const uniqueProcuradores = [...new Set(updates.map(u => u.nomeProcurador))];
    const procuradores = await Promise.all(
      uniqueProcuradores.map(nome =>
        retryOperation(() =>
          prisma.procurador.upsert({
            where: { nome },
            update: { nome },
            create: { nome },
          })
        )
      )
    );

    // Criar um mapa de nome -> id do procurador para referência rápida
    const procuradorMap = new Map(
      procuradores.map(p => [p.nome, p.id])
    );

    // Dividir as atualizações em sub-lotes para evitar timeout
    const subBatchSize = 1000;
    const updateBatches = chunkArray(updates, subBatchSize);

    for (const batch of updateBatches) {
      await retryOperation(() =>
        prisma.$transaction(
          batch.map(({ numeroProcesso, nomeProcurador }) =>
            prisma.processo.update({
              where: { numero: numeroProcesso },
              data: {
                procurador: {
                  connect: { id: procuradorMap.get(nomeProcurador)! }
                }
              }
            })
          )
        )
      );
    }

    console.log(`Lote de ${updates.length} processos atualizado com sucesso`);
  } catch (error: any) {
    const errorMessage = `Erro ao atualizar lote de processos: ${error.message}`;
    console.error(errorMessage);
    await logError(errorMessage);
    throw error; // Propaga o erro para permitir retry
  }
}

// Função para processar um arquivo JSON
async function processJsonFile(filePath: string): Promise<void> {
  try {
    const filename = path.basename(filePath);
    const rpiNumero = getRpiNumberFromFilename(filename);
    
    if (!rpiNumero) {
      console.log(`Não foi possível extrair o número da RPI do arquivo ${filename}`);
      return;
    }

    console.log(`\nProcessando RPI ${rpiNumero} do arquivo: ${filename}`);

    // Primeiro busca todos os processos desta RPI no banco
    const processosNoBanco = await prisma.processo.findMany({
      where: {
        despachos: {
          some: {
            rpi: {
              numero: rpiNumero
            }
          }
        },
        procuradorId: null // Apenas processos sem procurador
      },
      select: {
        numero: true
      }
    });

    if (processosNoBanco.length === 0) {
      console.log(`Nenhum processo sem procurador encontrado para a RPI ${rpiNumero}`);
      return;
    }

    console.log(`Encontrados ${processosNoBanco.length} processos sem procurador na RPI ${rpiNumero}`);

    // Criar um Set dos números dos processos para busca rápida
    const numerosProcessos = new Set(processosNoBanco.map(p => p.numero));

    // Ler o arquivo JSON e encontrar os procuradores apenas para os processos que precisamos
    const fileContent = await readFile(filePath, "utf-8");
    const jsonData = JSON.parse(fileContent);
    
    const updates: { numeroProcesso: string; nomeProcurador: string }[] = [];
    
    if (Array.isArray(jsonData.processos)) {
      for (const processo of jsonData.processos) {
        if (processo.procurador && numerosProcessos.has(processo.numero)) {
          updates.push({
            numeroProcesso: processo.numero,
            nomeProcurador: processo.procurador
          });
        }
      }
    }

    if (updates.length === 0) {
      console.log(`Nenhum processo para atualizar na RPI ${rpiNumero}`);
      return;
    }

    console.log(`Encontrados ${updates.length} processos para atualizar na RPI ${rpiNumero}`);

    // Processar em lotes
    const batches = chunkArray(updates, BATCH_SIZE);
    for (const [index, batch] of batches.entries()) {
      await retryOperation(() => updateProcessosBatch(batch));
      console.log(`Lote ${index + 1}/${batches.length} da RPI ${rpiNumero} processado`);
    }

    console.log(`RPI ${rpiNumero} processada completamente`);
  } catch (error: any) {
    const errorMessage = `Erro ao processar arquivo ${filePath}: ${error.message}`;
    console.error(errorMessage);
    await logError(errorMessage);
  }
}

async function main() {
  console.log("Iniciando atualização de procuradores...");
  console.time("Tempo total de processamento");

  const directory = path.join(__dirname, "../../uploads/rpi/processed");
  const files = await readdir(directory);
  const jsonFiles = files.filter(f => f.endsWith(".json"));

  console.log(`\nEncontrados ${jsonFiles.length} arquivos JSON para processar`);
  console.log(`Configuração: BATCH_SIZE=${BATCH_SIZE}, CONCURRENT_FILES=${CONCURRENT_FILES}\n`);

  // Processar arquivos em lotes
  const fileChunks = chunkArray(jsonFiles, CONCURRENT_FILES);
  let processedFiles = 0;

  for (const [chunkIndex, chunk] of fileChunks.entries()) {
    console.log(`\nProcessando lote de arquivos ${chunkIndex + 1}/${fileChunks.length}`);
    
    await Promise.all(
      chunk.map(async (file) => {
        const filePath = path.join(directory, file);
        await processJsonFile(filePath);
        processedFiles += 1;
        const progress = Math.round((processedFiles / jsonFiles.length) * 100);
        console.log(`\nProgresso geral: ${progress}% (${processedFiles}/${jsonFiles.length} arquivos)`);
      })
    );
  }

  console.log("\nProcessamento concluído!");
  console.timeEnd("Tempo total de processamento");
  console.log(`Total de arquivos processados: ${processedFiles}`);
}

// Configurar listeners para graceful shutdown
process.on('SIGINT', async () => {
  console.log('\nRecebido SIGINT. Finalizando...');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\nRecebido SIGTERM. Finalizando...');
  await prisma.$disconnect();
  process.exit(0);
});

main()
  .catch(async (e) => {
    const errorMessage = `Erro crítico: ${e.message}`;
    console.error(errorMessage);
    await logError(errorMessage);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 