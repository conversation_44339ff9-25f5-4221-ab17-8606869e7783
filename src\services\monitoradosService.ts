import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export const buscaProcessosMonitorados = async () => {
  try {
    const processosMonitorados = await prisma.processo.findMany({
      where: {
        procurador:{
            nome:{
                contains:"REGISTRE-SE LTDA",
                mode:"insensitive"
            }
        }
      },
      select: {
        id: true,
        numero: true
      },
    });

    return processosMonitorados;
  } catch (error) {
    console.error("Erro ao buscar processos monitorados:", error);
    return null;
  }
};
