import {
  PrismaClient,
  Processo,
  <PERSON>pacho,
  RPI,
  Procurador,
} from "@prisma/client";
import { writeFileSync } from "fs";
import cliProgress from "cli-progress";
import { differenceInDays, isValid, parseISO } from "date-fns";
import { format as formatDate } from 'date-fns/format';

const prisma = new PrismaClient({
  log: ["warn", "error"], // Log apenas avisos e erros
});

const NOME_PROCURADOR_FILTRO = "REGISTRE-SE LTDA.";
const DATA_INICIO_FILTRO = new Date("2014-12-01T00:00:00.000Z");
const DATA_FIM_FILTRO = new Date("2020-04-22T00:00:00.000Z");
const TAMANHO_LOTE = 5000; // Ajuste conforme necessário para memória/performance

interface DespachoInfo {
  nome: string;
  codigosEncontrados: Set<string>;
  ultimoCodigoVisto: string | null;
  contagem: number;
  ultimaDataPublicacao: Date | null;
  somaTemposAposDepositoEmDias: number;
  contagemTemposValidos: number;
  minTempoAposDeposito: number | null;
  maxTempoAposDeposito: number | null;
  ocorrenciasAnteriores: { [nomeDespacho: string]: number };
  ocorrenciasPosteriores: { [nomeDespacho: string]: number };
}

interface ResultadoAnaliseGrupo {
  totalProcessosConsiderados: number;
  despachos: {
    [nomeDespacho: string]: DespachoInfo;
  };
}

type ProcessoComDespachosOrdenados = Processo & {
  despachos: (Despacho & { rpi: RPI })[];
  procurador: Procurador | null;
};

function formatarNomeDespacho(
  codigo: string | null,
  nome: string | null
): string {
  const c = codigo || "CODIGO_NULO";
  const n = nome || "NOME_NULO";
  return `${c.replace(/-/g, '_')}-${n.replace(/-/g, '_')}`;
}

// Função auxiliar para escapar campos CSV (coloca entre aspas se necessário)
function escapeCsvField(field: string | number | null | undefined): string {
    if (field === null || field === undefined) {
        return '""'; // Representa nulo/vazio como string vazia entre aspas
    }
    const stringField = String(field);
    // Se contém aspas, vírgula ou quebra de linha, coloca entre aspas e duplica as aspas internas
    if (stringField.includes('"') || stringField.includes(',') || stringField.includes('\n')) {
        return `"${stringField.replace(/"/g, '""')}"`;
    }
    // Caso contrário, apenas retorna o campo como string (sem aspas extras se não precisar)
    // Ou sempre colocar aspas? Para consistência, vamos sempre colocar aspas.
    return `"${stringField.replace(/"/g, '""')}"`;
}

// Função para obter a chave (nome) mais frequente
function getNomeMaisFrequente(ocorrencias: { [nomeDespacho: string]: number }): string | null {
    if (Object.keys(ocorrencias).length === 0) {
        return null;
    }

    let maisFrequenteKey: string | null = null;
    let maxCount = -1;

    for (const [key, count] of Object.entries(ocorrencias)) {
        if (count > maxCount) {
            maxCount = count;
            maisFrequenteKey = key;
        }
    }
    return maisFrequenteKey;
}

async function processarLote(
  processos: ProcessoComDespachosOrdenados[],
  analiseGrupo: ResultadoAnaliseGrupo
): Promise<void> {
  for (const processo of processos) {
    if (!processo.dataDeposito) continue; // Ignora processos sem data de depósito

    const dataDeposito = processo.dataDeposito;

    processo.despachos.forEach((despacho, index, arr) => {
      if (!despacho.nome || !despacho.codigo || !despacho.rpi?.dataPublicacao) {
        // console.warn(`Despacho inválido ou sem data RPI no processo ${processo.numero}:`, despacho);
        return; // Pula despachos inválidos
      }

      const nomeDespachoAtual = despacho.nome;
      const codigoDespachoAtual = despacho.codigo;
      const dataPublicacaoRpi = despacho.rpi.dataPublicacao;

      if (!analiseGrupo.despachos[nomeDespachoAtual]) {
        analiseGrupo.despachos[nomeDespachoAtual] = {
          nome: nomeDespachoAtual,
          codigosEncontrados: new Set<string>(),
          ultimoCodigoVisto: null,
          contagem: 0,
          ultimaDataPublicacao: null,
          somaTemposAposDepositoEmDias: 0,
          contagemTemposValidos: 0,
          minTempoAposDeposito: null,
          maxTempoAposDeposito: null,
          ocorrenciasAnteriores: {},
          ocorrenciasPosteriores: {},
        };
      }

      const info = analiseGrupo.despachos[nomeDespachoAtual];
      info.contagem++;
      info.codigosEncontrados.add(codigoDespachoAtual);

      if (
        !info.ultimaDataPublicacao ||
        dataPublicacaoRpi > info.ultimaDataPublicacao
      ) {
        info.ultimaDataPublicacao = dataPublicacaoRpi;
        info.ultimoCodigoVisto = codigoDespachoAtual;
      }

      if (isValid(dataDeposito) && isValid(dataPublicacaoRpi)) {
        const diff = differenceInDays(dataPublicacaoRpi, dataDeposito);
        if (diff >= 0) {
          info.somaTemposAposDepositoEmDias += diff;
          info.contagemTemposValidos++;
          if (info.minTempoAposDeposito === null || diff < info.minTempoAposDeposito) {
            info.minTempoAposDeposito = diff;
          }
          if (info.maxTempoAposDeposito === null || diff > info.maxTempoAposDeposito) {
            info.maxTempoAposDeposito = diff;
          }
        }
      }

      const despachoAnterior = index > 0 ? arr[index - 1] : null;
      const nomeAnteriorKey = despachoAnterior?.nome ?? "INICIO";
      if (nomeAnteriorKey) {
        info.ocorrenciasAnteriores[nomeAnteriorKey] =
          (info.ocorrenciasAnteriores[nomeAnteriorKey] || 0) + 1;
      }

      const despachoPosterior = index < arr.length - 1 ? arr[index + 1] : null;
      const nomePosteriorKey = despachoPosterior?.nome ?? "FIM";
      if (nomePosteriorKey) {
        info.ocorrenciasPosteriores[nomePosteriorKey] =
          (info.ocorrenciasPosteriores[nomePosteriorKey] || 0) + 1;
      }
    });
  }
}

async function analisarDespachos(
  filtroProcurador: boolean
): Promise<ResultadoAnaliseGrupo> {
  const whereClause: any = {
    dataDeposito: {
      gte: DATA_INICIO_FILTRO,
      lte: DATA_FIM_FILTRO,
    },
    numero: {
        not: {
            startsWith: 'TESTE-'
        }
    }
  };

  if (filtroProcurador) {
    whereClause.procurador = {
      nome: NOME_PROCURADOR_FILTRO,
    };
  }

  console.log(
    `\nIniciando análise para: ${
      filtroProcurador
        ? `Procurador "${NOME_PROCURADOR_FILTRO}"`
        : "Todos os Processos (exceto TESTE-)"
    }`
  );
  console.log(
    `Filtro de Data Depósito: >= ${DATA_INICIO_FILTRO.toISOString()}`
  );
  console.log("Excluindo processos com número iniciando em 'TESTE-'");

  const totalProcessos = await prisma.processo.count({ where: whereClause });
  console.log(`Total de processos a analisar neste grupo: ${totalProcessos}`);

  if (totalProcessos === 0) {
    console.log(
      "Nenhum processo encontrado para este grupo com os filtros aplicados."
    );
    return { totalProcessosConsiderados: 0, despachos: {} };
  }

  const barraProgresso = new cliProgress.SingleBar({
    format:
      "Análise em andamento |{bar}| {percentage}% | {value}/{total} Processos | ETA: {eta}s",
    barCompleteChar: "\u2588",
    barIncompleteChar: "\u2591",
    hideCursor: true,
  });

  barraProgresso.start(totalProcessos, 0);

  const analiseGrupo: ResultadoAnaliseGrupo = {
    totalProcessosConsiderados: totalProcessos,
    despachos: {},
  };

  let processedCount = 0;
  while (processedCount < totalProcessos) {
    const processosLote = await prisma.processo.findMany({
      where: whereClause,
      include: {
        procurador: true, // Mesmo que filtrado, incluímos para consistência da tipagem
        despachos: {
          include: {
            rpi: true,
          },
          orderBy: {
            rpi: {
              dataPublicacao: "asc",
            },
          },
        },
      },
      skip: processedCount,
      take: TAMANHO_LOTE,
    });

    if (processosLote.length === 0) {
      // Segurança caso a contagem mude durante o processo
      break;
    }

    await processarLote(
      processosLote as ProcessoComDespachosOrdenados[],
      analiseGrupo
    );
    processedCount += processosLote.length;
    barraProgresso.update(processedCount);
  }

  barraProgresso.stop();
  console.log(
    `Análise do grupo concluída. ${
      Object.keys(analiseGrupo.despachos).length
    } tipos de despachos únicos (por nome) encontrados.`
  );
  return analiseGrupo;
}

async function gerarCsv(
    resultadosTodos: ResultadoAnaliseGrupo,
    resultadosProcurador: ResultadoAnaliseGrupo,
    nomeArquivo: string
): Promise<void> {
    const header = [
        "Grupo",
        "NomeDespacho",
        "UltimoCodigoVisto",
        "CodigosAssociados",
        "ContagemOcorrencias",
        "UltimaDataPublicacao",
        "TempoMinAposDepositoDias",
        "TempoMaxAposDepositoDias",
        "TempoMediaAposDepositoDias",
        "NomeDespachoAnteriorMaisFrequente",
        "TodosNomesDespachosAnterioresImediatos",
        "NomeDespachoPosteriorMaisFrequente",
        "TodosNomesDespachosPosterioresImediatos"
    ].map(escapeCsvField).join(',');

    const linhasCsv: string[] = [header];

    const processarGrupoParaCsv = (grupo: ResultadoAnaliseGrupo, nomeGrupo: string) => {
         Object.values(grupo.despachos)
            .sort((a, b) => b.contagem - a.contagem)
            .forEach(info => {
                const mediaDias = info.contagemTemposValidos > 0
                    ? Math.round(info.somaTemposAposDepositoEmDias / info.contagemTemposValidos)
                    : null;

                const anteriorMaisFrequente = getNomeMaisFrequente(info.ocorrenciasAnteriores);
                const posteriorMaisFrequente = getNomeMaisFrequente(info.ocorrenciasPosteriores);
                const codigosAssociadosStr = Array.from(info.codigosEncontrados).join('; ');

                const todosAnterioresStr = Object.keys(info.ocorrenciasAnteriores).sort().join('; ');
                const todosPosterioresStr = Object.keys(info.ocorrenciasPosteriores).sort().join('; ');

                const linha = [
                    nomeGrupo,
                    info.nome,
                    info.ultimoCodigoVisto,
                    codigosAssociadosStr,
                    info.contagem,
                    info.ultimaDataPublicacao ? formatDate(info.ultimaDataPublicacao, 'yyyy-MM-dd') : null,
                    info.minTempoAposDeposito,
                    info.maxTempoAposDeposito,
                    mediaDias,
                    anteriorMaisFrequente,
                    todosAnterioresStr,
                    posteriorMaisFrequente,
                    todosPosterioresStr
                ].map(escapeCsvField).join(',');
                linhasCsv.push(linha);
            });
    };

    console.log('\nFormatando dados para CSV...');
    processarGrupoParaCsv(resultadosTodos, 'Todos (Exceto TESTE-)');
    processarGrupoParaCsv(resultadosProcurador, `Procurador ${NOME_PROCURADOR_FILTRO} (Exceto TESTE-)`);

    try {
        writeFileSync(nomeArquivo, linhasCsv.join('\n'), { encoding: 'utf-8' });
        console.log(`\nResultados completos salvos em formato CSV: ${nomeArquivo}`);
    } catch (error) {
        console.error(`Erro ao salvar o arquivo CSV ${nomeArquivo}:`, error);
    }
}

async function main() {
  try {
    console.log("Iniciando script de análise de despachos...");

    const resultadosTodos = await analisarDespachos(false);
    const resultadosProcurador = await analisarDespachos(true);

    const dataHora = new Date().toISOString().replace(/[:.]/g, "-");
    const nomeArquivoCsv = `analise-despachos-${dataHora}.csv`;

    await gerarCsv(resultadosTodos, resultadosProcurador, nomeArquivoCsv);

    console.log('\n\n=== Resumo da Análise ===');
    console.log(`Análise concluída em: ${new Date().toISOString()}`);
    console.log(`Filtro de data de depósito: >= ${DATA_INICIO_FILTRO.toISOString()}`);
    console.log("Processos com número iniciando em 'TESTE-' foram excluídos.");
    console.log("Análise agregada por NOME do despacho.");

    console.log(`\n--- Grupo: Todos os Processos (Exceto TESTE-) ---`);
    console.log(`Total de processos considerados: ${resultadosTodos.totalProcessosConsiderados}`);
    console.log(`Total de nomes de despachos únicos: ${Object.keys(resultadosTodos.despachos).length}`);

    console.log(`\n--- Grupo: Procurador "${NOME_PROCURADOR_FILTRO}" (Exceto TESTE-) ---`);
    console.log(`Total de processos considerados: ${resultadosProcurador.totalProcessosConsiderados}`);
    console.log(`Total de nomes de despachos únicos: ${Object.keys(resultadosProcurador.despachos).length}`);

  } catch (error) {
    console.error("\nErro durante a execução do script:", error);
  } finally {
    await prisma.$disconnect();
    console.log("\nConexão com o banco de dados fechada.");
  }
}

if (require.main === module) {
  main();
}
