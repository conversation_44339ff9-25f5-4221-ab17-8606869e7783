import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
import cliProgress from "cli-progress";
import axios from "axios";
import fs from "fs";

dotenv.config();

const prisma = new PrismaClient();

// Controle de taxa para API do CRM (reutilizando a mesma lógica)
class RateLimiter {
  private queue: (() => Promise<void>)[] = [];
  private running = false;
  private requestCount = 0;
  private resetTime = Date.now() + 30000; // 30 segundos
  private maxRequests: number;
  private timeWindow: number;

  constructor(maxRequests = 120, timeWindow = 30000) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindow;
  }

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await fn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      if (!this.running) {
        this.processQueue();
      }
    });
  }

  private async processQueue() {
    if (this.queue.length === 0) {
      this.running = false;
      return;
    }

    this.running = true;

    // Verificar se precisamos resetar o contador
    const now = Date.now();
    if (now >= this.resetTime) {
      this.requestCount = 0;
      this.resetTime = now + this.timeWindow;
    }

    // Verificar se atingimos o limite
    if (this.requestCount >= this.maxRequests) {
      const waitTime = this.resetTime - now;
      console.log(`Limite de requisições atingido. Aguardando ${waitTime / 1000} segundos...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      this.requestCount = 0;
      this.resetTime = Date.now() + this.timeWindow;
    }

    // Executar a próxima função na fila
    const nextFn = this.queue.shift();
    if (nextFn) {
      this.requestCount++;
      await nextFn();
    }

    // Processar o próximo item na fila
    this.processQueue();
  }
}

const rateLimiter = new RateLimiter(120, 30000);

// Função auxiliar para extrair DDD + últimos 8 dígitos do telefone
function extrairIdentificadorTelefone(telefone: string): string {
  if (!telefone || telefone === "NoPhone") {
    return "0000000000"; // Retorna 10 dígitos zerados quando não há telefone
  }

  // Remove todos os caracteres não numéricos
  const numeroLimpo = telefone.replace(/\D/g, '');
  
  // Se o número estiver vazio após limpeza
  if (!numeroLimpo) {
    return "0000000000";
  }

  let numeroProcessado = numeroLimpo;

  // Lista de DDDs válidos no Brasil (11-99, excluindo alguns não utilizados)
  const dddsValidos = new Set([
    11, 12, 13, 14, 15, 16, 17, 18, 19, // SP
    21, 22, 24, // RJ/ES
    27, 28, // ES
    31, 32, 33, 34, 35, 37, 38, // MG
    41, 42, 43, 44, 45, 46, // PR
    47, 48, 49, // SC
    51, 53, 54, 55, // RS
    61, // DF/GO
    62, 64, // GO
    63, // TO
    65, 66, // MT
    67, // MS
    68, // AC
    69, // RO
    71, 73, 74, 75, 77, // BA
    79, // SE
    81, 87, // PE
    82, // AL
    83, // PB
    84, // RN
    85, 88, // CE
    86, 89, // PI
    91, 93, 94, // PA
    92, 97, // AM
    95, // RR
    96, // AP
    98, 99 // MA
  ]);

  // Lógica melhorada para detecção de prefixo do país
  if (numeroProcessado.startsWith('55') && numeroProcessado.length >= 12) {
    // Verifica se após remover 55, temos um número válido brasileiro
    const semPrefixo = numeroProcessado.substring(2);
    
    // Número brasileiro tem 10 ou 11 dígitos (DDD + 8 ou 9 dígitos)
    if (semPrefixo.length === 10 || semPrefixo.length === 11) {
      // Verifica se o DDD é válido
      const possibleDDD = parseInt(semPrefixo.substring(0, 2));
      if (dddsValidos.has(possibleDDD)) {
        numeroProcessado = semPrefixo;
      }
    }
  }

  // Se ainda não temos pelo menos 10 dígitos, preenche com zeros
  if (numeroProcessado.length < 10) {
    numeroProcessado = numeroProcessado.padStart(10, '0');
  }

  // Extrai DDD (primeiros 2 dígitos) e últimos 8 dígitos
  const ddd = numeroProcessado.substring(0, 2);
  const ultimosOitoDigitos = numeroProcessado.slice(-8);
  
  return ddd + ultimosOitoDigitos;
}

// Função para analisar telefones detalhadamente
function analisarTelefones(contactPhones: any[]): {
  telefonePrincipal: string | null;
  telefoneSecundario: string | null;
  todosOsTelefones: any[];
  identificadorGerado: string;
  problemas: string[];
} {
  const problemas: string[] = [];
  const todosOsTelefones = contactPhones || [];
  
  if (!contactPhones || contactPhones.length === 0) {
    problemas.push("Nenhum telefone encontrado no array contactPhones");
    return {
      telefonePrincipal: null,
      telefoneSecundario: null,
      todosOsTelefones: [],
      identificadorGerado: "0000000000",
      problemas
    };
  }

  // Buscar telefone principal
  let telefonePrincipal = contactPhones.find((p: any) => p.is_main === 1)?.phone;
  if (!telefonePrincipal) {
    telefonePrincipal = contactPhones[0]?.phone;
    problemas.push("Nenhum telefone marcado como principal (is_main=1), usando o primeiro disponível");
  }

  if (!telefonePrincipal) {
    problemas.push("Telefone principal está vazio ou null");
    return {
      telefonePrincipal: null,
      telefoneSecundario: null,
      todosOsTelefones,
      identificadorGerado: "0000000000",
      problemas
    };
  }

  // Buscar telefone secundário
  const telefonesSecundarios = contactPhones.filter((p: any) => p.is_main !== 1);
  const telefoneSecundario = telefonesSecundarios[0]?.phone || null;

  // Gerar identificador
  const identificadorGerado = extrairIdentificadorTelefone(telefonePrincipal);
  
  if (identificadorGerado === "0000000000") {
    problemas.push(`Não foi possível gerar identificador válido do telefone: ${telefonePrincipal}`);
  }

  return {
    telefonePrincipal,
    telefoneSecundario,
    todosOsTelefones,
    identificadorGerado,
    problemas
  };
}

interface ProcessoCrm {
  id: string;
  numeroProcesso: string;
  person?: {
    name?: string;
    cpf?: string;
    contactPhones?: Array<{
      phone: string;
      is_main: number;
    }>;
    contactEmails?: Array<{
      email: string;
    }>;
    address?: string;
    address_number?: string;
    address_postal_code?: string;
    district?: string;
  };
  stage_id: number;
  customFields?: Array<{
    id: number;
    value: string;
  }>;
}

interface ProcessoBanco {
  id: number;
  numero: string;
  cliente?: {
    id: number;
    nome: string;
    identificador: string;
  } | null;
}

// Função para buscar todos os deals do CRM com paginação
async function buscarTodosDealsComProcesso(): Promise<ProcessoCrm[]> {
  console.log("🔍 Buscando todos os deals do pipeline no CRM...");
  
  const crmToken = process.env.CRM_TOKEN || "";
  const pipelineId = 56897;
  const idCampoPersonalizadoNumProcesso = 194250;
  const itensPorPagina = 200;
  
  let todosDeals: ProcessoCrm[] = [];
  let paginaAtual = 1;
  let totalPaginas = 1;
  let tentativasErro = 0;
  const maxTentativas = 3;
  
  const progressBar = new cliProgress.SingleBar({
    format: "Buscando deals |{bar}| {percentage}% || Página {value}/{total} | Total: {custom_total}",
    barCompleteChar: "\u2588",
    barIncompleteChar: "\u2591",
    hideCursor: true,
  });

  try {
    do {
      try {
        const response = await rateLimiter.execute(async () => {
          console.log(`\nBuscando página ${paginaAtual}/${totalPaginas}...`);
          
          const url = `https://api.pipe.run/v1/deals?show=${itensPorPagina}&with=customFields,person.contactPhones,person.contactEmails&pipeline_id=${pipelineId}&page=${paginaAtual}`;
          
          const { data } = await axios.get(url, {
            headers: {
              token: crmToken,
            },
            timeout: 45000, // 45 segundos de timeout
          });
          
          if (!data || !data.data) {
            throw new Error(`Resposta da API inválida`);
          }
          
          return data;
        });

        // Resetar contador de tentativas em caso de sucesso
        tentativasErro = 0;

        if (paginaAtual === 1) {
          // Extrair informações de paginação da meta
          const meta = response.meta || {};
          totalPaginas = meta.total_pages || 1;
          const totalItens = meta.total || 0;
          
          console.log(`\n📊 Informações do CRM:`);
          console.log(`   • Total de deals no pipeline: ${totalItens}`);
          console.log(`   • Total de páginas: ${totalPaginas}`);
          console.log(`   • Itens por página: ${itensPorPagina}`);
          
          progressBar.start(totalPaginas, 0, {
            custom_total: 0
          });
        }

        const deals = response.data || [];
        
        // Filtrar apenas deals que possuem número de processo no campo personalizado
        const dealsComProcesso = deals.filter((deal: any) => {
          // Buscar no array de customFields pelo campo com ID 194250
          const campoProcesso = deal.customFields?.find(
            (field: any) => field.id === idCampoPersonalizadoNumProcesso
          );
          
          // Verificar se o campo existe e tem um valor válido
          const numeroProcesso = campoProcesso?.value || campoProcesso?.raw_value;
          return numeroProcesso && String(numeroProcesso).trim() !== '';
        }).map((deal: any) => {
          // Extrair o número do processo do campo personalizado
          const campoProcesso = deal.customFields?.find(
            (field: any) => field.id === idCampoPersonalizadoNumProcesso
          );
          const numeroProcesso = campoProcesso?.value || campoProcesso?.raw_value || '';
          
          return {
            id: deal.id,
            numeroProcesso: String(numeroProcesso).trim(),
            person: deal.person,
            stage_id: deal.stage_id,
            customFields: deal.customFields
          };
        });

        todosDeals.push(...dealsComProcesso);
        
        progressBar.increment(1, {
          custom_total: todosDeals.length
        });
        
        console.log(`   • Deals encontrados nesta página: ${deals.length}`);
        console.log(`   • Deals com processo nesta página: ${dealsComProcesso.length}`);
        console.log(`   • Total acumulado com processo: ${todosDeals.length}`);
        
        paginaAtual++;
        
      } catch (error: any) {
        tentativasErro++;
        console.error(`❌ Erro na página ${paginaAtual} (tentativa ${tentativasErro}/${maxTentativas}): ${error.message}`);
        
        if (tentativasErro >= maxTentativas) {
          throw new Error(`Falha ao buscar página ${paginaAtual} após ${maxTentativas} tentativas: ${error.message}`);
        }
        
        // Aguardar antes de tentar novamente
        const tempoEspera = Math.min(Math.pow(2, tentativasErro) * 1000, 15000);
        console.log(`   ⏳ Aguardando ${tempoEspera/1000} segundos antes de tentar novamente...`);
        await new Promise(resolve => setTimeout(resolve, tempoEspera));
      }
      
    } while (paginaAtual <= totalPaginas);
    
    progressBar.stop();
    console.log(`\n✅ Busca concluída!`);
    console.log(`   📊 Total de deals com processo encontrados: ${todosDeals.length}`);
    console.log(`   📄 Pipeline analisado: ${pipelineId}`);
    
    return todosDeals;
    
  } catch (error: any) {
    progressBar.stop();
    console.error("❌ Erro crítico ao buscar deals do CRM:", error.message);
    
    // Log adicional para debug
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Data: ${JSON.stringify(error.response.data)}`);
    }
    
    throw error;
  }
}

// Função para buscar processos específicos do banco baseado nos números do CRM
async function buscarProcessosEspecificosNoBanco(numerosProcessosCrm: string[]): Promise<any[]> {
  console.log(`🔍 Buscando ${numerosProcessosCrm.length} processos específicos no banco de dados...`);
  
  try {
    // Buscar apenas os processos que estão no CRM
    const processos = await prisma.processo.findMany({
      where: {
        numero: {
          in: numerosProcessosCrm
        }
      },
      select: {
        id: true,
        numero: true,
        clienteId: true,
        cliente: {
          select: {
            id: true,
            nome: true,
            identificador: true
          }
        }
      }
    });
    
    console.log(`✅ Encontrados ${processos.length} processos no banco dos ${numerosProcessosCrm.length} do CRM`);
    
    return processos;
    
  } catch (error: any) {
    console.error("❌ Erro ao buscar processos específicos do banco:", error.message);
    throw error;
  }
}

// Função para criar/vincular cliente (baseada no obterLeadsCrm.ts)
async function criarVincularCliente(processoCrm: ProcessoCrm, processoId: string): Promise<{
  sucesso: boolean;
  motivo: string;
  dadosDebug?: any;
}> {
  try {
    const { customFields, person } = processoCrm;
    
    // Analisar telefones detalhadamente
    const analisesTelefones = analisarTelefones(person?.contactPhones || []);
    
    // Obter e-mail principal (se houver)
    const emails = person?.contactEmails || [];
    const emailPrincipal = emails[0]?.email || null;

    // Verificar se tem telefone válido
    if (!analisesTelefones.telefonePrincipal || analisesTelefones.telefonePrincipal === "NoPhone") {
      return {
        sucesso: false,
        motivo: "Telefone principal inválido",
        dadosDebug: {
          leadId: processoCrm.id,
          numeroProcesso: processoCrm.numeroProcesso,
          nomeCliente: person?.name,
          analisesTelefones,
          person: person
        }
      };
    }

    // Verificar se o identificador foi gerado corretamente
    if (analisesTelefones.identificadorGerado === "0000000000") {
      return {
        sucesso: false,
        motivo: "Identificador inválido gerado",
        dadosDebug: {
          leadId: processoCrm.id,
          numeroProcesso: processoCrm.numeroProcesso,
          nomeCliente: person?.name,
          analisesTelefones,
          person: person
        }
      };
    }

    // Criar objeto cliente
    const clienteData = {
      crmId: parseInt(processoCrm.id),
      nome: person?.name || "Nome não informado",
      crmStageId: processoCrm.stage_id.toString(),
      nomeDaMarca: customFields?.find((field: any) => field.id === 213657)?.value || "Marca não informada",
      identificador: analisesTelefones.identificadorGerado,
      numeroDocumento: person?.cpf || "CPF não informado",
      tipoDeDocumento: "CPF",
    };

    // Verificar se já existe cliente com este identificador ou documento
    let clienteExistente = await prisma.cliente.findFirst({
      where: { identificador: clienteData.identificador },
      include: { processos: true, contatos: true }
    });

    if (!clienteExistente && clienteData.numeroDocumento && clienteData.numeroDocumento !== "CPF não informado") {
      clienteExistente = await prisma.cliente.findFirst({
        where: { numeroDocumento: clienteData.numeroDocumento },
        include: { processos: true, contatos: true }
      });
    }

    const taxaConcessaoPaga = clienteData.crmStageId === "280246";
    
    if (clienteExistente) {
      // Cliente encontrado - conectar o processo
      await prisma.cliente.update({
        where: { id: clienteExistente.id },
        data: {
          nome: clienteData.nome,
          crmStageId: clienteData.crmStageId,
          nomeDaMarca: clienteData.nomeDaMarca,
          // Atualizar documento se o novo for mais completo
          numeroDocumento: (clienteData.numeroDocumento && clienteData.numeroDocumento !== "CPF não informado") 
                           ? clienteData.numeroDocumento 
                           : clienteExistente.numeroDocumento,
          tipoDeDocumento: (clienteData.numeroDocumento && clienteData.numeroDocumento !== "CPF não informado") 
                           ? clienteData.tipoDeDocumento 
                           : clienteExistente.tipoDeDocumento,
          // Atualizar identificador se estava null
          identificador: clienteExistente.identificador || clienteData.identificador,
          // Conectar o processo
          processos: {
            connect: { id: processoId }
          },
          // Adicionar novo contato se não existir um com o mesmo telefone
          ...(clienteExistente.contatos.find(c => c.telefone === analisesTelefones.telefonePrincipal) ? {} : {
            contatos: {
              create: {
                telefone: analisesTelefones.telefonePrincipal,
                telefoneSegundario: analisesTelefones.telefoneSecundario || "Não informado",
                endereco: person?.address ? `${person.address} ${person.address_number || ''}` : "Endereço não informado",
                cep: person?.address_postal_code || "CEP não informado",
                email: emailPrincipal || "Email não informado",
                cidade: person?.district || null,
                estado: null
              }
            }
          })
        }
      });
    } else {
      // Cliente não encontrado - criar novo
      await prisma.cliente.create({
        data: {
          crmId: clienteData.crmId,
          nome: clienteData.nome,
          crmStageId: clienteData.crmStageId,
          nomeDaMarca: clienteData.nomeDaMarca,
          identificador: clienteData.identificador,
          numeroDocumento: clienteData.numeroDocumento,
          tipoDeDocumento: clienteData.tipoDeDocumento,
          contatos: {
            create: {
              telefone: analisesTelefones.telefonePrincipal,
              telefoneSegundario: analisesTelefones.telefoneSecundario || "Não informado",
              endereco: person?.address ? `${person.address} ${person.address_number || ''}` : "Endereço não informado",
              cep: person?.address_postal_code || "CEP não informado",
              email: emailPrincipal || "Email não informado",
              cidade: person?.district || null,
              estado: null
            }
          },
          processos: {
            connect: { id: processoId }
          }
        }
      });
    }

    // Atualizar o status do processo
    await prisma.processo.update({
      where: { id: processoId },
      data: { taxaConcessaoPaga }
    });

    return {
      sucesso: true,
      motivo: clienteExistente ? "Cliente existente atualizado" : "Novo cliente criado"
    };
    
  } catch (error: any) {
    console.error(`❌ Erro ao criar/vincular cliente para lead ${processoCrm.id}:`, error.message);
    return {
      sucesso: false,
      motivo: `Erro no banco de dados: ${error.message}`,
      dadosDebug: {
        leadId: processoCrm.id,
        numeroProcesso: processoCrm.numeroProcesso,
        nomeCliente: processoCrm.person?.name,
        erro: error.message,
        person: processoCrm.person
      }
    };
  }
}

// Função para vincular processos sem cliente automaticamente
async function vincularProcessosSemCliente(processosComCrmSemCliente: Array<{
  processosBanco: any;
  processosCrm: ProcessoCrm;
}>): Promise<{
  vinculados: number;
  semTelefone: number;
  identificadorInvalido: number;
  erros: number;
  problemasDetalhados: any[];
}> {
  if (processosComCrmSemCliente.length === 0) {
    console.log("✅ Nenhum processo sem cliente encontrado para vincular.");
    return { 
      vinculados: 0, 
      semTelefone: 0, 
      identificadorInvalido: 0, 
      erros: 0, 
      problemasDetalhados: [] 
    };
  }

  console.log(`\n🔗 Iniciando vinculação automática de ${processosComCrmSemCliente.length} processos sem cliente...`);
  
  let vinculados = 0;
  let semTelefone = 0;
  let identificadorInvalido = 0;
  let erros = 0;
  const problemasDetalhados: any[] = [];
  
  const progressBar = new cliProgress.SingleBar({
    format: "Vinculando |{bar}| {percentage}% || {value}/{total} | ✅ {custom_vinculados} | 📞 {custom_sem_telefone} | 🔢 {custom_identificador} | ❌ {custom_erros}",
    barCompleteChar: "\u2588",
    barIncompleteChar: "\u2591",
    hideCursor: true,
  });
  
  progressBar.start(processosComCrmSemCliente.length, 0, {
    custom_vinculados: 0,
    custom_sem_telefone: 0,
    custom_identificador: 0,
    custom_erros: 0
  });
  
  for (const item of processosComCrmSemCliente) {
    try {
      const resultado = await criarVincularCliente(item.processosCrm, item.processosBanco.id);
      
      if (resultado.sucesso) {
        vinculados++;
      } else {
        // Categorizar o problema
        if (resultado.motivo.includes("Telefone principal inválido")) {
          semTelefone++;
        } else if (resultado.motivo.includes("Identificador inválido")) {
          identificadorInvalido++;
        } else {
          erros++;
        }
        
        // Adicionar aos problemas detalhados
        problemasDetalhados.push({
          processoId: item.processosBanco.id,
          numeroProcesso: item.processosBanco.numero,
          dealId: item.processosCrm.id,
          motivo: resultado.motivo,
          dadosDebug: resultado.dadosDebug
        });
      }
      
    } catch (error: any) {
      console.error(`❌ Erro ao processar processo ${item.processosBanco.numero}:`, error.message);
      erros++;
      
      problemasDetalhados.push({
        processoId: item.processosBanco.id,
        numeroProcesso: item.processosBanco.numero,
        dealId: item.processosCrm.id,
        motivo: `Erro inesperado: ${error.message}`,
        dadosDebug: {
          erro: error.message,
          person: item.processosCrm.person
        }
      });
    }
    
    progressBar.increment(1, {
      custom_vinculados: vinculados,
      custom_sem_telefone: semTelefone,
      custom_identificador: identificadorInvalido,
      custom_erros: erros
    });
  }
  
  progressBar.stop();
  
  console.log(`\n🎯 RESULTADO DA VINCULAÇÃO:`);
  console.log(`   ✅ Processos vinculados com sucesso: ${vinculados}`);
  console.log(`   📞 Processos sem telefone válido: ${semTelefone}`);
  console.log(`   🔢 Processos com identificador inválido: ${identificadorInvalido}`);
  console.log(`   ❌ Erros durante vinculação: ${erros}`);
  
  const percentualVinculados = ((vinculados / processosComCrmSemCliente.length) * 100).toFixed(1);
  console.log(`   📈 Taxa de vinculação: ${percentualVinculados}%`);
  
  return { 
    vinculados, 
    semTelefone, 
    identificadorInvalido, 
    erros, 
    problemasDetalhados 
  };
}

// Função para comparar e gerar relatório
async function compararProcessos() {
  const startTime = Date.now();
  
  try {
    console.log("🚀 Iniciando comparação entre processos do CRM e banco de dados...\n");
    
    // PASSO 1: Buscar todos os processos do CRM
    console.log("📡 PASSO 1: Buscando dados do CRM...");
    const processosCrm = await buscarTodosDealsComProcesso();
    
    if (processosCrm.length === 0) {
      console.warn("⚠️ Nenhum processo encontrado no CRM. Encerrando análise.");
      return;
    }
    
    console.log(`\n📊 RESUMO CRM:`);
    console.log(`   • Total de deals com processo: ${processosCrm.length}`);
    
    // PASSO 2: Extrair números de processo do CRM
    const numerosProcessosCrm = processosCrm.map(p => p.numeroProcesso);
    console.log(`   • Números únicos de processo: ${new Set(numerosProcessosCrm).size}`);
    
    // PASSO 3: Buscar apenas esses processos específicos no banco
    console.log("\n📡 PASSO 2: Verificando quais existem no banco...");
    const processosBanco = await buscarProcessosEspecificosNoBanco(numerosProcessosCrm);
    
    // PASSO 4: Análise comparativa
    console.log("\n🔄 PASSO 3: Processando análise...");
    
    // Criar maps para facilitar comparação
    const mapProcessosCrm = new Map<string, ProcessoCrm>();
    const mapProcessosBanco = new Map<string, any>();
    
    processosCrm.forEach(p => {
      const numeroLimpo = p.numeroProcesso.trim().replace(/\s+/g, ' ');
      mapProcessosCrm.set(numeroLimpo, p);
    });
    
    processosBanco.forEach(p => {
      const numeroLimpo = p.numero.trim().replace(/\s+/g, ' ');
      mapProcessosBanco.set(numeroLimpo, p);
    });
    
    // ANÁLISE 1: Processos do CRM que NÃO estão no banco
    const processosCrmNaoNoBanco: ProcessoCrm[] = [];
    mapProcessosCrm.forEach((processoCrm, numero) => {
      if (!mapProcessosBanco.has(numero)) {
        processosCrmNaoNoBanco.push(processoCrm);
      }
    });
    
    // ANÁLISE 2: Processos do CRM que estão no banco MAS sem cliente
    const processosComCrmSemCliente: {
      processosBanco: any;
      processosCrm: ProcessoCrm;
    }[] = [];
    
    // ANÁLISE 3: Processos do CRM que estão no banco E têm cliente
    const processosComCrmComCliente: {
      processosBanco: any;
      processosCrm: ProcessoCrm;
    }[] = [];
    
    mapProcessosCrm.forEach((processoCrm, numero) => {
      const processoBanco = mapProcessosBanco.get(numero);
      if (processoBanco) {
        if (!processoBanco.cliente || !processoBanco.clienteId) {
          // Existe no banco mas não tem cliente
          processosComCrmSemCliente.push({
            processosBanco: processoBanco,
            processosCrm: processoCrm
          });
        } else {
          // Existe no banco e tem cliente
          processosComCrmComCliente.push({
            processosBanco: processoBanco,
            processosCrm: processoCrm
          });
        }
      }
    });
    
    console.log("\n📋 RESULTADOS DA ANÁLISE:");
    console.log(`   • 📊 Total de processos no CRM: ${processosCrm.length}`);
    console.log(`   • ❌ Processos do CRM que NÃO estão no banco: ${processosCrmNaoNoBanco.length}`);
    console.log(`   • ⚠️  Processos do CRM que estão no banco MAS SEM cliente: ${processosComCrmSemCliente.length}`);
    console.log(`   • ✅ Processos do CRM que estão no banco E têm cliente: ${processosComCrmComCliente.length}`);
    
    // Calcular percentuais
    const percentualNoBanco = ((processosBanco.length / processosCrm.length) * 100).toFixed(1);
    const percentualSemCliente = ((processosComCrmSemCliente.length / processosCrm.length) * 100).toFixed(1);
    const percentualComCliente = ((processosComCrmComCliente.length / processosCrm.length) * 100).toFixed(1);
    
    console.log("\n📊 PERCENTUAIS:");
    console.log(`   • Processos do CRM encontrados no banco: ${percentualNoBanco}%`);
    console.log(`   • Processos sem cliente (oportunidade): ${percentualSemCliente}%`);
    console.log(`   • Processos já com cliente (ok): ${percentualComCliente}%`);
    
    // PASSO 5: Vinculação automática dos processos sem cliente
    let resultadoVinculacao: {
      vinculados: number;
      semTelefone: number;
      identificadorInvalido: number;
      erros: number;
      problemasDetalhados: any[];
    } = { vinculados: 0, semTelefone: 0, identificadorInvalido: 0, erros: 0, problemasDetalhados: [] };
    if (processosComCrmSemCliente.length > 0) {
      resultadoVinculacao = await vincularProcessosSemCliente(processosComCrmSemCliente);
    }
    
    // Gerar relatórios
    console.log("\n📄 Gerando relatórios...");
    gerarRelatorioComparacao({
      processosCrmNaoNoBanco,
      processosBancoNaoNoCrm: [], // Não aplicável nesta análise
      processosBancoSemCliente: processosComCrmSemCliente.map(p => p.processosBanco),
      processosEmAmbosSemCliente: processosComCrmSemCliente,
      totalProcessosCrm: processosCrm.length,
      totalProcessosBanco: processosBanco.length,
      resultadoVinculacao
    });
    
    const endTime = Date.now();
    const executionTime = (endTime - startTime) / 1000;
    console.log(`\n⏱️ Tempo de execução: ${executionTime} segundos`);
    
    return {
      processosCrmNaoNoBanco,
      processosComCrmSemCliente,
      processosComCrmComCliente,
      resultadoVinculacao,
      resumo: {
        totalCrm: processosCrm.length,
        naoNoBanco: processosCrmNaoNoBanco.length,
        semCliente: processosComCrmSemCliente.length,
        comCliente: processosComCrmComCliente.length
      }
    };
    
  } catch (error: any) {
    console.error("❌ Erro durante a comparação:", error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Função para gerar relatório CSV
function gerarRelatorioComparacao(dados: {
  processosCrmNaoNoBanco: ProcessoCrm[];
  processosBancoNaoNoCrm: any[];
  processosBancoSemCliente: any[];
  processosEmAmbosSemCliente: Array<{
    processosBanco: any;
    processosCrm: ProcessoCrm;
  }>;
  totalProcessosCrm: number;
  totalProcessosBanco: number;
  resultadoVinculacao?: {
    vinculados: number;
    semTelefone: number;
    identificadorInvalido: number;
    erros: number;
    problemasDetalhados: any[];
  };
}) {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
  
  // Relatório resumo
  const resumo = [
    'RELATÓRIO DE COMPARAÇÃO E VINCULAÇÃO PROCESSOS CRM vs BANCO',
    `Data/Hora: ${new Date().toLocaleString('pt-BR')}`,
    '',
    'RESUMO EXECUTIVO:',
    `Total de processos no CRM: ${dados.totalProcessosCrm}`,
    `Total de processos no banco: ${dados.totalProcessosBanco}`,
    `Processos no CRM que não estão no banco: ${dados.processosCrmNaoNoBanco.length}`,
    `Processos no banco que não estão no CRM: ${dados.processosBancoNaoNoCrm.length}`,
    `Processos no banco sem cliente relacionado: ${dados.processosBancoSemCliente.length}`,
    `Processos em ambos mas sem cliente no banco: ${dados.processosEmAmbosSemCliente.length}`,
    ''
  ];
  
  if (dados.resultadoVinculacao) {
    resumo.push(
      'RESULTADO DA VINCULAÇÃO AUTOMÁTICA:',
      `Processos vinculados com sucesso: ${dados.resultadoVinculacao.vinculados}`,
      `Processos sem telefone válido: ${dados.resultadoVinculacao.semTelefone}`,
      `Processos com identificador inválido: ${dados.resultadoVinculacao.identificadorInvalido}`,
      `Erros durante vinculação: ${dados.resultadoVinculacao.erros}`,
      `Taxa de vinculação: ${((dados.resultadoVinculacao.vinculados / dados.processosEmAmbosSemCliente.length) * 100).toFixed(1)}%`,
      ''
    );
  }
  
  fs.writeFileSync(`relatorio-comparacao-vinculacao-${timestamp}.txt`, resumo.join('\n'), 'utf8');
  
  // Relatório detalhado de problemas (JSON)
  if (dados.resultadoVinculacao && dados.resultadoVinculacao.problemasDetalhados.length > 0) {
    const relatorioProblemas = {
      metadata: {
        dataHora: new Date().toLocaleString('pt-BR'),
        totalProblemas: dados.resultadoVinculacao.problemasDetalhados.length,
        categorias: {
          semTelefone: dados.resultadoVinculacao.semTelefone,
          identificadorInvalido: dados.resultadoVinculacao.identificadorInvalido,
          errosBanco: dados.resultadoVinculacao.erros
        }
      },
      problemas: dados.resultadoVinculacao.problemasDetalhados
    };
    
    fs.writeFileSync(
      `problemas-vinculacao-detalhado-${timestamp}.json`, 
      JSON.stringify(relatorioProblemas, null, 2), 
      'utf8'
    );
  }
  
  // CSV dos problemas de telefone/identificador (simplificado)
  if (dados.resultadoVinculacao && dados.resultadoVinculacao.problemasDetalhados.length > 0) {
    const csvProblemas = [
      'processo_id,numero_processo,deal_id,nome_cliente,motivo,telefones_encontrados,identificador_gerado,problemas_telefone',
      ...dados.resultadoVinculacao.problemasDetalhados.map(p => [
        `"${p.processoId}"`,
        `"${p.numeroProcesso}"`,
        `"${p.dealId}"`,
        `"${p.dadosDebug?.nomeCliente || 'N/A'}"`,
        `"${p.motivo}"`,
        `"${p.dadosDebug?.analisesTelefones?.todosOsTelefones ? JSON.stringify(p.dadosDebug.analisesTelefones.todosOsTelefones) : 'N/A'}"`,
        `"${p.dadosDebug?.analisesTelefones?.identificadorGerado || 'N/A'}"`,
        `"${p.dadosDebug?.analisesTelefones?.problemas ? p.dadosDebug.analisesTelefones.problemas.join('; ') : 'N/A'}"`
      ].join(','))
    ].join('\n');
    
    fs.writeFileSync(`problemas-telefone-identificador-${timestamp}.csv`, csvProblemas, 'utf8');
  }
  
  // CSV 1: Processos no CRM que não estão no banco
  if (dados.processosCrmNaoNoBanco.length > 0) {
    const csvCrmNaoBanco = [
      'deal_id,numero_processo,nome_cliente,cpf,telefone,stage_id,marca',
      ...dados.processosCrmNaoNoBanco.map(p => [
        `"${p.id}"`,
        `"${p.numeroProcesso}"`,
        `"${p.person?.name || 'Não informado'}"`,
        `"${p.person?.cpf || 'Não informado'}"`,
        `"${p.person?.contactPhones?.find(ph => ph.is_main === 1)?.phone || p.person?.contactPhones?.[0]?.phone || 'Não informado'}"`,
        `"${p.stage_id}"`,
        `"${p.customFields?.find(f => f.id === 213657)?.value || 'Não informado'}"`
      ].join(','))
    ].join('\n');
    
    fs.writeFileSync(`processos-crm-nao-no-banco-${timestamp}.csv`, csvCrmNaoBanco, 'utf8');
  }
  
  // CSV 2: Processos no banco sem cliente relacionado
  if (dados.processosBancoSemCliente.length > 0) {
    const csvBancoSemCliente = [
      'processo_id,numero_processo,tem_cliente',
      ...dados.processosBancoSemCliente.map(p => [
        `"${p.id}"`,
        `"${p.numero}"`,
        '"Não"'
      ].join(','))
    ].join('\n');
    
    fs.writeFileSync(`processos-banco-sem-cliente-${timestamp}.csv`, csvBancoSemCliente, 'utf8');
  }
  
  // CSV 3: Processos em ambos mas sem cliente no banco (oportunidades de melhoria)
  if (dados.processosEmAmbosSemCliente.length > 0) {
    const csvOportunidades = [
      'processo_banco_id,numero_processo,deal_crm_id,nome_cliente_crm,cpf_crm,telefone_crm,stage_id,marca,status_vinculacao',
      ...dados.processosEmAmbosSemCliente.map(item => [
        `"${item.processosBanco.id}"`,
        `"${item.processosBanco.numero}"`,
        `"${item.processosCrm.id}"`,
        `"${item.processosCrm.person?.name || 'Não informado'}"`,
        `"${item.processosCrm.person?.cpf || 'Não informado'}"`,
        `"${item.processosCrm.person?.contactPhones?.find(ph => ph.is_main === 1)?.phone || item.processosCrm.person?.contactPhones?.[0]?.phone || 'Não informado'}"`,
        `"${item.processosCrm.stage_id}"`,
        `"${item.processosCrm.customFields?.find(f => f.id === 213657)?.value || 'Não informado'}"`,
        `"${dados.resultadoVinculacao ? 'Processado automaticamente' : 'Pendente vinculação'}"`
      ].join(','))
    ].join('\n');
    
    fs.writeFileSync(`oportunidades-vinculacao-${timestamp}.csv`, csvOportunidades, 'utf8');
  }
  
  console.log("\n📁 ARQUIVOS GERADOS:");
  console.log(`   📄 Relatório resumo: relatorio-comparacao-vinculacao-${timestamp}.txt`);
  
  if (dados.resultadoVinculacao && dados.resultadoVinculacao.problemasDetalhados.length > 0) {
    console.log(`   🔍 Problemas detalhados (JSON): problemas-vinculacao-detalhado-${timestamp}.json`);
    console.log(`   📞 Problemas telefone/identificador (CSV): problemas-telefone-identificador-${timestamp}.csv`);
  }
  
  if (dados.processosCrmNaoNoBanco.length > 0) {
    console.log(`   📊 Processos CRM não no banco: processos-crm-nao-no-banco-${timestamp}.csv`);
  }
  
  if (dados.processosBancoSemCliente.length > 0) {
    console.log(`   📊 Processos banco sem cliente: processos-banco-sem-cliente-${timestamp}.csv`);
  }
  
  if (dados.processosEmAmbosSemCliente.length > 0) {
    console.log(`   📊 Oportunidades de vinculação: oportunidades-vinculacao-${timestamp}.csv`);
  }
  
  console.log(`\n📂 Localização: ${process.cwd()}/`);
}

// Função principal
async function main() {
  console.log("🚀 Iniciando análise comparativa e vinculação automática entre processos do CRM e banco de dados...\n");
  
  try {
    await compararProcessos();
    
    console.log("\n✅ Análise e vinculação concluídas com sucesso!");
    console.log("\n💡 PRÓXIMOS PASSOS SUGERIDOS:");
    console.log("   1. Revisar o arquivo 'oportunidades-vinculacao' para verificar resultados da vinculação");
    console.log("   2. Analisar processos do CRM que não estão no banco para possível importação");
    console.log("   3. Executar novamente para processar novos processos sem cliente");
    
  } catch (error: any) {
    console.error("❌ Erro durante a execução:", error.message);
    process.exit(1);
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  main();
}

export { compararProcessos, buscarTodosDealsComProcesso, buscarProcessosEspecificosNoBanco }; 