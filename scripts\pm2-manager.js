#!/usr/bin/env node

/**
 * 🎯 PM2 Manager Script
 * Script helper para gerenciar os serviços Node.js + Python FastAPI
 */

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// Cores para output
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m',
    bold: '\x1b[1m'
};

const log = (message, color = 'reset') => {
    console.log(`${colors[color]}${message}${colors.reset}`);
};

// Comandos disponíveis
const commands = {
    start: () => startServices(),
    stop: () => stopServices(),
    restart: () => restartServices(),
    status: () => showStatus(),
    logs: () => showLogs(),
    'logs-node': () => showLogs('api-v3-rgsys'),
    'logs-python': () => showLogs('pdf-processor'),
    install: () => installDependencies(),
    help: () => showHelp()
};

async function execCommand(command) {
    return new Promise((resolve, reject) => {
        exec(command, (error, stdout, stderr) => {
            if (error) {
                reject({ error, stderr });
            } else {
                resolve(stdout);
            }
        });
    });
}

async function startServices() {
    try {
        log('🚀 Iniciando serviços...', 'blue');
        
        // Verificar se ecosystem.config.js existe
        if (!fs.existsSync('./ecosystem.config.js')) {
            log('❌ Arquivo ecosystem.config.js não encontrado!', 'red');
            return;
        }
        
        const result = await execCommand('pm2 start ecosystem.config.js --env production');
        log('✅ Serviços iniciados com sucesso!', 'green');
        console.log(result);
        
        // Aguardar um pouco e mostrar status
        setTimeout(showStatus, 3000);
        
    } catch (err) {
        log('❌ Erro ao iniciar serviços:', 'red');
        console.error(err.stderr || err.error);
    }
}

async function stopServices() {
    try {
        log('🛑 Parando serviços...', 'yellow');
        
        const result = await execCommand('pm2 stop ecosystem.config.js');
        log('✅ Serviços parados!', 'green');
        console.log(result);
        
    } catch (err) {
        log('❌ Erro ao parar serviços:', 'red');
        console.error(err.stderr || err.error);
    }
}

async function restartServices() {
    try {
        log('🔄 Reiniciando serviços...', 'blue');
        
        const result = await execCommand('pm2 restart ecosystem.config.js');
        log('✅ Serviços reiniciados!', 'green');
        console.log(result);
        
        // Aguardar um pouco e mostrar status
        setTimeout(showStatus, 3000);
        
    } catch (err) {
        log('❌ Erro ao reiniciar serviços:', 'red');
        console.error(err.stderr || err.error);
    }
}

async function showStatus() {
    try {
        log('📊 Status dos serviços:', 'blue');
        
        const result = await execCommand('pm2 list');
        console.log(result);
        
        // Testar conectividade
        log('\n🔗 Testando conectividade:', 'blue');
        
        try {
            const healthCheck = await execCommand('curl -s http://localhost:8000/health || echo "❌ Python FastAPI offline"');
            if (healthCheck.includes('healthy')) {
                log('✅ Python FastAPI: Online', 'green');
            } else {
                log('❌ Python FastAPI: Offline', 'red');
            }
        } catch (err) {
            log('❌ Python FastAPI: Erro de conexão', 'red');
        }
        
    } catch (err) {
        log('❌ Erro ao verificar status:', 'red');
        console.error(err.stderr || err.error);
    }
}

async function showLogs(serviceName) {
    try {
        const service = serviceName || 'all';
        log(`📋 Logs do serviço: ${service}`, 'blue');
        
        let command;
        if (serviceName) {
            command = `pm2 logs ${serviceName} --lines 50`;
        } else {
            command = 'pm2 logs --lines 50';
        }
        
        const result = await execCommand(command);
        console.log(result);
        
    } catch (err) {
        log('❌ Erro ao mostrar logs:', 'red');
        console.error(err.stderr || err.error);
    }
}

async function installDependencies() {
    try {
        log('📦 Instalando dependências...', 'blue');
        
        // Node.js
        log('\n📦 Instalando dependências Node.js...', 'yellow');
        await execCommand('npm install');
        log('✅ Node.js: OK', 'green');
        
        // Python
        log('\n🐍 Instalando dependências Python...', 'yellow');
        const pythonCmd = process.platform === 'win32' ? 'python' : 'python3';
        await execCommand(`cd pdf-processor-service && ${pythonCmd} -m pip install -r requirements.txt`);
        log('✅ Python: OK', 'green');
        
        // Criar diretórios necessários
        log('\n📁 Criando diretórios...', 'yellow');
        const dirs = ['logs', 'uploads/protocolos', 'uploads/imagens-marca', 'pdf-processor-service/extracted_images'];
        dirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                log(`   ✅ ${dir}`, 'green');
            }
        });
        
        log('\n✅ Todas as dependências instaladas!', 'green');
        
    } catch (err) {
        log('❌ Erro ao instalar dependências:', 'red');
        console.error(err.stderr || err.error);
    }
}

function showHelp() {
    log('\n🎯 PM2 Manager - Gerenciador de Serviços', 'bold');
    log('===========================================', 'blue');
    
    const commandsHelp = [
        ['start', 'Iniciar todos os serviços'],
        ['stop', 'Parar todos os serviços'],
        ['restart', 'Reiniciar todos os serviços'],
        ['status', 'Mostrar status dos serviços'],
        ['logs', 'Mostrar logs de todos os serviços'],
        ['logs-node', 'Mostrar logs apenas do Node.js'],
        ['logs-python', 'Mostrar logs apenas do Python'],
        ['install', 'Instalar dependências (Node.js + Python)'],
        ['help', 'Mostrar esta ajuda']
    ];
    
    commandsHelp.forEach(([cmd, desc]) => {
        log(`  ${cmd.padEnd(12)} - ${desc}`, 'green');
    });
    
    log('\n📖 Exemplos de uso:', 'yellow');
    log('  node scripts/pm2-manager.js start', 'blue');
    log('  node scripts/pm2-manager.js logs-python', 'blue');
    log('  node scripts/pm2-manager.js status', 'blue');
    
    log('\n🔗 URLs importantes:', 'yellow');
    log('  Node.js API: http://localhost:3321', 'blue');
    log('  Python API: http://localhost:8000', 'blue');
    log('  Python Docs: http://localhost:8000/docs', 'blue');
}

// Execução principal
const command = process.argv[2];

if (!command || !commands[command]) {
    log('❌ Comando inválido!', 'red');
    showHelp();
    process.exit(1);
}

commands[command](); 