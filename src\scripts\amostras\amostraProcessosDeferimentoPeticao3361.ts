import { Prisma<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ProtocoloDespacho, R<PERSON> } from "@prisma/client";
import { writeFileSync } from "fs";

const prisma = new PrismaClient({
  log: ["warn", "error"],
});

const NOME_DESPACHO_CONTAINS = "Deferimento da petição";
const CODIGO_SERVICO_PROTOCOLO = "3361";
const TAMANHO_AMOSTRA = 10;
const TAMANHO_LOTE_BIND_VARIABLES = 10000; // Para evitar erro P2035

interface DespachoAmostra extends Despacho {
  rpi: RPI | null;
  protocolos: ProtocoloDespacho[];
}

interface ProcessoAmostra {
  numeroProcesso: string;
  dataDeposito: Date | null;
  despachos: DespachoAmostra[];
  despachoFiltrado?: DespachoAmostra;
}

async function buscarAmostraProcessosPriorizandoProtocolo() {
  console.log(`Buscando ${TAMANHO_AMOSTRA} processos com despacho contendo "${NOME_DESPACHO_CONTAINS}" e protocolo ${CODIGO_SERVICO_PROTOCOLO} (priorizando protocolo)...`);

  // Passo 1: Encontrar Despacho IDs a partir do código de serviço do protocolo
  const protocolosComCodigoServico = await prisma.protocoloDespacho.findMany({
    where: {
      codigoServico: CODIGO_SERVICO_PROTOCOLO,
    },
    select: {
      despachoId: true,
    },
  });

  if (protocolosComCodigoServico.length === 0) {
    console.log(`Nenhum protocolo encontrado com o código de serviço ${CODIGO_SERVICO_PROTOCOLO}.`);
    return;
  }

  let despachoIdsComProtocoloCorreto = [...new Set(protocolosComCodigoServico.map(p => p.despachoId))];
  console.log(`${despachoIdsComProtocoloCorreto.length} despachos únicos associados ao código de serviço ${CODIGO_SERVICO_PROTOCOLO}.`);

  // Passo 2: Filtrar esses despachos pelo nome e obter os processoIds, processando em lotes
  let processoIdsUnicosColetados = new Set<string>();
  
  for (let i = 0; i < despachoIdsComProtocoloCorreto.length; i += TAMANHO_LOTE_BIND_VARIABLES) {
    const loteDeDespachoIds = despachoIdsComProtocoloCorreto.slice(i, i + TAMANHO_LOTE_BIND_VARIABLES);
    console.log(`Processando lote de ${loteDeDespachoIds.length} despacho IDs para encontrar processos... (Lote ${Math.floor(i / TAMANHO_LOTE_BIND_VARIABLES) + 1})`);

    const despachosDoLote = await prisma.despacho.findMany({
      where: {
        id: {
          in: loteDeDespachoIds,
        },
        nome: {
          contains: NOME_DESPACHO_CONTAINS,
          mode: 'insensitive',
        },
      },
      select: {
        processo:{
            select:{
                id:true,
                numero:true,
            }
        }
      },
      distinct: ['processoId'],
    });

    despachosDoLote.forEach(d => processoIdsUnicosColetados.add(d.processo.id));

    // Se já encontramos o número desejado para a amostra, podemos parar antes
    if (processoIdsUnicosColetados.size >= TAMANHO_AMOSTRA) {
      console.log(`Amostra de ${TAMANHO_AMOSTRA} processoIDs atingida. Interrompendo busca de mais processos.`);
      break;
    }
  }

  if (processoIdsUnicosColetados.size === 0) {
    console.log("Nenhum despacho correspondente encontrado após filtrar pelo nome do despacho em todos os lotes.");
    return;
  }

  // Pegar apenas o número desejado para a amostra final
  const processoIdsParaAmostra = Array.from(processoIdsUnicosColetados).slice(0, TAMANHO_AMOSTRA);
  console.log(`IDs de processos únicos encontrados para amostra final (${processoIdsParaAmostra.length}): ${processoIdsParaAmostra.join(', ')}`);

  // Passo 3: Buscar os detalhes completos dos processos encontrados
  const processosEncontrados = await prisma.processo.findMany({
    where: {
      id: {
        in: processoIdsParaAmostra, // Usar a lista final para amostra
      },
    },
    include: {
      despachos: {
        include: {
          rpi: true,
          protocolos: true,
        },
        orderBy: {
          rpi: {
            dataPublicacao: "asc",
          },
        },
      },
    },
  });

  if (processosEncontrados.length === 0) {
    console.log("Nenhum processo encontrado na fase final (busca por IDs). Algo inesperado ocorreu.");
    return;
  }

  console.log(`Detalhes completos obtidos para ${processosEncontrados.length} processos.`);

  const amostraFormatada: ProcessoAmostra[] = processosEncontrados.map(p => {
    const despachoFiltrado = p.despachos.find(d =>
      d.nome?.toLowerCase().includes(NOME_DESPACHO_CONTAINS.toLowerCase()) &&
      d.protocolos.some(proto => proto.codigoServico === CODIGO_SERVICO_PROTOCOLO)
    ) as DespachoAmostra | undefined;

    return {
      numeroProcesso: p.numero,
      dataDeposito: p.dataDeposito || null,
      despachos: p.despachos as DespachoAmostra[],
      despachoFiltrado: despachoFiltrado,
    };
  });

  const dataHora = new Date().toISOString().replace(/[:.]/g, "-");
  const nomeArquivo = `amostra-deferimento-peticao-protocolo-${CODIGO_SERVICO_PROTOCOLO}-${dataHora}.json`;

  try {
    writeFileSync(nomeArquivo, JSON.stringify(amostraFormatada, null, 2));
    console.log(`
Amostra de ${amostraFormatada.length} processos salva em: ${nomeArquivo}`);
  } catch (error) {
    console.error("\nErro ao salvar arquivo JSON da amostra:", error);
  }
}

async function main() {
  try {
    await buscarAmostraProcessosPriorizandoProtocolo();
  } catch (error) {
    console.error("\nErro na execução principal:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log("\nConexão com o banco de dados fechada.");
  }
}

if (require.main === module) {
  main();
} 