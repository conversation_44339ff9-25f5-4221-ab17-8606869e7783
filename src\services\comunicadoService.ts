import { PrismaClient, TipoComunicado, StatusComunicado } from '@prisma/client';
import { chatGuruLogger } from '../utils/logger';

const prisma = new PrismaClient();

export interface CriarComunicadoData {
  tipo: TipoComunicado;
  titulo: string;
  descricao?: string;
  clienteId: number;
  processosIds?: string[];
  marcasIds?: string[];
  rdStationDataId?: string;
  rdStationEventId?: string;
  email?: string;
  dadosAdicionais?: any;
}

export interface RegistrarComunicadoProtocoloData {
  clienteIdentificador: string;
  processosNumeros: string[];
  titulo: string;
  descricao?: string;
  email?: string;
  rdStationDataId?: string;
  rdStationEventId?: string;
  dadosAdicionais?: any;
}

/**
 * Cria um novo registro de comunicado
 */
export async function criarComunicado(data: CriarComunicadoData) {
  try {
    const comunicado = await prisma.comunicado.create({
      data: {
        tipo: data.tipo,
        titulo: data.titulo,
        descricao: data.descricao,
        clienteId: data.clienteId,
        processosIds: data.processosIds || [],
        marcasIds: data.marcasIds || [],
        rdStationDataId: data.rdStationDataId,
        rdStationEventId: data.rdStationEventId,
        email: data.email,
        dadosAdicionais: data.dadosAdicionais || {},
        status: StatusComunicado.ENVIADO
      },
      include: {
        cliente: {
          select: {
            id: true,
            nome: true,
            identificador: true,
            crmId: true
          }
        }
      }
    });

    chatGuruLogger.info(`Comunicado criado com sucesso`, {
      comunicadoId: comunicado.id,
      tipo: comunicado.tipo,
      clienteId: comunicado.clienteId,
      processosIds: comunicado.processosIds
    });

    return comunicado;
  } catch (error) {
    chatGuruLogger.error('Erro ao criar comunicado:', error);
    throw error;
  }
}

/**
 * Registra um comunicado de protocolo baseado no identificador do cliente
 * e números dos processos
 */
export async function registrarComunicadoProtocolo(data: RegistrarComunicadoProtocoloData) {
  try {
    // Buscar cliente pelo identificador
    const cliente = await prisma.cliente.findFirst({
      where: {
        identificador: data.clienteIdentificador
      }
    });

    if (!cliente) {
      throw new Error(`Cliente não encontrado com identificador: ${data.clienteIdentificador}`);
    }

    // Buscar processos pelos números
    const processos = await prisma.processo.findMany({
      where: {
        numero: {
          in: data.processosNumeros
        }
      },
      include: {
        marca: true
      }
    });

    if (processos.length === 0) {
      throw new Error(`Nenhum processo encontrado com os números: ${data.processosNumeros.join(', ')}`);
    }

    // Extrair IDs dos processos e marcas
    const processosIds = processos.map(p => p.id);
    const marcasIds = processos
      .filter(p => p.marca)
      .map(p => p.marca!.id);

    // Criar o comunicado
    const comunicado = await criarComunicado({
      tipo: TipoComunicado.PROTOCOLO_MARCA,
      titulo: data.titulo,
      descricao: data.descricao,
      clienteId: cliente.id,
      processosIds,
      marcasIds,
      rdStationDataId: data.rdStationDataId,
      rdStationEventId: data.rdStationEventId,
      email: data.email,
      dadosAdicionais: {
        ...data.dadosAdicionais,
        processosNumeros: data.processosNumeros,
        processosEncontrados: processos.length,
        marcasEncontradas: marcasIds.length
      }
    });

    return {
      ...comunicado,
      processosEncontrados: processos.length,
      marcasEncontradas: marcasIds.length,
      processosNumeros: data.processosNumeros
    };
  } catch (error) {
    chatGuruLogger.error('Erro ao registrar comunicado de protocolo:', error);
    throw error;
  }
}

/**
 * Lista comunicados de um cliente
 */
export async function listarComunicadosCliente(clienteId: number, page: number = 1, limit: number = 20) {
  try {
    const skip = (page - 1) * limit;

    const [comunicados, total] = await Promise.all([
      prisma.comunicado.findMany({
        where: { clienteId },
        orderBy: { dataEnvio: 'desc' },
        skip,
        take: limit,
        include: {
          cliente: {
            select: {
              id: true,
              nome: true,
              identificador: true
            }
          }
        }
      }),
      prisma.comunicado.count({
        where: { clienteId }
      })
    ]);

    return {
      comunicados,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  } catch (error) {
    chatGuruLogger.error('Erro ao listar comunicados do cliente:', error);
    throw error;
  }
}

/**
 * Busca comunicados por tipo
 */
export async function listarComunicadosPorTipo(tipo: TipoComunicado, page: number = 1, limit: number = 20) {
  try {
    const skip = (page - 1) * limit;

    const [comunicados, total] = await Promise.all([
      prisma.comunicado.findMany({
        where: { tipo },
        orderBy: { dataEnvio: 'desc' },
        skip,
        take: limit,
        include: {
          cliente: {
            select: {
              id: true,
              nome: true,
              identificador: true
            }
          }
        }
      }),
      prisma.comunicado.count({
        where: { tipo }
      })
    ]);

    return {
      comunicados,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  } catch (error) {
    chatGuruLogger.error('Erro ao listar comunicados por tipo:', error);
    throw error;
  }
} 