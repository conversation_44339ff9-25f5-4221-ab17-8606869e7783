import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface EstimativaTempos {
  mediaEmDias: number;
  desvioPadrao: number;
  quantidade: number;
  minimo: number;
  maximo: number;
}

interface EstimativaMerito {
  semIntervencoes: EstimativaTempos;
  comOposicao: EstimativaTempos;
  comSobrestamento: EstimativaTempos;
  comExigencia: EstimativaTempos;
}

interface AnaliseTempos {
  depositoAtePublicacao: EstimativaTempos;
  publicacaoAtePrimeiraIntervencao?: EstimativaTempos;
  intervencaoAteMerito?: EstimativaTempos;
  depositoAteMerito: EstimativaTempos;
}

// Novas interfaces para análise refinada
interface EstimativaPorApresentacao {
  nominativa: EstimativaMerito;
  mista: EstimativaMerito;
  figurativa: EstimativaMerito;
  tridimensional: EstimativaMerito;
  [key: string]: EstimativaMerito; // Para outros tipos de apresentação
}

interface EstimativaPorClasse {
  [classe: string]: EstimativaMerito; // Usando string para classes NCL (ex: "1", "2", etc.)
}

interface ResultadoEstimativas {
  estimativaMerito: EstimativaMerito;
  analiseTempos: AnaliseTempos;
  // Novas propriedades para análise refinada
  estimativaPorApresentacao?: EstimativaPorApresentacao;
  estimativaPorClasse?: EstimativaPorClasse;
  dataAnalise: Date;
  periodoAnalise: {
    inicio: Date;
    fim: Date;
  };
  metadados: {
    totalProcessosAnalisados: number;
    processosDescartados: number;
    motivosDescarte: {
      [motivo: string]: number;
    };
    distribuicaoDespachos: {
      [codigo: string]: number;
    };
    distribuicaoIntervencoes: {
      semIntervencoes: number;
      oposicao: number;
      sobrestamento: number;
      exigencia: number;
      multiplas: number;
    };
    // Novos metadados
    distribuicaoApresentacao?: {
      [apresentacao: string]: number;
    };
    distribuicaoClasses?: {
      [classe: string]: number;
    };
  };
}

function calcularEstatisticas(diasArray: number[]): EstimativaTempos {
  if (diasArray.length === 0) {
    return {
      mediaEmDias: 0,
      desvioPadrao: 0,
      quantidade: 0,
      minimo: 0,
      maximo: 0,
    };
  }

  // Remove outliers (valores muito extremos)
  const sorted = [...diasArray].sort((a, b) => a - b);
  const q1 = sorted[Math.floor(sorted.length * 0.25)];
  const q3 = sorted[Math.floor(sorted.length * 0.75)];
  const iqr = q3 - q1;
  const lowerBound = q1 - 1.5 * iqr;
  const upperBound = q3 + 1.5 * iqr;
  
  const filteredDias = sorted.filter(dias => dias >= lowerBound && dias <= upperBound);

  const quantidade = filteredDias.length;
  const soma = filteredDias.reduce((acc, dias) => acc + dias, 0);
  const media = soma / quantidade;
  
  const somaQuadradosDiferencas = filteredDias.reduce(
    (acc, dias) => acc + Math.pow(dias - media, 2),
    0
  );
  
  const desvioPadrao = Math.sqrt(somaQuadradosDiferencas / quantidade);
  const minimo = Math.min(...filteredDias);
  const maximo = Math.max(...filteredDias);

  return {
    mediaEmDias: Math.round(media),
    desvioPadrao: Math.round(desvioPadrao),
    quantidade,
    minimo,
    maximo,
  };
}

interface ProcessoAnalise {
  dataDeposito: Date;
  despachoPublicacao?: {
    data: Date;
    nome: string;
  };
  despachoMerito: {
    data: Date;
    nome: string;
  };
  intervencoes: {
    oposicao?: { data: Date; nome: string };
    sobrestamento?: { data: Date; nome: string };
    exigencia?: { data: Date; nome: string };
  };
}

export async function calcularEstimativasMerito(): Promise<ResultadoEstimativas> {
  const motivosDescarte = {
    semDataDeposito: 0,
    semDataPublicacao: 0,
    semDespachoPublicacao: 0,
    semDespachoMerito: 0,
    tempoInvalido: 0,
    semApresentacao: 0,
    semClasse: 0,
  };

  const distribuicaoDespachos: Record<string, number> = {};
  const distribuicaoIntervencoes = {
    semIntervencoes: 0,
    oposicao: 0,
    sobrestamento: 0,
    exigencia: 0,
    multiplas: 0,
  };
  
  // Novas distribuições
  const distribuicaoApresentacao: Record<string, number> = {};
  const distribuicaoClasses: Record<string, number> = {};

  // Arrays para análise de mérito por tipo de intervenção
  const temposMerito = {
    semIntervencoes: [] as number[],
    comOposicao: [] as number[],
    comSobrestamento: [] as number[],
    comExigencia: [] as number[],
  };

  // Arrays para análise de tempos entre despachos
  const temposAnalise = {
    depositoAtePublicacao: [] as number[],
    publicacaoAtePrimeiraIntervencao: [] as number[],
    intervencaoAteMerito: [] as number[],
    depositoAteMerito: [] as number[],
  };

  // Novos arrays para análise refinada
  const temposMeritoPorApresentacao: Record<string, {
    semIntervencoes: number[],
    comOposicao: number[],
    comSobrestamento: number[],
    comExigencia: number[],
  }> = {};
  
  const temposMeritoPorClasse: Record<string, {
    semIntervencoes: number[],
    comOposicao: number[],
    comSobrestamento: number[],
    comExigencia: number[],
  }> = {};

  // Busca as duas últimas RPIs com 4 dígitos (ex: 2828, 2829)
  const ultimasRPIs = await prisma.rPI.findMany({
    where: {
      // Filtra apenas RPIs com 4 dígitos
      numero: {
        gte: 1000,
        lt: 10000
      }
    },
    orderBy: {
      dataPublicacao: 'desc',
    },
    take: 2,
    select: {
      id: true,
      numero: true,
      dataPublicacao: true,
    },
  });

  console.log('RPIs analisadas:', ultimasRPIs.map(rpi => ({ 
    numero: rpi.numero, 
    data: rpi.dataPublicacao 
  })));

  if (ultimasRPIs.length < 2) {
    throw new Error('Não há RPIs suficientes para análise');
  }

  // Busca processos com mérito nas últimas RPIs
  const processosComMerito = await prisma.processo.findMany({
    where: {
      despachos: {
        some: {
          rpiId: {
            in: ultimasRPIs.map(rpi => rpi.id),
          },
          OR: [
            { nome: { contains: 'Deferimento do pedido' } },
            { nome: { contains: 'Indeferimento do pedido' } },
          ],
        },
      },
      dataDeposito: { not: null },
    },
    include: {
      despachos: {
        include: {
          rpi: true,
        },
        orderBy: {
          rpi: {
            dataPublicacao: 'asc'
          }
        }
      },
      marca: {
        include: {
          ncl: true
        }
      }
    },
  });

  console.log('Total de processos encontrados:', processosComMerito.length);

  let totalProcessados = 0;

  for (const processo of processosComMerito) {
    if (!processo.dataDeposito) {
      motivosDescarte.semDataDeposito++;
      continue;
    }

    // Registra distribuição de despachos
    processo.despachos.forEach(d => {
      if (d.nome) {
        distribuicaoDespachos[d.nome] = (distribuicaoDespachos[d.nome] || 0) + 1;
      }
    });

    // Encontra despacho de publicação
    const despachoPublicacao = processo.despachos.find(
      (d) =>
        d.nome?.includes("Publicação de pedido de registro para oposição (exame formal concluído)") &&
        d.rpi?.dataPublicacao
    );

    // Encontra despacho de mérito mais recente nas últimas RPIs
    const despachoMerito = processo.despachos
      .filter(d => {
        const ehMerito = 
          d.nome?.includes('Deferimento do pedido') || 
          d.nome?.includes('Indeferimento do pedido');
        const ehUltimaRPI = ultimasRPIs.some(rpi => rpi.id === d.rpiId);
        return ehMerito && ehUltimaRPI && d.rpi?.dataPublicacao;
      })
      .sort((a, b) => {
        const dataA = a.rpi?.dataPublicacao || new Date(0);
        const dataB = b.rpi?.dataPublicacao || new Date(0);
        return dataB.getTime() - dataA.getTime();
      })[0];

    if (!despachoMerito?.rpi?.dataPublicacao) {
      motivosDescarte.semDespachoMerito++;
      continue;
    }

    // Encontra intervenções
    const intervencoes = {
      oposicao: processo.despachos.find(d => 
        d.nome === 'Notificação de oposição' && d.rpi?.dataPublicacao && 
        d.rpi.dataPublicacao < despachoMerito.rpi!.dataPublicacao
      ),
      sobrestamento: processo.despachos.find(d => 
        d.nome === 'Sobrestamento do exame de mérito' && d.rpi?.dataPublicacao && 
        d.rpi.dataPublicacao < despachoMerito.rpi!.dataPublicacao
      ),
      exigencia: processo.despachos.find(d => 
        d.nome === 'Exigência de mérito' && d.rpi?.dataPublicacao && 
        d.rpi.dataPublicacao < despachoMerito.rpi!.dataPublicacao
      ),
    };

    // Conta intervenções e tipos
    const tiposIntervencaoPresentes = [];
    if (intervencoes.oposicao) tiposIntervencaoPresentes.push('oposicao');
    if (intervencoes.sobrestamento) tiposIntervencaoPresentes.push('sobrestamento');
    if (intervencoes.exigencia) tiposIntervencaoPresentes.push('exigencia');
    
    if (tiposIntervencaoPresentes.length === 0) {
      distribuicaoIntervencoes.semIntervencoes++;
    } else if (tiposIntervencaoPresentes.length > 1) {
      distribuicaoIntervencoes.multiplas++;
    } else {
      const tipo = tiposIntervencaoPresentes[0];
      distribuicaoIntervencoes[tipo as keyof typeof distribuicaoIntervencoes]++;
    }

    // Calcula tempos
    const tempoAteMerito = Math.ceil(
      (despachoMerito.rpi.dataPublicacao.getTime() - processo.dataDeposito.getTime()) /
        (1000 * 60 * 60 * 24)
    );

    if (tempoAteMerito < 0 || tempoAteMerito > 3650) {
      motivosDescarte.tempoInvalido++;
      continue;
    }

    // Obter apresentação e classe NCL
    const apresentacao = processo.marca?.apresentacao || 'desconhecida';
    
    // Registra distribuição de apresentação
    distribuicaoApresentacao[apresentacao] = (distribuicaoApresentacao[apresentacao] || 0) + 1;
    
    // Inicializa arrays para apresentação se não existirem
    if (!temposMeritoPorApresentacao[apresentacao]) {
      temposMeritoPorApresentacao[apresentacao] = {
        semIntervencoes: [],
        comOposicao: [],
        comSobrestamento: [],
        comExigencia: [],
      };
    }
    
    // Obter classe NCL (primeira classe se houver múltiplas)
    let classeNCL = 'desconhecida';
    if (processo.marca?.ncl && processo.marca.ncl.length > 0 && processo.marca.ncl[0].codigo) {
      classeNCL = processo.marca.ncl[0].codigo;
      // Registra distribuição de classes
      distribuicaoClasses[classeNCL] = (distribuicaoClasses[classeNCL] || 0) + 1;
    }
    
    // Inicializa arrays para classe NCL se não existirem
    if (!temposMeritoPorClasse[classeNCL]) {
      temposMeritoPorClasse[classeNCL] = {
        semIntervencoes: [],
        comOposicao: [],
        comSobrestamento: [],
        comExigencia: [],
      };
    }

    // Adiciona aos grupos de análise de mérito
    if (tiposIntervencaoPresentes.length === 0) {
      temposMerito.semIntervencoes.push(tempoAteMerito);
      // Adiciona aos grupos refinados
      temposMeritoPorApresentacao[apresentacao].semIntervencoes.push(tempoAteMerito);
      temposMeritoPorClasse[classeNCL].semIntervencoes.push(tempoAteMerito);
    } else if (tiposIntervencaoPresentes.length === 1) {
      const tipo = tiposIntervencaoPresentes[0];
      switch(tipo) {
        case 'oposicao':
          temposMerito.comOposicao.push(tempoAteMerito);
          temposMeritoPorApresentacao[apresentacao].comOposicao.push(tempoAteMerito);
          temposMeritoPorClasse[classeNCL].comOposicao.push(tempoAteMerito);
          break;
        case 'sobrestamento':
          temposMerito.comSobrestamento.push(tempoAteMerito);
          temposMeritoPorApresentacao[apresentacao].comSobrestamento.push(tempoAteMerito);
          temposMeritoPorClasse[classeNCL].comSobrestamento.push(tempoAteMerito);
          break;
        case 'exigencia':
          temposMerito.comExigencia.push(tempoAteMerito);
          temposMeritoPorApresentacao[apresentacao].comExigencia.push(tempoAteMerito);
          temposMeritoPorClasse[classeNCL].comExigencia.push(tempoAteMerito);
          break;
      }
    }

    // Análise de tempos entre despachos
    temposAnalise.depositoAteMerito.push(tempoAteMerito);

    if (despachoPublicacao?.rpi?.dataPublicacao) {
      const tempoAtePublicacao = Math.ceil(
        (despachoPublicacao.rpi.dataPublicacao.getTime() - processo.dataDeposito.getTime()) /
          (1000 * 60 * 60 * 24)
      );

      if (tempoAtePublicacao >= 0 && tempoAtePublicacao <= 3650) {
        temposAnalise.depositoAtePublicacao.push(tempoAtePublicacao);

        // Se houve intervenção, calcula tempo entre publicação e primeira intervenção
        if (tiposIntervencaoPresentes.length > 0) {
          const datasIntervencoes = [];
          if (intervencoes.oposicao?.rpi?.dataPublicacao) 
            datasIntervencoes.push(intervencoes.oposicao.rpi.dataPublicacao);
          if (intervencoes.sobrestamento?.rpi?.dataPublicacao) 
            datasIntervencoes.push(intervencoes.sobrestamento.rpi.dataPublicacao);
          if (intervencoes.exigencia?.rpi?.dataPublicacao) 
            datasIntervencoes.push(intervencoes.exigencia.rpi.dataPublicacao);

          const primeiraIntervencao = datasIntervencoes.length > 0 
            ? new Date(Math.min(...datasIntervencoes.map(d => d.getTime())))
            : null;

          if (primeiraIntervencao) {
            const tempoAteIntervencao = Math.ceil(
              (primeiraIntervencao.getTime() - despachoPublicacao.rpi.dataPublicacao.getTime()) /
                (1000 * 60 * 60 * 24)
            );
            
            if (tempoAteIntervencao >= 0) {
              temposAnalise.publicacaoAtePrimeiraIntervencao.push(tempoAteIntervencao);

              const tempoIntervencaoMerito = Math.ceil(
                (despachoMerito.rpi.dataPublicacao.getTime() - primeiraIntervencao.getTime()) /
                  (1000 * 60 * 60 * 24)
              );

              if (tempoIntervencaoMerito >= 0) {
                temposAnalise.intervencaoAteMerito.push(tempoIntervencaoMerito);
              }
            }
          }
        }
      }
    }

    totalProcessados++;
  }

  // Processa estatísticas por apresentação
  const estimativaPorApresentacao: EstimativaPorApresentacao = {} as EstimativaPorApresentacao;
  
  for (const [apresentacao, tempos] of Object.entries(temposMeritoPorApresentacao)) {
    estimativaPorApresentacao[apresentacao] = {
      semIntervencoes: calcularEstatisticas(tempos.semIntervencoes),
      comOposicao: calcularEstatisticas(tempos.comOposicao),
      comSobrestamento: calcularEstatisticas(tempos.comSobrestamento),
      comExigencia: calcularEstatisticas(tempos.comExigencia),
    };
  }
  
  // Processa estatísticas por classe NCL
  const estimativaPorClasse: EstimativaPorClasse = {};
  
  for (const [classe, tempos] of Object.entries(temposMeritoPorClasse)) {
    estimativaPorClasse[classe] = {
      semIntervencoes: calcularEstatisticas(tempos.semIntervencoes),
      comOposicao: calcularEstatisticas(tempos.comOposicao),
      comSobrestamento: calcularEstatisticas(tempos.comSobrestamento),
      comExigencia: calcularEstatisticas(tempos.comExigencia),
    };
  }

  const resultado: ResultadoEstimativas = {
    estimativaMerito: {
      semIntervencoes: calcularEstatisticas(temposMerito.semIntervencoes),
      comOposicao: calcularEstatisticas(temposMerito.comOposicao),
      comSobrestamento: calcularEstatisticas(temposMerito.comSobrestamento),
      comExigencia: calcularEstatisticas(temposMerito.comExigencia),
    },
    analiseTempos: {
      depositoAtePublicacao: calcularEstatisticas(temposAnalise.depositoAtePublicacao),
      publicacaoAtePrimeiraIntervencao: calcularEstatisticas(temposAnalise.publicacaoAtePrimeiraIntervencao),
      intervencaoAteMerito: calcularEstatisticas(temposAnalise.intervencaoAteMerito),
      depositoAteMerito: calcularEstatisticas(temposAnalise.depositoAteMerito),
    },
    // Adiciona análises refinadas
    estimativaPorApresentacao,
    estimativaPorClasse,
    dataAnalise: new Date(),
    periodoAnalise: {
      inicio: ultimasRPIs[1].dataPublicacao,
      fim: ultimasRPIs[0].dataPublicacao,
    },
    metadados: {
      totalProcessosAnalisados: totalProcessados,
      processosDescartados: Object.values(motivosDescarte).reduce((a, b) => a + b, 0),
      motivosDescarte,
      distribuicaoDespachos,
      distribuicaoIntervencoes,
      distribuicaoApresentacao,
      distribuicaoClasses,
    },
  };

  // Logs detalhados
  console.log('Distribuição de intervenções:', distribuicaoIntervencoes);
  console.log('Distribuição de apresentações:', distribuicaoApresentacao);
  console.log('Distribuição de classes NCL:', distribuicaoClasses);
  
  console.log('Médias de tempo até mérito:');
  console.log('- Sem intervenções:', resultado.estimativaMerito.semIntervencoes.mediaEmDias);
  console.log('- Com oposição:', resultado.estimativaMerito.comOposicao.mediaEmDias);
  console.log('- Com sobrestamento:', resultado.estimativaMerito.comSobrestamento.mediaEmDias);
  console.log('- Com exigência:', resultado.estimativaMerito.comExigencia.mediaEmDias);

  // Log de algumas estatísticas refinadas
  const apresentacoesPrincipais = Object.keys(distribuicaoApresentacao)
    .filter(a => a !== 'desconhecida')
    .sort((a, b) => distribuicaoApresentacao[b] - distribuicaoApresentacao[a])
    .slice(0, 3);
    
  console.log('Estatísticas por apresentação principal:');
  for (const apresentacao of apresentacoesPrincipais) {
    console.log(`- ${apresentacao}:`);
    console.log(`  - Sem intervenções: ${estimativaPorApresentacao[apresentacao].semIntervencoes.mediaEmDias} dias (±${estimativaPorApresentacao[apresentacao].semIntervencoes.desvioPadrao})`);
    console.log(`  - Com oposição: ${estimativaPorApresentacao[apresentacao].comOposicao.mediaEmDias} dias (±${estimativaPorApresentacao[apresentacao].comOposicao.desvioPadrao})`);
  }
  
  const classesPrincipais = Object.keys(distribuicaoClasses)
    .filter(c => c !== 'desconhecida')
    .sort((a, b) => distribuicaoClasses[b] - distribuicaoClasses[a])
    .slice(0, 3);
    
  console.log('Estatísticas por classe NCL principal:');
  for (const classe of classesPrincipais) {
    console.log(`- Classe ${classe}:`);
    console.log(`  - Sem intervenções: ${estimativaPorClasse[classe].semIntervencoes.mediaEmDias} dias (±${estimativaPorClasse[classe].semIntervencoes.desvioPadrao})`);
    console.log(`  - Com oposição: ${estimativaPorClasse[classe].comOposicao.mediaEmDias} dias (±${estimativaPorClasse[classe].comOposicao.desvioPadrao})`);
  }

  return resultado;
}

// Nova função para calcular apenas as estimativas de mérito (mais leve)
export async function calcularEstimativasMeritoSimplificado(): Promise<{ estimativaMerito: EstimativaMerito }> {
  const motivosDescarte = {
    semDataDeposito: 0,
    semDespachoMerito: 0,
    tempoInvalido: 0,
  };

  const distribuicaoIntervencoes = {
    semIntervencoes: 0,
    oposicao: 0,
    sobrestamento: 0,
    exigencia: 0,
    multiplas: 0,
  };

  // Arrays para análise de mérito por tipo de intervenção
  const temposMerito = {
    semIntervencoes: [] as number[],
    comOposicao: [] as number[],
    comSobrestamento: [] as number[],
    comExigencia: [] as number[],
  };

  // Busca as duas últimas RPIs com 4 dígitos (ex: 2828, 2829)
  const ultimasRPIs = await prisma.rPI.findMany({
    where: {
      // Filtra apenas RPIs com 4 dígitos
      numero: {
        gte: 1000,
        lt: 10000
      }
    },
    orderBy: {
      dataPublicacao: 'desc',
    },
    take: 2,
    select: {
      id: true,
      numero: true,
      dataPublicacao: true,
    },
  });

  console.log('RPIs analisadas:', ultimasRPIs.map(rpi => ({ 
    numero: rpi.numero, 
    data: rpi.dataPublicacao 
  })));

  if (ultimasRPIs.length < 2) {
    throw new Error('Não há RPIs suficientes para análise');
  }

  // Busca processos com mérito nas últimas RPIs (consulta mais leve)
  const processosComMerito = await prisma.processo.findMany({
    where: {
      despachos: {
        some: {
          rpiId: {
            in: ultimasRPIs.map(rpi => rpi.id),
          },
          OR: [
            { nome: { contains: 'Deferimento do pedido' } },
            { nome: { contains: 'Indeferimento do pedido' } },
          ],
        },
      },
      dataDeposito: { not: null },
    },
    include: {
      despachos: {
        include: {
          rpi: true,
        },
        orderBy: {
          rpi: {
            dataPublicacao: 'asc'
          }
        }
      },
    },
  });

  for (const processo of processosComMerito) {
    if (!processo.dataDeposito) {
      motivosDescarte.semDataDeposito++;
      continue;
    }

    // Encontra despacho de mérito mais recente nas últimas RPIs
    const despachoMerito = processo.despachos
      .filter(d => {
        const ehMerito = 
          d.nome?.includes('Deferimento do pedido') || 
          d.nome?.includes('Indeferimento do pedido');
        const ehUltimaRPI = ultimasRPIs.some(rpi => rpi.id === d.rpiId);
        return ehMerito && ehUltimaRPI && d.rpi?.dataPublicacao;
      })
      .sort((a, b) => {
        const dataA = a.rpi?.dataPublicacao || new Date(0);
        const dataB = b.rpi?.dataPublicacao || new Date(0);
        return dataB.getTime() - dataA.getTime();
      })[0];

    if (!despachoMerito?.rpi?.dataPublicacao) {
      motivosDescarte.semDespachoMerito++;
      continue;
    }

    // Encontra intervenções
    const intervencoes = {
      oposicao: processo.despachos.find(d => 
        d.nome === 'Notificação de oposição' && d.rpi?.dataPublicacao && 
        d.rpi.dataPublicacao < despachoMerito.rpi!.dataPublicacao
      ),
      sobrestamento: processo.despachos.find(d => 
        d.nome === 'Sobrestamento do exame de mérito' && d.rpi?.dataPublicacao && 
        d.rpi.dataPublicacao < despachoMerito.rpi!.dataPublicacao
      ),
      exigencia: processo.despachos.find(d => 
        d.nome === 'Exigência de mérito' && d.rpi?.dataPublicacao && 
        d.rpi.dataPublicacao < despachoMerito.rpi!.dataPublicacao
      ),
    };

    // Conta intervenções e tipos
    const tiposIntervencaoPresentes = [];
    if (intervencoes.oposicao) tiposIntervencaoPresentes.push('oposicao');
    if (intervencoes.sobrestamento) tiposIntervencaoPresentes.push('sobrestamento');
    if (intervencoes.exigencia) tiposIntervencaoPresentes.push('exigencia');
    
    if (tiposIntervencaoPresentes.length === 0) {
      distribuicaoIntervencoes.semIntervencoes++;
    } else if (tiposIntervencaoPresentes.length > 1) {
      distribuicaoIntervencoes.multiplas++;
    } else {
      const tipo = tiposIntervencaoPresentes[0];
      distribuicaoIntervencoes[tipo as keyof typeof distribuicaoIntervencoes]++;
    }

    // Calcula tempos
    const tempoAteMerito = Math.ceil(
      (despachoMerito.rpi.dataPublicacao.getTime() - processo.dataDeposito.getTime()) /
        (1000 * 60 * 60 * 24)
    );

    if (tempoAteMerito < 0 || tempoAteMerito > 3650) {
      motivosDescarte.tempoInvalido++;
      continue;
    }

    // Adiciona aos grupos de análise de mérito
    if (tiposIntervencaoPresentes.length === 0) {
      temposMerito.semIntervencoes.push(tempoAteMerito);
    } else if (tiposIntervencaoPresentes.length === 1) {
      const tipo = tiposIntervencaoPresentes[0];
      switch(tipo) {
        case 'oposicao':
          temposMerito.comOposicao.push(tempoAteMerito);
          break;
        case 'sobrestamento':
          temposMerito.comSobrestamento.push(tempoAteMerito);
          break;
        case 'exigencia':
          temposMerito.comExigencia.push(tempoAteMerito);
          break;
      }
    }
  }

  return {
    estimativaMerito: {
      semIntervencoes: calcularEstatisticas(temposMerito.semIntervencoes),
      comOposicao: calcularEstatisticas(temposMerito.comOposicao),
      comSobrestamento: calcularEstatisticas(temposMerito.comSobrestamento),
      comExigencia: calcularEstatisticas(temposMerito.comExigencia),
    }
  };
}

/**
 * Executa o cálculo das estimativas de mérito e salva os resultados no banco de dados.
 * @returns O registro criado no banco de dados
 */
export async function executarECadastrarEstimativasMerito() {
  console.log('Iniciando cálculo das estimativas de mérito simplificado...');
  
  try {
    // Obter os resultados do cálculo
    const resultado = await calcularEstimativasMeritoSimplificado();
    const { estimativaMerito } = resultado;

    console.log('Salvando estimativas no banco de dados...');
    
    // Criar registro no banco de dados
    const registroSalvo = await prisma.estimativaMeritoSalva.create({
      data: {
        // Sem Intervenções
        mediaSemIntervencoes: estimativaMerito.semIntervencoes.mediaEmDias,
        medianaSemIntervencoes: null,
        minSemIntervencoes: estimativaMerito.semIntervencoes.minimo,
        maxSemIntervencoes: estimativaMerito.semIntervencoes.maximo,
        nSemIntervencoes: estimativaMerito.semIntervencoes.quantidade,

        // Com Oposição
        mediaComOposicao: estimativaMerito.comOposicao.mediaEmDias,
        medianaComOposicao: null,
        minComOposicao: estimativaMerito.comOposicao.minimo,
        maxComOposicao: estimativaMerito.comOposicao.maximo,
        nComOposicao: estimativaMerito.comOposicao.quantidade,

        // Com Sobrestamento
        mediaComSobrestamento: estimativaMerito.comSobrestamento.mediaEmDias,
        medianaComSobrestamento: null,
        minComSobrestamento: estimativaMerito.comSobrestamento.minimo,
        maxComSobrestamento: estimativaMerito.comSobrestamento.maximo,
        nComSobrestamento: estimativaMerito.comSobrestamento.quantidade,

        // Com Exigência
        mediaComExigencia: estimativaMerito.comExigencia.mediaEmDias,
        medianaComExigencia: null,
        minComExigencia: estimativaMerito.comExigencia.minimo,
        maxComExigencia: estimativaMerito.comExigencia.maximo,
        nComExigencia: estimativaMerito.comExigencia.quantidade,
      },
    });

    console.log('Estimativas salvas com sucesso!', registroSalvo.id);
    return registroSalvo;
  } catch (error) {
    console.error('Erro ao executar e cadastrar estimativas de mérito:', error);
    throw error;
  }
}

// Script para popular o banco com o primeiro registro
export async function popularBancoComPrimeiraEstimativa() {
  console.log('Populando o banco com a primeira estimativa de mérito...');
  try {
    const resultado = await executarECadastrarEstimativasMerito();
    console.log('População concluída com sucesso. ID do registro:', resultado.id);
    return resultado;
  } catch (err) {
    console.error('Falha ao popular o banco:', err);
    throw err;
  }
} 