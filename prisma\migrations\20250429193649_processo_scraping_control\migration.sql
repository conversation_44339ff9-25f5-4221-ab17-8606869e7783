/*
  Warnings:

  - You are about to drop the `Scraping<PERSON>ob` table. If the table is not empty, all the data it contains will be lost.

*/
-- <PERSON><PERSON><PERSON>num
CREATE TYPE "ScrapingStage" AS ENUM ('AGUAR<PERSON><PERSON>O_PUBLICACAO', 'AGU<PERSON><PERSON><PERSON><PERSON>_FIM_PRAZO_OPOSICAO', 'AG<PERSON><PERSON><PERSON><PERSON><PERSON>_PAGAMENTO_TAXA_CONCESSAO', 'MON<PERSON><PERSON><PERSON>DO_NULIDADE_POS_CONCESSAO', 'MONIT<PERSON>ANDO_POS_NOTIFICACAO_CADUCIDADE', 'MONITORANDO_PERIODO_RENOVACAO', 'AGUAR<PERSON><PERSON><PERSON>_RECURSO_INDEFERIMENTO', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_POS_RECURSO_PROVIDO', 'MON<PERSON><PERSON>ANDO_POS_RECURSO_NEGADO', 'CONCLUIDO_SEM_SCRAPING_ATIVO');

-- DropTable
DROP TABLE "ScrapingJob";

-- DropEnum
DROP TYPE "ScrapingJobStatus";

-- CreateTable
CREATE TABLE "ProcessoScrapingControl" (
    "id" TEXT NOT NULL,
    "processoId" TEXT NOT NULL,
    "processoNumero" TEXT NOT NULL,
    "stage" "ScrapingStage" NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastScrapedAt" TIMESTAMP(3),
    "nextScrapeDueAt" TIMESTAMP(3),
    "stageEnteredAt" TIMESTAMP(3) NOT NULL,
    "stageCompletedAt" TIMESTAMP(3),
    "failureCount" INTEGER NOT NULL DEFAULT 0,
    "lastErrorMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProcessoScrapingControl_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ProcessoScrapingControl_processoId_key" ON "ProcessoScrapingControl"("processoId");

-- CreateIndex
CREATE INDEX "ProcessoScrapingControl_stage_isActive_nextScrapeDueAt_idx" ON "ProcessoScrapingControl"("stage", "isActive", "nextScrapeDueAt");

-- CreateIndex
CREATE INDEX "ProcessoScrapingControl_processoNumero_idx" ON "ProcessoScrapingControl"("processoNumero");

-- AddForeignKey
ALTER TABLE "ProcessoScrapingControl" ADD CONSTRAINT "ProcessoScrapingControl_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE CASCADE ON UPDATE CASCADE;
