import { PrismaClient, TipoComunicado, ComunicadoStatus } from '@prisma/client';
import readline from 'readline';

const prisma = new PrismaClient();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

interface FiltrosComunicados {
  clienteId?: number;
  clienteIdentificador?: string;
  dataInicial?: Date;
  dataFinal?: Date;
  tipo?: TipoComunicado;
  status?: ComunicadoStatus;
}

async function obterEstatisticasPorFiltro(filtros: FiltrosComunicados) {
  // Construir where clause para Comunicado
  const whereComunicado: any = {};
  if (filtros.clienteId) whereComunicado.clienteId = filtros.clienteId;
  if (filtros.dataInicial || filtros.dataFinal) {
    whereComunicado.dataEnvio = {};
    if (filtros.dataInicial) whereComunicado.dataEnvio.gte = filtros.dataInicial;
    if (filtros.dataFinal) whereComunicado.dataEnvio.lte = filtros.dataFinal;
  }
  if (filtros.tipo) whereComunicado.tipo = filtros.tipo;
  if (filtros.status) whereComunicado.status = filtros.status;

  // Para buscar por identificador, precisamos fazer join
  if (filtros.clienteIdentificador) {
    whereComunicado.cliente = {
      identificador: filtros.clienteIdentificador
    };
  }

  // Construir where clause para ComunicadoPrazoMerito
  const wherePrazoMerito: any = {};
  if (filtros.clienteId) {
    wherePrazoMerito.processo = {
      clienteId: filtros.clienteId
    };
  }
  if (filtros.clienteIdentificador) {
    wherePrazoMerito.processo = {
      cliente: {
        identificador: filtros.clienteIdentificador
      }
    };
  }
  if (filtros.dataInicial || filtros.dataFinal) {
    wherePrazoMerito.dataEnvio = {};
    if (filtros.dataInicial) wherePrazoMerito.dataEnvio.gte = filtros.dataInicial;
    if (filtros.dataFinal) wherePrazoMerito.dataEnvio.lte = filtros.dataFinal;
  }

  const [
    totalComunicados,
    totalComunicadosPrazoMerito
  ] = await Promise.all([
    prisma.comunicado.count({ where: whereComunicado }),
    prisma.comunicadoPrazoMerito.count({ where: wherePrazoMerito })
  ]);

  return {
    totalComunicados,
    totalComunicadosPrazoMerito,
    totalGeral: totalComunicados + totalComunicadosPrazoMerito
  };
}

async function perguntarFiltros(): Promise<FiltrosComunicados> {
  const filtros: FiltrosComunicados = {};

  console.log('\n🔧 Configurando filtros para limpeza seletiva...\n');

  return new Promise((resolve) => {
    const perguntas = [
      {
        pergunta: '1. ID do Cliente (deixe vazio para ignorar): ',
        processar: (resposta: string) => {
          if (resposta.trim()) filtros.clienteId = parseInt(resposta.trim());
        }
      },
      {
        pergunta: '2. Identificador do Cliente (deixe vazio para ignorar): ',
        processar: (resposta: string) => {
          if (resposta.trim()) filtros.clienteIdentificador = resposta.trim();
        }
      },
      {
        pergunta: '3. Data inicial (YYYY-MM-DD, deixe vazio para ignorar): ',
        processar: (resposta: string) => {
          if (resposta.trim()) filtros.dataInicial = new Date(resposta.trim());
        }
      },
      {
        pergunta: '4. Data final (YYYY-MM-DD, deixe vazio para ignorar): ',
        processar: (resposta: string) => {
          if (resposta.trim()) filtros.dataFinal = new Date(resposta.trim());
        }
      }
    ];

    let indiceAtual = 0;

    const fazerProximaPergunta = () => {
      if (indiceAtual >= perguntas.length) {
        resolve(filtros);
        return;
      }

      const perguntaAtual = perguntas[indiceAtual];
      rl.question(perguntaAtual.pergunta, (resposta) => {
        try {
          perguntaAtual.processar(resposta);
        } catch (error) {
          console.log('⚠️  Valor inválido, ignorando...');
        }
        indiceAtual++;
        fazerProximaPergunta();
      });
    };

    fazerProximaPergunta();
  });
}

async function limparComunicadosSeletivo() {
  try {
    console.log('🎯 LIMPEZA SELETIVA DE COMUNICADOS');
    console.log('Este script permite deletar comunicados com filtros específicos.\n');

    const filtros = await perguntarFiltros();

    console.log('\n📋 Filtros aplicados:');
    if (filtros.clienteId) console.log(`   • Cliente ID: ${filtros.clienteId}`);
    if (filtros.clienteIdentificador) console.log(`   • Cliente Identificador: ${filtros.clienteIdentificador}`);
    if (filtros.dataInicial) console.log(`   • Data inicial: ${filtros.dataInicial.toISOString().split('T')[0]}`);
    if (filtros.dataFinal) console.log(`   • Data final: ${filtros.dataFinal.toISOString().split('T')[0]}`);
    
    if (Object.keys(filtros).length === 0) {
      console.log('   • Nenhum filtro aplicado (TODOS os comunicados serão deletados!)');
    }

    console.log('\n🔍 Analisando comunicados que atendem aos filtros...');

    const stats = await obterEstatisticasPorFiltro(filtros);

    console.log('\n📊 Comunicados encontrados:');
    console.log(`   • Comunicados gerais: ${stats.totalComunicados}`);
    console.log(`   • Comunicados de prazo/mérito: ${stats.totalComunicadosPrazoMerito}`);
    console.log(`   • TOTAL: ${stats.totalGeral} registros\n`);

    if (stats.totalGeral === 0) {
      console.log('✅ Não há comunicados que atendam aos filtros especificados!');
      return;
    }

    console.warn('\x1b[31m%s\x1b[0m', '⚠️  ATENÇÃO: Esta operação é IRREVERSÍVEL!');
    console.warn('\x1b[33m%s\x1b[0m', `Serão deletados ${stats.totalGeral} registros de comunicados.`);

    return new Promise<void>((resolve) => {
      rl.question('\nTem certeza que deseja continuar? Digite "SIM, DELETAR" para confirmar: ', async (answer) => {
        if (answer.trim() === 'SIM, DELETAR') {
          console.log('\n🔄 Iniciando limpeza seletiva...\n');

          try {
            await prisma.$transaction(async (tx) => {
              let totalDeletados = 0;

              // Deletar comunicados gerais
              if (stats.totalComunicados > 0) {
                console.log('1️⃣ Deletando comunicados gerais...');
                const whereComunicado: any = {};
                
                if (filtros.clienteId) whereComunicado.clienteId = filtros.clienteId;
                if (filtros.dataInicial || filtros.dataFinal) {
                  whereComunicado.dataEnvio = {};
                  if (filtros.dataInicial) whereComunicado.dataEnvio.gte = filtros.dataInicial;
                  if (filtros.dataFinal) whereComunicado.dataEnvio.lte = filtros.dataFinal;
                }
                if (filtros.tipo) whereComunicado.tipo = filtros.tipo;
                if (filtros.status) whereComunicado.status = filtros.status;
                if (filtros.clienteIdentificador) {
                  whereComunicado.cliente = { identificador: filtros.clienteIdentificador };
                }

                const result = await tx.comunicado.deleteMany({ where: whereComunicado });
                console.log(`   ✅ ${result.count} comunicados gerais deletados`);
                totalDeletados += result.count;
              }

              // Deletar comunicados de prazo/mérito
              if (stats.totalComunicadosPrazoMerito > 0) {
                console.log('2️⃣ Deletando comunicados de prazo/mérito...');
                
                // Primeiro obter os IDs dos comunicados que atendem aos filtros
                const wherePrazoMerito: any = {};
                if (filtros.clienteId) {
                  wherePrazoMerito.processo = { clienteId: filtros.clienteId };
                }
                if (filtros.clienteIdentificador) {
                  wherePrazoMerito.processo = {
                    cliente: { identificador: filtros.clienteIdentificador }
                  };
                }
                if (filtros.dataInicial || filtros.dataFinal) {
                  wherePrazoMerito.dataEnvio = {};
                  if (filtros.dataInicial) wherePrazoMerito.dataEnvio.gte = filtros.dataInicial;
                  if (filtros.dataFinal) wherePrazoMerito.dataEnvio.lte = filtros.dataFinal;
                }

                const comunicadosPrazoMerito = await tx.comunicadoPrazoMerito.findMany({
                  where: wherePrazoMerito,
                  select: { id: true }
                });

                if (comunicadosPrazoMerito.length > 0) {
                  const ids = comunicadosPrazoMerito.map(c => c.id);
                  
                  // Deletar históricos relacionados
                  const historicoResult = await tx.historicoComunicadoPrazoMerito.deleteMany({
                    where: { comunicadoPrazoMeritoId: { in: ids } }
                  });
                  console.log(`   ✅ ${historicoResult.count} registros de histórico deletados`);

                  // Deletar os comunicados de prazo/mérito
                  const prazoMeritoResult = await tx.comunicadoPrazoMerito.deleteMany({
                    where: { id: { in: ids } }
                  });
                  console.log(`   ✅ ${prazoMeritoResult.count} comunicados de prazo/mérito deletados`);
                  totalDeletados += prazoMeritoResult.count + historicoResult.count;
                }
              }

              console.log(`\n🎉 Limpeza seletiva concluída! ${totalDeletados} registros removidos.`);
            });

          } catch (error) {
            console.error('\n❌ Erro durante a limpeza seletiva:', error);
            throw error;
          }
        } else {
          console.log('\n❌ Operação cancelada pelo usuário.');
        }
        
        resolve();
      });
    });

  } catch (error) {
    console.error('\n❌ Erro na limpeza seletiva:', error);
    throw error;
  }
}

// Função principal
async function main() {
  try {
    console.log('🚀 Iniciando script de limpeza seletiva de comunicados...');
    await limparComunicadosSeletivo();
  } catch (error) {
    console.error('\n💥 Falha no script:', error);
    process.exit(1);
  } finally {
    rl.close();
    await prisma.$disconnect();
    console.log('\n👋 Script finalizado.');
    process.exit(0);
  }
}

// Executar o script
main(); 