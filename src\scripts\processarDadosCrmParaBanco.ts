import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
import fs from "fs";
import path from "path";
import { Logger } from "../services/logger.service";

dotenv.config();

const prisma = new PrismaClient();

interface ProcessamentoStats {
  totalOportunidades: number;
  clientesCriados: number;
  clientesAtualizados: number;
  contatosCriados: number;
  processosVinculados: number;
  titularesRelacionados: number;
  erros: number;
  tempoExecucao: number;
}

interface DadosSanitizados {
  crmId: number;
  nome: string;
  numeroDocumento: string | null;
  tipoDocumento: string;
  telefone: string | null;
  telefoneSecundario: string | null;
  email: string | null;
  endereco: string | null;
  cidade: string | null;
  estado: string | null;
  cep: string | null;
  nomeMarca: string | null;
  stageId: string;
  customFields: any[];
  numeroProcesso: string | null;
  isValido: boolean;
  motivosInvalidez: string[];
}

interface RelacionamentoTitular {
  titularId: string;
  clienteId: number;
  numeroProcesso: string;
  motivoRelacionamento: string;
}

// Função para sanitizar telefone
function sanitizarTelefone(telefone: string | null | undefined): string | null {
  if (!telefone || typeof telefone !== 'string') return null;
  
  // Remove todos os caracteres não numéricos
  const apenasNumeros = telefone.replace(/\D/g, '');
  
  // Verifica se tem o mínimo de dígitos (10 ou 11)
  if (apenasNumeros.length < 10 || apenasNumeros.length > 11) return null;
  
  // Remove código do país se presente (55)
  let numeroLimpo = apenasNumeros;
  if (numeroLimpo.startsWith('55') && numeroLimpo.length >= 12) {
    numeroLimpo = numeroLimpo.substring(2);
  }
  
  // Verifica DDD válido (primeiros 2 dígitos)
  const ddd = numeroLimpo.substring(0, 2);
  if (!/^[1-9][1-9]$/.test(ddd)) return null;
  
  // Formatar telefone
  const resto = numeroLimpo.substring(2);
  if (resto.length === 9 && resto.startsWith('9')) {
    // Celular
    return `(${ddd}) ${resto.substring(0, 5)}-${resto.substring(5)}`;
  } else if (resto.length === 8) {
    // Fixo
    return `(${ddd}) ${resto.substring(0, 4)}-${resto.substring(4)}`;
  }
  
  return null;
}

// Função para extrair identificador dos últimos 3 dígitos do documento
function extrairIdentificador(documento: string | null): string | null {
  if (!documento) return null;
  
  const apenasNumeros = documento.replace(/\D/g, '');
  if (apenasNumeros.length < 3) return null;
  
  // Pega os últimos 3 dígitos + 7 dígitos aleatórios para formar o identificador de 10 dígitos
  const ultimos3 = apenasNumeros.slice(-3);
  const aleatorios = Math.random().toString().slice(2, 9); // 7 dígitos aleatórios
  
  return aleatorios + ultimos3;
}

// Função para extrair número do processo dos custom fields
function extrairNumeroProcesso(customFields: any[]): string | null {
  if (!Array.isArray(customFields)) return null;
  
  // Campo ID 213657 é "Nome da marca ✍️" - pode conter número do processo
  const campoMarca = customFields.find(field => field.id === 213657);
  if (!campoMarca?.value) return null;
  
  // Tentar extrair número de processo do formato: XXXXXXXXX (9 dígitos)
  const matches = campoMarca.value.match(/\b\d{9}\b/g);
  return matches ? matches[0] : null;
}

// Função para sanitizar dados de uma oportunidade
function sanitizarOportunidade(oportunidade: any): DadosSanitizados {
  const motivosInvalidez: string[] = [];
  
  // Extrair dados básicos
  const crmId = oportunidade.id;
  const nome = oportunidade.person?.name || oportunidade.name || null;
  const customFields = oportunidade.customFields || [];
  
  // Extrair documentos
  const cpf = oportunidade.person?.cpf || null;
  const cnpj = oportunidade.person?.cnpj || oportunidade.company?.cnpj || null;
  let numeroDocumento = cpf || cnpj || null;
  let tipoDocumento = 'CPF';
  
  if (cnpj && !cpf) {
    tipoDocumento = 'CNPJ';
  }
  
  // Limpar documento
  if (numeroDocumento) {
    numeroDocumento = numeroDocumento.replace(/\D/g, '');
    if (numeroDocumento.length !== 11 && numeroDocumento.length !== 14) {
      motivosInvalidez.push('Documento inválido');
      numeroDocumento = null;
    }
  } else {
    motivosInvalidez.push('Sem documento');
  }
  
  // Extrair contatos
  const telefones = oportunidade.person?.contactPhones || [];
  const emails = oportunidade.person?.contactEmails || [];
  
  let telefone = null;
  let telefoneSecundario = null;
  
  if (telefones.length > 0) {
    telefone = sanitizarTelefone(telefones[0]?.phone);
    if (telefones.length > 1) {
      telefoneSecundario = sanitizarTelefone(telefones[1]?.phone);
    }
  }
  
  if (!telefone) {
    motivosInvalidez.push('Sem telefone válido');
  }
  
  const email = emails.find((e: any) => e.is_main)?.email || emails[0]?.email || null;
  
  // Extrair endereço
  const person = oportunidade.person;
  let endereco = null;
  let cidade = null;
  let estado = null;
  let cep = null;
  
  if (person) {
    endereco = person.address ? `${person.address} ${person.address_number || ''}`.trim() : null;
    cidade = person.district || person.city || null;
    estado = person.state || null;
    cep = person.address_postal_code || null;
  }
  
  // Extrair marca
  const nomeMarca = customFields.find((field: any) => field.id === 213657)?.value || null;
  
  // Extrair stage
  const stageId = oportunidade.stage_id?.toString() || '';
  
  // Extrair número do processo
  const numeroProcesso = extrairNumeroProcesso(customFields);
  
  // Validações
  if (!nome) motivosInvalidez.push('Sem nome');
  if (!crmId) motivosInvalidez.push('Sem CRM ID');
  
  const isValido = motivosInvalidez.length === 0 && nome && telefone;
  
  return {
    crmId,
    nome: nome || `Cliente CRM ${crmId}`,
    numeroDocumento,
    tipoDocumento,
    telefone,
    telefoneSecundario,
    email,
    endereco,
    cidade,
    estado,
    cep,
    nomeMarca,
    stageId,
    customFields,
    numeroProcesso,
    isValido,
    motivosInvalidez
  };
}

// Função para buscar JSONs na pasta
async function buscarArquivosJson(basePath?: string): Promise<string[]> {
  Logger.info("🔍 Buscando arquivos JSON para processar...");
  
  let pastaBase = basePath;
  
  if (!pastaBase) {
    // Buscar a pasta mais recente em dados-crm-extraidos
    const pastaPrincipal = path.join(process.cwd(), 'dados-crm-extraidos');
    
    if (!fs.existsSync(pastaPrincipal)) {
      throw new Error("Pasta 'dados-crm-extraidos' não encontrada. Execute primeiro o orquestrador CRM.");
    }
    
    const subpastas = fs.readdirSync(pastaPrincipal, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name)
      .sort()
      .reverse(); // Mais recente primeiro
    
    if (subpastas.length === 0) {
      throw new Error("Nenhuma subpasta encontrada em 'dados-crm-extraidos'.");
    }
    
    pastaBase = path.join(pastaPrincipal, subpastas[0]);
    Logger.info(`📂 Usando pasta mais recente: ${subpastas[0]}`);
  }
  
  const pastaOportunidades = path.join(pastaBase, 'oportunidades-completas');
  
  if (!fs.existsSync(pastaOportunidades)) {
    throw new Error(`Pasta de oportunidades não encontrada: ${pastaOportunidades}`);
  }
  
  const arquivos = fs.readdirSync(pastaOportunidades)
    .filter(arquivo => arquivo.endsWith('.json'))
    .map(arquivo => path.join(pastaOportunidades, arquivo))
    .sort();
  
  Logger.success(`✅ ${arquivos.length} arquivos JSON encontrados`);
  return arquivos;
}

// Função para processar um arquivo JSON
async function processarArquivoJson(caminhoArquivo: string): Promise<DadosSanitizados[]> {
  const nomeArquivo = path.basename(caminhoArquivo);
  Logger.info(`📄 Processando ${nomeArquivo}...`);
  
  try {
    const conteudo = fs.readFileSync(caminhoArquivo, 'utf8');
    const dados = JSON.parse(conteudo);
    
    if (!dados.oportunidades || !Array.isArray(dados.oportunidades)) {
      throw new Error(`Formato inválido no arquivo ${nomeArquivo}`);
    }
    
    const dadosSanitizados = dados.oportunidades.map(sanitizarOportunidade);
    
    Logger.success(`✅ ${nomeArquivo}: ${dadosSanitizados.length} oportunidades processadas`);
    return dadosSanitizados;
    
  } catch (error: any) {
    Logger.error(`❌ Erro ao processar ${nomeArquivo}: ${error.message}`);
    return [];
  }
}

// Função para criar/atualizar cliente
async function criarOuAtualizarCliente(dados: DadosSanitizados): Promise<number | null> {
  try {
    // Verificar se cliente já existe
    const clienteExistente = await prisma.cliente.findUnique({
      where: { crmId: dados.crmId },
      include: { contatos: true }
    });
    
    const identificador = extrairIdentificador(dados.numeroDocumento);
    
    const dadosCliente = {
      nome: dados.nome,
      crmId: dados.crmId,
      numeroDocumento: dados.numeroDocumento,
      tipoDeDocumento: dados.tipoDocumento,
      nomeDaMarca: dados.nomeMarca,
      crmStageId: dados.stageId,
      identificador: identificador,
      camposPersonalizados: dados.customFields.length > 0 ? dados.customFields : undefined
    };
    
    if (clienteExistente) {
      // Atualizar cliente existente
      await prisma.cliente.update({
        where: { id: clienteExistente.id },
        data: dadosCliente
      });
      
      // Atualizar contatos se necessário
      if (clienteExistente.contatos.length === 0 && dados.telefone) {
        await prisma.contatoCliente.create({
          data: {
            clienteId: clienteExistente.id,
            telefone: dados.telefone,
            telefoneSegundario: dados.telefoneSecundario,
            email: dados.email,
            endereco: dados.endereco,
            cidade: dados.cidade,
            estado: dados.estado,
            cep: dados.cep
          }
        });
      }
      
      return clienteExistente.id;
    } else {
      // Criar novo cliente
      const novoCliente = await prisma.cliente.create({
        data: {
          ...dadosCliente,
          contatos: dados.telefone ? {
            create: {
              telefone: dados.telefone,
              telefoneSegundario: dados.telefoneSecundario,
              email: dados.email,
              endereco: dados.endereco,
              cidade: dados.cidade,
              estado: dados.estado,
              cep: dados.cep
            }
          } : undefined
        }
      });
      
      return novoCliente.id;
    }
    
  } catch (error: any) {
    Logger.error(`❌ Erro ao criar/atualizar cliente CRM ${dados.crmId}: ${error.message}`);
    return null;
  }
}

// Função para relacionar cliente com processo
async function relacionarClienteComProcesso(clienteId: number, numeroProcesso: string | null): Promise<boolean> {
  if (!numeroProcesso) return false;
  
  try {
    const processo = await prisma.processo.findUnique({
      where: { numero: numeroProcesso }
    });
    
    if (!processo) return false;
    
    // Vincular cliente ao processo se ainda não estiver vinculado
    if (!processo.clienteId) {
      await prisma.processo.update({
        where: { id: processo.id },
        data: { clienteId }
      });
      return true;
    }
    
    return false;
  } catch (error: any) {
    Logger.error(`❌ Erro ao relacionar cliente ${clienteId} com processo ${numeroProcesso}: ${error.message}`);
    return false;
  }
}

// Função para relacionar cliente com titulares
async function relacionarClienteComTitulares(clienteId: number, numeroProcesso: string | null): Promise<RelacionamentoTitular[]> {
  const relacionamentos: RelacionamentoTitular[] = [];
  
  if (!numeroProcesso) return relacionamentos;
  
  try {
    // Buscar titulares do processo que têm numeroDocumento
    const titulares = await prisma.titular.findMany({
      where: {
        processo: { numero: numeroProcesso },
        numeroDocumento: { not: null },
        clienteId: null // Só relacionar se ainda não tiver cliente
      }
    });
    
    for (const titular of titulares) {
      await prisma.titular.update({
        where: { id: titular.id },
        data: { clienteId }
      });
      
      relacionamentos.push({
        titularId: titular.id,
        clienteId,
        numeroProcesso,
        motivoRelacionamento: 'Processo em comum + numeroDocumento válido'
      });
    }
    
    return relacionamentos;
    
  } catch (error: any) {
    Logger.error(`❌ Erro ao relacionar cliente ${clienteId} com titulares do processo ${numeroProcesso}: ${error.message}`);
    return relacionamentos;
  }
}

// Função principal de processamento
async function processarDadosCrmParaBanco(basePath?: string): Promise<ProcessamentoStats> {
  const startTime = Date.now();
  const stats: ProcessamentoStats = {
    totalOportunidades: 0,
    clientesCriados: 0,
    clientesAtualizados: 0,
    contatosCriados: 0,
    processosVinculados: 0,
    titularesRelacionados: 0,
    erros: 0,
    tempoExecucao: 0
  };
  
  try {
    Logger.section("🏗️ PROCESSAMENTO DE DADOS CRM PARA BANCO");
    
    // 1. Buscar arquivos JSON
    const arquivosJson = await buscarArquivosJson(basePath);
    
    if (arquivosJson.length === 0) {
      throw new Error("Nenhum arquivo JSON encontrado para processar");
    }
    
    // 2. Processar cada arquivo
    const todosDadosSanitizados: DadosSanitizados[] = [];
    
    for (const arquivo of arquivosJson) {
      const dadosArquivo = await processarArquivoJson(arquivo);
      todosDadosSanitizados.push(...dadosArquivo);
    }
    
    stats.totalOportunidades = todosDadosSanitizados.length;
    Logger.info(`📊 Total de oportunidades para processar: ${stats.totalOportunidades}`);
    
    // 3. Filtrar apenas dados válidos
    const dadosValidos = todosDadosSanitizados.filter(dados => dados.isValido);
    const dadosInvalidos = todosDadosSanitizados.filter(dados => !dados.isValido);
    
    Logger.info(`✅ Oportunidades válidas: ${dadosValidos.length}`);
    Logger.info(`❌ Oportunidades inválidas: ${dadosInvalidos.length}`);
    
    // 4. Processar dados válidos
    Logger.info("🔄 Iniciando criação/atualização de clientes...");
    
    for (let i = 0; i < dadosValidos.length; i++) {
      const dados = dadosValidos[i];
      
      try {
        // Criar/atualizar cliente
        const clienteId = await criarOuAtualizarCliente(dados);
        
        if (clienteId) {
          stats.clientesCriados++; // Simplificado, na prática seria clientesCriados vs atualizados
          
          // Relacionar com processo
          const processoVinculado = await relacionarClienteComProcesso(clienteId, dados.numeroProcesso);
          if (processoVinculado) {
            stats.processosVinculados++;
          }
          
          // Relacionar com titulares
          const relacionamentosTitulares = await relacionarClienteComTitulares(clienteId, dados.numeroProcesso);
          stats.titularesRelacionados += relacionamentosTitulares.length;
        }
        
        // Log de progresso a cada 100 registros
        if ((i + 1) % 100 === 0) {
          Logger.info(`📈 Progresso: ${i + 1}/${dadosValidos.length} (${((i + 1) / dadosValidos.length * 100).toFixed(1)}%)`);
        }
        
      } catch (error: any) {
        stats.erros++;
        Logger.error(`❌ Erro ao processar cliente CRM ${dados.crmId}: ${error.message}`);
      }
    }
    
    stats.tempoExecucao = (Date.now() - startTime) / 1000;
    
    return stats;
    
  } catch (error: any) {
    Logger.error("❌ Erro durante processamento:", error.message);
    stats.tempoExecucao = (Date.now() - startTime) / 1000;
    throw error;
  }
}

// Função para gerar relatório
async function gerarRelatorioProcessamento(stats: ProcessamentoStats): Promise<void> {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  
  const relatorio = [
    '='.repeat(80),
    'RELATÓRIO DE PROCESSAMENTO CRM → BANCO DE DADOS',
    '='.repeat(80),
    `Data da execução: ${new Date().toLocaleString('pt-BR')}`,
    `Tempo de execução: ${stats.tempoExecucao.toFixed(2)} segundos`,
    '',
    'RESUMO GERAL:',
    `📊 Total de oportunidades processadas: ${stats.totalOportunidades.toLocaleString('pt-BR')}`,
    `👥 Clientes criados/atualizados: ${stats.clientesCriados.toLocaleString('pt-BR')}`,
    `📞 Contatos criados: ${stats.contatosCriados.toLocaleString('pt-BR')}`,
    `🔗 Processos vinculados: ${stats.processosVinculados.toLocaleString('pt-BR')}`,
    `👤 Titulares relacionados: ${stats.titularesRelacionados.toLocaleString('pt-BR')}`,
    `❌ Erros durante processamento: ${stats.erros.toLocaleString('pt-BR')}`,
    '',
    'TAXA DE SUCESSO:',
    `✅ ${((stats.clientesCriados / stats.totalOportunidades) * 100).toFixed(1)}% das oportunidades geraram clientes`,
    `🔗 ${((stats.processosVinculados / stats.clientesCriados) * 100).toFixed(1)}% dos clientes foram vinculados a processos`,
    `👤 ${((stats.titularesRelacionados / stats.clientesCriados) * 100).toFixed(1)}% dos clientes foram relacionados com titulares`,
    '',
    'PRÓXIMOS PASSOS:',
    '1. Verificar clientes sem processos vinculados',
    '2. Analisar titulares não relacionados',
    '3. Validar identificadores gerados',
    '4. Testar sistema de login',
    '',
    '='.repeat(80),
    ''
  ].join('\n');
  
  fs.writeFileSync(`relatorio-processamento-crm-${timestamp}.txt`, relatorio, 'utf8');
  
  Logger.success(`📋 Relatório salvo: relatorio-processamento-crm-${timestamp}.txt`);
}

// Função principal
async function main() {
  Logger.section("🎯 PROCESSAMENTO DE DADOS CRM PARA BANCO");
  
  try {
    const resultado = await processarDadosCrmParaBanco();
    
    Logger.success("\n✅ PROCESSAMENTO CONCLUÍDO!");
    Logger.info("=".repeat(60));
    Logger.info(`📊 Oportunidades: ${resultado.totalOportunidades.toLocaleString('pt-BR')}`);
    Logger.info(`👥 Clientes: ${resultado.clientesCriados.toLocaleString('pt-BR')}`);
    Logger.info(`🔗 Processos vinculados: ${resultado.processosVinculados.toLocaleString('pt-BR')}`);
    Logger.info(`👤 Titulares relacionados: ${resultado.titularesRelacionados.toLocaleString('pt-BR')}`);
    Logger.info(`❌ Erros: ${resultado.erros.toLocaleString('pt-BR')}`);
    Logger.info(`⏱️ Tempo: ${resultado.tempoExecucao.toFixed(2)} segundos`);
    
    await gerarRelatorioProcessamento(resultado);
    
    Logger.info("\n💡 ESTRUTURA CRIADA:");
    Logger.info("✅ Clientes com dados sanitizados");
    Logger.info("✅ Contatos vinculados aos clientes");
    Logger.info("✅ Relacionamento Cliente ↔ Processo");
    Logger.info("✅ Relacionamento Cliente ↔ Titular (via processo comum)");
    
  } catch (error: any) {
    Logger.error("❌ Erro durante a execução:", error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  main();
}

export { processarDadosCrmParaBanco }; 