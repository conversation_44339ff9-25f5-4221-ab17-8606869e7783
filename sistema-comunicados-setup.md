# Sistema de Comunicados REGISTRE-SE (Sistema B)

## 📋 Visão Geral do Projeto

Este é o **Sistema B** da arquitetura de comunicados automáticos da REGISTRE-SE. Trabalha em conjunto com o Sistema A (api-v3-rgsys) para processar e enviar comunicados inteligentes sobre despachos de marcas do INPI.

### 🏗️ Arquitetura Geral

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Sistema A     │    │   Banco Shared   │    │   Sistema B     │
│  (processJson)  │───▶│  DespachoElegivel│◀───│ (comunicados)   │
│                 │    │                  │    │                 │
│ • Processa RPIs │    │ • PENDENTE       │    │ • <PERSON><PERSON><PERSON> tipos │
│ • Registra      │    │ • PROCESSANDO    │    │ • Envia comunic.│
│   despachos     │    │ • ENVIADO        │    │ • Atualiza status│
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🎯 Responsabilidades do Sistema B

### ✅ **O que FAZ**
- Monitora `DespachoElegivel` com status `PENDENTE`
- Analisa códigos de serviço e tipos de despacho
- Determina se despacho requer comunicado
- Envia comunicados via Chatguru/RD Station
- Atualiza status e cria registros `Comunicado`
- Logs estruturados e monitoramento

### ❌ **O que NÃO FAZ**
- Processar arquivos RPI (responsabilidade do Sistema A)
- Gerenciar procuradores/titulares
- Criar/atualizar processos
- Scraping ou análise de PDFs

## 🗃️ Modelo de Dados (Compartilhado)

```typescript
// Modelo principal que conecta os sistemas
DespachoElegivel {
  id: uuid
  processoId: string (FK → Processo)
  clienteId: int (FK → Cliente)  
  despachoId: string (FK → Despacho)
  statusComunicado: StatusComunicadoElegivel
  dataRegistro: datetime
  dataProcessamento: datetime?
  
  // Relacionamentos
  processo: Processo
  cliente: Cliente  
  despacho: Despacho
  comunicados: Comunicado[] // 1:N
}

enum StatusComunicadoElegivel {
  PENDENTE      // Criado pelo Sistema A, aguardando processamento
  PROCESSANDO   // Sistema B está analisando
  ENVIADO       // Comunicado enviado com sucesso
  FALHA         // Erro no envio
  IGNORADO      // Despacho analisado mas não requer comunicado
}
```

## 🚀 Setup do Projeto

### 1. **Pré-requisitos**
```bash
# Node.js 18+ e npm
node --version  # >= 18.0.0
npm --version

# PostgreSQL (mesmo banco do Sistema A)
# Variáveis de ambiente do banco já configuradas
```

### 2. **Inicialização do Projeto**
```bash
# Criar diretório e inicializar
mkdir sistema-comunicados-registrese
cd sistema-comunicados-registrese

# Inicializar package.json
npm init -y

# Instalar dependências principais
npm install express prisma @prisma/client
npm install typescript ts-node @types/node @types/express nodemon --save-dev

# Configurar TypeScript
npx tsc --init
```

### 3. **Configuração do package.json**
```json
{
  "name": "sistema-comunicados-registrese",
  "version": "1.0.0",
  "description": "Sistema B - Processamento e envio de comunicados automáticos",
  "main": "dist/server.js",
  "scripts": {
    "dev": "nodemon src/server.ts",
    "build": "tsc",
    "start": "node dist/server.js",
    "prisma:generate": "prisma generate",
    "prisma:deploy": "prisma migrate deploy"
  },
  "dependencies": {
    "express": "^4.18.2",
    "@prisma/client": "^5.0.0",
    "node-cron": "^3.0.3",
    "axios": "^1.6.0"
  },
  "devDependencies": {
    "typescript": "^5.0.0",
    "ts-node": "^10.9.0",
    "@types/node": "^20.0.0",
    "@types/express": "^4.17.0",
    "@types/node-cron": "^3.0.11",
    "nodemon": "^3.0.0"
  }
}
```

### 4. **Configuração do Prisma (Banco Compartilhado)**
```bash
# Inicializar Prisma
npx prisma init
```

**Arquivo `.env`:**
```env
# Banco de dados compartilhado com Sistema A
DATABASE_URL="postgresql://usuario:senha@localhost:5432/registrese_db"

# APIs de comunicação
CHATGURU_API_KEY="sua_chave_aqui"
RD_STATION_TOKEN="seu_token_aqui"

# Configurações do sistema
NODE_ENV="development"
PORT=3001
```

**Arquivo `prisma/schema.prisma`:**
```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// IMPORTANTE: Copiar TODOS os modelos do Sistema A
// para manter sincronização total

model DespachoElegivel {
  id                String                     @id @default(uuid())
  processoId        String
  clienteId         Int
  despachoId        String
  statusComunicado  StatusComunicadoElegivel   @default(PENDENTE)
  dataRegistro      DateTime                   @default(now())
  dataProcessamento DateTime?

  // Relacionamentos
  processo     Processo     @relation(fields: [processoId], references: [id])
  cliente      Cliente      @relation(fields: [clienteId], references: [id])
  despacho     Despacho     @relation(fields: [despachoId], references: [id])
  comunicados  Comunicado[]

  @@unique([processoId, despachoId])
  @@index([statusComunicado])
  @@index([dataRegistro])
  @@index([clienteId])
  @@map("despachos_elegiveis")
}

enum StatusComunicadoElegivel {
  PENDENTE
  PROCESSANDO
  ENVIADO
  FALHA
  IGNORADO
}

// ... demais modelos copiados do Sistema A
```

### 5. **Sincronizar Schema com Banco Existente**
```bash
# Gerar cliente Prisma baseado no banco existente
npx prisma db pull

# Gerar cliente TypeScript
npx prisma generate
```

### 6. **Estrutura Inicial do Projeto**
```
src/
├── server.ts              # Servidor Express principal
├── app.ts                 # Configuração do app
├── controllers/           # Controllers REST API
│   └── despachoController.ts
├── services/              # Lógica de negócio
│   ├── despachoProcessor.ts
│   ├── comunicadoService.ts
│   └── integracoes/
│       ├── chatguruApi.ts
│       └── rdStationApi.ts
├── jobs/                  # Jobs/Cron tasks
│   └── processarDespachos.ts
├── types/                 # Tipos TypeScript
│   └── comunicados.types.ts
├── utils/                 # Utilitários
│   └── logger.ts
└── config/                # Configurações
    └── database.ts
```

## 📝 Contexto Histórico para IA

### **Problema Original**
A REGISTRE-SE precisava automatizar comunicados para clientes sobre despachos importantes de suas marcas no INPI, mas o sistema monolítico estava misturando responsabilidades.

### **Solução Arquitetural**
1. **Sistema A** (`processJson.ts`) processa RPIs e marca despachos elegíveis
2. **Sistema B** (este projeto) consome despachos elegíveis e envia comunicados

### **Decisões Técnicas Importantes**
- **Procurador REGISTRE-SE:** ID fixo `65b3c0c0-aa3b-4d89-85fb-2a144e538800`
- **Critério de Elegibilidade:** `processo.monitorado = true` E `processo.clienteId != null`
- **Titulares:** Lógica especial para evitar duplicatas entre sistemas
- **Separação:** Sistema A não analisa tipos, apenas registra TODOS os despachos

### **Códigos de Despacho Relevantes**
```typescript
// Exemplos de mapeamento (a ser implementado)
const TIPOS_COMUNICADO = {
  '123': 'OPOSICAO_DEFERIDA',
  '456': 'EXIGENCIA_FORMAL', 
  '789': 'DEFERIMENTO',
  // ... mapeamento completo a ser definido
};
```

## 🔄 Fluxo de Trabalho

### **1. Monitoramento Contínuo**
```typescript
// Job executa a cada 5 minutos
async function processarDespachosPendentes() {
  const despachos = await buscarDespachosPendentes();
  for (const despacho of despachos) {
    await processarDespacho(despacho);
  }
}
```

### **2. Processamento Individual**
```typescript
async function processarDespacho(despacho: DespachoElegivel) {
  // 1. Marcar como PROCESSANDO
  // 2. Analisar códigos e determinar tipo
  // 3. Se requer comunicado → enviar
  // 4. Atualizar status (ENVIADO/IGNORADO/FALHA)
  // 5. Criar registro Comunicado se enviado
}
```

## 🎯 Primeiros Passos de Desenvolvimento

### **Fase 1: Foundation**
1. Setup completo do projeto ✅
2. Sincronização com banco de dados ✅
3. Tipos TypeScript e interfaces ✅
4. Servidor Express básico ✅

### **Fase 2: Core Logic**
1. `DespachoProcessor` - lógica principal
2. Análise de códigos de despacho
3. Integração com APIs externas
4. Sistema de jobs/cron

### **Fase 3: Monitoramento**
1. Dashboard para acompanhamento
2. Logs estruturados
3. Métricas e alertas
4. Tratamento de erros robusto

## 🔗 Integração com Sistema A

O Sistema A já está preparado e registrando `DespachoElegivel` automaticamente. Este sistema deve:

1. **Consumir** registros com `statusComunicado = 'PENDENTE'`
2. **Processar** de forma assíncrona e independente
3. **Atualizar** status após processamento
4. **Não interferir** no funcionamento do Sistema A

## ⚠️ Observações Importantes

- **Banco Compartilhado:** Cuidado com migrações - coordenar com Sistema A
- **IDs Únicos:** Usar UUIDs para evitar conflitos
- **Performance:** Sistema A processa milhares de processos - não impactar
- **Idempotência:** Garantir que reprocessar não cause duplicatas
- **Monitoramento:** Sistema crítico - logs detalhados obrigatórios

---

**Repositório Sistema A:** `api-v3-rgsys`  
**Documentação Adicional:** Consultar `/docs` no Sistema A  
**Banco de Dados:** PostgreSQL compartilhado  
**Maintainer:** Equipe REGISTRE-SE 