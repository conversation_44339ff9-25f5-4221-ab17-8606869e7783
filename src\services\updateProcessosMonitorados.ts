import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient({
  log: ['warn', 'error'],
});

const NOVO_PROCURADOR_ID = "dd38b719-9f86-49f0-a4c0-5eb5a5c80c33";
const BATCH_SIZE = 100;

async function updateProcessosMonitorados(): Promise<void> {
  try {
    console.log("Iniciando atualização dos processos...");

    // Verifica se o procurador monitorado existe
    const procuradorMonitorado = await prisma.procuradorMonitorado.findUnique({
      where: { id: NOVO_PROCURADOR_ID }
    });

    if (!procuradorMonitorado) {
      throw new Error(`ProcuradorMonitorado com ID ${NOVO_PROCURADOR_ID} não encontrado`);
    }

    // Conta total de processos a serem atualizados
    const totalProcessos = await prisma.processo.count({
      where: {
        procurador: {
          nome: {
            contains: "REGISTRE-SE",
            mode: "insensitive"
          }
        }
      }
    });

    console.log(`Total de processos para atualizar: ${totalProcessos}`);

    if (totalProcessos === 0) {
      console.log("Nenhum processo para atualizar.");
      return;
    }

    let processados = 0;

    while (processados < totalProcessos) {
      const processosParaAtualizar = await prisma.processo.findMany({
        where: {
          procurador: {
            nome: {
              contains: "REGISTRE-SE",
              mode: "insensitive"
            }
          },
          monitorado: false
        },
        select: {
          id: true
        },
        take: BATCH_SIZE
      });

      if (processosParaAtualizar.length === 0) {
        break;
      }

      // Atualiza cada processo individualmente
      for (const processo of processosParaAtualizar) {
        await prisma.processo.update({
          where: { id: processo.id },
          data: {
            procuradorMonitorado: {
              connect: { id: NOVO_PROCURADOR_ID }
            },
            monitorado: true
          }
        });
      }

      processados += processosParaAtualizar.length;
      const progresso = Math.round((processados / totalProcessos) * 100);
      console.log(`Progresso: ${progresso}% (${processados}/${totalProcessos})`);
    }

    console.log("Atualização concluída com sucesso!");

  } catch (error) {
    console.error("Erro durante a atualização:", error);
    throw error;
  }
}

// Executa o script
updateProcessosMonitorados()
  .catch((error) => {
    console.error("Erro crítico:", error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 