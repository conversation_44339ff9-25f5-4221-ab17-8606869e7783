import { PrismaClient } from '@prisma/client';
import { extrairImagemMarca, verificarMicroservico, extrairMetadados } from '../utils/pdfProcessorClient';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

// Configurações
const FINAL_PDFS_DIR = path.join(process.cwd(), 'temp', 'final_pdfs');
const UPLOADS_PROTOCOLOS_DIR = path.join(process.cwd(), 'uploads', 'protocolos');
const BATCH_SIZE = 5; // Processar 5 PDFs por vez
const DELAY_BETWEEN_BATCHES = 2000; // 2 segundos entre lotes

interface ProcessResult {
  arquivo: string;
  success: boolean;
  numeroProcesso?: string;
  elementoNominativo?: string;
  logoExtraida?: boolean;
  nomeArquivoFinal?: string;
  error?: string;
  jaExistia?: boolean;
}

interface ProcessStats {
  total: number;
  processados: number;
  sucessos: number;
  falhas: number;
  jaExistiam: number;
  metadadosExtraidos: number;
  logosExtraidas: number;
  adicionadosNaFila: number;
  errors: string[];
}

/**
 * Cria estrutura de diretórios necessários
 */
function criarEstruturaDiretorios(): void {
  const dirs = [UPLOADS_PROTOCOLOS_DIR];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 Diretório criado: ${dir}`);
    }
  });
}

/**
 * Lista todos os PDFs na pasta final
 */
function listarPDFs(): string[] {
  if (!fs.existsSync(FINAL_PDFS_DIR)) {
    throw new Error(`Pasta de PDFs não encontrada: ${FINAL_PDFS_DIR}`);
  }
  
  const arquivos = fs.readdirSync(FINAL_PDFS_DIR)
    .filter(arquivo => arquivo.toLowerCase().endsWith('.pdf'))
    .sort();
  
  console.log(`📄 Encontrados ${arquivos.length} PDFs para processar`);
  return arquivos;
}

/**
 * Verifica se protocolo já foi processado
 */
async function protocoloJaProcessado(nomeArquivo: string): Promise<boolean> {
  // Verificar se já existe na fila de processamento
  const existeNaFila = await prisma.processamentoProtocolo.findFirst({
    where: { nomeArquivoPdf: nomeArquivo }
  });
  
  // Verificar se arquivo já existe na pasta uploads/protocolos
  const caminhoUpload = path.join(UPLOADS_PROTOCOLOS_DIR, nomeArquivo);
  const existeNoUpload = fs.existsSync(caminhoUpload);
  
  return !!(existeNaFila || existeNoUpload);
}

/**
 * Processa um único PDF (LÓGICA IDÊNTICA AO uploadProtocolo)
 */
async function processarPDF(nomeArquivo: string): Promise<ProcessResult> {
  const result: ProcessResult = {
    arquivo: nomeArquivo,
    success: false
  };
  
  console.log(`\n📄 Processando: ${nomeArquivo}`);
  
  try {
    // Verificar se já foi processado
    const jaProcessado = await protocoloJaProcessado(nomeArquivo);
    if (jaProcessado) {
      console.log(`⏭️ Protocolo já processado, pulando...`);
      result.success = true;
      result.jaExistia = true;
      return result;
    }
    
    const arquivoOriginal = path.join(FINAL_PDFS_DIR, nomeArquivo);
    const arquivoTemporario = path.join(UPLOADS_PROTOCOLOS_DIR, nomeArquivo);
    
    // Copiar arquivo para pasta uploads/protocolos
    fs.copyFileSync(arquivoOriginal, arquivoTemporario);
    console.log(`📋 Arquivo copiado para: ${arquivoTemporario}`);
    
    // Verificar se microserviço está online (IGUAL AO CONTROLLER)
    const microservicoOnline = await verificarMicroservico();
    if (!microservicoOnline) {
      console.log('⚠️ Microserviço Python offline - PDF salvo mas não processado');
      result.success = true;
      result.nomeArquivoFinal = nomeArquivo;
      result.error = 'Microserviço offline';
      return result;
    }
    
    // 1. Extrair metadados (IGUAL AO CONTROLLER)
    console.log('🔍 Extraindo metadados do PDF...');
    const metadados = await extrairMetadados(arquivoTemporario);
    
    let novoNomeArquivo: string;
    let caminhoFinal: string;
    
    if (metadados && metadados.process_number && metadados.elemento_nominativo) {
      // Criar nome do arquivo com metadados extraídos (IGUAL AO CONTROLLER)
      novoNomeArquivo = `${metadados.process_number}-${metadados.elemento_nominativo}.pdf`;
      caminhoFinal = path.join(UPLOADS_PROTOCOLOS_DIR, novoNomeArquivo);
      
      // Renomear arquivo (IGUAL AO CONTROLLER)
      if (arquivoTemporario !== caminhoFinal) {
        fs.renameSync(arquivoTemporario, caminhoFinal);
        console.log(`✅ Arquivo renomeado: ${novoNomeArquivo}`);
      }
      
      result.numeroProcesso = metadados.process_number;
      result.elementoNominativo = metadados.elemento_nominativo;
    } else {
      // Se não conseguiu extrair metadados, manter nome original (IGUAL AO CONTROLLER)
      console.log('⚠️ Não foi possível extrair metadados - mantendo nome original');
      novoNomeArquivo = nomeArquivo;
      caminhoFinal = arquivoTemporario;
    }
    
    result.nomeArquivoFinal = novoNomeArquivo;
    
    // 2. Chamar microserviço Python para extrair imagem (IGUAL AO CONTROLLER)
    console.log('🐍 Processando extração de imagem...');
    const resultadoExtracao = await extrairImagemMarca(caminhoFinal);
    
    result.logoExtraida = resultadoExtracao?.success || false;
    
    // 3. Se os metadados foram extraídos com sucesso, adicionar à fila de processamento (IGUAL AO CONTROLLER)
    const numeroProcesso = metadados?.process_number;
    const elementoNominativo = metadados?.elemento_nominativo;
    
    if (numeroProcesso && elementoNominativo) {
      try {
        await prisma.processamentoProtocolo.create({
          data: {
            numeroProcesso: numeroProcesso,
            elementoNominativo: elementoNominativo,
            nomeArquivoPdf: novoNomeArquivo,
            status: 'PENDENTE'
          }
        });
        console.log(`📋 Protocolo ${numeroProcesso} adicionado à fila de processamento`);
      } catch (filaError) {
        // Log do erro mas não falhar o processamento (IGUAL AO CONTROLLER)
        console.error('Erro ao adicionar protocolo à fila:', filaError);
      }
    }
    
    result.success = true;
    console.log(`✅ PDF processado com sucesso: ${novoNomeArquivo}`);
    
  } catch (error) {
    console.error(`❌ Erro ao processar ${nomeArquivo}:`, error);
    result.error = error instanceof Error ? error.message : 'Erro desconhecido';
    
    // Remover arquivo se houve erro
    const arquivoTemporario = path.join(UPLOADS_PROTOCOLOS_DIR, nomeArquivo);
    if (fs.existsSync(arquivoTemporario)) {
      fs.unlinkSync(arquivoTemporario);
    }
  }
  
  return result;
}

/**
 * Processa PDFs em lotes
 */
async function processarLoteDeArquivos(arquivos: string[]): Promise<ProcessResult[]> {
  const resultados: ProcessResult[] = [];
  
  for (let i = 0; i < arquivos.length; i += BATCH_SIZE) {
    const lote = arquivos.slice(i, i + BATCH_SIZE);
    
    console.log(`\n📦 Processando lote ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(arquivos.length / BATCH_SIZE)}`);
    console.log(`📋 Arquivos neste lote: ${lote.length}`);
    
    // Processar lote em paralelo
    const promessasLote = lote.map(arquivo => processarPDF(arquivo));
    const resultadosLote = await Promise.allSettled(promessasLote);
    
    // Processar resultados
    for (let j = 0; j < resultadosLote.length; j++) {
      const resultado = resultadosLote[j];
      
      if (resultado.status === 'fulfilled') {
        resultados.push(resultado.value);
      } else {
        resultados.push({
          arquivo: lote[j],
          success: false,
          error: resultado.reason
        });
      }
    }
    
    // Delay entre lotes
    if (i + BATCH_SIZE < arquivos.length) {
      console.log(`⏸️ Aguardando ${DELAY_BETWEEN_BATCHES}ms antes do próximo lote...`);
      await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES));
    }
  }
  
  return resultados;
}

/**
 * Gera relatório final
 */
function gerarRelatorio(stats: ProcessStats, resultados: ProcessResult[]): string {
  const timestamp = new Date().toLocaleString('pt-BR');
  const sucessos = resultados.filter(r => r.success);
  const falhas = resultados.filter(r => !r.success);
  const jaExistiam = resultados.filter(r => r.jaExistia);
  const novos = sucessos.filter(r => !r.jaExistia);
  
  let relatorio = `# 📊 Relatório - Processamento Protocolos Baixados\n\n`;
  relatorio += `**Data/Hora:** ${timestamp}\n\n`;
  
  // Resumo Geral
  relatorio += `## 📈 Resumo da Operação\n\n`;
  relatorio += `| Métrica | Valor |\n`;
  relatorio += `|---------|-------|\n`;
  relatorio += `| 📊 PDFs Encontrados | ${stats.total} |\n`;
  relatorio += `| ✅ Processados com Sucesso | ${stats.sucessos} |\n`;
  relatorio += `| ❌ Falhas | ${stats.falhas} |\n`;
  relatorio += `| ⏭️ Já Existiam | ${stats.jaExistiam} |\n`;
  relatorio += `| 🆕 Novos Processados | ${novos.length} |\n`;
  relatorio += `| 🔍 Metadados Extraídos | ${stats.metadadosExtraidos} |\n`;
  relatorio += `| 🖼️ Logos Extraídas | ${stats.logosExtraidas} |\n`;
  relatorio += `| 📋 Adicionados na Fila | ${stats.adicionadosNaFila} |\n`;
  
  if (stats.total > 0) {
    const taxaSucesso = ((stats.sucessos / stats.total) * 100).toFixed(1);
    relatorio += `| 🎯 Taxa de Sucesso | ${taxaSucesso}% |\n`;
  }
  
  relatorio += `\n`;
  
  // Detalhes das Falhas
  if (falhas.length > 0) {
    relatorio += `## ❌ Falhas de Processamento\n\n`;
    falhas.forEach((falha, index) => {
      relatorio += `### ${index + 1}. ${falha.arquivo}\n`;
      relatorio += `- **Erro:** ${falha.error}\n\n`;
    });
  }
  
  // Sucessos com Metadados
  const sucessosComMetadados = sucessos.filter(s => s.numeroProcesso && !s.jaExistia);
  if (sucessosComMetadados.length > 0) {
    relatorio += `## ✅ Protocolos Processados com Sucesso\n\n`;
    sucessosComMetadados.slice(0, 20).forEach((sucesso, index) => {
      relatorio += `### ${index + 1}. ${sucesso.numeroProcesso}\n`;
      relatorio += `- **Arquivo:** ${sucesso.arquivo}\n`;
      relatorio += `- **Elemento:** ${sucesso.elementoNominativo}\n`;
      relatorio += `- **Logo:** ${sucesso.logoExtraida ? '✅' : '❌'}\n\n`;
    });
    
    if (sucessosComMetadados.length > 20) {
      relatorio += `*... e mais ${sucessosComMetadados.length - 20} protocolos processados*\n\n`;
    }
  }
  
  return relatorio;
}

/**
 * Função principal
 */
async function main(): Promise<void> {
  console.log('🚀 Iniciando processamento de protocolos baixados...\n');
  
  const stats: ProcessStats = {
    total: 0,
    processados: 0,
    sucessos: 0,
    falhas: 0,
    jaExistiam: 0,
    metadadosExtraidos: 0,
    logosExtraidas: 0,
    adicionadosNaFila: 0,
    errors: []
  };
  
  try {
    // 1. Criar estrutura de diretórios
    console.log('📁 Criando estrutura de diretórios...');
    criarEstruturaDiretorios();
    
    // 2. Listar PDFs para processar
    const arquivos = listarPDFs();
    stats.total = arquivos.length;
    
    if (arquivos.length === 0) {
      console.log('⚠️ Nenhum PDF encontrado para processar');
      return;
    }
    
    // 3. Verificar microserviço
    console.log('🔍 Verificando microserviço Python...');
    const microservicoOnline = await verificarMicroservico();
    if (!microservicoOnline) {
      console.log('⚠️ ATENÇÃO: Microserviço Python está offline!');
      console.log('📄 PDFs serão copiados mas metadados e logos não serão extraídos');
    }
    
    // 4. Processar arquivos em lotes
    console.log(`\n🎯 Iniciando processamento de ${arquivos.length} PDFs...`);
    const resultados = await processarLoteDeArquivos(arquivos);
    
    // 5. Calcular estatísticas
    stats.processados = resultados.length;
    stats.sucessos = resultados.filter(r => r.success).length;
    stats.falhas = resultados.filter(r => !r.success).length;
    stats.jaExistiam = resultados.filter(r => r.jaExistia).length;
    stats.metadadosExtraidos = resultados.filter(r => r.numeroProcesso).length;
    stats.logosExtraidas = resultados.filter(r => r.logoExtraida).length;
    stats.adicionadosNaFila = resultados.filter(r => r.numeroProcesso && !r.jaExistia).length;
    
    // 6. Gerar relatório
    const relatorio = gerarRelatorio(stats, resultados);
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
    const relatorioPath = path.join(process.cwd(), `relatorio-processamento-protocolos-${timestamp}.md`);
    
    fs.writeFileSync(relatorioPath, relatorio, 'utf8');
    
    // 7. Resumo final
    console.log('\n' + '='.repeat(60));
    console.log('📊 RESUMO FINAL');
    console.log('='.repeat(60));
    console.log(`📄 PDFs encontrados: ${stats.total}`);
    console.log(`✅ Processados com sucesso: ${stats.sucessos}`);
    console.log(`❌ Falhas: ${stats.falhas}`);
    console.log(`⏭️ Já existiam: ${stats.jaExistiam}`);
    console.log(`🆕 Novos processados: ${stats.sucessos - stats.jaExistiam}`);
    console.log(`🔍 Metadados extraídos: ${stats.metadadosExtraidos}`);
    console.log(`🖼️ Logos extraídas: ${stats.logosExtraidas}`);
    console.log(`📋 Adicionados na fila: ${stats.adicionadosNaFila}`);
    console.log(`📁 Pasta uploads: ${UPLOADS_PROTOCOLOS_DIR}`);
    console.log(`📄 Relatório: ${relatorioPath}`);
    console.log('='.repeat(60));
    
    if (stats.adicionadosNaFila > 0) {
      console.log('\n🎯 PRÓXIMO PASSO:');
      console.log(`📋 ${stats.adicionadosNaFila} protocolos foram adicionados à fila de processamento`);
      console.log('🤖 A cron existente se encarregará de criar/atualizar clientes e ChatGuru');
    }
    
  } catch (error) {
    console.error('❌ Erro geral na execução:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar script se chamado diretamente
if (require.main === module) {
  main()
    .catch(console.error)
    .finally(() => {
      console.log('\n🏁 Script finalizado!');
      process.exit(0);
    });
}

export { main as processarProtocolosBaixados }; 