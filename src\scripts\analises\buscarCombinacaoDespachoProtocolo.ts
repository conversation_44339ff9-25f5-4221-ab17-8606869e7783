import { PrismaClient, Processo, Prisma } from "@prisma/client";
import { writeFileSync } from "fs";

const prisma = new PrismaClient({
  log: ["warn", "error"],
});

// --- Defina as combinações e filtros aqui ---
// REMOVIDO: const DATA_DEPOSITO_MINIMA = new Date("2002-01-01T00:00:00.000Z");

interface CombinacaoBusca {
  nomeDespacho: string;
  codigoServicoProtocolo: string;
  limiteResultados?: number; // Limite de processos *mais recentes* com a combinação
}

const combinacoesParaBuscar: CombinacaoBusca[] = [
   {
    nomeDespacho: "Recurso provido (decisão reformada para: Deferimento)",
    codigoServicoProtocolo: "33310",
    limiteResultados: 10,
  },
   {
    nomeDespacho: "Recurso provido (decisão reformada para: Deferimento)",
    codigoServicoProtocolo: "33315",
    limiteResultados: 10,
  },
   {
    nomeDespacho: "Recurso provido (decisão reformada para: Deferimento)",
    codigoServicoProtocolo: "33316",
    limiteResultados: 10,
  },
   {
    nomeDespacho: "Recurso provido (decisão reformada para: Deferimento)",
    codigoServicoProtocolo: "3339",
    limiteResultados: 10,
  },
  {
    nomeDespacho: "Recurso provido (decisão reformada para: Deferimento)",
    codigoServicoProtocolo: "3623",
    limiteResultados: 10,
  },
  {
    nomeDespacho: "Recurso provido (decisão reformada para: Deferimento)",
    codigoServicoProtocolo: "3918",
    limiteResultados: 10,
  },
  // Adicione mais combinações aqui, se necessário
  // {
  //   nomeDespacho: "Outro Nome de Despacho",
  //   codigoServicoProtocolo: "XYZ", // Buscará códigos que contenham "XYZ"
  //   limiteResultados: 5
  // },
];
// ------------------------------------------------------

// Função auxiliar para escapar campos CSV (inalterada)
function escapeCsvField(field: string | number | null | undefined): string {
    if (field === null || field === undefined) { return '""'; }
    const stringField = String(field);
    if (stringField.includes('"') || stringField.includes(',') || stringField.includes('\n')) {
        return `"${stringField.replace(/"/g, '""')}"`;
    }
    return `"${stringField.replace(/"/g, '""')}"`;
}

// Função atualizada para buscar os processos mais recentes com a combinação
async function buscarProcessosPorCombinacao(
  combinacao: CombinacaoBusca
): Promise<string[]> {
  const limite = combinacao.limiteResultados ?? 10;

  try {
    // Busca DESPACHOS que correspondem, ordenados pelo mais recente
    const despachosEncontrados = await prisma.despacho.findMany({
      where: {
        // Filtro no nome do despacho
        nome: {
          contains: combinacao.nomeDespacho,
          mode: "insensitive",
        },
        // Filtro no protocolo associado
        protocolos: {
          some: {
            codigoServico: {
              contains: combinacao.codigoServicoProtocolo,
              mode: 'insensitive',
            },
          },
        },
        // Filtro no processo associado (excluir testes)
        processo: {
          numero: {
            not: {
              startsWith: 'TESTE-',
            },
          },
          // REMOVIDO: Filtro de data de depósito
        },
      },
      // Inclui dados necessários para ordenação e resultado
      include: {
        processo: {
          select: { numero: true } // Para obter o número do processo
        },
        rpi: {
          select: { dataPublicacao: true } // Para ordenar pela data da RPI
        }
      },
      // Ordena pela data da RPI do despacho, mais recente primeiro
      orderBy: {
        rpi: {
          dataPublicacao: 'desc'
        }
      },
      // Não limitamos aqui, pois precisamos obter processos únicos
    });

    // Processa os despachos para obter números de processo únicos e recentes até o limite
    const numerosProcessoRecentesUnicos = new Set<string>();
    for (const despacho of despachosEncontrados) {
        if (despacho.processo?.numero) {
            numerosProcessoRecentesUnicos.add(despacho.processo.numero);
            // Para de adicionar assim que atingir o limite desejado
            if (numerosProcessoRecentesUnicos.size >= limite) {
                break;
            }
        }
    }

    return Array.from(numerosProcessoRecentesUnicos); // Retorna a lista limitada de números únicos

  } catch (error) {
    console.error(
      `Erro ao buscar processos recentes para combinação (Despacho Contendo: "${combinacao.nomeDespacho}", Servico Contendo: ${combinacao.codigoServicoProtocolo}):`, // Log atualizado
      error
    );
    return [];
  }
}

// Função buscarCodigoServicoReal (inalterada)
async function buscarCodigoServicoReal(
    processoNumero: string,
    combinacao: CombinacaoBusca
): Promise<string | null> {
    try {
        const protocolo = await prisma.protocoloDespacho.findFirst({
            where: {
                despacho: {
                    processo: {
                        numero: processoNumero,
                    },
                    nome: {
                        contains: combinacao.nomeDespacho,
                        mode: 'insensitive',
                    },
                },
                codigoServico: {
                    contains: combinacao.codigoServicoProtocolo,
                    mode: 'insensitive',
                },
            },
            select: {
                codigoServico: true,
            },
        });
        return protocolo?.codigoServico ?? null;
    } catch (error) {
        console.error(`Erro buscando código de serviço real para processo ${processoNumero}:`, error);
        return null;
    }
}

interface ResultadoDetalhado {
  numeroProcesso: string;
  codigoServicoEncontrado: string | null;
}

// Função gerarCsvResultados (inalterada)
async function gerarCsvResultados(
  resultados: { [key: string]: ResultadoDetalhado[] },
  nomeArquivo: string
): Promise<void> {
  console.log(`\nGerando arquivo CSV: ${nomeArquivo}...`);
  const header = [
    "NomeDespachoBuscado",
    "CodigoServicoBuscado",
    "NumeroProcessoEncontrado",
    "CodigoServicoEncontrado",
    "LinkINPI",
  ].map(escapeCsvField).join(",");

  const linhasCsv: string[] = [header];

  const parseChaveResultado = (chave: string): { nomeDespacho: string; codigoServico: string } => {
      const match = chave.match(/Despacho Contendo: "(.+)" \| Servico Contendo: (.+)/);
      if (match && match.length === 3) {
          return { nomeDespacho: match[1], codigoServico: match[2] };
      }
      return { nomeDespacho: 'ERRO_PARSE', codigoServico: 'ERRO_PARSE' };
  }

  for (const [chave, detalhesProcessos] of Object.entries(resultados)) {
    if (detalhesProcessos.length > 0) {
      const { nomeDespacho, codigoServico } = parseChaveResultado(chave);
      for (const detalhe of detalhesProcessos) {
        const linkInpi = `https://busca.inpi.gov.br/pePI/servlet/MarcasServletController?Action=searchMarca&tipoPesquisa=BY_NUM_PROC&Source=OAMI&NumPedido=${detalhe.numeroProcesso}`;

        const linha = [
          nomeDespacho,
          codigoServico,
          detalhe.numeroProcesso,
          detalhe.codigoServicoEncontrado,
          linkInpi,
        ].map(escapeCsvField).join(",");
        linhasCsv.push(linha);
      }
    }
  }

  try {
    writeFileSync(nomeArquivo, linhasCsv.join("\n"), { encoding: "utf-8" });
    console.log(`Arquivo CSV "${nomeArquivo}" gerado com sucesso.`);
  } catch (error) {
    console.error(`Erro ao salvar o arquivo CSV "${nomeArquivo}":`, error);
  }
}

// Função main (lógica inalterada, logs refletem a mudança)
async function main() {
  console.log(
    "Iniciando busca pelos processos MAIS RECENTES com combinações Despacho/Protocolo (usando 'contains')..." // Log atualizado
  );

  const resultados: { [key: string]: ResultadoDetalhado[] } = {};

  for (const combinacao of combinacoesParaBuscar) {
    const chaveResultado = `Despacho Contendo: "${combinacao.nomeDespacho}" | Servico Contendo: ${combinacao.codigoServicoProtocolo}`;
    console.log(`\nBuscando processos para: ${chaveResultado}`);

    // A busca agora retorna os números dos processos mais recentes com a combinação
    const numerosProcesso = await buscarProcessosPorCombinacao(combinacao);
    const detalhesEncontrados: ResultadoDetalhado[] = [];

    if (numerosProcesso.length > 0) {
      console.log(`  Encontrados ${numerosProcesso.length} processos (mais recentes). Buscando códigos de serviço reais...`);
      for (const numero of numerosProcesso) {
        const codigoReal = await buscarCodigoServicoReal(numero, combinacao);
        detalhesEncontrados.push({
          numeroProcesso: numero,
          codigoServicoEncontrado: codigoReal,
        });
      }
      detalhesEncontrados.slice(0,5).forEach(d => console.log(`  - Proc: ${d.numeroProcesso}, Cod. Real: ${d.codigoServicoEncontrado ?? 'N/A'}`));
      if(detalhesEncontrados.length > 5) console.log('  ...');
    } else {
      console.log("  Nenhum processo encontrado para esta combinação."); // Log atualizado
    }
    resultados[chaveResultado] = detalhesEncontrados;
  }

  console.log("\n\nBusca concluída.");

  const dataHora = new Date().toISOString().replace(/[:.]/g, "-");
  const nomeArquivoCsv = `combinacoes-despacho-protocolo-recentes-${dataHora}.csv`; // Nome do arquivo atualizado
  await gerarCsvResultados(resultados, nomeArquivoCsv);
}

main()
  .catch((e) => {
    console.error("\nErro inesperado na execução principal:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
    console.log("\nConexão com o banco de dados fechada.");
  });
