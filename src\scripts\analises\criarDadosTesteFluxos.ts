import { PrismaClient } from '@prisma/client';
import { readFileSync } from 'fs';
import cliProgress from 'cli-progress';

const prisma = new PrismaClient();

/**
 * Cria uma abreviação mais única para um nome de despacho.
 */
function abreviarDespacho(nome: string | null): string {
  if (!nome) return 'N/A';

  // Mapa de overrides para nomes comuns/conflitantes
  const overrides: { [key: string]: string } = {
    'Deferimento do pedido': 'DefPed',
    'Deferimento da petição': 'DefPet',
    'Indeferimento do pedido': 'IndPed',
    'Indeferimento da petição': 'IndPet',
    'Publicação de pedido de registro para oposição': 'PubOpo',
    'Publicação de pedido de registro para oposição (exame formal concluído)': 'PubOpoEx',
    'Notificação de oposição': 'NotOpo',
    'Notificação de recurso': 'NotRec',
    'Notificação de caducidade': 'NotCad',
    'Concessão de registro': 'ConcReg',
    'Arquivamento definitivo de pedido de registro por falta de pagamento da concessão': 'ArqPagConc',
    'Arquivamento definitivo de pedido de registro por falta de cumprimento de exigência de mérito': 'ArqExiMer',
    'Exigência de mérito': 'ExiMer',
    'Exigência formal': 'ExiFor',
    'Sobrestamento do exame de mérito': 'SobExaMer',
    'Recurso não provido (decisão mantida)': 'RecNeg',
    'Recurso provido (decisão reformada para: Deferimento)': 'RecDef',
    // Adicione mais conforme identificar necessidade
  };

  if (overrides[nome]) {
    return overrides[nome];
  }

  // Geração genérica
  const palavras = nome.split(' ');
  const primeiraPalavra = palavras[0] || '';
  const abreviacao = primeiraPalavra.substring(0, 3);

  // Encontrar a segunda palavra significativa
  const palavrasIgnoradas = ['de', 'da', 'do', 'a', 'o', 'em', 'para', 'por'];
  let segundaSigla = '';
  for (let i = 1; i < palavras.length; i++) {
    if (!palavrasIgnoradas.includes(palavras[i].toLowerCase())) {
      segundaSigla = palavras[i].substring(0, 1);
      break;
    }
  }

  // Retorna a abreviação genérica (max 4 chars)
  return (abreviacao + segundaSigla).substring(0, 4);
}

/**
 * Cria um nome de marca resumido baseado na sequência de despachos.
 */
function criarNomeMarcaResumido(sequencia: string[], limite = 50): string {
  if (!sequencia || sequencia.length === 0) {
    return 'Fluxo Vazio'.substring(0, limite);
  }

  // Criar abreviações para cada despacho
  const abreviacoes = sequencia.map(abreviarDespacho);
  const nomeCompletoAbrev = abreviacoes.join('-');

  // Formato: "Fluxo(N): AB1-AB2-AB3"
  let nome = `Fluxo(${sequencia.length}): ${nomeCompletoAbrev}`;

  // Se o nome ficar muito longo, usar primeiro e último abreviados
  if (nome.length > limite) {
    const primeiraAbrev = abreviacoes[0] || 'Inicio';
    const ultimaAbrev = abreviacoes[abreviacoes.length - 1] || 'Fim';
    nome = `Fluxo(${sequencia.length}): ${primeiraAbrev}...${ultimaAbrev}`;
  }

  return nome.substring(0, limite); // Garantir o limite final
}

/**
 * Função principal para criar o cliente e processos de teste.
 */
async function criarDadosTeste(caminhoFluxosUnicos: string, identificadorCliente: string) {
  try {
    console.log(`Lendo fluxos únicos de: ${caminhoFluxosUnicos}`);
    const fluxosUnicosJson = readFileSync(caminhoFluxosUnicos, 'utf-8');
    const fluxosUnicos: string[][] = JSON.parse(fluxosUnicosJson);

    console.log(`Total de fluxos únicos a serem criados: ${fluxosUnicos.length}`);

    // 1. Criar ou encontrar o cliente de teste
    let cliente = await prisma.cliente.findFirst({
      where: { identificador: identificadorCliente }
    });

    if (!cliente) {
      console.log(`Criando novo cliente com identificador: ${identificadorCliente}`);
      cliente = await prisma.cliente.create({
        data: {
          identificador: identificadorCliente,
          nome: `Cliente de Teste (${identificadorCliente})`,
          tipoDeDocumento: 'TESTE',
          numeroDocumento: '00000001'
        }
      });
    } else {
      console.log(`Cliente encontrado com ID: ${cliente.id}`);
    }

    // 2. Configurar barra de progresso
    const barraProgresso = new cliProgress.SingleBar({
      format: 'Criando processos |{bar}| {percentage}% | {value}/{total} Fluxos',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });
    barraProgresso.start(fluxosUnicos.length, 0);

    // 3. Criar processos e despachos para cada fluxo
    let processosCriados = 0;
    for (const [index, fluxo] of fluxosUnicos.entries()) {
      if (!fluxo || fluxo.length === 0) continue; // Pular fluxos vazios

      const nomeMarca = criarNomeMarcaResumido(fluxo);
      const numeroProcesso = `TESTE-${identificadorCliente}-${index + 1}`.padStart(9, '0');

      try {
        // Criar o processo e a marca associada
        const processo = await prisma.processo.create({
          data: {
            numero: numeroProcesso,
            clienteId: cliente.id,
            dataDeposito: new Date(),
            monitorado: true, // Marcar como monitorado se necessário
            marca: {
              create: {
                nome: nomeMarca,
                apresentacao: 'Nominativa', // Definir valores padrão
                natureza: 'Produto'       // Definir valores padrão
              }
            }
          },
          include: {
            marca: true // Incluir a marca para obter o ID
          }
        });

        // Criar RPI fictícia e despachos para o processo
        // Usar uma única RPI para todos os despachos de um processo
        const rpi = await prisma.rPI.create({
          data: {
            numero: 999900000 + index, // Número único fictício
            dataPublicacao: new Date()
          }
        });

        await prisma.despacho.createMany({
          data: fluxo.map((nomeDespacho) => ({
            processoId: processo.id,
            rpiId: rpi.id,
            codigo: 'TESTE', // Código fictício
            nome: nomeDespacho
          }))
        });

        processosCriados++;
      } catch (error: any) {
        // Tratar erro de número de processo duplicado (caso o script seja executado novamente)
        if (error.code === 'P2002' && error.meta?.target?.includes('numero')) {
          console.warn(`\nAviso: Processo ${numeroProcesso} já existe. Pulando.`);
        } else {
          console.error(`\nErro ao criar processo ${numeroProcesso}:`, error.message);
        }
      }
      barraProgresso.increment();
    }

    barraProgresso.stop();
    console.log(`\nCriação concluída. ${processosCriados} processos criados para o cliente ${identificadorCliente}.`);

  } catch (error: any) {
    console.error('Erro ao criar dados de teste:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// --- Configuração ---
const arquivoFluxos = 'fluxos-unicos.json';
const identificadorClienteTeste = '00000001';
// ------------------

// Executar o script
criarDadosTeste(arquivoFluxos, identificadorClienteTeste); 