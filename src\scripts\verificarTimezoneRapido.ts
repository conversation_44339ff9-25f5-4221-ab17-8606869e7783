import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function verificarTimezone() {
  console.log('🕐 Verificação rápida de timezone...\n');

  try {
    // 1. Verificar timezone do PostgreSQL
    const timezoneConfig = await prisma.$queryRaw`SHOW timezone` as any[];
    console.log('🌍 Timezone do PostgreSQL:', timezoneConfig[0]?.timezone);

    // 2. Testar agora
    const agora = await prisma.$queryRaw`SELECT NOW() as agora_postgres, NOW() AT TIME ZONE 'America/Sao_Paulo' as agora_br` as any[];
    console.log('⏰ Agora no PostgreSQL:', agora[0]?.agora_postgres);
    console.log('🇧🇷 Agora no horário BR:', agora[0]?.agora_br);

    // 3. Data do JavaScript
    const agoraJS = new Date();
    console.log('💻 Agora no JavaScript:', agoraJS.toISOString());
    console.log('🇧🇷 Agora JS (hor<PERSON><PERSON>):', agoraJS.toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' }));

    // 4. Teste simples: criar um registro
    const testMarca = await prisma.marca.findFirst({
      where: { nome: { not: null } },
      select: { createdAt: true, nome: true }
    });

    if (testMarca) {
      console.log('📅 Exemplo de data do banco:', testMarca.createdAt.toISOString());
      console.log('🇧🇷 Convertida para BR:', testMarca.createdAt.toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' }));
    }

    console.log('\n📋 RESPOSTA:');
    
    // A resposta mais provável
    if (timezoneConfig[0]?.timezone === 'UTC') {
      console.log('✅ O Prisma/PostgreSQL está usando UTC');
      console.log('💡 Para buscar "hoje" no horário brasileiro, você DEVE ajustar o timezone');
      console.log('');
      console.log('📝 Use este código:');
      console.log('```typescript');
      console.log('const hojeBR = new Date(new Date().toLocaleString("en-US", { timeZone: "America/Sao_Paulo" }));');
      console.log('hojeBR.setHours(0, 0, 0, 0);');
      console.log('const amanhaBR = new Date(hojeBR.getTime() + 24 * 60 * 60 * 1000);');
      console.log('```');
    } else {
      console.log(`✅ O PostgreSQL está usando timezone: ${timezoneConfig[0]?.timezone}`);
      console.log('💡 Verifique se este é o timezone correto para sua aplicação');
    }

  } catch (error) {
    console.error('❌ Erro:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verificarTimezone(); 