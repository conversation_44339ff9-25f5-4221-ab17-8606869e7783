import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
import fs from "fs";
import path from "path";
import { Logger } from "../services/logger.service";
import readline from "readline";

dotenv.config();

const prisma = new PrismaClient();

interface OportunidadeAnalisada {
  id: number;
  name: string;
  numeroProcessoField?: any;
  numerosProcessoEncontrados: string[];
  linkProtocoloField?: any;
  linkProtocolo?: string;
  person?: any;
  company?: any;
  temTelefone: boolean;
  telefoneOrigin: 'person' | 'company' | 'nenhum';
  telefones: string[];
}

interface ResultadoAnalise1 {
  totalRegistrosComNumeroProcesso: number;
  registrosComMultiplosNumeros: number;
  registrosComUmNumero: number;
  exemplosMultiplos: Array<{
    dealId: number;
    dealName: string;
    value: string;
    numerosEncontrados: string[];
  }>;
  distribuicaoQuantidade: Map<number, number>;
}

interface ResultadoAnalise2 {
  totalComNumeroProcesso: number;
  comTelefonePerson: number;
  comTelefoneCompany: number;
  semTelefone: number;
  exemplosSemTelefone: Array<{
    dealId: number;
    dealName: string;
    numerosProcesso: string[];
  }>;
}

interface ResultadoAnalise3 {
  numerosUnicosProcesso: string[];
  processosExistentesNoBanco: string[];
  processosNaoExistentesNoBanco: string[];
  processosSemProcurador: Array<{
    numero: string;
    id: string;
    procuradorId: string | null;
  }>;
  estatisticas: {
    totalUnicos: number;
    existentesNoBanco: number;
    naoExistentesNoBanco: number;
    semProcurador: number;
  };
}

interface ResultadoAnalise4 {
  totalComLinkProtocolo: number;
  linksUnicosProtocolo: string[];
  exemplosSemLink: Array<{
    dealId: number;
    dealName: string;
    numerosProcesso: string[];
  }>;
  estatisticas: {
    totalOportunidades: number;
    comLinkProtocolo: number;
    semLinkProtocolo: number;
    linksUnicos: number;
  };
}

interface ResultadoAnalise5 {
  registrosSemTelefone: Array<{
    dealId: number;
    dealName: string;
    numerosProcesso: string[];
  }>;
  numerosProcessoSemTelefone: string[];
  processosExistentesNoBanco: Array<{
    numero: string;
    id: string;
    procuradorId: string | null;
  }>;
  processosNaoExistentesNoBanco: string[];
  processosSemProcurador: Array<{
    numero: string;
    id: string;
    procuradorId: string | null;
  }>;
  estatisticas: {
    totalRegistrosSemTelefone: number;
    totalNumerosProcessoSemTelefone: number;
    processosExistentesNoBanco: number;
    processosNaoExistentesNoBanco: number;
    processosSemProcurador: number;
  };
}

interface PersonConflito {
  personId: number;
  personName: string;
  dealIds: number[];
  dealNames: string[];
}

interface ResultadoAnalise6 {
  telefonesComConflito: Record<string, PersonConflito[]>;
  telefonesUnicos: Record<string, PersonConflito>;
  estatisticas: {
    totalTelefonesAnalisados: number;
    telefonesComConflito: number;
    telefonesUnicos: number;
    totalPersonsEnvolvidas: number;
    oportunidadesSemPerson: number;
  };
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Função para extrair números de processo de um valor
function extrairNumerosProcesso(valor: string): string[] {
  if (!valor || typeof valor !== 'string') return [];
  
  // Padrão para números de processo: 9 dígitos consecutivos
  const matches = valor.match(/\b\d{9}\b/g);
  return matches ? [...new Set(matches)] : []; // Remove duplicatas
}

// Função para verificar se tem telefone válido
function verificarTelefones(person: any, company: any): { temTelefone: boolean; origem: 'person' | 'company' | 'nenhum'; telefones: string[] } {
  const telefones: string[] = [];
  
  // Verificar telefones da person
  if (person?.contactPhones && Array.isArray(person.contactPhones)) {
    const telefonesPerson = person.contactPhones
      .map((contact: any) => contact.phone)
      .filter((phone: any) => phone && phone.toString().length >= 10);
    telefones.push(...telefonesPerson);
  }
  
  if (telefones.length > 0) {
    return { temTelefone: true, origem: 'person', telefones };
  }
  
  // Verificar telefones da company
  if (company?.contactPhones && Array.isArray(company.contactPhones)) {
    const telefonesCompany = company.contactPhones
      .map((contact: any) => contact.phone)
      .filter((phone: any) => phone && phone.toString().length >= 10);
    telefones.push(...telefonesCompany);
  }
  
  if (telefones.length > 0) {
    return { temTelefone: true, origem: 'company', telefones };
  }
  
  return { temTelefone: false, origem: 'nenhum', telefones: [] };
}

// Função para processar um arquivo JSON
async function processarArquivoJson(caminhoArquivo: string): Promise<OportunidadeAnalisada[]> {
  const nomeArquivo = path.basename(caminhoArquivo);
  
  try {
    const conteudo = fs.readFileSync(caminhoArquivo, 'utf8');
    const dados = JSON.parse(conteudo);
    
    if (!dados.oportunidades || !Array.isArray(dados.oportunidades)) {
      Logger.warn(`Formato inválido no arquivo ${nomeArquivo}`);
      return [];
    }
    
    const oportunidadesAnalisadas: OportunidadeAnalisada[] = [];
    
    for (const oportunidade of dados.oportunidades) {
      // Buscar campo "Número Processo" (ID 194250)
      const numeroProcessoField = oportunidade.customFields?.find((field: any) => field.id === 194250);
      const numerosProcessoEncontrados = numeroProcessoField?.value ? 
        extrairNumerosProcesso(numeroProcessoField.value) : [];
      
      // Buscar campo "LinkProtocolo" (ID 587550)
      const linkProtocoloField = oportunidade.customFields?.find((field: any) => field.id === 587550);
      const linkProtocolo = linkProtocoloField?.value || null;
      
      // Verificar telefones
      const resultadoTelefone = verificarTelefones(oportunidade.person, oportunidade.company);
      
      oportunidadesAnalisadas.push({
        id: oportunidade.id,
        name: oportunidade.name || `Deal ${oportunidade.id}`,
        numeroProcessoField,
        numerosProcessoEncontrados,
        linkProtocoloField,
        linkProtocolo,
        person: oportunidade.person,
        company: oportunidade.company,
        temTelefone: resultadoTelefone.temTelefone,
        telefoneOrigin: resultadoTelefone.origem,
        telefones: resultadoTelefone.telefones
      });
    }
    
    Logger.info(`✅ ${nomeArquivo}: ${oportunidadesAnalisadas.length} oportunidades analisadas`);
    return oportunidadesAnalisadas;
    
  } catch (error: any) {
    Logger.error(`❌ Erro ao processar ${nomeArquivo}: ${error.message}`);
    return [];
  }
}

// Função para buscar e processar todos os JSONs em paralelo
async function carregarTodosOsDados(): Promise<OportunidadeAnalisada[]> {
  Logger.section("📥 CARREGAMENTO DE DADOS CRM");
  
  // Buscar pasta mais recente
  const pastaPrincipal = path.join(process.cwd(), 'dados-crm-extraidos');
  
  if (!fs.existsSync(pastaPrincipal)) {
    throw new Error("Pasta 'dados-crm-extraidos' não encontrada. Execute primeiro o orquestrador CRM.");
  }
  
  const subpastas = fs.readdirSync(pastaPrincipal, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name)
    .sort()
    .reverse();
  
  if (subpastas.length === 0) {
    throw new Error("Nenhuma subpasta encontrada em 'dados-crm-extraidos'.");
  }
  
  const pastaBase = path.join(pastaPrincipal, subpastas[0]);
  const pastaOportunidades = path.join(pastaBase, 'oportunidades-completas');
  
  Logger.info(`📂 Usando pasta: ${subpastas[0]}`);
  
  const arquivos = fs.readdirSync(pastaOportunidades)
    .filter(arquivo => arquivo.endsWith('.json'))
    .map(arquivo => path.join(pastaOportunidades, arquivo))
    .sort();
  
  Logger.info(`📁 ${arquivos.length} arquivos JSON encontrados`);
  
  // Processar em lotes paralelos (5 arquivos por vez para não sobrecarregar)
  const LOTE_SIZE = 5;
  const totalLotes = Math.ceil(arquivos.length / LOTE_SIZE);
  const todasOportunidades: OportunidadeAnalisada[] = [];
  
  Logger.info(`🔄 Processando em ${totalLotes} lotes paralelos...`);
  
  for (let i = 0; i < totalLotes; i++) {
    const inicio = i * LOTE_SIZE;
    const fim = Math.min(inicio + LOTE_SIZE, arquivos.length);
    const loteAtual = arquivos.slice(inicio, fim);
    
    Logger.info(`📦 Lote ${i + 1}/${totalLotes}: processando ${loteAtual.length} arquivos...`);
    
    // Processar arquivos do lote em paralelo
    const promessasLote = loteAtual.map(arquivo => processarArquivoJson(arquivo));
    const resultadosLote = await Promise.all(promessasLote);
    
    // Consolidar resultados do lote
    resultadosLote.forEach(resultado => {
      todasOportunidades.push(...resultado);
    });
    
    Logger.success(`✅ Lote ${i + 1} concluído: ${resultadosLote.reduce((acc, r) => acc + r.length, 0)} oportunidades`);
  }
  
  Logger.success(`🎯 Total carregado: ${todasOportunidades.length} oportunidades de ${arquivos.length} arquivos`);
  return todasOportunidades;
}

// ANÁLISE 1: Verificar múltiplos números de processo
async function analise1_VerificarMultiplosNumeros(dados: OportunidadeAnalisada[]): Promise<ResultadoAnalise1> {
  Logger.section("1️⃣ ANÁLISE: MÚLTIPLOS NÚMEROS DE PROCESSO");
  
  const registrosComNumeroProcesso = dados.filter(oportunidade => 
    oportunidade.numeroProcessoField?.value && oportunidade.numerosProcessoEncontrados.length > 0
  );
  
  const registrosComMultiplos = registrosComNumeroProcesso.filter(r => r.numerosProcessoEncontrados.length > 1);
  const registrosComUm = registrosComNumeroProcesso.filter(r => r.numerosProcessoEncontrados.length === 1);
  
  // Distribuição de quantidade de números por registro
  const distribuicaoQuantidade = new Map<number, number>();
  registrosComNumeroProcesso.forEach(registro => {
    const quantidade = registro.numerosProcessoEncontrados.length;
    distribuicaoQuantidade.set(quantidade, (distribuicaoQuantidade.get(quantidade) || 0) + 1);
  });
  
  // Exemplos de múltiplos
  const exemplosMultiplos = registrosComMultiplos.slice(0, 10).map(registro => ({
    dealId: registro.id,
    dealName: registro.name,
    value: registro.numeroProcessoField.value,
    numerosEncontrados: registro.numerosProcessoEncontrados
  }));
  
  return {
    totalRegistrosComNumeroProcesso: registrosComNumeroProcesso.length,
    registrosComMultiplosNumeros: registrosComMultiplos.length,
    registrosComUmNumero: registrosComUm.length,
    exemplosMultiplos,
    distribuicaoQuantidade
  };
}

// ANÁLISE 2: Verificar telefones
async function analise2_VerificarTelefones(dados: OportunidadeAnalisada[]): Promise<ResultadoAnalise2> {
  Logger.section("2️⃣ ANÁLISE: TELEFONES EM REGISTROS COM PROCESSO");
  
  const registrosComNumeroProcesso = dados.filter(oportunidade => 
    oportunidade.numerosProcessoEncontrados.length > 0
  );
  
  const comTelefonePerson = registrosComNumeroProcesso.filter(r => r.telefoneOrigin === 'person').length;
  const comTelefoneCompany = registrosComNumeroProcesso.filter(r => r.telefoneOrigin === 'company').length;
  const semTelefone = registrosComNumeroProcesso.filter(r => r.telefoneOrigin === 'nenhum');
  
  const exemplosSemTelefone = semTelefone.slice(0, 10).map(registro => ({
    dealId: registro.id,
    dealName: registro.name,
    numerosProcesso: registro.numerosProcessoEncontrados
  }));
  
  return {
    totalComNumeroProcesso: registrosComNumeroProcesso.length,
    comTelefonePerson,
    comTelefoneCompany,
    semTelefone: semTelefone.length,
    exemplosSemTelefone
  };
}

// ANÁLISE 3: Verificar processos no banco
async function analise3_VerificarProcessosNoBanco(dados: OportunidadeAnalisada[]): Promise<ResultadoAnalise3> {
  Logger.section("3️⃣ ANÁLISE: PROCESSOS NO BANCO DE DADOS");
  
  // Coletar todos os números únicos de processo
  const numerosUnicos = new Set<string>();
  dados.forEach(oportunidade => {
    oportunidade.numerosProcessoEncontrados.forEach(numero => {
      numerosUnicos.add(numero);
    });
  });
  
  const numerosUnicosArray = Array.from(numerosUnicos).sort();
  Logger.info(`📊 ${numerosUnicosArray.length} números únicos de processo encontrados nos JSONs`);
  
  // Verificar quais existem no banco
  Logger.info("🔍 Verificando existência no banco de dados...");
  
  const processosExistentes = await prisma.processo.findMany({
    where: {
      numero: { in: numerosUnicosArray }
    },
    select: {
      id: true,
      numero: true,
      procuradorId: true
    }
  });
  
  const numerosExistentes = processosExistentes.map(p => p.numero);
  const numerosNaoExistentes = numerosUnicosArray.filter(numero => !numerosExistentes.includes(numero));
  
  // Verificar quais não têm procurador
  const processosSemProcurador = processosExistentes
    .filter(processo => !processo.procuradorId)
    .map(processo => ({
      numero: processo.numero,
      id: processo.id,
      procuradorId: processo.procuradorId
    }));
  
  return {
    numerosUnicosProcesso: numerosUnicosArray,
    processosExistentesNoBanco: numerosExistentes,
    processosNaoExistentesNoBanco: numerosNaoExistentes,
    processosSemProcurador,
    estatisticas: {
      totalUnicos: numerosUnicosArray.length,
      existentesNoBanco: numerosExistentes.length,
      naoExistentesNoBanco: numerosNaoExistentes.length,
      semProcurador: processosSemProcurador.length
    }
  };
}

// ANÁLISE 4: Verificar links de protocolo
async function analise4_VerificarLinksProtocolo(dados: OportunidadeAnalisada[]): Promise<ResultadoAnalise4> {
  Logger.section("4️⃣ ANÁLISE: LINKS DE PROTOCOLO");
  
  // Filtrar apenas oportunidades que têm números de processo (para fazer sentido ter protocolo)
  const oportunidadesComProcesso = dados.filter(oportunidade => 
    oportunidade.numerosProcessoEncontrados.length > 0
  );
  
  const registrosComLinkProtocolo = oportunidadesComProcesso.filter(oportunidade => 
    oportunidade.linkProtocoloField?.value && 
    typeof oportunidade.linkProtocoloField.value === 'string' &&
    oportunidade.linkProtocoloField.value.trim().length > 0
  );
  
  const registrosSemLinkProtocolo = oportunidadesComProcesso.filter(oportunidade => 
    !oportunidade.linkProtocoloField?.value || 
    typeof oportunidade.linkProtocoloField.value !== 'string' ||
    oportunidade.linkProtocoloField.value.trim().length === 0
  );
  
  const linksUnicos = new Set<string>();
  registrosComLinkProtocolo.forEach(oportunidade => {
    const valor = oportunidade.linkProtocoloField.value;
    if (typeof valor === 'string') {
      // Se tem vírgulas, dividir; senão, usar o valor completo
      const links = valor.includes(',') ? valor.split(',') : [valor];
      links.forEach((link: string) => {
        const linkLimpo = link.trim();
        if (linkLimpo.length > 0) {
          linksUnicos.add(linkLimpo);
        }
      });
    }
  });
  
  const linksUnicosArray = Array.from(linksUnicos).sort();
  Logger.info(`📊 ${linksUnicosArray.length} links únicos encontrados nos JSONs`);
  
  const exemplosSemLink = registrosSemLinkProtocolo.slice(0, 10).map(registro => ({
    dealId: registro.id,
    dealName: registro.name,
    numerosProcesso: registro.numerosProcessoEncontrados
  }));
  
  return {
    totalComLinkProtocolo: registrosComLinkProtocolo.length,
    linksUnicosProtocolo: linksUnicosArray,
    exemplosSemLink,
    estatisticas: {
      totalOportunidades: oportunidadesComProcesso.length,
      comLinkProtocolo: registrosComLinkProtocolo.length,
      semLinkProtocolo: registrosSemLinkProtocolo.length,
      linksUnicos: linksUnicosArray.length
    }
  };
}

// ANÁLISE 5: Verificar registros sem telefone
async function analise5_VerificarRegistrosSemTelefone(dados: OportunidadeAnalisada[]): Promise<ResultadoAnalise5> {
  Logger.section("5️⃣ ANÁLISE: REGISTROS SEM TELEFONE");
  
  const oportunidadesSemTelefone = dados.filter(oportunidade => 
    oportunidade.temTelefone === false && oportunidade.numerosProcessoEncontrados.length > 0
  );
  
  const registrosSemTelefone = oportunidadesSemTelefone.map(oportunidade => ({
    dealId: oportunidade.id,
    dealName: oportunidade.name,
    numerosProcesso: oportunidade.numerosProcessoEncontrados
  }));
  
  // Coletar todos os números de processo únicos dos registros sem telefone
  const numerosProcessoSemTelefone = new Set<string>();
  oportunidadesSemTelefone.forEach(oportunidade => {
    oportunidade.numerosProcessoEncontrados.forEach(numero => {
      numerosProcessoSemTelefone.add(numero);
    });
  });
  
  const numerosProcessoArray = Array.from(numerosProcessoSemTelefone);
  Logger.info(`📊 ${numerosProcessoArray.length} números únicos de processo sem telefone`);
  
  // Verificar quais existem no banco
  Logger.info("🔍 Verificando existência no banco de dados...");
  
  const processosExistentes = await prisma.processo.findMany({
    where: {
      numero: { in: numerosProcessoArray }
    },
    select: {
      id: true,
      numero: true,
      procuradorId: true
    }
  });
  
  const numerosExistentesNoBanco = processosExistentes.map(p => p.numero);
  const processosNaoExistentesNoBanco = numerosProcessoArray.filter(numero => !numerosExistentesNoBanco.includes(numero));
  
  // Verificar quais não têm procurador
  const processosSemProcurador = processosExistentes
    .filter(processo => !processo.procuradorId)
    .map(processo => ({
      numero: processo.numero,
      id: processo.id,
      procuradorId: processo.procuradorId
    }));
  
  return {
    registrosSemTelefone,
    numerosProcessoSemTelefone: numerosProcessoArray,
    processosExistentesNoBanco: processosExistentes,
    processosNaoExistentesNoBanco,
    processosSemProcurador,
    estatisticas: {
      totalRegistrosSemTelefone: registrosSemTelefone.length,
      totalNumerosProcessoSemTelefone: numerosProcessoArray.length,
      processosExistentesNoBanco: processosExistentes.length,
      processosNaoExistentesNoBanco: processosNaoExistentesNoBanco.length,
      processosSemProcurador: processosSemProcurador.length
    }
  };
}

// Função para reformatar número (copiada do atualizarCamposChatguru.ts)
function reformatChatNumber(chatNumber: string): string {
  if (!chatNumber || typeof chatNumber !== "string") {
    throw new Error("Número de chat inválido");
  }

  const countryCode = chatNumber.slice(0, 2); // Código do país
  const areaCode = chatNumber.slice(2, 4); // Código de área
  let phoneNumber = chatNumber.slice(4);

  if (phoneNumber.length === 9) {
    phoneNumber = phoneNumber.slice(1);
  } else if (phoneNumber.length === 8) {
    phoneNumber = "9" + phoneNumber;
  }

  return countryCode + areaCode + phoneNumber;
}

// Função para normalizar telefone
function normalizarTelefone(telefone: string): string {
  if (!telefone || typeof telefone !== 'string') return '';
  
  // Remover caracteres especiais
  let limpo = telefone.replace(/\D/g, '');
  
  // Se não tem código do país, adicionar 55
  if (!limpo.startsWith('55')) {
    limpo = '55' + limpo;
  }
  
  // Usar a função de reformatação
  try {
    return reformatChatNumber(limpo);
  } catch {
    return limpo; // Se falhar, retornar apenas o número limpo
  }
}

// ANÁLISE 6: Verificar conflitos de telefone entre persons
async function analise6_VerificarConflitosPersonTelefone(dados: OportunidadeAnalisada[]): Promise<ResultadoAnalise6> {
  Logger.section("6️⃣ ANÁLISE: CONFLITOS DE TELEFONE ENTRE PERSONS");
  
  // Filtrar apenas oportunidades que têm person
  const oportunidadesComPerson = dados.filter(oportunidade => 
    oportunidade.person && oportunidade.person.id
  );
  
  const oportunidadesSemPerson = dados.length - oportunidadesComPerson.length;
  
  Logger.info(`📊 ${oportunidadesComPerson.length} oportunidades com person de ${dados.length} total`);
  Logger.info(`❌ ${oportunidadesSemPerson} oportunidades ignoradas (sem person)`);
  
  // Mapa: telefone normalizado -> array de persons
  const mapaTelefonePersons = new Map<string, PersonConflito[]>();
  
  // Processar cada oportunidade
  for (const oportunidade of oportunidadesComPerson) {
    const person = oportunidade.person;
    
    if (!person.contactPhones || !Array.isArray(person.contactPhones)) {
      continue;
    }
    
    // Extrair todos os telefones da person
    const telefonesValidos = person.contactPhones
      .map((contact: any) => contact.phone)
      .filter((phone: any) => phone && phone.toString().length >= 10)
      .map((phone: any) => phone.toString());
    
    // Processar cada telefone
    for (const telefoneOriginal of telefonesValidos) {
      const telefoneNormalizado = normalizarTelefone(telefoneOriginal);
      
      if (!telefoneNormalizado) continue;
      
      // Verificar se já existe este telefone no mapa
      if (!mapaTelefonePersons.has(telefoneNormalizado)) {
        mapaTelefonePersons.set(telefoneNormalizado, []);
      }
      
      const personsExistentes = mapaTelefonePersons.get(telefoneNormalizado)!;
      
      // Verificar se esta person já está associada a este telefone
      let personExistente = personsExistentes.find(p => p.personId === person.id);
      
      if (personExistente) {
        // Person já existe, adicionar este deal aos arrays
        if (!personExistente.dealIds.includes(oportunidade.id)) {
          personExistente.dealIds.push(oportunidade.id);
          personExistente.dealNames.push(oportunidade.name);
        }
      } else {
        // Nova person para este telefone
        personsExistentes.push({
          personId: person.id,
          personName: person.name || `Person ${person.id}`,
          dealIds: [oportunidade.id],
          dealNames: [oportunidade.name]
        });
      }
    }
  }
  
  // Separar telefones com conflito vs únicos
  const telefonesComConflito: Record<string, PersonConflito[]> = {};
  const telefonesUnicos: Record<string, PersonConflito> = {};
  
  for (const [telefone, persons] of mapaTelefonePersons.entries()) {
    if (persons.length > 1) {
      // Conflito: mesmo telefone com múltiplas persons
      telefonesComConflito[telefone] = persons;
    } else if (persons.length === 1) {
      // Único: telefone associado a apenas uma person
      telefonesUnicos[telefone] = persons[0];
    }
  }
  
  // Calcular estatísticas
  const totalPersonsEnvolvidas = Object.values(telefonesComConflito)
    .reduce((total, persons) => total + persons.length, 0) +
    Object.keys(telefonesUnicos).length;
  
  const resultado: ResultadoAnalise6 = {
    telefonesComConflito,
    telefonesUnicos,
    estatisticas: {
      totalTelefonesAnalisados: mapaTelefonePersons.size,
      telefonesComConflito: Object.keys(telefonesComConflito).length,
      telefonesUnicos: Object.keys(telefonesUnicos).length,
      totalPersonsEnvolvidas,
      oportunidadesSemPerson
    }
  };
  
  Logger.info(`📊 Resultados da análise:`);
  Logger.info(`   • ${resultado.estatisticas.totalTelefonesAnalisados} telefones únicos analisados`);
  Logger.info(`   • ${resultado.estatisticas.telefonesComConflito} telefones COM conflito`);
  Logger.info(`   • ${resultado.estatisticas.telefonesUnicos} telefones sem conflito`);
  Logger.info(`   • ${resultado.estatisticas.totalPersonsEnvolvidas} persons envolvidas`);
  
  return resultado;
}

// Função para gerar relatórios
async function gerarRelatorios(
  resultado1?: ResultadoAnalise1, 
  resultado2?: ResultadoAnalise2, 
  resultado3?: ResultadoAnalise3,
  resultado4?: ResultadoAnalise4,
  resultado5?: ResultadoAnalise5,
  resultado6?: ResultadoAnalise6
): Promise<void> {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  
  if (resultado1) {
    const relatorio1 = [
      '='.repeat(80),
      'ANÁLISE 1: MÚLTIPLOS NÚMEROS DE PROCESSO',
      '='.repeat(80),
      `Data da análise: ${new Date().toLocaleString('pt-BR')}`,
      '',
      'RESUMO:',
      `📊 Total de registros com campo "Número Processo": ${resultado1.totalRegistrosComNumeroProcesso.toLocaleString('pt-BR')}`,
      `📝 Registros com UM número: ${resultado1.registrosComUmNumero.toLocaleString('pt-BR')} (${((resultado1.registrosComUmNumero / resultado1.totalRegistrosComNumeroProcesso) * 100).toFixed(1)}%)`,
      `📝 Registros com MÚLTIPLOS números: ${resultado1.registrosComMultiplosNumeros.toLocaleString('pt-BR')} (${((resultado1.registrosComMultiplosNumeros / resultado1.totalRegistrosComNumeroProcesso) * 100).toFixed(1)}%)`,
      '',
      'DISTRIBUIÇÃO POR QUANTIDADE:',
      ...Array.from(resultado1.distribuicaoQuantidade.entries())
        .sort(([a], [b]) => a - b)
        .map(([quantidade, registros]) => 
          `   ${quantidade} número(s): ${registros.toLocaleString('pt-BR')} registros`
        ),
      '',
      'EXEMPLOS DE MÚLTIPLOS NÚMEROS:',
      ...resultado1.exemplosMultiplos.map(exemplo => 
        `   Deal ${exemplo.dealId} (${exemplo.dealName}): "${exemplo.value}" → [${exemplo.numerosEncontrados.join(', ')}]`
      ),
      '',
      '='.repeat(80)
    ].join('\n');
    
    fs.writeFileSync(`analise1-multiplos-numeros-${timestamp}.txt`, relatorio1, 'utf8');
    Logger.success('📄 Relatório da Análise 1 salvo');
  }
  
  if (resultado2) {
    const relatorio2 = [
      '='.repeat(80),
      'ANÁLISE 2: TELEFONES EM REGISTROS COM PROCESSO',
      '='.repeat(80),
      `Data da análise: ${new Date().toLocaleString('pt-BR')}`,
      '',
      'RESUMO:',
      `📊 Total com número de processo: ${resultado2.totalComNumeroProcesso.toLocaleString('pt-BR')}`,
      `📞 Com telefone na Person: ${resultado2.comTelefonePerson.toLocaleString('pt-BR')} (${((resultado2.comTelefonePerson / resultado2.totalComNumeroProcesso) * 100).toFixed(1)}%)`,
      `🏢 Com telefone na Company: ${resultado2.comTelefoneCompany.toLocaleString('pt-BR')} (${((resultado2.comTelefoneCompany / resultado2.totalComNumeroProcesso) * 100).toFixed(1)}%)`,
      `❌ Sem telefone: ${resultado2.semTelefone.toLocaleString('pt-BR')} (${((resultado2.semTelefone / resultado2.totalComNumeroProcesso) * 100).toFixed(1)}%)`,
      '',
      'EXEMPLOS SEM TELEFONE:',
      ...resultado2.exemplosSemTelefone.map(exemplo => 
        `   Deal ${exemplo.dealId} (${exemplo.dealName}): processos [${exemplo.numerosProcesso.join(', ')}]`
      ),
      '',
      '='.repeat(80)
    ].join('\n');
    
    fs.writeFileSync(`analise2-telefones-${timestamp}.txt`, relatorio2, 'utf8');
    Logger.success('📄 Relatório da Análise 2 salvo');
  }
  
  if (resultado3) {
    const relatorio3 = [
      '='.repeat(80),
      'ANÁLISE 3: PROCESSOS NO BANCO DE DADOS',
      '='.repeat(80),
      `Data da análise: ${new Date().toLocaleString('pt-BR')}`,
      '',
      'RESUMO:',
      `📊 Números únicos encontrados nos JSONs: ${resultado3.estatisticas.totalUnicos.toLocaleString('pt-BR')}`,
      `✅ Existentes no banco: ${resultado3.estatisticas.existentesNoBanco.toLocaleString('pt-BR')} (${((resultado3.estatisticas.existentesNoBanco / resultado3.estatisticas.totalUnicos) * 100).toFixed(1)}%)`,
      `❌ Não existentes no banco: ${resultado3.estatisticas.naoExistentesNoBanco.toLocaleString('pt-BR')} (${((resultado3.estatisticas.naoExistentesNoBanco / resultado3.estatisticas.totalUnicos) * 100).toFixed(1)}%)`,
      `⚠️ Existentes mas sem procurador: ${resultado3.estatisticas.semProcurador.toLocaleString('pt-BR')} (${((resultado3.estatisticas.semProcurador / resultado3.estatisticas.existentesNoBanco) * 100).toFixed(1)}% dos existentes)`,
      '',
      'PROCESSOS SEM PROCURADOR:',
      ...resultado3.processosSemProcurador.slice(0, 20).map(processo => 
        `   ${processo.numero} (ID: ${processo.id})`
      ),
      ...(resultado3.processosSemProcurador.length > 20 ? [`   ... e mais ${resultado3.processosSemProcurador.length - 20} processos`] : []),
      '',
      '='.repeat(80)
    ].join('\n');
    
    fs.writeFileSync(`analise3-processos-banco-${timestamp}.txt`, relatorio3, 'utf8');
    
    // CSV com processos sem procurador
    if (resultado3.processosSemProcurador.length > 0) {
      const csvSemProcurador = [
        'numero_processo,processo_id',
        ...resultado3.processosSemProcurador.map(p => `"${p.numero}","${p.id}"`)
      ].join('\n');
      
      fs.writeFileSync(`processos-sem-procurador-${timestamp}.csv`, csvSemProcurador, 'utf8');
    }
    
    Logger.success('📄 Relatório da Análise 3 salvo');
  }
  
  if (resultado4) {
    const relatorio4 = [
      '='.repeat(80),
      'ANÁLISE 4: LINKS DE PROTOCOLO',
      '='.repeat(80),
      `Data da análise: ${new Date().toLocaleString('pt-BR')}`,
      '',
      'RESUMO:',
      `📊 Oportunidades com números de processo: ${resultado4.estatisticas.totalOportunidades.toLocaleString('pt-BR')}`,
      `✅ Com link de protocolo: ${resultado4.totalComLinkProtocolo.toLocaleString('pt-BR')} (${((resultado4.totalComLinkProtocolo / resultado4.estatisticas.totalOportunidades) * 100).toFixed(1)}%)`,
      `❌ Sem link de protocolo: ${resultado4.estatisticas.semLinkProtocolo.toLocaleString('pt-BR')} (${((resultado4.estatisticas.semLinkProtocolo / resultado4.estatisticas.totalOportunidades) * 100).toFixed(1)}%)`,
      `🔗 Links únicos encontrados: ${resultado4.linksUnicosProtocolo.length.toLocaleString('pt-BR')}`,
      '',
      'EXEMPLOS SEM LINK DE PROTOCOLO:',
      ...resultado4.exemplosSemLink.map(exemplo => 
        `   Deal ${exemplo.dealId} (${exemplo.dealName}): processos [${exemplo.numerosProcesso.join(', ')}]`
      ),
      ...(resultado4.exemplosSemLink.length === 0 ? ['   Nenhum exemplo (todos têm links de protocolo)'] : []),
      '',
      '='.repeat(80)
    ].join('\n');
    
    fs.writeFileSync(`analise4-links-protocolo-${timestamp}.txt`, relatorio4, 'utf8');
    
    // CSV com links únicos para download
    if (resultado4.linksUnicosProtocolo.length > 0) {
      const csvLinksUnicos = [
        'link_protocolo',
        ...resultado4.linksUnicosProtocolo.map(link => `"${link}"`)
      ].join('\n');
      
      fs.writeFileSync(`links-unicos-protocolo-${timestamp}.csv`, csvLinksUnicos, 'utf8');
      Logger.success(`📁 CSV com ${resultado4.linksUnicosProtocolo.length} links únicos salvo: links-unicos-protocolo-${timestamp}.csv`);
    }
    
    Logger.success('📄 Relatório da Análise 4 salvo');
  }
  
  if (resultado5) {
    const relatorio5 = [
      '='.repeat(80),
      'ANÁLISE 5: REGISTROS SEM TELEFONE',
      '='.repeat(80),
      `Data da análise: ${new Date().toLocaleString('pt-BR')}`,
      '',
      'RESUMO:',
      `📊 Registros sem telefone: ${resultado5.estatisticas.totalRegistrosSemTelefone.toLocaleString('pt-BR')}`,
      `📊 Números de processo sem telefone: ${resultado5.estatisticas.totalNumerosProcessoSemTelefone.toLocaleString('pt-BR')}`,
      `✅ Existentes no banco: ${resultado5.estatisticas.processosExistentesNoBanco.toLocaleString('pt-BR')}`,
      `❌ Não existentes no banco: ${resultado5.estatisticas.processosNaoExistentesNoBanco.toLocaleString('pt-BR')}`,
      `⚠️ Sem procurador: ${resultado5.estatisticas.processosSemProcurador.toLocaleString('pt-BR')}`,
      '',
      'PROCESSOS SEM PROCURADOR:',
      ...resultado5.processosSemProcurador.slice(0, 20).map(processo => 
        `   ${processo.numero} (ID: ${processo.id})`
      ),
      ...(resultado5.processosSemProcurador.length > 20 ? [`   ... e mais ${resultado5.processosSemProcurador.length - 20} processos`] : []),
      '',
      '='.repeat(80)
    ].join('\n');
    
    fs.writeFileSync(`analise5-registros-sem-telefone-${timestamp}.txt`, relatorio5, 'utf8');
    
    // CSV com processos sem procurador
    if (resultado5.processosSemProcurador.length > 0) {
      const csvSemProcurador = [
        'numero_processo,processo_id',
        ...resultado5.processosSemProcurador.map(p => `"${p.numero}","${p.id}"`)
      ].join('\n');
      
      fs.writeFileSync(`processos-sem-telefone-sem-procurador-${timestamp}.csv`, csvSemProcurador, 'utf8');
    }
    
    Logger.success('📄 Relatório da Análise 5 salvo');
  }
  
  if (resultado6) {
    const relatorio6 = [
      '='.repeat(80),
      'ANÁLISE 6: CONFLITOS DE TELEFONE ENTRE PERSONS',
      '='.repeat(80),
      `Data da análise: ${new Date().toLocaleString('pt-BR')}`,
      '',
      'RESUMO:',
      `📊 Total de telefones analisados: ${resultado6.estatisticas.totalTelefonesAnalisados.toLocaleString('pt-BR')}`,
      `⚠️ Telefones com conflito: ${resultado6.estatisticas.telefonesComConflito.toLocaleString('pt-BR')} (${((resultado6.estatisticas.telefonesComConflito / resultado6.estatisticas.totalTelefonesAnalisados) * 100).toFixed(1)}%)`,
      `✅ Telefones únicos: ${resultado6.estatisticas.telefonesUnicos.toLocaleString('pt-BR')} (${((resultado6.estatisticas.telefonesUnicos / resultado6.estatisticas.totalTelefonesAnalisados) * 100).toFixed(1)}%)`,
      `👥 Total de persons envolvidas: ${resultado6.estatisticas.totalPersonsEnvolvidas.toLocaleString('pt-BR')}`,
      `❌ Oportunidades sem person (ignoradas): ${resultado6.estatisticas.oportunidadesSemPerson.toLocaleString('pt-BR')}`,
      '',
      'EXEMPLOS DE CONFLITOS:',
      ...Object.entries(resultado6.telefonesComConflito).slice(0, 10).map(([telefone, persons]) => {
        const exemploPerson = persons.map(p => `${p.personName} (ID: ${p.personId}, ${p.dealIds.length} deals)`).join(' | ');
        return `   📞 ${telefone}: ${exemploPerson}`;
      }),
      ...(Object.keys(resultado6.telefonesComConflito).length === 0 ? ['   Nenhum conflito encontrado! 🎉'] : []),
      '',
      '='.repeat(80)
    ].join('\n');
    
    fs.writeFileSync(`analise6-conflitos-telefone-person-${timestamp}.txt`, relatorio6, 'utf8');
    
    // Salvar dados completos em JSON
    const dadosCompletos = {
      metadados: {
        dataAnalise: new Date().toISOString(),
        estatisticas: resultado6.estatisticas
      },
      telefonesComConflito: resultado6.telefonesComConflito,
      telefonesUnicos: resultado6.telefonesUnicos
    };
    
    fs.writeFileSync(`conflitos-telefone-person-${timestamp}.json`, JSON.stringify(dadosCompletos, null, 2), 'utf8');
    
    // CSV resumido com conflitos para análise rápida
    if (Object.keys(resultado6.telefonesComConflito).length > 0) {
      const csvConflitos = [
        'telefone_normalizado,quantidade_persons,person_ids,person_names,total_deals',
        ...Object.entries(resultado6.telefonesComConflito).map(([telefone, persons]) => {
          const personIds = persons.map(p => p.personId).join(';');
          const personNames = persons.map(p => p.personName).join(';');
          const totalDeals = persons.reduce((total, p) => total + p.dealIds.length, 0);
          return `"${telefone}","${persons.length}","${personIds}","${personNames}","${totalDeals}"`;
        })
      ].join('\n');
      
      fs.writeFileSync(`conflitos-telefone-resumo-${timestamp}.csv`, csvConflitos, 'utf8');
    }
    
    Logger.success('📄 Relatório da Análise 6 salvo');
    Logger.success(`📁 JSON completo salvo: conflitos-telefone-person-${timestamp}.json`);
  }
}

// Menu principal
function mostrarMenu(): Promise<string> {
  return new Promise((resolve) => {
    console.log('\n' + '='.repeat(60));
    console.log('🔍 ANÁLISES DETALHADAS DOS DADOS CRM');
    console.log('='.repeat(60));
    console.log('1 - Análise de múltiplos números de processo');
    console.log('2 - Análise de telefones em registros com processo');
    console.log('3 - Análise de processos no banco de dados');
    console.log('4 - Análise de links de protocolo');
    console.log('5 - Análise de registros sem telefone');
    console.log('6 - Análise de conflitos de telefone entre persons');
    console.log('7 - Executar todas as análises');
    console.log('0 - Sair');
    console.log('='.repeat(60));
    
    rl.question('Escolha uma opção: ', (resposta) => {
      resolve(resposta.trim());
    });
  });
}

// Função principal
async function main() {
  try {
    Logger.section("🎯 ANÁLISES DETALHADAS DOS DADOS CRM");
    
    let dadosCarregados: OportunidadeAnalisada[] | null = null;
    
    while (true) {
      const opcao = await mostrarMenu();
      
      switch (opcao) {
        case '1':
          if (!dadosCarregados) dadosCarregados = await carregarTodosOsDados();
          const resultado1 = await analise1_VerificarMultiplosNumeros(dadosCarregados);
          await gerarRelatorios(resultado1);
          
          console.log('\n📊 RESULTADO ANÁLISE 1:');
          console.log(`   Registros com número processo: ${resultado1.totalRegistrosComNumeroProcesso.toLocaleString('pt-BR')}`);
          console.log(`   Com múltiplos números: ${resultado1.registrosComMultiplosNumeros.toLocaleString('pt-BR')} (${((resultado1.registrosComMultiplosNumeros / resultado1.totalRegistrosComNumeroProcesso) * 100).toFixed(1)}%)`);
          break;
          
        case '2':
          if (!dadosCarregados) dadosCarregados = await carregarTodosOsDados();
          const resultado2 = await analise2_VerificarTelefones(dadosCarregados);
          await gerarRelatorios(undefined, resultado2);
          
          console.log('\n📞 RESULTADO ANÁLISE 2:');
          console.log(`   Registros com processo: ${resultado2.totalComNumeroProcesso.toLocaleString('pt-BR')}`);
          console.log(`   Com telefone Person: ${resultado2.comTelefonePerson.toLocaleString('pt-BR')} (${((resultado2.comTelefonePerson / resultado2.totalComNumeroProcesso) * 100).toFixed(1)}%)`);
          console.log(`   Com telefone Company: ${resultado2.comTelefoneCompany.toLocaleString('pt-BR')} (${((resultado2.comTelefoneCompany / resultado2.totalComNumeroProcesso) * 100).toFixed(1)}%)`);
          console.log(`   Sem telefone: ${resultado2.semTelefone.toLocaleString('pt-BR')} (${((resultado2.semTelefone / resultado2.totalComNumeroProcesso) * 100).toFixed(1)}%)`);
          break;
          
        case '3':
          if (!dadosCarregados) dadosCarregados = await carregarTodosOsDados();
          const resultado3 = await analise3_VerificarProcessosNoBanco(dadosCarregados);
          await gerarRelatorios(undefined, undefined, resultado3);
          
          console.log('\n🏦 RESULTADO ANÁLISE 3:');
          console.log(`   Números únicos nos JSONs: ${resultado3.estatisticas.totalUnicos.toLocaleString('pt-BR')}`);
          console.log(`   Existentes no banco: ${resultado3.estatisticas.existentesNoBanco.toLocaleString('pt-BR')} (${((resultado3.estatisticas.existentesNoBanco / resultado3.estatisticas.totalUnicos) * 100).toFixed(1)}%)`);
          console.log(`   Sem procurador: ${resultado3.estatisticas.semProcurador.toLocaleString('pt-BR')}`);
          break;
          
        case '4':
          if (!dadosCarregados) dadosCarregados = await carregarTodosOsDados();
          const resultado4 = await analise4_VerificarLinksProtocolo(dadosCarregados);
          await gerarRelatorios(undefined, undefined, undefined, resultado4);
          
          console.log('\n📊 RESULTADO ANÁLISE 4:');
          console.log(`   Oportunidades com processo: ${resultado4.estatisticas.totalOportunidades.toLocaleString('pt-BR')}`);
          console.log(`   Com link de protocolo: ${resultado4.totalComLinkProtocolo.toLocaleString('pt-BR')} (${((resultado4.totalComLinkProtocolo / resultado4.estatisticas.totalOportunidades) * 100).toFixed(1)}%)`);
          console.log(`   Sem link de protocolo: ${resultado4.estatisticas.semLinkProtocolo.toLocaleString('pt-BR')} (${((resultado4.estatisticas.semLinkProtocolo / resultado4.estatisticas.totalOportunidades) * 100).toFixed(1)}%)`);
          console.log(`   Links únicos: ${resultado4.linksUnicosProtocolo.length.toLocaleString('pt-BR')}`);
          break;
          
        case '5':
          if (!dadosCarregados) dadosCarregados = await carregarTodosOsDados();
          const resultado5 = await analise5_VerificarRegistrosSemTelefone(dadosCarregados);
          await gerarRelatorios(undefined, undefined, undefined, undefined, resultado5);
          
          console.log('\n📊 RESULTADO ANÁLISE 5:');
          console.log(`   Registros sem telefone: ${resultado5.estatisticas.totalRegistrosSemTelefone.toLocaleString('pt-BR')}`);
          console.log(`   Números de processo sem telefone: ${resultado5.estatisticas.totalNumerosProcessoSemTelefone.toLocaleString('pt-BR')}`);
          console.log(`   Existentes no banco: ${resultado5.estatisticas.processosExistentesNoBanco.toLocaleString('pt-BR')}`);
          console.log(`   Sem procurador: ${resultado5.estatisticas.processosSemProcurador.toLocaleString('pt-BR')}`);
          break;
          
        case '6':
          if (!dadosCarregados) dadosCarregados = await carregarTodosOsDados();
          const resultado6 = await analise6_VerificarConflitosPersonTelefone(dadosCarregados);
          await gerarRelatorios(undefined, undefined, undefined, undefined, undefined, resultado6);
          
          console.log('\n📞 RESULTADO ANÁLISE 6:');
          console.log(`   Telefones analisados: ${resultado6.estatisticas.totalTelefonesAnalisados.toLocaleString('pt-BR')}`);
          console.log(`   Telefones com conflito: ${resultado6.estatisticas.telefonesComConflito.toLocaleString('pt-BR')} (${((resultado6.estatisticas.telefonesComConflito / resultado6.estatisticas.totalTelefonesAnalisados) * 100).toFixed(1)}%)`);
          console.log(`   Persons envolvidas: ${resultado6.estatisticas.totalPersonsEnvolvidas.toLocaleString('pt-BR')}`);
          if (resultado6.estatisticas.telefonesComConflito > 0) {
            console.log(`   🎯 JSON salvo com dados completos dos conflitos`);
          }
          break;
          
        case '7':
          if (!dadosCarregados) dadosCarregados = await carregarTodosOsDados();
          
          Logger.info('🔄 Executando todas as análises...');
          const [res1, res2, res3, res4, res5, res6] = await Promise.all([
            analise1_VerificarMultiplosNumeros(dadosCarregados),
            analise2_VerificarTelefones(dadosCarregados),
            analise3_VerificarProcessosNoBanco(dadosCarregados),
            analise4_VerificarLinksProtocolo(dadosCarregados),
            analise5_VerificarRegistrosSemTelefone(dadosCarregados),
            analise6_VerificarConflitosPersonTelefone(dadosCarregados)
          ]);
          
          await gerarRelatorios(res1, res2, res3, res4, res5, res6);
          
          console.log('\n🎯 RESUMO TODAS AS ANÁLISES:');
          console.log(`📊 Análise 1: ${res1.registrosComMultiplosNumeros}/${res1.totalRegistrosComNumeroProcesso} com múltiplos números`);
          console.log(`📞 Análise 2: ${res2.semTelefone}/${res2.totalComNumeroProcesso} sem telefone`);
          console.log(`🏦 Análise 3: ${res3.estatisticas.semProcurador}/${res3.estatisticas.existentesNoBanco} sem procurador`);
          console.log(`🔗 Análise 4: ${res4.totalComLinkProtocolo}/${res4.estatisticas.totalOportunidades} com link de protocolo (${res4.linksUnicosProtocolo.length} únicos)`);
          console.log(`📵 Análise 5: ${res5.estatisticas.totalRegistrosSemTelefone} registros sem telefone (${res5.estatisticas.processosSemProcurador} sem procurador)`);
          console.log(`☎️ Análise 6: ${res6.estatisticas.telefonesComConflito}/${res6.estatisticas.totalTelefonesAnalisados} telefones com conflito`);
          break;
          
        case '0':
          console.log('👋 Saindo...');
          rl.close();
          return;
          
        default:
          console.log('❌ Opção inválida. Tente novamente.');
      }
      
      console.log('\n📁 Relatórios salvos na pasta raiz do projeto.');
      console.log('Pressione ENTER para continuar...');
      await new Promise(resolve => rl.question('', resolve));
    }
    
  } catch (error: any) {
    Logger.error("❌ Erro durante a execução:", error.message);
    rl.close();
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  main();
}

export { main as analisarDadosCrmDetalhado }; 