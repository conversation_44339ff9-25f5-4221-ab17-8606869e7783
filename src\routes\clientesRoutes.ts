import { Router } from 'express';
import * as clientesController from '../controllers/clientesController';

const router = Router();

/**
 * @route   GET /api/clientes/:idc/processos
 * @desc    Obter cliente por IDC (últimos 8 dígitos do telefone) e seus processos
 * @access  Público
 */
router.get('/:idc/processos', clientesController.getClienteProcessosByIdc);

/**
 * @route   GET /api/cliente/identificador-por-processo/:numeroProcesso
 * @desc    Obter o identificador de um cliente usando o número do processo
 * @access  Público
 */
router.get('/identificador-por-processo/:numeroProcesso', clientesController.handleGetIdentificadorPorNumeroProcesso);

export default router; 