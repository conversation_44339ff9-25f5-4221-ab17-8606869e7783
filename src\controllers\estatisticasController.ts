import { RequestHandler } from 'express';
import prisma from '../dbClient';

export const obterEstatisticas: RequestHandler = async (_req, res) => {
  try {
    // 1. Buscar 5 processos mais completos (com mais relacionamentos)
    const processosCompletos = await prisma.processo.findMany({
      where: {
     procurador:{
      nome:{
        contains:"REGISTRE-SE LTDA"
      }
     }
      },
      take: 5,
      include: {
        marca: {
          include: {
            ncl: true,
            cfe: true,
          },
        },
        titulares: true,
        despachos: {
          select: {
            rpiId: true,
            nome: true,
            textoComplementar: true,
            protocolos: true,
            rpi: {
              select: {
                numero: true,
                dataPublicacao: true,
              },
            },
          },
        },
        sobrestadores: true,
      },
      orderBy: [
        {
          despachos: {
            _count: "desc",
          },
        },
        {
          titulares: {
            _count: "desc",
          },
        },
      ],
    });

    // 2. Total de processos monitorados
    const totalMonitorados = await prisma.processo.count({
      where: {
        monitorado: true
      }
    });

    // 3. Total de processos no banco
    const totalProcessos = await prisma.processo.count();

    // 4. Total de revistas no banco (assumindo que cada processo tem uma data de depósito única)
    const totalRevistas = await prisma.rPI.count();
 
    res.json({
      processosCompletos: processosCompletos.map(processo => ({
        ...processo,
        _relacionamentos: {
          totalDespachos: processo.despachos.length,
          totalTitulares: processo.titulares.length,
          totalSobrestadores: processo.sobrestadores.length,
          totalNCL: processo.marca?.ncl.length || 0,
          totalCFE: processo.marca?.cfe.length || 0,
        }
      })),
      estatisticas: {
        totalProcessos,
        totalMonitorados,
        totalRevistas: totalRevistas,
      }
    });

  } catch (error) {
    console.error('Erro ao obter estatísticas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
}; 