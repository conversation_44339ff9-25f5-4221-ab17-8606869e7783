import { PrismaClient, TipoAcesso } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Busca todos os processos que um cliente tem acesso
 */
export async function getProcessosComAcesso(clienteId: number, tiposAcesso?: TipoAcesso[]) {
  const whereClause = {
    acessosCliente: {
      some: {
        clienteId: clienteId,
        ativo: true,
        ...(tiposAcesso && { tipoAcesso: { in: tiposAcesso } })
      }
    }
  };

  return await prisma.processo.findMany({
    where: whereClause,
    include: {
      marca: true,
      procurador: true,
      cliente: true,
      acessosCliente: {
        where: { clienteId: clienteId, ativo: true },
        select: {
          tipoAcesso: true,
          dataConcessao: true,
          observacoes: true
        }
      }
    },
    orderBy: { numero: 'asc' }
  });
}

/**
 * Verifica se um cliente tem acesso a um processo específico
 */
export async function clienteTemAcesso(
  clienteId: number, 
  processoId: string, 
  tipoAcessoMinimo?: TipoAcesso
): Promise<boolean> {
  const acesso = await prisma.clienteProcessoAcesso.findUnique({
    where: {
      clienteId_processoId: {
        clienteId: clienteId,
        processoId: processoId
      }
    },
    select: { tipoAcesso: true, ativo: true }
  });

  if (!acesso || !acesso.ativo) {
    return false;
  }

  // Se não especificou tipo mínimo, qualquer acesso vale
  if (!tipoAcessoMinimo) {
    return true;
  }

  // Hierarquia de acessos (mais restritivo -> mais permissivo)
  const hierarquia = {
    [TipoAcesso.TEMPORARIO]: 1,
    [TipoAcesso.VISUALIZACAO]: 2,
    [TipoAcesso.AUDITORIA]: 3,
    [TipoAcesso.PARCEIRO]: 4,
    [TipoAcesso.PROPRIETARIO]: 5,
    [TipoAcesso.CORINGA]: 6
  };

  return hierarquia[acesso.tipoAcesso] >= hierarquia[tipoAcessoMinimo];
}

/**
 * Concede acesso de um cliente a um processo
 */
export async function concederAcesso(
  clienteId: number,
  processoId: string,
  tipoAcesso: TipoAcesso,
  observacoes?: string,
  dataExpiracao?: Date,
  concedidoPor?: string
) {
  return await prisma.clienteProcessoAcesso.upsert({
    where: {
      clienteId_processoId: {
        clienteId: clienteId,
        processoId: processoId
      }
    },
    update: {
      tipoAcesso: tipoAcesso,
      observacoes: observacoes,
      dataExpiracao: dataExpiracao,
      concedidoPor: concedidoPor,
      ativo: true,
      updatedAt: new Date()
    },
    create: {
      clienteId: clienteId,
      processoId: processoId,
      tipoAcesso: tipoAcesso,
      observacoes: observacoes,
      dataExpiracao: dataExpiracao,
      concedidoPor: concedidoPor,
      ativo: true
    }
  });
}

/**
 * Remove acesso de um cliente a um processo
 */
export async function removerAcesso(clienteId: number, processoId: string) {
  return await prisma.clienteProcessoAcesso.update({
    where: {
      clienteId_processoId: {
        clienteId: clienteId,
        processoId: processoId
      }
    },
    data: {
      ativo: false,
      updatedAt: new Date()
    }
  });
}

/**
 * Lista clientes que têm acesso a um processo específico
 */
export async function getClientesComAcessoAoProcesso(processoId: string) {
  return await prisma.clienteProcessoAcesso.findMany({
    where: {
      processoId: processoId,
      ativo: true
    },
    include: {
      cliente: {
        select: {
          id: true,
          identificador: true,
          nome: true,
          tipoDeDocumento: true
        }
      }
    },
    orderBy: [
      { tipoAcesso: 'desc' }, // CORINGA primeiro, depois PROPRIETARIO, etc.
      { dataConcessao: 'asc' }
    ]
  });
}

/**
 * Busca processos com filtros avançados baseado em permissões
 */
export async function buscarProcessosComPermissao(
  clienteId: number,
  filtros?: {
    numero?: string;
    marca?: string;
    procurador?: string;
    dataDepositoInicio?: Date;
    dataDepositoFim?: Date;
    tiposAcesso?: TipoAcesso[];
    limite?: number;
    offset?: number;
  }
) {
  const whereClause: any = {
    acessosCliente: {
      some: {
        clienteId: clienteId,
        ativo: true,
        ...(filtros?.tiposAcesso && { tipoAcesso: { in: filtros.tiposAcesso } })
      }
    }
  };

  // Adicionar filtros específicos
  if (filtros?.numero) {
    whereClause.numero = { contains: filtros.numero, mode: 'insensitive' };
  }

  if (filtros?.marca) {
    whereClause.marca = {
      nome: { contains: filtros.marca, mode: 'insensitive' }
    };
  }

  if (filtros?.procurador) {
    whereClause.procurador = {
      nome: { contains: filtros.procurador, mode: 'insensitive' }
    };
  }

  if (filtros?.dataDepositoInicio || filtros?.dataDepositoFim) {
    whereClause.dataDeposito = {};
    if (filtros.dataDepositoInicio) {
      whereClause.dataDeposito.gte = filtros.dataDepositoInicio;
    }
    if (filtros.dataDepositoFim) {
      whereClause.dataDepositoFim.lte = filtros.dataDepositoFim;
    }
  }

  const [processos, total] = await Promise.all([
    prisma.processo.findMany({
      where: whereClause,
      include: {
        marca: true,
        procurador: true,
        cliente: true,
        acessosCliente: {
          where: { clienteId: clienteId, ativo: true },
          select: {
            tipoAcesso: true,
            dataConcessao: true,
            observacoes: true
          }
        }
      },
      orderBy: { numero: 'asc' },
      ...(filtros?.limite && { take: filtros.limite }),
      ...(filtros?.offset && { skip: filtros.offset })
    }),
    prisma.processo.count({ where: whereClause })
  ]);

  return {
    processos,
    total,
    temMais: filtros?.limite ? (filtros.offset || 0) + filtros.limite < total : false
  };
}

/**
 * Relatório de permissões por cliente
 */
export async function relatorioPermissoesCliente(clienteId: number) {
  const acessos = await prisma.clienteProcessoAcesso.findMany({
    where: { clienteId: clienteId, ativo: true },
    include: {
      processo: {
        select: {
          numero: true,
          marca: { select: { nome: true } },
          procurador: { select: { nome: true } }
        }
      }
    },
    orderBy: { tipoAcesso: 'desc' }
  });

  const resumo = await prisma.clienteProcessoAcesso.groupBy({
    by: ['tipoAcesso'],
    where: { clienteId: clienteId, ativo: true },
    _count: { id: true }
  });

  return {
    acessos,
    resumo: resumo.reduce((acc, item) => {
      acc[item.tipoAcesso] = item._count.id;
      return acc;
    }, {} as Record<TipoAcesso, number>)
  };
}

/**
 * Limpa acessos expirados
 */
export async function limparAcessosExpirados() {
  const agora = new Date();
  
  const resultado = await prisma.clienteProcessoAcesso.updateMany({
    where: {
      dataExpiracao: { lte: agora },
      ativo: true
    },
    data: {
      ativo: false,
      updatedAt: agora
    }
  });

  return resultado.count;
} 