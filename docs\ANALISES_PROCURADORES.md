# Análises de Procuradores - Taxa de Assertividade

Este documento descreve as funcionalidades implementadas para análise de procuradores incluindo taxa de assertividade baseada em despachos de mérito.

## ⚠️ **Importante: Lógica Corrigida**

A lógica foi corrigida para resolver o problema de correlação temporal entre depósitos e despachos de mérito:

- **Problema anterior**: Buscava processos depositados em 2025 E seus despachos de mérito (que poderiam ser publicados em 2026/2027)
- **Solução atual**: Duas abordagens distintas disponíveis

## Endpoints Disponíveis

### 1. Ranking Híbrido (Recomendado para análise de produtividade)
**Endpoint:** `GET /analises/procuradores/ranking`

**Metodologia:**
- **Ranking**: Baseado em processos **depositados** no período
- **Mérito**: Baseado em despachos **publicados** no período

**Parâmetros:**
- `ano` (opcional): Ano para filtrar (ex: 2024)
- `dataInicio` e `dataFim` (opcional): Período específico (formato: YYYY-MM-DD)
- `excluirSemProcurador` (opcional): true/false - Se deve excluir processos sem procurador
- `page` (opcional): Página (padrão: 1)
- `limit` (opcional): Registros por página (padrão: 50)

**Retorno:**
```json
{
  "data": [
    {
      "procuradorNome": "Nome do Procurador",
      "quantidadeProcessos": 150,
      "porcentagem": 25.5,
      "merito": {
        "totalProcessosComMerito": 45,
        "deferimentos": 38,
        "indeferimentos": 7,
        "taxaAssertividade": 84.44
      }
    }
  ],
  "total": 589,
  "resumoMerito": {
    "totalDespachosMerito": 312,
    "totalDeferimentos": 267,
    "totalIndeferimentos": 45,
    "observacao": "Ranking baseado em processos DEPOSITADOS no período. Mérito baseado em despachos PUBLICADOS no período."
  }
}
```

### 2. Ranking Puro por Mérito (Recomendado para análise de assertividade)
**Endpoint:** `GET /analises/procuradores/ranking-merito`

**Metodologia:**
- **Ranking**: Baseado exclusivamente em despachos de mérito **publicados** no período
- **Mérito**: Todos os dados são do mesmo período temporal

**Parâmetros:**
- `ano` (opcional): Ano para filtrar
- `dataInicio` e `dataFim` (opcional): Período específico
- `excluirSemProcurador` (opcional): true/false

**Retorno:**
```json
{
  "data": [
    {
      "posicao": 1,
      "procuradorNome": "Nome do Procurador",
      "totalProcessosComMerito": 45,
      "porcentagemDoTotal": 14.42,
      "deferimentos": 38,
      "indeferimentos": 7,
      "taxaAssertividade": 84.44
    }
  ],
  "resumoGeral": {
    "totalDespachosMerito": 312,
    "totalDeferimentos": 267,
    "totalIndeferimentos": 45,
    "taxaAssertividadeGeral": 85.58,
    "observacao": "Ranking baseado exclusivamente em despachos de mérito PUBLICADOS no período filtrado."
  }
}
```

### 3. Listagem de Processos com Despachos de Mérito
**Endpoint:** `GET /analises/procuradores/merito`

**Parâmetros:**
- `ano` (opcional): Ano para filtrar
- `dataInicio` e `dataFim` (opcional): Período específico
- `page` (opcional): Página (padrão: 1)
- `limit` (opcional): Registros por página (padrão: 200)
- `tipoMerito` (opcional): "deferimento" ou "indeferimento" para filtrar tipo específico
- `procuradorId` (opcional): ID específico do procurador para filtrar

**Retorno:**
```json
{
  "data": [
    {
      "numeroProcesso": "*********",
      "nomeDespacho": "Deferimento do pedido",
      "tipoMerito": "Favorável",
      "dataDespacho": "2024-06-15T10:30:00.000Z",
      "numeroRPI": "2567",
      "procurador": "Nome do Procurador",
      "procuradorId": "uuid-procurador",
      "nomeMarca": "MARCA EXEMPLO",
      "classe": "01",
      "dataDeposito": "2023-12-01T08:00:00.000Z"
    }
  ],
  "pagination": {
    "total": 312,
    "page": 1,
    "limit": 200,
    "pages": 2
  },
  "estatisticas": {
    "totalProcessosComMerito": 312,
    "totalFavoraveisNaPagina": 180,
    "totalDesfavoraveisNaPagina": 20,
    "taxaAssertividadeGeral": 85.58,
    "observacao": "Contadores da página atual. Taxa geral calculada sobre o total de registros."
  }
}
```

## Qual Endpoint Usar?

### 🎯 **Para análise de produtividade** (quantos processos cada procurador depositou):
Use `/ranking` - mostra quantos processos foram depositados no período + taxa de assertividade dos despachos publicados no mesmo período.

### 📊 **Para análise de assertividade pura** (como cada procurador se saiu nos despachos):
Use `/ranking-merito` - mostra apenas procuradores que tiveram despachos de mérito publicados no período, ordenados por quantidade de méritos.

### 📋 **Para análise detalhada dos casos**:
Use `/merito` - lista todos os processos com despachos de mérito do período.

## Critérios de Mérito

### Despachos Favoráveis (Deferimentos):
- "Deferimento do pedido"
- "Recurso provido (decisão reformada para: Deferimento)"

### Despachos Desfavoráveis (Indeferimentos):
- "Indeferimento do pedido"

## Cálculo da Taxa de Assertividade

```
Taxa de Assertividade = (Total de Deferimentos / Total de Processos com Mérito) × 100
```

## Performance e Escalabilidade

### Estratégias Implementadas:
1. **Ranking Otimizado**: Busca primeiro os procuradores, depois seus despachos
2. **Índices Utilizados**: Aproveita índices existentes em processoId, nome, dataDeposito, dataPublicacao
3. **Paginação Eficiente**: Separação entre contagem total e dados da página
4. **Filtros Direcionados**: Reduz escopo de busca quando possível
5. **Dados Mínimos**: Busca apenas campos necessários

## Exemplos de Uso

### Ranking de produtividade em 2025:
```
GET /analises/procuradores/ranking?ano=2025
```

### Ranking de assertividade pura em 2025:
```
GET /analises/procuradores/ranking-merito?ano=2025
```

### Despachos de mérito publicados em junho/2025:
```
GET /analises/procuradores/merito?dataInicio=2025-06-01&dataFim=2025-06-30
```

### Assertividade de um procurador específico:
```
GET /analises/procuradores/ranking-merito?ano=2025&procuradorId=uuid-do-procurador
``` 