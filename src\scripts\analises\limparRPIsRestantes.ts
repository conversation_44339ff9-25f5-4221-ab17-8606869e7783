import { PrismaClient } from '@prisma/client';
import cliProgress from 'cli-progress';

const prisma = new PrismaClient();

// Configurações otimizadas para RPIs
const BATCH_SIZE_RPIS = 20; // Lotes pequenos para evitar timeout
const MAX_CONCURRENT_BATCHES = 2; // Baixa concorrência
const TRANSACTION_TIMEOUT = 60000; // 60 segundos

// Ranges de números de RPI fictícios criados pelos scripts
const RANGES_RPI_FICTICIOS = [
  { min: 999900000, max: 999999999 }, // criarDadosTesteFluxos.ts
  { min: 888800000, max: 888899999 }, // criarDadosTesteCliente002.ts
  { min: 777700000, max: 777799999 }  // criarDadosTesteCliente003.ts
];

// Códigos de despacho dos dados de teste
const CODIGOS_DESPACHO_TESTE = ['TESTE', 'FLUXO_ESP', 'FLUXO_DT3'];

/**
 * Divide um array em lotes de tamanho específico
 */
function dividirEmLotes<T>(array: T[], tamanhoBatch: number): T[][] {
  const lotes: T[][] = [];
  for (let i = 0; i < array.length; i += tamanhoBatch) {
    lotes.push(array.slice(i, i + tamanhoBatch));
  }
  return lotes;
}

/**
 * Processa um lote de RPIs de forma atômica e mais conservadora
 */
async function processarLoteRPIs(loteRPIs: string[]) {
  return await prisma.$transaction(async (tx) => {
    console.log(`   Processando lote de ${loteRPIs.length} RPIs...`);
    
    // 1. Remover Despachos órfãos que referenciam estas RPIs
    const despachosRemovidos = await tx.despacho.deleteMany({
      where: { rpiId: { in: loteRPIs } }
    });
    console.log(`   • ${despachosRemovidos.count} despachos órfãos removidos`);

    // 2. Remover Titulares que referenciam estas RPIs
    const titularesRemovidos = await tx.titular.deleteMany({
      where: { rpiId: { in: loteRPIs } }
    });
    console.log(`   • ${titularesRemovidos.count} titulares órfãos removidos`);

    // 3. Remover RPIs
    const rpisRemovidas = await tx.rPI.deleteMany({
      where: { id: { in: loteRPIs } }
    });
    console.log(`   • ${rpisRemovidas.count} RPIs removidas`);

    return rpisRemovidas.count;
  }, {
    timeout: TRANSACTION_TIMEOUT,
    maxWait: 10000
  });
}

/**
 * Função principal para limpar RPIs restantes
 */
async function limparRPIsRestantes() {
  try {
    console.log('🧹 Limpando RPIs fictícias restantes...\n');

    // 1. Identificar RPIs fictícias restantes
    console.log('📋 Identificando RPIs fictícias restantes...');
    const rpisFicticias = await prisma.rPI.findMany({
      where: { 
        OR: RANGES_RPI_FICTICIOS.map(range => ({
          numero: { gte: range.min, lte: range.max }
        }))
      },
      select: { id: true, numero: true }
    });

    console.log(`   Encontradas ${rpisFicticias.length} RPIs fictícias restantes\n`);

    if (rpisFicticias.length === 0) {
      console.log('✅ Nenhuma RPI fictícia encontrada para remover.');
      
      // Fazer limpeza final de despachos órfãos por código
      console.log('🧹 Verificando despachos órfãos por código...');
      const despachosOrfaos = await prisma.despacho.deleteMany({
        where: { codigo: { in: CODIGOS_DESPACHO_TESTE } }
      });
      console.log(`   ✅ ${despachosOrfaos.count} despachos órfãos removidos por código\n`);
      
      return;
    }

    // 2. Configurar processamento
    console.log('⚡ Configuração Conservadora:');
    console.log(`   • Lote de RPIs: ${BATCH_SIZE_RPIS} registros`);
    console.log(`   • Lotes paralelos: ${MAX_CONCURRENT_BATCHES} simultâneos`);
    console.log(`   • Timeout: ${TRANSACTION_TIMEOUT}ms`);
    console.log(`   • Total de lotes: ${Math.ceil(rpisFicticias.length / BATCH_SIZE_RPIS)}\n`);

    // 3. Dividir em lotes
    const rpisIds = rpisFicticias.map(r => r.id);
    const lotesRPIs = dividirEmLotes(rpisIds, BATCH_SIZE_RPIS);

    // 4. Processar lotes sequencialmente (mais seguro)
    console.log('🔄 Removendo RPIs em lotes sequenciais...\n');
    let totalRemovido = 0;
    
    for (let i = 0; i < lotesRPIs.length; i++) {
      const lote = lotesRPIs[i];
      console.log(`📦 Lote ${i + 1}/${lotesRPIs.length}:`);
      
      try {
        const removidas = await processarLoteRPIs(lote);
        totalRemovido += removidas;
        console.log(`   ✅ Lote ${i + 1} concluído\n`);
        
        // Pequena pausa entre lotes para não sobrecarregar
        if (i < lotesRPIs.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } catch (error: any) {
        console.error(`   ❌ Erro no lote ${i + 1}:`, error.message);
        console.log(`   ⏸️ Continuando com próximo lote...\n`);
      }
    }

    // 5. Limpeza final
    console.log('🧹 Limpeza final de despachos órfãos por código...');
    const despachosOrfaos = await prisma.despacho.deleteMany({
      where: { codigo: { in: CODIGOS_DESPACHO_TESTE } }
    });
    console.log(`   ✅ ${despachosOrfaos.count} despachos órfãos removidos\n`);

    // Resumo final
    console.log('🎉 LIMPEZA DE RPIs CONCLUÍDA!');
    console.log('📊 Resumo:');
    console.log(`   • ${totalRemovido} RPIs removidas`);
    console.log(`   • ${despachosOrfaos.count} despachos órfãos limpos`);
    console.log(`   • Processamento sequencial para máxima estabilidade\n`);

  } catch (error: any) {
    console.error('\n❌ Erro na limpeza de RPIs:', error.message);
    if (error.code) {
      console.error('Código do erro:', error.code);
    }
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar limpeza
console.log('🚀 Iniciando limpeza focada em RPIs restantes...');
limparRPIsRestantes(); 