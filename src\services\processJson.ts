import { PrismaClient } from "@prisma/client";
import { readdir, readFile, rename } from "fs/promises";
import fs from "fs";
import path from "path";

const prisma = new PrismaClient();
const ERROR_LOG_FILE = path.join(__dirname, "error.log");
const PROCESSED_DIR = path.join(__dirname, "../../uploads/rpi/processed");

// ID fixo do procurador REGISTRE-SE para evitar duplicatas
const REGISTRE_SE_PROCURADOR_ID = "65b3c0c0-aa3b-4d89-85fb-2a144e538800";

// ✅ NOVO: Estrutura para relatório da REGISTRE-SE
interface RelatorioItem {
  processo: string;
  despacho: string;
}

interface RelatorioRegistreSe {
  items: RelatorioItem[];
  contadores: Map<string, number>;
  despachosCriados: number;
  despachosAtualizados: number;
  protocolosProcessados: number;
  despachosElegiveisRegistrados: number;
}

const relatorioRegistreSe: RelatorioRegistreSe = {
  items: [],
  contadores: new Map(),
  despachosCriados: 0,
  despachosAtualizados: 0,
  protocolosProcessados: 0,
  despachosElegiveisRegistrados: 0
};

async function retry<T>(
  fn: () => Promise<T>,
  retries = 3,
  delay = 1000
): Promise<T> {
  try {
    return await fn();
  } catch (err) {
    if (retries > 0) {
      const errorDetails =
        err instanceof Error
          ? `\nMensagem: ${err.message}\nStack: ${err.stack}`
          : `\nErro: ${JSON.stringify(err)}`;
      console.warn(
        `Erro detectado, tentando novamente em ${delay}ms... (${retries} tentativas restantes)${errorDetails}`
      );
      await new Promise((res) => setTimeout(res, delay));
      return retry(fn, retries - 1, delay * 2);
    }
    throw err;
  }
}

async function logError(message: string): Promise<void> {
  const timestamp = new Date().toISOString();
  await fs.promises.appendFile(ERROR_LOG_FILE, `[${timestamp}] ${message}\n`);
}

function parseDate(dateStr: string | null): Date | null {
  if (!dateStr) return null;
  const [day, month, year] = dateStr.split("/");
  return new Date(Number(year), Number(month) - 1, Number(day));
}

async function upsertProcurador(nome: string): Promise<{ id: string }> {
  return await retry(() =>
    prisma.procurador.upsert({
      where: { nome },
      update: { nome },
      create: { nome },
    })
  );
}

async function processTitulares(
  titulares: any[],
  processoId: string,
  rpiId: string,
  processoMonitorado: boolean = false
): Promise<void> {
  if (!titulares || !titulares.length) return;

  for (const titular of titulares) {
    const nomeRazaoSocial = titular.nome_razao_social;
    if (!nomeRazaoSocial) continue;

    // Busca inicial: sempre com rpiId primeiro
    let existingTitular = await prisma.titular.findFirst({
      where: {
        processoId: processoId,
        rpiId: rpiId,
        nomeRazaoSocial: nomeRazaoSocial,
      },
    });

    // ✅ LÓGICA ESPECIAL: Só para processos REGISTRE-SE (monitorados)
    // Permite rastreabilidade de mudanças de titulares
    if (!existingTitular && processoMonitorado) {
      // Buscar titular sem rpiId (criado pelo sistema de protocolos)
      existingTitular = await prisma.titular.findFirst({
        where: {
          processoId: processoId,
          rpiId: null,
          nomeRazaoSocial: nomeRazaoSocial,
        },
      });

      // Se encontrou, confirma que titular permaneceu igual na RPI
      if (existingTitular) {
        await retry(() =>
          prisma.titular.update({
            where: { id: existingTitular!.id },
            data: {
              rpiId: rpiId, // ✅ Confirma titular na RPI
              // Atualizar outros campos se necessário
              pais: titular.pais || existingTitular!.pais,
              uf: titular.uf || existingTitular!.uf,
            },
          })
        );
        console.log(`✅ Titular confirmado na RPI: ${nomeRazaoSocial}`);
      }
    }

    // Se ainda não existe, criar novo titular
    // Para processos REGISTRE-SE: pode indicar mudança de titular
    if (!existingTitular) {
      await retry(() =>
        prisma.titular.create({
          data: {
            processo: { connect: { id: processoId } },
            rpi: { connect: { id: rpiId } },
            nomeRazaoSocial: nomeRazaoSocial,
            pais: titular.pais,
            uf: titular.uf,
          },
        })
      );
      
      if (processoMonitorado) {
        console.log(`🔄 Novo titular detectado (possível mudança): ${nomeRazaoSocial}`);
      }
    }
  }
}

async function processProtocolos(
  protocolos: any[],
  despachoId: string
): Promise<void> {
  if (!protocolos || !protocolos.length) return;

  for (const protocolo of protocolos) {
    const numeroProtocolo = protocolo.numero;
    if (!numeroProtocolo) continue;

    let procurador = null;
    if (protocolo.procurador) {
      const procuradorUpper = protocolo.procurador.toUpperCase();
      
      if (procuradorUpper.includes("REGISTRE-SE")) {
        procurador = { id: REGISTRE_SE_PROCURADOR_ID };
      } else {
        procurador = await upsertProcurador(protocolo.procurador);
      }
    }

    const existingProtocolo = await prisma.protocoloDespacho.findFirst({
      where: {
        despachoId: despachoId,
        numero: numeroProtocolo,
      },
    });

    if (!existingProtocolo) {
      await retry(() =>
        prisma.protocoloDespacho.create({
          data: {
            despacho: { connect: { id: despachoId } },
            numero: numeroProtocolo,
            data: parseDate(protocolo.data) || new Date(),
            codigoServico: protocolo.codigo_servico || "",
            requerenteNomeRazaoSocial:
              protocolo.requerente?.nome_razao_social || "",
            requerentePais: protocolo.requerente?.pais || "",
            requerenteUf: protocolo.requerente?.uf || "",
            procurador: procurador
              ? { connect: { id: procurador.id } }
              : undefined,
          },
        })
      );
    }
  }
}

async function processDespachos(
  despachos: any[],
  processoId: string,
  rpiId: string,
  processoNumero: string,
  isMonitorado: boolean = false,
  clienteId: number | null = null
): Promise<void> {
  if (!despachos || !despachos.length) return;

  const isElegivelParaComunicados = isMonitorado && clienteId !== null;

  for (const despacho of despachos) {
    const codigoDespacho = despacho.codigo;
    if (!codigoDespacho) continue;

    // ✅ NOVO: Coletar dados para relatório da REGISTRE-SE (sempre que for monitorado)
    if (isMonitorado && despacho.nome) {
      relatorioRegistreSe.items.push({
        processo: processoNumero,
        despacho: despacho.nome
      });
      
      const contador = relatorioRegistreSe.contadores.get(despacho.nome) || 0;
      relatorioRegistreSe.contadores.set(despacho.nome, contador + 1);
    }

    const existingDespacho = await prisma.despacho.findFirst({
      where: {
        processoId: processoId,
        rpiId: rpiId,
        nome: despacho.nome,
      },
      select: { id: true, codigo: true, textoComplementar: true },
    });

    let despachoRecordId: string;

    if (!existingDespacho) {
      // ✅ Criar novo despacho
      const novoDespacho = await retry(() =>
        prisma.despacho.create({
          data: {
            processo: { connect: { id: processoId } },
            rpi: { connect: { id: rpiId } },
            codigo: codigoDespacho,
            nome: despacho.nome,
            textoComplementar: despacho.texto_complementar,
          },
          select: { id: true },
        })
      );
      despachoRecordId = novoDespacho.id;

      // ✅ Incrementar contador para relatório
      if (isMonitorado) {
        relatorioRegistreSe.despachosCriados++;
      }

      if (Array.isArray(despacho.protocolos)) {
        await processProtocolos(despacho.protocolos, despachoRecordId);
        // ✅ Incrementar contador de protocolos
        if (isMonitorado) {
          relatorioRegistreSe.protocolosProcessados += despacho.protocolos.length;
        }
      }

      // ✅ NOVO: Registrar despacho como elegível para comunicados
      if (isElegivelParaComunicados) {
        try {
          // Verificar se já existe um despacho elegível para evitar duplicatas
          const existingDespachoElegivel = await prisma.despachoElegivel.findFirst({
            where: {
              processoId: processoId,
              clienteId: clienteId!,
              despachoId: despachoRecordId,
            },
          });

          if (!existingDespachoElegivel) {
            await retry(() =>
              prisma.despachoElegivel.create({
                data: {
                  processo: { connect: { id: processoId } },
                  cliente: { connect: { id: clienteId! } },
                  despacho: { connect: { id: despachoRecordId } },
                  statusComunicado: 'PENDENTE',
                  dataRegistro: new Date(),
                },
              })
            );
            console.log(`📢 Despacho elegível registrado: ${processoNumero} - ${despacho.nome}`);
            
            // ✅ Incrementar contador para relatório
            relatorioRegistreSe.despachosElegiveisRegistrados++;
          } else {
            console.log(`ℹ️ Despacho elegível já existe: ${processoNumero} - ${despacho.nome}`);
          }
        } catch (error: any) {
          // Log do erro mas não falha o processamento principal
          await logError(
            `Erro ao registrar despacho elegível - Processo: ${processoNumero}, Despacho: ${codigoDespacho}, Erro: ${error.message}`
          );
        }
      }
    } else {
      // ✅ Despacho existe - atualizar código e texto complementar se necessário
      despachoRecordId = existingDespacho.id;
      
      const precisaAtualizar = 
        existingDespacho.codigo !== codigoDespacho || 
        existingDespacho.textoComplementar !== despacho.texto_complementar;

      if (precisaAtualizar) {
        await retry(() =>
          prisma.despacho.update({
            where: { id: despachoRecordId },
            data: {
              codigo: codigoDespacho,
              textoComplementar: despacho.texto_complementar,
            },
          })
        );
        console.log(`🔄 Despacho atualizado: ${processoNumero} - ${despacho.nome} (código: ${existingDespacho.codigo} → ${codigoDespacho})`);
        
        // ✅ Incrementar contador para relatório
        if (isMonitorado) {
          relatorioRegistreSe.despachosAtualizados++;
        }
      }

      // ✅ Processar protocolos se necessário (apenas se novos)
      if (Array.isArray(despacho.protocolos) && despacho.protocolos.length > 0) {
        await processProtocolos(despacho.protocolos, despachoRecordId);
        // ✅ Incrementar contador de protocolos
        if (isMonitorado) {
          relatorioRegistreSe.protocolosProcessados += despacho.protocolos.length;
        }
      }

      // ✅ Registrar como elegível se for o caso
      if (isElegivelParaComunicados) {
        try {
          const existingDespachoElegivel = await prisma.despachoElegivel.findFirst({
            where: {
              processoId: processoId,
              clienteId: clienteId!,
              despachoId: despachoRecordId,
            },
          });

          if (!existingDespachoElegivel) {
            await retry(() =>
              prisma.despachoElegivel.create({
                data: {
                  processo: { connect: { id: processoId } },
                  cliente: { connect: { id: clienteId! } },
                  despacho: { connect: { id: despachoRecordId } },
                  statusComunicado: 'PENDENTE',
                  dataRegistro: new Date(),
                },
              })
            );
            console.log(`📢 Despacho elegível registrado: ${processoNumero} - ${despacho.nome}`);
            
            // ✅ Incrementar contador para relatório
            relatorioRegistreSe.despachosElegiveisRegistrados++;
          } else {
            console.log(`ℹ️ Despacho elegível já existe: ${processoNumero} - ${despacho.nome}`);
          }
        } catch (error: any) {
          await logError(
            `Erro ao registrar despacho elegível - Processo: ${processoNumero}, Despacho: ${codigoDespacho}, Erro: ${error.message}`
          );
        }
      }
    }
  }
}

async function processSobrestadores(
  sobrestadores: any[],
  processoId: string
): Promise<void> {
  if (!sobrestadores || !sobrestadores.length) return;

  for (const sobrestador of sobrestadores) {
    const referenciaProcessual = sobrestador.processo;
    if (!referenciaProcessual) continue;

    const existingSobrestador = await prisma.sobrestador.findFirst({
      where: {
        processoId: processoId,
        referenciaProcessual: referenciaProcessual,
      },
    });

    if (!existingSobrestador) {
      await retry(() =>
        prisma.sobrestador.create({
          data: {
            processo: { connect: { id: processoId } },
            referenciaProcessual: referenciaProcessual,
            marca: sobrestador.marca || "N/A",
          },
        })
      );
    }
  }
}

function addMonths(date: Date, months: number): Date {
  const d = new Date(date);
  const day = d.getDate();
  d.setMonth(d.getMonth() + months);
  if (d.getDate() < day) {
    d.setDate(0);
  }
  return d;
}

function calcularProrrogacoes(dataConcessao: Date): {
  ordinaria: string | null;
  extraordinaria: string | null;
} {
  try {
    const ordinariaInicio = new Date(dataConcessao);
    ordinariaInicio.setFullYear(ordinariaInicio.getFullYear() + 9);
    ordinariaInicio.setDate(ordinariaInicio.getDate() + 1);

    const ordinariaFim = new Date(dataConcessao);
    ordinariaFim.setFullYear(ordinariaFim.getFullYear() + 10);

    const extraordinariaInicio = new Date(dataConcessao);
    extraordinariaInicio.setFullYear(extraordinariaInicio.getFullYear() + 10);
    extraordinariaInicio.setDate(extraordinariaInicio.getDate() + 1);

    let extraordinariaFim = addMonths(extraordinariaInicio, 6);
    extraordinariaFim.setDate(extraordinariaFim.getDate() - 1);

    const formatarData = (data: Date) =>
      data.toLocaleDateString("pt-BR", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      });

    return {
      ordinaria: `${formatarData(ordinariaInicio)} até ${formatarData(
        ordinariaFim
      )}`,
      extraordinaria: `${formatarData(extraordinariaInicio)} até ${formatarData(
        extraordinariaFim
      )}`,
    };
  } catch (error) {
    console.error(
      `Erro ao calcular prorrogações para ${dataConcessao}: ${error}`
    );
    return { ordinaria: null, extraordinaria: null };
  }
}

function verificarDespachos(
  despachos: any[],
  dataPublicacaoRpi: Date,
  dataDeposito: Date | null
) {
  let temOposicao = false,
    temSobrestamento = false,
    temExigencia = false;
  let dataOposicao = null,
    dataSobrestamento = null,
    dataExigencia = null,
    dataMerito = null,
    dataConcessaoDespacho = null;
  let diasCorridosMerito: number | null = null;

  for (const despacho of despachos) {
    const nome = despacho.nome || "";
    if (nome.includes("Notificação de oposição")) {
      temOposicao = true;
      dataOposicao = dataPublicacaoRpi;
    }
    if (nome.includes("Sobrestamento")) {
      temSobrestamento = true;
      dataSobrestamento = dataPublicacaoRpi;
    }
    if (nome.includes("Exigência")) {
      temExigencia = true;
      dataExigencia = dataPublicacaoRpi;
    }
    if (nome.includes("Deferimento") || nome.includes("Indeferimento")) {
      if (!dataMerito) dataMerito = dataPublicacaoRpi;
    }
    if (nome.includes("Concessão de registro")) {
      dataConcessaoDespacho = dataPublicacaoRpi;
    }
  }

  if (dataMerito && dataDeposito) {
    const diff = dataMerito.getTime() - dataDeposito.getTime();
    diasCorridosMerito = Math.ceil(diff / (1000 * 60 * 60 * 24));
  }

  return {
    oposicao: temOposicao,
    dataOposicao,
    sobrestamento: temSobrestamento,
    dataSobrestamento,
    exigencia: temExigencia,
    dataExigencia,
    dataMerito,
    diasCorridosMerito,
    dataConcessaoDespacho,
  };
}

async function processMarcaSafe(
  marcaJson: any,
  processoId: string
): Promise<string | null> {
  if (!marcaJson || Object.keys(marcaJson).length === 0) return null;

  try {
    const existing = await prisma.marca.findUnique({
      where: { processoId },
      include: { ncl: true, cfe: true },
    });

    const nomeValido = marcaJson.nome?.trim();
    const apresentacaoValida = marcaJson.apresentacao?.trim();
    const naturezaValida = marcaJson.natureza?.trim();
    const nclsFromJson = (marcaJson.NCL || []) as Array<{
      codigo: string;
      especificacao?: string;
      status?: string;
      edicao?: string;
    }>;
    const cfesFromJson = (marcaJson.CFE || []) as Array<{
      codigo: string;
      edicao?: string;
    }>;

    const marca = await retry(() =>
      prisma.marca.upsert({
        where: { processoId },
        update: {
          nome: nomeValido || existing?.nome,
          apresentacao: apresentacaoValida || existing?.apresentacao,
          natureza: naturezaValida || existing?.natureza,
          ncl: existing && existing.ncl.length === 0 && nclsFromJson.length > 0
            ? {
                createMany: {
                  data: nclsFromJson.map((ncl) => ({
                    codigo: ncl.codigo,
                    especificacao: ncl.especificacao,
                    status: ncl.status,
                    edicao: ncl.edicao,
                  })),
                },
              }
            : undefined,
          cfe: existing && existing.cfe.length === 0 && cfesFromJson.length > 0
            ? {
                createMany: {
                  data: cfesFromJson.map((cfe) => ({
                    codigo: cfe.codigo,
                    edicao: cfe.edicao,
                  })),
                },
              }
            : undefined,
        },
        create: {
          processo: { connect: { id: processoId } },
          nome: nomeValido,
          apresentacao: apresentacaoValida,
          natureza: naturezaValida,
          ncl: nclsFromJson.length > 0
            ? {
                createMany: {
                  data: nclsFromJson.map((ncl) => ({
                    codigo: ncl.codigo,
                    especificacao: ncl.especificacao,
                    status: ncl.status,
                    edicao: ncl.edicao,
                  })),
                },
              }
            : undefined,
          cfe: cfesFromJson.length > 0
            ? {
                createMany: {
                  data: cfesFromJson.map((cfe) => ({
                    codigo: cfe.codigo,
                    edicao: cfe.edicao,
                  })),
                },
              }
            : undefined,
        },
        include: { ncl: true, cfe: true },
      })
    );

    return marca.id;
  } catch (error: any) {
    await logError(
      `Erro ao processar marca para processo ${processoId}: ${error.message} \n ${error.stack}`
    );
    throw error;
  }
}

function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

async function processProcesso(procJson: any, rpiId: string): Promise<void> {
  let processoRecord: {
    id: string;
    numero: string;
    dataDeposito: Date | null;
  } | null = null;

  try {
    const rpi = await prisma.rPI.findUnique({
      where: { id: rpiId },
      select: { dataPublicacao: true },
    });
    if (!rpi) throw new Error(`RPI com ID ${rpiId} não encontrada`);

    const dataDeposito = parseDate(procJson.data_deposito);

    const {
      oposicao,
      dataOposicao,
      sobrestamento,
      dataSobrestamento,
      exigencia,
      dataExigencia,
      dataMerito,
      diasCorridosMerito,
      dataConcessaoDespacho,
    } = verificarDespachos(
      procJson.despachos || [],
      rpi.dataPublicacao,
      dataDeposito
    );

    let procuradorId = null;
    let monitorado = false;

    if (procJson.procurador) {
      const procuradorUpper = procJson.procurador.toUpperCase();
      
      if (procuradorUpper.includes("REGISTRE-SE")) {
        monitorado = true;
        procuradorId = REGISTRE_SE_PROCURADOR_ID;
      } else {
        // Só faz upsert para outros procuradores
        const procurador = await upsertProcurador(procJson.procurador);
        procuradorId = procurador.id;
      }
    }

    const existingProcesso = await prisma.processo.findUnique({
      where: { numero: procJson.numero },
    });

    let dataConcessao = parseDate(procJson.data_concessao);
    let dataVigencia = parseDate(procJson.data_vigencia);
    if (!dataConcessao && dataConcessaoDespacho) {
      dataConcessao = dataConcessaoDespacho;
      dataVigencia = dataConcessaoDespacho;
    }

    let dataMeritoFinal = dataMerito || dataConcessao || null;
    let diasMeritoFinal = diasCorridosMerito;
    if (dataMeritoFinal && dataDeposito) {
      const diff = dataMeritoFinal.getTime() - dataDeposito.getTime();
      diasMeritoFinal = Math.ceil(diff / (1000 * 60 * 60 * 24));
    }

    const inicioVigencia = dataConcessao || dataVigencia;
    let prorrogacaoOrdinaria = null;
    let prorrogacaoExtraordinaria = null;
    if (inicioVigencia) {
      const prorrogacoes = calcularProrrogacoes(inicioVigencia);
      prorrogacaoOrdinaria = prorrogacoes.ordinaria;
      prorrogacaoExtraordinaria = prorrogacoes.extraordinaria;
    }

    const createData = {
      numero: procJson.numero,
      dataDeposito,
      dataConcessao,
      dataVigencia,
      inicioVigencia,
      prorrogacaoOrdinaria,
      prorrogacaoExtraordinaria,
      apostila: procJson.apostila,
      monitorado,
      dataMerito: dataMeritoFinal,
      diasCorridosMerito: diasMeritoFinal,
      dataMeritoEstimada: null,
      diasAteMeritoEstimada: null,
      oposicao,
      dataOposicao,
      sobrestamento,
      dataSobrestamento,
      exigencia,
      dataExigencia,
      procurador: procuradorId ? { connect: { id: procuradorId } } : undefined,
    };

    if (existingProcesso) {
      processoRecord = await retry(() =>
        prisma.processo.update({
          where: { numero: procJson.numero },
          data: {
            dataDeposito:
              existingProcesso.dataDeposito || createData.dataDeposito,
            dataConcessao:
              existingProcesso.dataConcessao || createData.dataConcessao,
            dataVigencia:
              existingProcesso.dataVigencia || createData.dataVigencia,
            inicioVigencia:
              existingProcesso.inicioVigencia || createData.inicioVigencia,
            prorrogacaoOrdinaria:
              existingProcesso.prorrogacaoOrdinaria ||
              createData.prorrogacaoOrdinaria,
            prorrogacaoExtraordinaria:
              existingProcesso.prorrogacaoExtraordinaria ||
              createData.prorrogacaoExtraordinaria,
            apostila: existingProcesso.apostila || createData.apostila,
            monitorado: existingProcesso.monitorado ?? createData.monitorado,
            dataMerito: existingProcesso.dataMerito || createData.dataMerito,
            diasCorridosMerito:
              existingProcesso.diasCorridosMerito ||
              createData.diasCorridosMerito,
            dataMeritoEstimada: existingProcesso.dataMeritoEstimada,
            diasAteMeritoEstimada: existingProcesso.diasAteMeritoEstimada,
            oposicao: existingProcesso.oposicao || createData.oposicao,
            dataOposicao:
              existingProcesso.dataOposicao || createData.dataOposicao,
            sobrestamento:
              existingProcesso.sobrestamento || createData.sobrestamento,
            dataSobrestamento:
              existingProcesso.dataSobrestamento ||
              createData.dataSobrestamento,
            exigencia: existingProcesso.exigencia || createData.exigencia,
            dataExigencia:
              existingProcesso.dataExigencia || createData.dataExigencia,
            procurador:
              procJson.procurador && procJson.procurador.trim() !== ""
                ? createData.procurador
                : existingProcesso.procuradorId
                ? { connect: { id: existingProcesso.procuradorId } }
                : undefined,
          },
        })
      );
    } else {
      processoRecord = await retry(() =>
        prisma.processo.create({
          data: createData,
        })
      );
    }

    if (!processoRecord)
      throw new Error(`Falha ao criar/atualizar processo ${procJson.numero}`);

    await Promise.all([
      Array.isArray(procJson.titulares)
        ? processTitulares(procJson.titulares, processoRecord.id, rpiId, monitorado)
        : null,
      Array.isArray(procJson.despachos)
        ? processDespachos(
            procJson.despachos, 
            processoRecord.id, 
            rpiId, 
            processoRecord.numero,
            monitorado,
            existingProcesso?.clienteId || null
          )
        : null,
      Array.isArray(procJson.sobrestadores)
        ? processSobrestadores(procJson.sobrestadores, processoRecord.id)
        : null,
      (async () => {
        if (procJson.marca) {
          const marcaData = {
            ...procJson.marca,
            NCL: procJson.NCL || [],
            CFE: procJson.CFE || [],
          };
          const marcaId = await processMarcaSafe(marcaData, processoRecord.id);
          if (marcaId) {
            await retry(() =>
              prisma.processo.update({
                where: { id: processoRecord?.id },
                data: { marcaId },
              })
            );
          }
        }
      })(),
    ]);
  } catch (error: any) {
    const errorMessage = `Erro no processamento do processo ${procJson.numero}: ${error.message}`;
    await logError(errorMessage);
    throw error;
  }
}

async function processProcessosBatch(
  processos: any[],
  rpiId: string
): Promise<void> {
  const batchSize = 100;
  const chunks = chunkArray(processos, batchSize);
  const totalChunks = chunks.length;
  let processed = 0;

  for (const chunk of chunks) {
    await Promise.all(chunk.map((proc) => processProcesso(proc, rpiId)));
    processed++;
    if (processed % Math.max(1, Math.floor(totalChunks * 0.05)) === 0) {
      const progress = Math.round((processed / totalChunks) * 100);
      console.log(
        `Progresso: ${progress}% (${processed}/${totalChunks} lotes)`
      );
    }
  }
}

async function processRpi(jsonData: any): Promise<void> {
  const rpiNumero = Number(jsonData.numero);
  const rpiRecord = await retry(() =>
    prisma.rPI.upsert({
      where: { numero: rpiNumero },
      update: {},
      create: {
        numero: rpiNumero,
        dataPublicacao: parseDate(jsonData.data)!,
        pdfProcessado: false,
        xmlProcessado: false,
      },
    })
  );

  if (Array.isArray(jsonData.processos)) {
    await processProcessosBatch(jsonData.processos, rpiRecord.id);
  }
}

async function processJsonFile(filePath: string): Promise<void> {
  try {
    const buffer = await readFile(filePath);
    const jsonData = JSON.parse(buffer.toString());
    await processRpi(jsonData);
    console.log(`Arquivo ${filePath} processado com sucesso.`);
    const destPath = path.join(PROCESSED_DIR, path.basename(filePath));
    await rename(filePath, destPath);
  } catch (error: any) {
    const errorDetails =
      error instanceof Error
        ? `\nMensagem: ${error.message}\nStack: ${error.stack}`
        : `\nErro: ${JSON.stringify(error)}`;
    const errorMessage = `Erro ao processar arquivo ${filePath}:${errorDetails}`;
    console.error(errorMessage);
    await logError(errorMessage);
  }
}

// ✅ NOVO: Função para gerar relatório da REGISTRE-SE
async function gerarRelatorioRegistreSe(): Promise<void> {
  if (relatorioRegistreSe.items.length === 0) {
    console.log("📄 Nenhum processo da REGISTRE-SE encontrado para relatório");
    return;
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
  const reportPath = path.join(__dirname, `../../output/relatorio_registre_se_${timestamp}.md`);
  
  let relatorioContent = `# Relatório de Despachos REGISTRE-SE\n\n`;
  relatorioContent += `**Data de Geração:** ${new Date().toLocaleString('pt-BR')}\n\n`;
  relatorioContent += `## Estatísticas Gerais\n\n`;
  relatorioContent += `- **Total de Despachos Processados:** ${relatorioRegistreSe.items.length}\n`;
  relatorioContent += `- **Despachos Criados:** ${relatorioRegistreSe.despachosCriados}\n`;
  relatorioContent += `- **Despachos Atualizados:** ${relatorioRegistreSe.despachosAtualizados}\n`;
  relatorioContent += `- **Protocolos Processados:** ${relatorioRegistreSe.protocolosProcessados}\n`;
  relatorioContent += `- **Despachos Elegíveis Registrados:** ${relatorioRegistreSe.despachosElegiveisRegistrados}\n\n`;
  relatorioContent += `## Detalhes dos Processos\n\n`;

  // Lista detalhada de processos e despachos
  for (const item of relatorioRegistreSe.items) {
    relatorioContent += `processo: ${item.processo}, despacho: ${item.despacho}\n\n`;
  }

  relatorioContent += `## Resumo por Tipo de Despacho\n\n`;

  // Ordenar contadores por quantidade (decrescente)
  const contadoresOrdenados = Array.from(relatorioRegistreSe.contadores.entries())
    .sort((a, b) => b[1] - a[1]);

  for (const [tipoDespacho, quantidade] of contadoresOrdenados) {
    relatorioContent += `**${tipoDespacho}:** ${quantidade}\n\n`;
  }

  // ✅ Seção de análise percentual
  relatorioContent += `## Análise Percentual\n\n`;
  
  const totalDespachos = relatorioRegistreSe.items.length;
  if (totalDespachos > 0) {
    const pctCriados = Math.round((relatorioRegistreSe.despachosCriados / totalDespachos) * 100);
    const pctAtualizados = Math.round((relatorioRegistreSe.despachosAtualizados / totalDespachos) * 100);
    
    relatorioContent += `- **Novos Despachos:** ${pctCriados}% (${relatorioRegistreSe.despachosCriados}/${totalDespachos})\n`;
    relatorioContent += `- **Despachos Atualizados:** ${pctAtualizados}% (${relatorioRegistreSe.despachosAtualizados}/${totalDespachos})\n`;
  }
  
  if (relatorioRegistreSe.despachosElegiveisRegistrados > 0) {
    relatorioContent += `- **Taxa de Elegibilidade:** ${relatorioRegistreSe.despachosElegiveisRegistrados} despachos elegíveis para comunicação\n`;
  }
  
  relatorioContent += `\n---\n\n*Relatório gerado automaticamente pelo sistema de processamento de RPIs*\n`;

  try {
    await fs.promises.writeFile(reportPath, relatorioContent, 'utf8');
    console.log(`📄 Relatório REGISTRE-SE gerado: ${reportPath}`);
    console.log(`📊 Resumo: ${relatorioRegistreSe.despachosCriados} criados, ${relatorioRegistreSe.despachosAtualizados} atualizados, ${relatorioRegistreSe.despachosElegiveisRegistrados} elegíveis`);
  } catch (error: any) {
    await logError(`Erro ao gerar relatório REGISTRE-SE: ${error.message}`);
    console.error(`Erro ao gerar relatório: ${error.message}`);
  }
}

async function main() {
  const directory = path.join(__dirname, "../../uploads/rpi/json");
  const files = await readdir(directory);
  const jsonFiles = files.filter((f) => f.endsWith(".json"));
  const totalFiles = jsonFiles.length;

  console.log(`Iniciando processamento de ${totalFiles} arquivos JSON`);

  let filesProcessed = 0;
  for (const file of jsonFiles) {
    const filePath = path.join(directory, file);
    await processJsonFile(filePath);
    filesProcessed++;
    console.log(`Arquivo ${filesProcessed}/${totalFiles} processado: ${file}`);
  }

  console.log("Processamento concluído!");
  
  // ✅ NOVO: Gerar relatório da REGISTRE-SE
  await gerarRelatorioRegistreSe();
}

main()
  .catch(async (e) => {
    const errorMessage = `Erro crítico: ${e.message}`;
    console.error(errorMessage);
    await logError(errorMessage);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
