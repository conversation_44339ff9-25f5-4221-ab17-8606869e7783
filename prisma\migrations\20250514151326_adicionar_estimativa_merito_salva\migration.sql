-- CreateTable
CREATE TABLE "EstimativaMeritoSalva" (
    "id" TEXT NOT NULL,
    "dataCalculo" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "mediaSemIntervencoes" DOUBLE PRECISION,
    "medianaSemIntervencoes" DOUBLE PRECISION,
    "minSemIntervencoes" INTEGER,
    "maxSemIntervencoes" INTEGER,
    "nSemIntervencoes" INTEGER NOT NULL,
    "mediaComOposicao" DOUBLE PRECISION,
    "medianaComOposicao" DOUBLE PRECISION,
    "minComOposicao" INTEGER,
    "maxComOposicao" INTEGER,
    "nComOposicao" INTEGER NOT NULL,
    "mediaComSobrestamento" DOUBLE PRECISION,
    "medianaComSobrestamento" DOUBLE PRECISION,
    "minComSobrestamento" INTEGER,
    "maxComSobrestamento" INTEGER,
    "nComSobrestamento" INTEGER NOT NULL,
    "mediaComExigencia" DOUBLE PRECISION,
    "medianaComExigencia" DOUBLE PRECISION,
    "minComExigencia" INTEGER,
    "maxComExigencia" INTEGER,
    "nComExigencia" INTEGER NOT NULL,

    CONSTRAINT "EstimativaMeritoSalva_pkey" PRIMARY KEY ("id")
);
