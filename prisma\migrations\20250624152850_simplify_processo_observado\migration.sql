/*
  Warnings:

  - You are about to drop the `ClienteProcessoAcesso` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "ClienteProcessoAcesso" DROP CONSTRAINT "ClienteProcessoAcesso_clienteId_fkey";

-- DropFore<PERSON>Key
ALTER TABLE "ClienteProcessoAcesso" DROP CONSTRAINT "ClienteProcessoAcesso_concessorId_fkey";

-- DropForeignKey
ALTER TABLE "ClienteProcessoAcesso" DROP CONSTRAINT "ClienteProcessoAcesso_processoId_fkey";

-- DropTable
DROP TABLE "ClienteProcessoAcesso";

-- DropEnum
DROP TYPE "TipoAcessoProcesso";

-- CreateTable
CREATE TABLE "ProcessoObservado" (
    "id" TEXT NOT NULL,
    "clienteId" INTEGER NOT NULL,
    "processoId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ProcessoObservado_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ProcessoObservado_clienteId_idx" ON "ProcessoObservado"("clienteId");

-- CreateIndex
CREATE INDEX "ProcessoObservado_processoId_idx" ON "ProcessoObservado"("processoId");

-- CreateIndex
CREATE UNIQUE INDEX "ProcessoObservado_clienteId_processoId_key" ON "ProcessoObservado"("clienteId", "processoId");

-- AddForeignKey
ALTER TABLE "ProcessoObservado" ADD CONSTRAINT "ProcessoObservado_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProcessoObservado" ADD CONSTRAINT "ProcessoObservado_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE CASCADE ON UPDATE CASCADE;
