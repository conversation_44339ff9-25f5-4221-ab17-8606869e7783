import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';

const prisma = new PrismaClient();

// ID do procurador específico para análise
const PROCURADOR_ID = '65b3c0c0-aa3b-4d89-85fb-2a144e538800';

interface ProcessoAnalise {
  numero: string;
  id: string;
  marca?: string;
  apresentacao?: string;
  temProtocolo: boolean;
  protocoloStatus?: string;
  protocoloId?: string;
  elementoNominativo?: string;
  dataProtocolo?: Date;
}

interface RelatorioAnalise {
  procuradorId: string;
  totalProcessos: number;
  processosComProtocolo: number;
  processosSemProtocolo: number;
  percentualComProtocolo: number;
  dataAnalise: string;
  detalhes: ProcessoAnalise[];
  resumoStatusProtocolos: Record<string, number>;
}

/**
 * Verifica se existe arquivo PDF do protocolo no sistema de arquivos
 */
async function verificarArquivoProtocolo(numeroProcesso: string): Promise<boolean> {
  try {
    const protocolosDir = path.join(process.cwd(), 'uploads', 'protocolos');
    // eslint-disable-next-line no-console
    console.log(protocolosDir);
    if (!fs.existsSync(protocolosDir)) {
      return false;
    }
    
    const arquivos = fs.readdirSync(protocolosDir);
    const arquivoEncontrado = arquivos.find((arquivo) => {
      return arquivo.includes(numeroProcesso) && path.extname(arquivo).toLowerCase() === '.pdf';
    });
    
    return Boolean(arquivoEncontrado);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error(`Erro ao verificar arquivo do processo ${numeroProcesso}:`, error);
    return false;
  }
}

/**
 * Analisa todos os processos do procurador específico
 */
async function analisarProtocolosProcurador(): Promise<RelatorioAnalise> {
  // eslint-disable-next-line no-console
  console.log(`🔍 Iniciando análise de protocolos para o procurador: ${PROCURADOR_ID}`);
  // eslint-disable-next-line no-console
  console.log(`📅 Data da análise: ${new Date().toLocaleString('pt-BR')}\n`);

  // Buscar todos os processos do procurador
  const processos = await prisma.processo.findMany({
    where: {
      procuradorId: PROCURADOR_ID
    },
    include: {
      marca: {
        select: {
          nome: true,
          apresentacao: true
        }
      },
      processamentosProtocolo: {
        select: {
          id: true,
          status: true,
          elementoNominativo: true,
          criadoEm: true
        }
      }
    },
    orderBy: {
      numero: 'asc'
    }
  });

  // eslint-disable-next-line no-console
  console.log(`📊 Total de processos encontrados: ${processos.length}`);

  const detalhes: ProcessoAnalise[] = [];
  const resumoStatusProtocolos: { [status: string]: number } = {};
  let processosComProtocolo = 0;

  // Analisar cada processo
  for (let i = 0; i < processos.length; i++) {
    const processo = processos[i];
    const progresso = `[${i + 1}/${processos.length}]`;
    
    // eslint-disable-next-line no-console
    console.log(`${progresso} Analisando processo: ${processo.numero}`);

    // Verificar se tem protocolo no banco
    const temProtocoloBanco = processo.processamentosProtocolo.length > 0;
    let temArquivoFisico = false;
    
    // Se tem no banco, verificar arquivo físico também
    if (temProtocoloBanco) {
      temArquivoFisico = await verificarArquivoProtocolo(processo.numero);
    }

    const temProtocolo = temProtocoloBanco && temArquivoFisico;
    
    if (temProtocolo) {
      processosComProtocolo++;
    }

    // Obter informações do protocolo se existir
    const protocoloInfo = processo.processamentosProtocolo[0];
    const protocoloStatus = protocoloInfo?.status;
    
    if (protocoloStatus) {
      resumoStatusProtocolos[protocoloStatus] = (resumoStatusProtocolos[protocoloStatus] || 0) + 1;
    }

    const analise: ProcessoAnalise = {
      numero: processo.numero,
      id: processo.id,
      marca: processo.marca?.nome || undefined,
      apresentacao: processo.marca?.apresentacao || undefined,
      temProtocolo,
      protocoloStatus,
      protocoloId: protocoloInfo?.id,
      elementoNominativo: protocoloInfo?.elementoNominativo || undefined,
      dataProtocolo: protocoloInfo?.criadoEm
    };

    detalhes.push(analise);

    // Log detalhado
    const statusIcon = temProtocolo ? '✅' : '❌';
    const statusTexto = temProtocolo ? 'COM PROTOCOLO' : 'SEM PROTOCOLO';
    const marcaTexto = processo.marca?.nome ? ` (${processo.marca.nome})` : '';
    
    // eslint-disable-next-line no-console
    console.log(`   ${statusIcon} ${statusTexto}${marcaTexto}`);
    
    if (temProtocoloBanco && !temArquivoFisico) {
      // eslint-disable-next-line no-console
      console.log(`   ⚠️  ATENÇÃO: Protocolo existe no banco mas arquivo PDF não foi encontrado`);
    }
  }

  const processosSemProtocolo = processos.length - processosComProtocolo;
  const percentualComProtocolo = processos.length > 0 ? 
    Math.round((processosComProtocolo / processos.length) * 100) : 0;

  const relatorio: RelatorioAnalise = {
    procuradorId: PROCURADOR_ID,
    totalProcessos: processos.length,
    processosComProtocolo,
    processosSemProtocolo,
    percentualComProtocolo,
    dataAnalise: new Date().toISOString(),
    detalhes,
    resumoStatusProtocolos
  };

  return relatorio;
}

/**
 * Gera relatório em formato texto
 */
function gerarRelatorioTexto(relatorio: RelatorioAnalise): string {
  const linhas: string[] = [];
  
  linhas.push('='.repeat(80));
  linhas.push('📋 RELATÓRIO DE ANÁLISE DE PROTOCOLOS POR PROCURADOR');
  linhas.push('='.repeat(80));
  linhas.push('');
  linhas.push(`🏛️ Procurador ID: ${relatorio.procuradorId}`);
  linhas.push(`📅 Data da análise: ${new Date(relatorio.dataAnalise).toLocaleString('pt-BR')}`);
  linhas.push('');
  
  // Resumo geral
  linhas.push('📊 RESUMO GERAL:');
  linhas.push('-'.repeat(40));
  linhas.push(`Total de processos: ${relatorio.totalProcessos}`);
  linhas.push(`Processos COM protocolo: ${relatorio.processosComProtocolo} (${relatorio.percentualComProtocolo}%)`);
  linhas.push(`Processos SEM protocolo: ${relatorio.processosSemProtocolo} (${100 - relatorio.percentualComProtocolo}%)`);
  linhas.push('');

  // Resumo por status
  if (Object.keys(relatorio.resumoStatusProtocolos).length > 0) {
    linhas.push('📈 RESUMO POR STATUS DOS PROTOCOLOS:');
    linhas.push('-'.repeat(40));
    for (const [status, count] of Object.entries(relatorio.resumoStatusProtocolos)) {
      linhas.push(`${status}: ${count}`);
    }
    linhas.push('');
  }

  // Lista processos SEM protocolo
  const processosSemProtocolo = relatorio.detalhes.filter(p => !p.temProtocolo);
  if (processosSemProtocolo.length > 0) {
    linhas.push('❌ PROCESSOS SEM PROTOCOLO:');
    linhas.push('-'.repeat(40));
    processosSemProtocolo.forEach(processo => {
      const marcaInfo = processo.marca ? ` - ${processo.marca}` : '';
      linhas.push(`• ${processo.numero}${marcaInfo}`);
    });
    linhas.push('');
  }

  // Lista processos COM protocolo
  const processosComProtocolo = relatorio.detalhes.filter(p => p.temProtocolo);
  if (processosComProtocolo.length > 0) {
    linhas.push('✅ PROCESSOS COM PROTOCOLO:');
    linhas.push('-'.repeat(40));
    processosComProtocolo.forEach(processo => {
      const marcaInfo = processo.marca ? ` - ${processo.marca}` : '';
      const statusInfo = processo.protocoloStatus ? ` [${processo.protocoloStatus}]` : '';
      const dataInfo = processo.dataProtocolo ? 
        ` (${processo.dataProtocolo.toLocaleDateString('pt-BR')})` : '';
      
      linhas.push(`• ${processo.numero}${marcaInfo}${statusInfo}${dataInfo}`);
    });
    linhas.push('');
  }

  linhas.push('='.repeat(80));
  linhas.push('Relatório gerado automaticamente pelo sistema');
  linhas.push('='.repeat(80));

  return linhas.join('\n');
}

/**
 * Salva relatórios em arquivos
 */
async function salvarRelatorios(relatorio: RelatorioAnalise): Promise<void> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const procuradorShort = PROCURADOR_ID.split('-')[0]; // Primeiros 8 caracteres
  
  // Salvar JSON detalhado
  const nomeArquivoJson = `relatorio-protocolos-procurador-${procuradorShort}-${timestamp}.json`;
  const caminhoJson = path.join(process.cwd(), nomeArquivoJson);
  
  fs.writeFileSync(caminhoJson, JSON.stringify(relatorio, null, 2), 'utf-8');
  // eslint-disable-next-line no-console
  console.log(`💾 Relatório JSON salvo: ${nomeArquivoJson}`);

  // Salvar relatório em texto
  const relatorioTexto = gerarRelatorioTexto(relatorio);
  const nomeArquivoTxt = `relatorio-protocolos-procurador-${procuradorShort}-${timestamp}.txt`;
  const caminhoTxt = path.join(process.cwd(), nomeArquivoTxt);
  
  fs.writeFileSync(caminhoTxt, relatorioTexto, 'utf-8');
  // eslint-disable-next-line no-console
  console.log(`📄 Relatório TXT salvo: ${nomeArquivoTxt}`);

  // Salvar CSV para análise em planilhas
  const linhasCSV = ['Numero Processo,Marca,Apresentacao,Tem Protocolo,Status Protocolo,Data Protocolo'];
  
  relatorio.detalhes.forEach(processo => {
    const linha = [
      processo.numero,
      processo.marca || '',
      processo.apresentacao || '',
      processo.temProtocolo ? 'SIM' : 'NÃO',
      processo.protocoloStatus || '',
      processo.dataProtocolo ? processo.dataProtocolo.toLocaleDateString('pt-BR') : ''
    ].map(field => `"${field}"`).join(',');
    
    linhasCSV.push(linha);
  });

  const nomeArquivoCSV = `relatorio-protocolos-procurador-${procuradorShort}-${timestamp}.csv`;
  const caminhoCSV = path.join(process.cwd(), nomeArquivoCSV);
  
  fs.writeFileSync(caminhoCSV, linhasCSV.join('\n'), 'utf-8');
  // eslint-disable-next-line no-console
  console.log(`📊 Relatório CSV salvo: ${nomeArquivoCSV}`);
}

/**
 * Função principal
 */
async function main(): Promise<void> {
  try {
    // eslint-disable-next-line no-console
    console.log('🚀 Iniciando análise de protocolos por procurador...\n');
    
    const relatorio = await analisarProtocolosProcurador();
    
    // eslint-disable-next-line no-console
    console.log('\n' + '='.repeat(60));
    // eslint-disable-next-line no-console
    console.log('📊 RESULTADO DA ANÁLISE:');
    // eslint-disable-next-line no-console
    console.log('='.repeat(60));
    // eslint-disable-next-line no-console
    console.log(`Total de processos: ${relatorio.totalProcessos}`);
    // eslint-disable-next-line no-console
    console.log(`Processos COM protocolo: ${relatorio.processosComProtocolo} (${relatorio.percentualComProtocolo}%)`);
    // eslint-disable-next-line no-console
    console.log(`Processos SEM protocolo: ${relatorio.processosSemProtocolo} (${100 - relatorio.percentualComProtocolo}%)`);
    
    if (Object.keys(relatorio.resumoStatusProtocolos).length > 0) {
      // eslint-disable-next-line no-console
      console.log('\nStatus dos protocolos:');
      for (const [status, count] of Object.entries(relatorio.resumoStatusProtocolos)) {
        // eslint-disable-next-line no-console
        console.log(`  ${status}: ${count}`);
      }
    }

    await salvarRelatorios(relatorio);
    
    // eslint-disable-next-line no-console
    console.log('\n✅ Análise concluída com sucesso!');
    
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('❌ Erro durante a análise:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

export { analisarProtocolosProcurador, RelatorioAnalise }; 