-- CreateEnum
CREATE TYPE "TipoAcessoProcesso" AS ENUM ('VISUAL<PERSON>Z<PERSON>A<PERSON>', 'DOWNLOAD', 'ACOM<PERSON><PERSON><PERSON>MENTO', 'COMPLETO');

-- CreateTable
CREATE TABLE "ClienteProcessoAcesso" (
    "id" TEXT NOT NULL,
    "clienteId" INTEGER NOT NULL,
    "processoId" TEXT NOT NULL,
    "tipoAcesso" "TipoAcessoProcesso" NOT NULL DEFAULT 'VISUALIZACAO',
    "dataConcessao" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dataRevogacao" TIMESTAMP(3),
    "ativo" BOOLEAN NOT NULL DEFAULT true,
    "observacao" TEXT,
    "concessorId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ClienteProcessoAcesso_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ClienteProcessoAcesso_clienteId_idx" ON "ClienteProcessoAcesso"("clienteId");

-- CreateIndex
CREATE INDEX "ClienteProcessoAcesso_processoId_idx" ON "ClienteProcessoAcesso"("processoId");

-- CreateIndex
CREATE INDEX "ClienteProcessoAcesso_ativo_idx" ON "ClienteProcessoAcesso"("ativo");

-- CreateIndex
CREATE INDEX "ClienteProcessoAcesso_dataConcessao_idx" ON "ClienteProcessoAcesso"("dataConcessao");

-- CreateIndex
CREATE UNIQUE INDEX "ClienteProcessoAcesso_clienteId_processoId_key" ON "ClienteProcessoAcesso"("clienteId", "processoId");

-- AddForeignKey
ALTER TABLE "ClienteProcessoAcesso" ADD CONSTRAINT "ClienteProcessoAcesso_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClienteProcessoAcesso" ADD CONSTRAINT "ClienteProcessoAcesso_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClienteProcessoAcesso" ADD CONSTRAINT "ClienteProcessoAcesso_concessorId_fkey" FOREIGN KEY ("concessorId") REFERENCES "Cliente"("id") ON DELETE SET NULL ON UPDATE CASCADE;
