import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { pipeline } from 'stream';

const streamPipeline = promisify(pipeline);

export interface DownloadResult {
  numeroProcesso: string;
  success: boolean;
  filePath?: string;
  error?: string;
  fileSize?: number;
}

/**
 * Baixa uma imagem de logo de processo e salva localmente
 */
export async function downloadLogoProcesso(
  numeroProcesso: string,
  baseUrl: string = 'https://registre-sys.registrese.app.br/images/logos/processos',
  outputDir: string = path.join(__dirname, '..', '..', '..', 'public', 'logos', 'processos')
): Promise<DownloadResult> {
  
  const imageUrl = `${baseUrl}/${numeroProcesso}.jpg`;
  const fileName = `${numeroProcesso}.jpg`;
  const filePath = path.join(outputDir, fileName);

  try {
    // Garantir que o diretório existe
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
      console.log(`📁 Diretório criado: ${outputDir}`);
    }

    // Fazer requisição para a imagem
    const response = await axios({
      method: 'GET',
      url: imageUrl,
      responseType: 'stream',
      timeout: 30000, // 30 segundos de timeout
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    // Verificar se o response é válido
    if (response.status !== 200) {
      return {
        numeroProcesso,
        success: false,
        error: `Status HTTP ${response.status} ao baixar ${imageUrl}`
      };
    }

    // Salvar o arquivo
    await streamPipeline(response.data, fs.createWriteStream(filePath));
    
    // Obter informações do arquivo salvo
    const stats = fs.statSync(filePath);
    const fileSize = stats.size;

    // Verificar se o arquivo não está vazio
    if (fileSize === 0) {
      fs.unlinkSync(filePath); // Remove arquivo vazio
      return {
        numeroProcesso,
        success: false,
        error: 'Arquivo baixado está vazio'
      };
    }

    return {
      numeroProcesso,
      success: true,
      filePath,
      fileSize
    };

  } catch (error: any) {
    // Se criou o arquivo mas deu erro, remove ele
    if (fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
      } catch (unlinkError) {
        console.warn(`Aviso: Não foi possível remover arquivo com erro: ${filePath}`);
      }
    }

    let errorMessage = 'Erro desconhecido';
    if (error.code === 'ENOTFOUND') {
      errorMessage = 'URL não encontrada (DNS)';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'Conexão recusada';
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = 'Timeout na requisição';
    } else if (error.response?.status === 404) {
      errorMessage = 'Imagem não encontrada (404)';
    } else if (error.message) {
      errorMessage = error.message;
    }

    return {
      numeroProcesso,
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Verifica se um arquivo de logo já existe localmente
 */
export function logoExisteLocalmente(
  numeroProcesso: string,
  outputDir: string = path.join(__dirname, '..', '..', '..', 'public', 'logos', 'processos')
): boolean {
  const filePath = path.join(outputDir, `${numeroProcesso}.jpg`);
  return fs.existsSync(filePath) && fs.statSync(filePath).size > 0;
} 