import { TipoAcesso } from '@prisma/client';
import {
  getProcessosComAcesso,
  clienteTemAcesso,
  concederAcesso,
  buscarProcessosComPermissao,
  relatorioPermissoesCliente
} from '../utils/permissoes';

/**
 * EXEMPLO 1: Buscar todos os processos que o cliente coringa (13) tem acesso
 */
export async function exemploClienteCoringa() {
  console.log('🔍 Buscando processos do cliente coringa...');
  
  const clienteCoringaId = 2539; // ID encontrado no script anterior
  const processos = await getProcessosComAcesso(clienteCoringaId);
  
  console.log(`Cliente coringa tem acesso a ${processos.length} processos`);
  
  // Mostrar apenas alguns exemplos
  processos.slice(0, 3).forEach(processo => {
    console.log(`  • ${processo.numero} - ${processo.marca?.nome || 'Sem marca'}`);
    console.log(`    Tipo de acesso: ${processo.acessosCliente[0]?.tipoAcesso}`);
  });
}

/**
 * EXEMPLO 2: Verificar se um cliente específico tem acesso a um processo
 */
export async function exemploVerificarAcesso() {
  console.log('🔐 Verificando acesso específico...');
  
  const clienteId = 1; // Cliente exemplo
  const processoId = 'algum-processo-id';
  
  const temAcesso = await clienteTemAcesso(clienteId, processoId);
  const temAcessoProprietario = await clienteTemAcesso(
    clienteId, 
    processoId, 
    TipoAcesso.PROPRIETARIO
  );
  
  console.log(`Cliente ${clienteId} tem acesso: ${temAcesso}`);
  console.log(`Cliente ${clienteId} é proprietário: ${temAcessoProprietario}`);
}

/**
 * EXEMPLO 3: Conceder acesso temporário a um cliente
 */
export async function exemploConcederAcesso() {
  console.log('👥 Concedendo acesso temporário...');
  
  const clienteId = 1;
  const processoId = 'processo-exemplo';
  const dataExpiracao = new Date();
  dataExpiracao.setDate(dataExpiracao.getDate() + 30); // 30 dias
  
  const novoAcesso = await concederAcesso(
    clienteId,
    processoId,
    TipoAcesso.TEMPORARIO,
    'Acesso temporário para auditoria',
    dataExpiracao,
    'admin-user-id'
  );
  
  console.log('Acesso concedido:', novoAcesso);
}

/**
 * EXEMPLO 4: Buscar processos com filtros para um cliente
 */
export async function exemploBuscarComFiltros() {
  console.log('🔍 Buscando processos com filtros...');
  
  const clienteId = 2539; // Cliente coringa
  
  const resultado = await buscarProcessosComPermissao(clienteId, {
    marca: 'MARCA EXEMPLO',
    limite: 10,
    offset: 0,
    tiposAcesso: [TipoAcesso.CORINGA, TipoAcesso.PROPRIETARIO]
  });
  
  console.log(`Encontrados ${resultado.total} processos`);
  console.log(`Mostrando ${resultado.processos.length} processos`);
  console.log(`Tem mais: ${resultado.temMais}`);
}

/**
 * EXEMPLO 5: Relatório de permissões de um cliente
 */
export async function exemploRelatorioCliente() {
  console.log('📊 Gerando relatório de permissões...');
  
  const clienteId = 2539; // Cliente coringa
  const relatorio = await relatorioPermissoesCliente(clienteId);
  
  console.log('Resumo de acessos:');
  Object.entries(relatorio.resumo).forEach(([tipo, quantidade]) => {
    console.log(`  • ${tipo}: ${quantidade} processos`);
  });
  
  console.log(`Total de acessos detalhados: ${relatorio.acessos.length}`);
}

/**
 * EXEMPLO 6: Uso em uma API Route (Next.js)
 */
export function exemploAPIRoute() {
  return `
// pages/api/processos/[clienteId].ts
import { getProcessosComAcesso } from '../../../utils/permissoes';

export default async function handler(req, res) {
  const { clienteId } = req.query;
  
  try {
    const processos = await getProcessosComAcesso(Number(clienteId));
    
    res.json({
      success: true,
      data: processos,
      total: processos.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao buscar processos'
    });
  }
}
`;
}

/**
 * EXEMPLO 7: Middleware de autorização
 */
export function exemploMiddleware() {
  return `
// middleware/auth.ts
import { clienteTemAcesso } from '../utils/permissoes';

export async function verificarAcessoProcesso(
  clienteId: number,
  processoId: string,
  tipoMinimo = TipoAcesso.VISUALIZACAO
) {
  const temAcesso = await clienteTemAcesso(clienteId, processoId, tipoMinimo);
  
  if (!temAcesso) {
    throw new Error('Acesso negado ao processo');
  }
  
  return true;
}

// Uso em um endpoint
app.get('/processo/:id', async (req, res) => {
  try {
    const clienteId = req.user.clienteId;
    const processoId = req.params.id;
    
    await verificarAcessoProcesso(clienteId, processoId);
    
    // Cliente tem acesso, buscar o processo...
    const processo = await prisma.processo.findUnique({
      where: { id: processoId }
    });
    
    res.json(processo);
  } catch (error) {
    res.status(403).json({ error: error.message });
  }
});
`;
}

/**
 * EXEMPLO 8: Hook React para usar no frontend
 */
export function exemploHookReact() {
  return `
// hooks/useProcessosCliente.ts
import { useState, useEffect } from 'react';

export function useProcessosCliente(clienteId: number) {
  const [processos, setProcessos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchProcessos() {
      try {
        setLoading(true);
        const response = await fetch(\`/api/processos/\${clienteId}\`);
        const data = await response.json();
        
        if (data.success) {
          setProcessos(data.data);
        } else {
          setError(data.error);
        }
      } catch (err) {
        setError('Erro ao carregar processos');
      } finally {
        setLoading(false);
      }
    }

    if (clienteId) {
      fetchProcessos();
    }
  }, [clienteId]);

  return { processos, loading, error };
}

// Uso no componente
function ProcessosList({ clienteId }) {
  const { processos, loading, error } = useProcessosCliente(clienteId);

  if (loading) return <div>Carregando...</div>;
  if (error) return <div>Erro: {error}</div>;

  return (
    <div>
      <h2>Seus Processos ({processos.length})</h2>
      {processos.map(processo => (
        <div key={processo.id}>
          <h3>{processo.numero}</h3>
          <p>Marca: {processo.marca?.nome}</p>
          <p>Acesso: {processo.acessosCliente[0]?.tipoAcesso}</p>
        </div>
      ))}
    </div>
  );
}
`;
}

// Executar exemplos
async function executarExemplos() {
  console.log('🎯 EXEMPLOS DE USO DO SISTEMA DE PERMISSÕES');
  console.log('═════════════════════════════════════════\n');
  
  try {
    await exemploClienteCoringa();
    console.log('\n' + '─'.repeat(50) + '\n');
    
    await exemploRelatorioCliente();
    console.log('\n' + '─'.repeat(50) + '\n');
    
    console.log('📝 Exemplo de API Route:');
    console.log(exemploAPIRoute());
    
    console.log('📝 Exemplo de Middleware:');
    console.log(exemploMiddleware());
    
    console.log('📝 Exemplo de Hook React:');
    console.log(exemploHookReact());
    
  } catch (error) {
    console.error('Erro nos exemplos:', error);
  }
}

// Descomente para executar
// executarExemplos(); 