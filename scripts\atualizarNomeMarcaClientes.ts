import { PrismaClient } from '@prisma/client';
import cliProgress from 'cli-progress';

const prisma = new PrismaClient();

async function atualizarNomeMarcaClientes() {
  try {
    console.log('🔍 Buscando clientes com processos para atualização de nomes de marca...\n');

    // Buscar todos os clientes que têm processos
    const clientesComProcessos = await prisma.cliente.findMany({
      where: {
        processos: {
          some: {} // Clientes que têm pelo menos um processo
        }
      },
      include: {
        processos: {
          include: {
            marca: true // Incluir dados da marca de cada processo
          }
        }
      },
      orderBy: {
        id: 'asc'
      }
    });

    console.log(`📊 Total de clientes com processos encontrados: ${clientesComProcessos.length}\n`);

    if (clientesComProcessos.length === 0) {
      console.log('✅ Nenhum cliente com processos encontrado!');
      return;
    }

    const progressBar = new cliProgress.SingleBar({
      format: "Processando clientes |{bar}| {percentage}% || {value}/{total}",
      barCompleteChar: "\u2588",
      barIncompleteChar: "\u2591",
      hideCursor: true,
    });

    progressBar.start(clientesComProcessos.length, 0);

    let atualizados = 0;
    let semMarcaValida = 0;
    let jaPossuemNome = 0;
    let erros = 0;

    for (const cliente of clientesComProcessos) {
      try {
        // Coletar todos os nomes de marca válidos dos processos do cliente
        const nomesMarcas: string[] = [];
        
        for (const processo of cliente.processos) {
          if (processo.marca && processo.marca.nome) {
            const nomeMarca = processo.marca.nome.trim();
            // Verificar se o nome não está vazio e se ainda não foi adicionado
            if (nomeMarca && !nomesMarcas.includes(nomeMarca)) {
              nomesMarcas.push(nomeMarca);
            }
          }
        }

        if (nomesMarcas.length > 0) {
          // Concatenar os nomes de marca com vírgula
          const novoNomeMarca = nomesMarcas.join(',');
          
          // Verificar se o cliente já possui esse nome de marca
          if (cliente.nomeDaMarca === novoNomeMarca) {
            jaPossuemNome++;
          } else {
            // Atualizar o nome da marca do cliente
            await prisma.cliente.update({
              where: { id: cliente.id },
              data: { nomeDaMarca: novoNomeMarca }
            });

            atualizados++;
            
            // Log detalhado para casos interessantes
            if (nomesMarcas.length > 1) {
              console.log(`\n✅ Cliente ID ${cliente.id}: ${nomesMarcas.length} marcas diferentes → "${novoNomeMarca}"`);
            } else if (cliente.id % 50 === 0) { // Log a cada 50 clientes para não poluir
              console.log(`\n✅ Cliente ID ${cliente.id}: "${novoNomeMarca}"`);
            }
          }
        } else {
          semMarcaValida++;
          if (cliente.id % 100 === 0) { // Log menos frequente para estes casos
            console.log(`\n⚠️ Cliente ID ${cliente.id}: Nenhuma marca válida encontrada em ${cliente.processos.length} processo(s)`);
          }
        }
      } catch (error: any) {
        erros++;
        console.error(`\n❌ Erro ao processar cliente ID ${cliente.id}: ${error.message}`);
      }

      progressBar.increment();
    }

    progressBar.stop();

    console.log(`\n📊 Resumo da atualização:`);
    console.log(`   ✅ Clientes atualizados com sucesso: ${atualizados}`);
    console.log(`   ✔️ Clientes que já possuíam o nome correto: ${jaPossuemNome}`);
    console.log(`   ⚠️ Clientes sem marca válida: ${semMarcaValida}`);
    console.log(`   ❌ Erros encontrados: ${erros}`);
    console.log(`   📱 Total processado: ${atualizados + jaPossuemNome + semMarcaValida + erros}`);

  } catch (error) {
    console.error('❌ Erro durante a atualização de nomes de marca:', error);
    throw error;
  }
}

async function listarEstatisticasMarcas() {
  try {
    console.log('📊 Analisando estatísticas de marcas dos clientes...\n');

    // Estatísticas gerais
    const totalClientes = await prisma.cliente.count();
    const clientesComNomeMarca = await prisma.cliente.count({
      where: {
        nomeDaMarca: {
          not: null,
        }
      }
    });

    console.log(`📈 Estatísticas Gerais:`);
    console.log(`   • Total de clientes: ${totalClientes}`);
    console.log(`   • Clientes com nome de marca: ${clientesComNomeMarca}`);
    console.log(`   • Clientes sem nome de marca: ${totalClientes - clientesComNomeMarca}`);
    console.log(`   • Percentual com nome de marca: ${((clientesComNomeMarca / totalClientes) * 100).toFixed(1)}%`);

    // Clientes com múltiplas marcas
    const clientesComMultiplasMarcas = await prisma.cliente.findMany({
      where: {
        nomeDaMarca: {
          contains: ','
        }
      },
      select: {
        id: true,
        nomeDaMarca: true,
        _count: {
          select: {
            processos: true
          }
        }
      }
    });

    console.log(`\n🔗 Clientes com múltiplas marcas: ${clientesComMultiplasMarcas.length}`);
    
    if (clientesComMultiplasMarcas.length > 0) {
      console.log(`\n📋 Exemplos de clientes com múltiplas marcas:`);
      clientesComMultiplasMarcas.slice(0, 10).forEach((cliente, index) => {
        const quantidadeMarcas = cliente.nomeDaMarca?.split(',').length || 0;
        console.log(`   ${index + 1}. Cliente ID ${cliente.id}: ${quantidadeMarcas} marcas, ${cliente._count.processos} processos`);
        console.log(`      Marcas: "${cliente.nomeDaMarca}"`);
      });
      
      if (clientesComMultiplasMarcas.length > 10) {
        console.log(`   ... e mais ${clientesComMultiplasMarcas.length - 10} clientes`);
      }
    }

  } catch (error) {
    console.error('❌ Erro ao gerar estatísticas:', error);
    throw error;
  }
}

async function executar() {
  try {
    const args = process.argv.slice(2);
    const shouldUpdate = args.includes('--atualizar') || args.includes('-u');
    const showStats = args.includes('--estatisticas') || args.includes('-s');

    if (showStats) {
      await listarEstatisticasMarcas();
    } else if (shouldUpdate) {
      await atualizarNomeMarcaClientes();
    } else {
      console.log('🔍 Script para atualização de nomes de marca dos clientes\n');
      
      // Mostrar estatísticas primeiro
      await listarEstatisticasMarcas();
      
      console.log('\n💡 Opções disponíveis:');
      console.log('   --atualizar ou -u    : Atualizar nomes de marca dos clientes');
      console.log('   --estatisticas ou -s : Mostrar apenas estatísticas');
      console.log('\n📝 Exemplos de uso:');
      console.log('   npm run atualizar-nomes-marca -- --atualizar');
      console.log('   npm run atualizar-nomes-marca -- --estatisticas');
    }
    
    console.log('\n✅ Execução concluída com sucesso!');
  } catch (error) {
    console.error('\n❌ Erro durante a execução:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executa o script apenas se for chamado diretamente
if (require.main === module) {
  executar();
}

export { atualizarNomeMarcaClientes, listarEstatisticasMarcas }; 