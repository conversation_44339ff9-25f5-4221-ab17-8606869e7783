import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';
import * as csv from 'fast-csv';

const prisma = new PrismaClient();

interface RelatorioIdentificadorTelefone {
  identificador: string;
  telefones: string;
}

async function gerarRelatorioIdentificadorTelefones(): Promise<void> {
  console.log('Iniciando a geração do relatório de identificadores e telefones...');

  try {
    const clientesComContatos = await prisma.cliente.findMany({
      where: {
        identificador: {
          not: null,
          notIn: ['0000000000'],
        },
      },
      select: {
        identificador: true,
        contatos: {
          select: {
            telefone: true,
          },
        },
      },
    });

    if (!clientesComContatos || clientesComContatos.length === 0) {
      console.log('Nenhum cliente com identificador válido encontrado.');
      return;
    }

    console.log(`Encontrados ${clientesComContatos.length} registros de clientes com identificadores válidos.`);

    const telefonesPorIdentificador: Map<string, Set<string>> = new Map();

    for (const cliente of clientesComContatos) {
      if (cliente.identificador) {
        const currentIdentificador = cliente.identificador;

        const telefonesDoCliente = cliente.contatos
          .map(contato => contato.telefone)
          .filter((tel): tel is string => typeof tel === 'string' && tel.length > 0);

        if (telefonesDoCliente.length > 0) {
          if (!telefonesPorIdentificador.has(currentIdentificador)) {
            telefonesPorIdentificador.set(currentIdentificador, new Set<string>());
          }
          
          const setDeTelefones = telefonesPorIdentificador.get(currentIdentificador);
          if (setDeTelefones) {
            telefonesDoCliente.forEach(tel => {
              setDeTelefones.add(tel);
            });
          }
        }
      }
    }
    
    if (telefonesPorIdentificador.size === 0) {
      console.log('Nenhum telefone encontrado para os identificadores válidos.');
      return;
    }

    console.log(`Identificadores únicos com telefones: ${telefonesPorIdentificador.size}`);

    const csvData: RelatorioIdentificadorTelefone[] = [];
    for (const [identificador, setDeTelefones] of telefonesPorIdentificador.entries()) {
      csvData.push({
        identificador,
        telefones: Array.from(setDeTelefones).join(' | '),
      });
    }

    const pastaOutput = path.join(__dirname, '..', '..', 'output');
    if (!fs.existsSync(pastaOutput)) {
      fs.mkdirSync(pastaOutput, { recursive: true });
    }
    const filePath = path.join(pastaOutput, 'relatorio_identificadores_telefones.csv');

    // Ordenar os dados pelo identificador para consistência
    csvData.sort((a, b) => a.identificador.localeCompare(b.identificador));

    const ws = fs.createWriteStream(filePath);
    csv.write(csvData, { headers: true })
      .pipe(ws)
      .on('finish', () => {
        console.log('Relatório CSV gerado com sucesso!');
        console.log(`Local: ${filePath}`);
        console.log(`Total de identificadores no CSV: ${csvData.length}`);
      });

  } catch (error) {
    console.error('Erro ao gerar o relatório de identificadores e telefones:', error);
  } finally {
    await prisma.$disconnect();
  }
}

async function main() {
  await gerarRelatorioIdentificadorTelefones();
}

main().catch(async (e) => {
  console.error(e);
  await prisma.$disconnect();
  process.exit(1);
}); 