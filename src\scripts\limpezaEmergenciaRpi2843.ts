import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

interface DuplicadoDespacho {
  processoId: string;
  processoNumero: string;
  nomeDespacho: string;
  registros: {
    id: string;
    codigo: string;
    isPendente: boolean;
  }[];
}

async function analisarDuplicadosParaLimpeza(): Promise<DuplicadoDespacho[]> {
  console.log("🔍 Identificando duplicados para limpeza...\n");

  // Encontrar RPI 2843
  const rpi2843 = await prisma.rPI.findFirst({
    where: { numero: 2843 },
    select: { id: true }
  });

  if (!rpi2843) {
    throw new Error("RPI 2843 não encontrada!");
  }

  // Buscar todos os despachos
  const todosDespachos = await prisma.despacho.findMany({
    where: { rpiId: rpi2843.id },
    include: {
      processo: { select: { numero: true } }
    },
    orderBy: [{ processoId: 'asc' }, { nome: 'asc' }]
  });

  // Agrupar por processo + nome
  const grupos = new Map<string, typeof todosDespachos>();
  for (const despacho of todosDespachos) {
    const chave = `${despacho.processoId}_${despacho.nome}`;
    if (!grupos.has(chave)) {
      grupos.set(chave, []);
    }
    grupos.get(chave)!.push(despacho);
  }

  // Filtrar apenas grupos duplicados
  const duplicados: DuplicadoDespacho[] = [];
  for (const [chave, despachos] of grupos.entries()) {
    if (despachos.length > 1) {
      duplicados.push({
        processoId: despachos[0].processoId,
        processoNumero: despachos[0].processo.numero,
        nomeDespacho: despachos[0].nome || 'N/A',
        registros: despachos.map(d => ({
          id: d.id,
          codigo: d.codigo,
          isPendente: d.codigo === 'PENDENTE'
        }))
      });
    }
  }

  return duplicados;
}

async function executarLimpeza(duplicados: DuplicadoDespacho[], executar: boolean = false): Promise<void> {
  console.log(`\n${executar ? '🧹 EXECUTANDO' : '📋 SIMULANDO'} LIMPEZA...\n`);

  let totalParaRemover = 0;
  let processosAfetados = 0;
  const idsParaRemover: string[] = [];

  for (const duplicado of duplicados) {
    const registrosPendentes = duplicado.registros.filter(r => r.isPendente);
    
    if (registrosPendentes.length > 0) {
      processosAfetados++;
      totalParaRemover += registrosPendentes.length;
      
      if (executar) {
        idsParaRemover.push(...registrosPendentes.map(r => r.id));
      }

      // Log apenas dos primeiros 20 para não sobrecarregar
      if (processosAfetados <= 20) {
        console.log(`${executar ? '🗑️' : '📝'} Processo ${duplicado.processoNumero}:`);
        console.log(`   Despacho: ${duplicado.nomeDespacho}`);
        console.log(`   Removendo ${registrosPendentes.length} registro(s) "PENDENTE"`);
        registrosPendentes.forEach(r => {
          console.log(`     - ID: ${r.id}`);
        });
        console.log("");
      }
    }
  }

  console.log("📊 RESUMO DA OPERAÇÃO:");
  console.log(`   Processos afetados: ${processosAfetados}`);
  console.log(`   Despachos a remover: ${totalParaRemover}`);

  if (executar && idsParaRemover.length > 0) {
    console.log(`\n🚀 Executando remoção em lotes...\n`);
    
    // Processar em lotes de 1000 para não sobrecarregar
    const loteSize = 1000;
    let removidos = 0;
    
    for (let i = 0; i < idsParaRemover.length; i += loteSize) {
      const lote = idsParaRemover.slice(i, i + loteSize);
      
      try {
        // Remover despachos elegíveis relacionados primeiro
        const despachoElegivelRemovidos = await prisma.despachoElegivel.deleteMany({
          where: {
            despachoId: { in: lote }
          }
        });

        // Remover protocolos relacionados
        const protocolosRemovidos = await prisma.protocoloDespacho.deleteMany({
          where: {
            despachoId: { in: lote }
          }
        });

        // Remover os despachos
        const despachosRemovidos = await prisma.despacho.deleteMany({
          where: {
            id: { in: lote }
          }
        });

        removidos += despachosRemovidos.count;
        
        console.log(`✅ Lote ${Math.ceil((i + 1) / loteSize)}: ${despachosRemovidos.count} despachos, ${protocolosRemovidos.count} protocolos, ${despachoElegivelRemovidos.count} elegíveis removidos`);
        
      } catch (error) {
        console.error(`❌ Erro no lote ${Math.ceil((i + 1) / loteSize)}:`, error);
      }
    }
    
    console.log(`\n🎉 Limpeza concluída! Total removido: ${removidos} despachos`);
  }
}

async function main() {
  try {
    console.log("🚨 SCRIPT DE LIMPEZA DE EMERGÊNCIA - RPI 2843\n");
    console.log("=".repeat(80));

    // 1. Analisar duplicados
    const duplicados = await analisarDuplicadosParaLimpeza();
    console.log(`📊 Grupos de duplicados encontrados: ${duplicados.length}`);

    if (duplicados.length === 0) {
      console.log("✅ Nenhum duplicado encontrado!");
      return;
    }

    // 2. Simulação primeiro
    await executarLimpeza(duplicados, false);
    
    console.log("\n" + "=".repeat(80));
    console.log("⚠️  ATENÇÃO: Esta foi apenas uma SIMULAÇÃO!");
    console.log("⚠️  Para executar a limpeza real, execute com parâmetro '--executar'");
    console.log("⚠️  Exemplo: npx ts-node limpezaEmergenciaRpi2843.ts --executar");
    console.log("=".repeat(80));

    // 3. Verificar se deve executar
    const executar = process.argv.includes('--executar');
    if (executar) {
      console.log("\n🚨 EXECUTANDO LIMPEZA REAL!\n");
      await executarLimpeza(duplicados, true);
    }

  } catch (error) {
    console.error("❌ Erro crítico:", error);
  } finally {
    await prisma.$disconnect();
  }
}

main().catch(console.error); 