import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function atualizarProcuradorProcessos() {
  try {
    // ID do procurador que será vinculado
    const procuradorId = '65b3c0c0-aa3b-4d89-85fb-2a144e538800';

    // Buscar processos que atendem aos critérios
    const processos = await prisma.processo.findMany({
      where: {
        clienteId: {
          not: null
        },
        procuradorId: null,
        cliente: {
          autoLoginUrl: {
            not: null
          }
        }
      },
      include: {
        cliente: true
      }
    });

    console.log(`Encontrados ${processos.length} processos para atualização`);

    // Atualizar cada processo
    for (const processo of processos) {
      await prisma.processo.update({
        where: {
          id: processo.id
        },
        data: {
          procuradorId: procuradorId
        }
      });

      console.log(`Processo ${processo.numero} atualizado com sucesso`);
    }

    console.log('Atualização concluída com sucesso!');
  } catch (error) {
    console.error('Erro ao atualizar processos:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script
atualizarProcuradorProcessos(); 