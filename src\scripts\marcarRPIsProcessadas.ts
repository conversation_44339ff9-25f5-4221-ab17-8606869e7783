import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function marcarRPIsProcessadas() {
  try {
    console.log('Iniciando marcação de todas as RPIs como processadas...');

    // Busca RPIs não processadas
    const rpisNaoProcessadas = await prisma.rPI.findMany({
      where: {
        estimativasProcessadas: false
      },
      select: {
        id: true,
        numero: true,
        dataPublicacao: true
      },
      orderBy: {
        numero: 'asc'
      }
    });

    console.log(`Encontradas ${rpisNaoProcessadas.length} RPIs não processadas.`);

    if (rpisNaoProcessadas.length === 0) {
      console.log('Nenhuma ação necessária.');
      return;
    }

    // Atualiza cada RPI
    console.log('Processando RPIs:');
    
    for (const rpi of rpisNaoProcessadas) {
      await prisma.rPI.update({
        where: { id: rpi.id },
        data: { estimativasProcessadas: true }
      });
      
      console.log(`- RPI ${rpi.numero} (${rpi.dataPublicacao.toISOString()}) marcada como processada.`);
    }

    console.log(`\nOperação concluída. ${rpisNaoProcessadas.length} RPIs foram marcadas como processadas.`);

  } catch (error) {
    console.error('Erro ao marcar RPIs como processadas:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executa o script
marcarRPIsProcessadas().then(() => {
  console.log('Script finalizado.');
  process.exit(0);
}).catch(error => {
  console.error('Erro fatal:', error);
  process.exit(1);
}); 