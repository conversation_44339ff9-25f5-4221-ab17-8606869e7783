import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const listarRenovacoes = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      page = 1,
      limit = 10,
      sortBy = 'dataVigencia',
      sortOrder = 'asc',
      filtroVencimento = 'todos', // 'vencidas', 'proximoVencimento', 'noAno', 'todos'
      mesesProximos = 12 // para filtro proximoVencimento
    } = req.query;

    const procuradorId = '65b3c0c0-aa3b-4d89-85fb-2a144e538800';
    const pageNumber = parseInt(page as string);
    const limitNumber = parseInt(limit as string);
    const offset = (pageNumber - 1) * limitNumber;

    // Buscar processos qualificados para renovação
    const processos = await prisma.processo.findMany({
      where: {
        procuradorId: procuradorId,
        despachos: {
          some: {
            nome: { contains: 'Concessão de registro' }
          }
        }
      },
      include: {
        marca: {
          include: {
            ncl: true
          }
        },
        cliente: {
          include: {
            contatos: true
          }
        },
        despachos: {
          include: {
            rpi: true,
            protocolos: true
          },
          orderBy: {
            rpi: {
              dataPublicacao: 'desc'
            }
          }
        }
      }
    });

    // Processar cada processo para calcular dados de renovação
    const renovacoes = [];

    for (const processo of processos) {
      // Encontrar despacho de concessão
      const despachoConcessao = processo.despachos.find(d => d.nome?.includes('Concessão de registro'));
      if (!despachoConcessao || !despachoConcessao.rpi) continue;

      // Verificar se há despachos de extinção/arquivamento pós-concessão
      const dataConcessaoRpi = despachoConcessao.rpi.dataPublicacao;
      const temDespachoExtincao = processo.despachos.some(d => {
        if (!d.rpi) return false;
        const isExtincao = d.nome?.includes('Extinção') || 
                          d.nome?.includes('Arquivamento') || 
                          d.nome?.includes('Caducidade') || 
                          d.nome?.includes('Renúncia');
        const isPostConcessao = d.rpi.dataPublicacao > dataConcessaoRpi;
        return isExtincao && isPostConcessao;
      });
      
      if (temDespachoExtincao) continue;

      const dataConcessao = despachoConcessao.rpi.dataPublicacao;
      const dataVigencia = new Date(dataConcessao);
      dataVigencia.setFullYear(dataVigencia.getFullYear() + 10);

      // Calcular prazos para renovação
      const inicioRenovacaoOrdinaria = new Date(dataVigencia);
      inicioRenovacaoOrdinaria.setFullYear(inicioRenovacaoOrdinaria.getFullYear() - 1);

      const fimRenovacaoOrdinaria = new Date(dataVigencia);

      const inicioRenovacaoExtraordinaria = new Date(dataVigencia);
      const fimRenovacaoExtraordinaria = new Date(dataVigencia);
      fimRenovacaoExtraordinaria.setMonth(fimRenovacaoExtraordinaria.getMonth() + 6);

      // Verificar renovações anteriores
      const renovacoesAnteriores = processo.despachos.filter(despacho => 
        despacho.protocolos.some(protocolo => 
          ['3745', '374.5', '3755', '3751', '375.5', '375.1'].includes(protocolo.codigoServico)
        )
      );

      const temRenovacaoAnterior = renovacoesAnteriores.length > 0;
      let proximaDataVigencia = dataVigencia;

      if (temRenovacaoAnterior) {
        // Calcular próxima data de vigência baseada nas renovações
        proximaDataVigencia = new Date(dataVigencia);
        proximaDataVigencia.setFullYear(proximaDataVigencia.getFullYear() + (renovacoesAnteriores.length * 10));
      }

      // Calcular tempo restante
      const agora = new Date();
      const diferencaMs = proximaDataVigencia.getTime() - agora.getTime();
      const diasRestantes = Math.floor(diferencaMs / (1000 * 60 * 60 * 24));
      
      const anos = Math.floor(diasRestantes / 365);
      const meses = Math.floor((diasRestantes % 365) / 30);
      const dias = diasRestantes % 30;

      // Aplicar filtros
      let incluirProcesso = true;
      
      if (filtroVencimento === 'vencidas' && diasRestantes > 0) {
        incluirProcesso = false;
      } else if (filtroVencimento === 'proximoVencimento') {
        const mesesRestantes = diasRestantes / 30;
        if (mesesRestantes > parseInt(mesesProximos as string)) {
          incluirProcesso = false;
        }
      } else if (filtroVencimento === 'noAno' && diasRestantes > 365) {
        incluirProcesso = false;
      }

      if (!incluirProcesso) continue;

      // Encontrar marca com dados completos
      let marcaNome = null;
      if (processo.marca && processo.marca.nome) {
        marcaNome = processo.marca.nome;
      }

      renovacoes.push({
        processoId: processo.id,
        numeroProcesso: processo.numero,
        dataDeposito: processo.dataDeposito,
        dataConcessao: dataConcessao,
        dataVigencia: proximaDataVigencia,
        prazoOrdinario: {
          inicio: inicioRenovacaoOrdinaria,
          fim: fimRenovacaoOrdinaria,
          formatado: `de ${inicioRenovacaoOrdinaria.toLocaleDateString('pt-BR')} até ${fimRenovacaoOrdinaria.toLocaleDateString('pt-BR')}`
        },
        prazoExtraordinario: {
          inicio: inicioRenovacaoExtraordinaria,
          fim: fimRenovacaoExtraordinaria,
          formatado: `de ${inicioRenovacaoExtraordinaria.toLocaleDateString('pt-BR')} até ${fimRenovacaoExtraordinaria.toLocaleDateString('pt-BR')}`
        },
        tempoRestante: {
          anos,
          meses,
          dias,
          diasTotal: diasRestantes,
          formatado: `${anos} anos, ${meses} meses e ${dias} dias`
        },
        marcaNome,
        renovacoesAnteriores: {
          quantidade: renovacoesAnteriores.length,
          detalhes: renovacoesAnteriores.map(r => ({
            data: r.rpi?.dataPublicacao,
            protocolos: r.protocolos.map(p => ({
              numero: p.numero,
              codigoServico: p.codigoServico,
              data: p.data
            }))
          }))
        },
        cliente: processo.cliente ? {
          identificador: processo.cliente.identificador,
          crmId: processo.cliente.crmId,
          nome: processo.cliente.nome,
          numeroDocumento: processo.cliente.numeroDocumento,
          contatos: processo.cliente.contatos
        } : null
      });
    }

    // Ordenação
    const sortField = sortBy as string;
    const order = sortOrder as string;
    
    renovacoes.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortField) {
        case 'dataVigencia':
          aValue = new Date(a.dataVigencia).getTime();
          bValue = new Date(b.dataVigencia).getTime();
          break;
        case 'numeroProcesso':
          aValue = a.numeroProcesso;
          bValue = b.numeroProcesso;
          break;
        case 'marcaNome':
          aValue = a.marcaNome || '';
          bValue = b.marcaNome || '';
          break;
        case 'diasRestantes':
          aValue = a.tempoRestante.diasTotal;
          bValue = b.tempoRestante.diasTotal;
          break;
        default:
          aValue = a.numeroProcesso;
          bValue = b.numeroProcesso;
      }
      
      if (order === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });

    // Paginação
    const total = renovacoes.length;
    const renovacoesPaginadas = renovacoes.slice(offset, offset + limitNumber);
    console.log(renovacoesPaginadas);
    return res.status(200).json({
      success: true,
      data: renovacoesPaginadas,
      pagination: {
        currentPage: pageNumber,
        totalPages: Math.ceil(total / limitNumber),
        totalItems: total,
        itemsPerPage: limitNumber
      },
      filtros: {
        filtroVencimento,
        mesesProximos,
        sortBy,
        sortOrder
      }
    });

  } catch (error: any) {
    console.error(`Erro ao listar renovações: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Erro ao listar renovações',
      error: error.message
    });
  }
};

export const atualizarMarca = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { nome, apresentacao, natureza } = req.body;

    // Verificar se a marca existe
    const marcaExistente = await prisma.marca.findUnique({
      where: { id }
    });

    if (!marcaExistente) {
      return res.status(404).json({ 
        success: false, 
        message: 'Marca não encontrada' 
      });
    }

    // Atualizar a marca
    const marcaAtualizada = await prisma.marca.update({
      where: { id },
      data: {
        nome: nome !== undefined ? nome : marcaExistente.nome,
        apresentacao: apresentacao !== undefined ? apresentacao : marcaExistente.apresentacao,
        natureza: natureza !== undefined ? natureza : marcaExistente.natureza
      },
      include: {
        ncl: true,
        cfe: true
      }
    });

    return res.status(200).json({
      success: true,
      message: 'Marca atualizada com sucesso',
      data: marcaAtualizada
    });
  } catch (error: any) {
    console.error(`Erro ao atualizar marca: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Erro ao atualizar marca',
      error: error.message
    });
  }
};