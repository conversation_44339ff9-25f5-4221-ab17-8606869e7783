import { PrismaClient } from '@prisma/client';
import { calcularEstimativasMeritoSimplificado } from './meritoService';
import * as cron from 'node-cron';

const prisma = new PrismaClient();

// Flag para controlar a execução da cron
let cronEmExecucao = false;

// Variável para armazenar a tarefa agendada
let tarefaAgendada: cron.ScheduledTask | null = null;

/**
 * Verifica se há uma nova RPI não processada
 * @returns Objeto contendo se uma RPI não processada foi encontrada e suas informações
 */
async function verificarNovaRPI(): Promise<{ rpiEncontrada: boolean; numeroRPI?: number; dataPublicacao?: Date, rpiId?: string }> {
  // Busca a RPI mais recente que ainda não foi processada para estimativas
  const ultimaRPI = await prisma.rPI.findFirst({
    orderBy: {
      numero: 'desc'
    },
    where: {
      estimativasProcessadas: true
    },
    select: {
      id: true,
      numero: true,
      dataPublicacao: true,
      estimativasProcessadas: true
    },
  });

  if (!ultimaRPI) {
    console.log('Não há nenhuma RPI pendente de processamento para estimativas');
    return { rpiEncontrada: false };
  }

  console.log(`RPI pendente encontrada: ${ultimaRPI.numero}, publicada em ${ultimaRPI.dataPublicacao.toISOString()}`);
  
  // Verifica se é uma RPI relativamente recente (menos de 15 dias)
  const hoje = new Date();
  const dataRPI = new Date(ultimaRPI.dataPublicacao);
  const diferencaDias = Math.floor((hoje.getTime() - dataRPI.getTime()) / (1000 * 60 * 60 * 24));

  // Apenas como segurança para não processar RPIs muito antigas
  if (diferencaDias > 15) {
    console.log(`Atenção: RPI ${ultimaRPI.numero} é de ${diferencaDias} dias atrás, mas será processada mesmo assim.`);
  }

  return { 
    rpiEncontrada: true, 
    numeroRPI: ultimaRPI.numero,
    dataPublicacao: ultimaRPI.dataPublicacao,
    rpiId: ultimaRPI.id
  };
}

/**
 * Atualiza as estimativas de mérito dos processos
 * @param rpiId ID da RPI que está sendo processada
 */
async function atualizarEstimativasMerito(rpiId: string) {
  try {
    console.log('Iniciando atualização de estimativas de mérito...');
    
    // Busca todos os processos onde somos procuradores
    const processos = await prisma.processo.findMany({
      where: {
        procurador: {
          nome: {
            contains: "REGISTRE-SE",
            mode: "insensitive"
          }
        }
      },
      include: {
        despachos: {
          include: {
            rpi: true
          },
          orderBy: {
            rpi: {
              dataPublicacao: 'asc'
            }
          }
        }
      }
    });

    console.log(`Encontrados ${processos.length} processos para atualização`);

    // Calcula as estimativas atuais
    const { estimativaMerito } = await calcularEstimativasMeritoSimplificado();

    let processosAtualizados = 0;
    let processosComSobrestamento = 0;
    let processosComOposicao = 0;
    let processosComExigencia = 0;
    let processosSemIntervencao = 0;
    let processosComDataDepositoEstimada = 0;

    for (const processo of processos) {
      // Se não tem data de depósito, tenta estimar baseado no despacho de publicação
      let dataDeposito = processo.dataDeposito;
      if (!dataDeposito) {
        const despachoPublicacao = processo.despachos.find(d => 
          d.nome?.toLowerCase().includes('publicação de pedido de registro para oposição')
        );
        
        if (despachoPublicacao?.rpi?.dataPublicacao) {
          dataDeposito = new Date(despachoPublicacao.rpi.dataPublicacao);
          dataDeposito.setDate(dataDeposito.getDate() - 23); // 23 dias antes da publicação
          processosComDataDepositoEstimada++;
        }
      }

      // Verifica sobrestamento
      const sobrestamento = processo.despachos.find(d => 
        d.nome?.toLowerCase().includes('sobrestamento do exame de mérito')
      );

      if (sobrestamento) {
        await prisma.processo.update({
          where: { id: processo.id },
          data: {
            sobrestamento: true,
            dataSobrestamento: sobrestamento.rpi.dataPublicacao,
            dataMeritoEstimada: null,
            diasAteMeritoEstimada: null
          }
        });
        processosComSobrestamento++;
        continue;
      }

      // Verifica oposição e exigência
      const oposicao = processo.despachos.find(d => 
        d.nome?.toLowerCase().includes('notificação de oposição')
      );
      const exigencia = processo.despachos.find(d => 
        d.nome?.toLowerCase().includes('exigência de mérito')
      );

      if (oposicao || exigencia) {
        let diasEstimados: number;
        if (oposicao) {
          diasEstimados = estimativaMerito.comOposicao.mediaEmDias;
          processosComOposicao++;
        } else {
          diasEstimados = estimativaMerito.comExigencia.mediaEmDias;
          processosComExigencia++;
        }

        const dataMeritoEstimada = dataDeposito ? 
          new Date(dataDeposito.getTime() + (diasEstimados * 24 * 60 * 60 * 1000)) : 
          null;

        await prisma.processo.update({
          where: { id: processo.id },
          data: {
            oposicao: !!oposicao,
            dataOposicao: oposicao?.rpi.dataPublicacao || null,
            exigencia: !!exigencia,
            dataExigencia: exigencia?.rpi.dataPublicacao || null,
            dataMeritoEstimada,
            diasAteMeritoEstimada: diasEstimados
          }
        });

        processosAtualizados++;
      } else if (dataDeposito) {
        // Se não tem intervenções e tem data de depósito, atualiza com estimativa sem intervenções
        const diasEstimados = estimativaMerito.semIntervencoes.mediaEmDias;
        const dataMeritoEstimada = new Date(dataDeposito.getTime() + (diasEstimados * 24 * 60 * 60 * 1000));

        await prisma.processo.update({
          where: { id: processo.id },
          data: {
            oposicao: false,
            dataOposicao: null,
            exigencia: false,
            dataExigencia: null,
            dataMeritoEstimada,
            diasAteMeritoEstimada: diasEstimados
          }
        });

        processosSemIntervencao++;
        processosAtualizados++;
      }
    }

    console.log('\nResumo da atualização:');
    console.log(`- Total de processos processados: ${processos.length}`);
    console.log(`- Processos com data de depósito estimada: ${processosComDataDepositoEstimada}`);
    console.log(`- Processos com sobrestamento: ${processosComSobrestamento}`);
    console.log(`- Processos com oposição: ${processosComOposicao}`);
    console.log(`- Processos com exigência: ${processosComExigencia}`);
    console.log(`- Processos sem intervenções: ${processosSemIntervencao}`);
    console.log(`- Processos atualizados com estimativas: ${processosAtualizados}`);

    // Marca a RPI como processada
    await prisma.rPI.update({
      where: { id: rpiId },
      data: { estimativasProcessadas: true }
    });
    
    console.log(`RPI marcada como processada para estimativas.`);

    // Marca a tarefa como concluída
    cronEmExecucao = false;

  } catch (error) {
    console.error('Erro ao atualizar estimativas:', error);
    // Libera a execução mesmo em caso de erro
    cronEmExecucao = false;
  }
}

/**
 * Função principal que verifica e atualiza as estimativas
 */
export async function verificarEAtualizarEstimativas(): Promise<{ processada: boolean }> {
  try {
    // Se já estiver em execução, não faz nada
    if (cronEmExecucao) {
      console.log('Já existe uma verificação em andamento. Pulando execução.');
      return { processada: false };
    }

    console.log('Verificando se há nova RPI disponível...');
    const { rpiEncontrada, numeroRPI, dataPublicacao, rpiId } = await verificarNovaRPI();

    if (rpiEncontrada && numeroRPI && dataPublicacao && rpiId) {
      console.log(`Nova RPI encontrada para processamento: ${numeroRPI} (${dataPublicacao})`);
      cronEmExecucao = true;
      
      // Executa a atualização das estimativas
      await atualizarEstimativasMerito(rpiId);
      return { processada: true };
    } else {
      console.log('Nenhuma nova RPI pendente de processamento');
      return { processada: false };
    }
  } catch (error) {
    console.error('Erro ao verificar e atualizar estimativas:', error);
    cronEmExecucao = false;
    return { processada: false };
  }
}

/**
 * Função para agendar a próxima terça-feira
 */
function agendarProximaTerca() {
  // Cancela o agendamento anterior se existir
  if (tarefaAgendada) {
    tarefaAgendada.stop();
  }

  // Agenda para todas as terças a partir das 4h
  tarefaAgendada = cron.schedule('0 4-23 * * 2', async () => {
    const agora = new Date();
    const hora = agora.getHours();
    
    console.log(`Verificação agendada executando às ${hora}h de terça-feira...`);
    
    // Executa a verificação
    const resultado = await verificarEAtualizarEstimativas();
    
    // Se uma RPI foi processada com sucesso, para as próximas execuções de hoje
    if (resultado && resultado.processada) {
      console.log('RPI processada com sucesso. Suspendendo verificações até a próxima terça-feira.');
      tarefaAgendada?.stop();
      
      // Reagenda para a próxima terça-feira na mesma hora
      setTimeout(() => {
        agendarProximaTerca();
      }, 24 * 60 * 60 * 1000); // Espera 24 horas para reagendar
    }
  });

  console.log('Serviço de atualização de estimativas agendado para terças-feiras a partir das 4h da manhã.');
}

/**
 * Inicia o agendamento da atualização de estimativas
 */
export function iniciarAtualizacaoEstimativas() {
  agendarProximaTerca();
} 