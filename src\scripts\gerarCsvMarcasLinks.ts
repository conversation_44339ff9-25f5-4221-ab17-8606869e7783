import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';
import dotenv from 'dotenv';

dotenv.config();
const prisma = new PrismaClient();

interface CampoPersonalizado {
  id: number;
  hash: string;
  name: string;
  type: number;
  value: string;
  belongs: number;
  formula: any;
  options: any;
  raw_value: string;
  currency_id: any;
  output_type: any;
  thousand_sep: number;
  allow_negative: any;
  decimal_places: number;
  selected_options: any;
}

function extrairNomeMarca(camposPersonalizados: any): string | null {
  if (!camposPersonalizados || typeof camposPersonalizados !== 'object') {
    return null;
  }

  // Se for um array, iterar pelos campos
  if (Array.isArray(camposPersonalizados)) {
    for (const campo of camposPersonalizados) {
      if (campo.name === "Nome da marca ✍️" && campo.value) {
        return campo.value.trim();
      }
    }
  }

  return null;
}

function escapeCsvValue(value: string): string {
  // Se contém vírgula, aspas ou quebra de linha, envolver em aspas
  if (value.includes(',') || value.includes('"') || value.includes('\n') || value.includes('\r')) {
    // Duplicar aspas internas e envolver em aspas
    return `"${value.replace(/"/g, '""')}"`;
  }
  return value;
}

async function gerarCsvMarcasLinks() {
  console.log('📊 Iniciando geração de CSV com marcas e links...\n');
  
  let stats = {
    clientesComLinks: 0,
    clientesComMarca: 0,
    clientesSemMarca: 0,
    csvGerado: false
  };

  try {
    // Buscar todos os clientes que têm autoLoginUrl
    console.log('🔍 Buscando clientes com links gerados...');
    const clientes = await prisma.cliente.findMany({
      where: {
        autoLoginUrl: {
          not: null
        }
      },
      select: {
        id: true,
        nome: true,
        identificador: true,
        autoLoginUrl: true,
        camposPersonalizados: true
      }
    });

    console.log(`✅ Encontrados ${clientes.length} clientes com links\n`);
    stats.clientesComLinks = clientes.length;

    if (clientes.length === 0) {
      console.log('⚠️  Nenhum cliente com link encontrado. Execute primeiro o script gerar-links-auto-login');
      return;
    }

    // Preparar dados para CSV
    const csvData: Array<{nome: string, link: string, clienteId: number}> = [];

    for (const cliente of clientes) {
      console.log(`🔍 Processando cliente ID: ${cliente.id} - ${cliente.nome || 'Sem nome'}`);
      
      // Extrair nome da marca dos campos personalizados
      const nomeMarca = extrairNomeMarca(cliente.camposPersonalizados);
      
      if (nomeMarca) {
        console.log(`   ✅ Marca encontrada: "${nomeMarca}"`);
        csvData.push({
          nome: nomeMarca,
          link: cliente.autoLoginUrl!,
          clienteId: cliente.id
        });
        stats.clientesComMarca++;
      } else {
        console.log(`   ❌ Marca não encontrada nos campos personalizados`);
        console.log(`   📋 Campos disponíveis:`, JSON.stringify(cliente.camposPersonalizados, null, 2));
        stats.clientesSemMarca++;
      }
    }

    if (csvData.length === 0) {
      console.log('\n⚠️  Nenhuma marca encontrada para gerar CSV');
      return;
    }

    // Gerar CSV
    console.log(`\n📝 Gerando CSV com ${csvData.length} registros...`);
    
    // Cabeçalho do CSV
    let csvContent = 'Nome da Marca,Link de Auto-Login,Cliente ID\n';
    
    // Adicionar dados
    for (const item of csvData) {
      const nomeEscaped = escapeCsvValue(item.nome);
      const linkEscaped = escapeCsvValue(item.link);
      csvContent += `${nomeEscaped},${linkEscaped},${item.clienteId}\n`;
    }

    // Salvar arquivo
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
    const fileName = `marcas-links-${timestamp}.csv`;
    const filePath = path.join(process.cwd(), fileName);
    
    fs.writeFileSync(filePath, csvContent, 'utf8');
    stats.csvGerado = true;

    console.log(`✅ CSV gerado com sucesso: ${fileName}`);
    console.log(`📍 Localização: ${filePath}`);

  } catch (error) {
    console.error('❌ Erro geral na execução:', error);
  } finally {
    await prisma.$disconnect();
  }

  // Relatório final
  console.log('\n' + '='.repeat(60));
  console.log('📊 RELATÓRIO FINAL - GERAÇÃO CSV MARCAS E LINKS');
  console.log('='.repeat(60));
  console.log(`🔗 Clientes com links: ${stats.clientesComLinks}`);
  console.log(`✅ Clientes com marca: ${stats.clientesComMarca}`);
  console.log(`❌ Clientes sem marca: ${stats.clientesSemMarca}`);
  console.log(`📄 CSV gerado: ${stats.csvGerado ? 'SIM' : 'NÃO'}`);
  
  if (stats.clientesComMarca > 0) {
    console.log(`\n🎯 Taxa de sucesso: ${((stats.clientesComMarca / stats.clientesComLinks) * 100).toFixed(1)}%`);
  }
  
  if (stats.csvGerado) {
    console.log('\n🎉 Operação concluída com sucesso!');
    console.log('💡 Dica: Abra o arquivo CSV no Excel ou Google Sheets');
  } else if (stats.clientesSemMarca > 0) {
    console.log('\n⚠️  Alguns clientes não têm o campo "Nome da marca ✍️" nos campos personalizados.');
    console.log('   Verifique os logs acima para ver a estrutura dos campos disponíveis.');
  }
  
  console.log('='.repeat(60));
}

// Executar o script
if (require.main === module) {
  gerarCsvMarcasLinks()
    .catch(console.error)
    .finally(() => process.exit());
}

export { gerarCsvMarcasLinks }; 