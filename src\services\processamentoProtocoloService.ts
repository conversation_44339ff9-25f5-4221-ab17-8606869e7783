import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import crypto from 'crypto';
import querystring from 'querystring';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

interface DadosCrm {
  id: number;
  title: string;
  status: string;
  person?: {
    id: number;
    name: string;
    cpf?: string;
    contactPhones?: Array<{
      id: number;
      phone: string;
      is_main: number;
    }>;
    contactEmails?: Array<{
      id: number;
      email: string;
      is_main: number;
    }>;
  };
  company?: {
    id: number;
    name: string;
    cnpj?: string;
    contactPhones?: Array<{
      id: number;
      phone: string;
      is_main: number;
    }>;
    contactEmails?: Array<{
      id: number;
      email: string;
      is_main: number;
    }>;
  };
  customFields?: Array<{
    id: number;
    name: string;
    value: any;
  }>;
}

/**
 * Extrai identificador único a partir de um telefone (FUNÇÃO TESTADA E APROVADA)
 * Cópia exata da função do script criarClientesCrmCompleto.ts
 */
function extrairIdentificadorTelefone(telefone: string): string {
  if (!telefone || telefone === "NoPhone") {
    return "0000000000";
  }

  const numeroLimpo = telefone.replace(/\D/g, '');
  
  if (!numeroLimpo) {
    return "0000000000";
  }

  let numeroProcessado = numeroLimpo;

  // Lista de DDDs válidos no Brasil
  const dddsValidos = new Set([
    11, 12, 13, 14, 15, 16, 17, 18, 19, // SP
    21, 22, 24, // RJ/ES
    27, 28, // ES
    31, 32, 33, 34, 35, 37, 38, // MG
    41, 42, 43, 44, 45, 46, // PR
    47, 48, 49, // SC
    51, 53, 54, 55, // RS
    61, // DF/GO
    62, 64, // GO
    63, // TO
    65, 66, // MT
    67, // MS
    68, // AC
    69, // RO
    71, 73, 74, 75, 77, // BA
    79, // SE
    81, 87, // PE
    82, // AL
    83, // PB
    84, // RN
    85, 88, // CE
    86, 89, // PI
    91, 93, 94, // PA
    92, 97, // AM
    95, // RR
    96, // AP
    98, 99 // MA
  ]);

  // CORREÇÃO: Detectar duplo "55" (país + DDD)
  if (numeroProcessado.startsWith('5555') && numeroProcessado.length >= 12) {
    // Remove primeiro "55" (código do país), mantém segundo "55" (DDD)
    numeroProcessado = numeroProcessado.substring(2);
  } else if (numeroProcessado.startsWith('55') && numeroProcessado.length >= 12) {
    // Verifica se após remover 55, temos um número válido brasileiro
    const semPrefixo = numeroProcessado.substring(2);
    
    if (semPrefixo.length === 10 || semPrefixo.length === 11) {
      const possibleDDD = parseInt(semPrefixo.substring(0, 2));
      if (dddsValidos.has(possibleDDD)) {
        numeroProcessado = semPrefixo;
      }
    }
  }

  // Se ainda não temos pelo menos 10 dígitos, preenche com zeros à direita
  if (numeroProcessado.length < 10) {
    numeroProcessado = numeroProcessado.padEnd(10, '0');
  }

  // Extrai DDD (primeiros 2 dígitos) e últimos 8 dígitos
  const ddd = numeroProcessado.substring(0, 2);
  const ultimosOitoDigitos = numeroProcessado.slice(-8);
  const resultado = ddd + ultimosOitoDigitos;
  
  return resultado;
}

/**
 * Lista de valores que indicam marca figurativa (devem ser filtrados)
 */
const VALORES_FIGURATIVOS = [
  'figurativo', 'figurativa', 'figura', 'figurative',
  'sem nome', 'marca figurativa', 'marca figurativ',
  'logotipo', 'logo', 'imagem', 'simbolo', 'símbolo'
];

/**
 * Determina se um nome de marca indica marca figurativa
 */
function isMarcaFigurativa(nomeMarca: string): boolean {
  if (!nomeMarca || typeof nomeMarca !== 'string') {
    return true;
  }
  
  const nomeNormalizado = nomeMarca.trim().toLowerCase();
  return VALORES_FIGURATIVOS.some(valor => nomeNormalizado.includes(valor));
}

/**
 * Gera nome apropriado para exibição em ChatGuru/RD Station
 */
function gerarNomeMarcaParaExibicao(nomeMarca: string | null | undefined, numeroProcesso: string, apresentacao?: string | null): string {
  // Se tem nome válido (não figurativo), usar o nome
  if (nomeMarca && nomeMarca.trim() && !isMarcaFigurativa(nomeMarca)) {
    return nomeMarca.trim();
  }
  
  // Se não tem nome ou é figurativo, usar padrão descritivo
  if (apresentacao?.toLowerCase().includes('figurativa')) {
    return `Figurativa - ${numeroProcesso}`;
  }
  
  // Fallback
  return `Figurativa - ${numeroProcesso}`;
}

const crmToken = process.env.CRM_TOKEN || "";

// Configurações de criptografia (idênticas ao gerarLinksAutoLogin.ts)
const ALGORITHM = 'aes-256-cbc';
const JWT_SECRET = process.env.JWT_SECRET!;

const KEY = crypto.createHash('sha256').update(JWT_SECRET).digest();
const BASE_URL = 'https://cliente.registre.se';

/**
 * Busca dados do protocolo no CRM Pipe
 */
async function buscarDadosCrm(numeroProcesso: string): Promise<DadosCrm | null> {
  try {
    console.log(`[CRM] Buscando protocolo ${numeroProcesso}...`);
    
    const response = await axios.get(
      `https://api.pipe.run/v1/deals?token=${crmToken}&custom_fields[194250]=${numeroProcesso}&with=person.contactPhones,person.contactEmails,company.contactPhones,customFields`,
      {
        headers: {
          token: crmToken,
        },
      }
    );

    if (response.data && response.data.data && response.data.data.length > 0) {
      const dadosCrm = response.data.data[0];
      const clienteNome = dadosCrm.person?.name || dadosCrm.company?.name || 'Cliente sem nome';
      console.log(`[CRM] Protocolo ${numeroProcesso} encontrado - Cliente: ${clienteNome}`);
      return dadosCrm;
    }
    
    console.log(`[CRM] Protocolo ${numeroProcesso} não encontrado`);
    return null;
  } catch (error) {
    console.log(`[CRM] Erro na busca: ${error}`);
    throw error;
  }
}

/**
 * Gera todos os identificadores possíveis a partir dos telefones do CRM
 */
function gerarIdentificadoresPossiveis(dadosCrm: DadosCrm): string[] {
  const identificadores: Set<string> = new Set();
  
  if (dadosCrm.person?.contactPhones) {
    for (const telefone of dadosCrm.person.contactPhones) {
      const id = extrairIdentificadorTelefone(telefone.phone);
      if (id && id !== "0000000000") {
        identificadores.add(id);
      }
    }
  }
  
  if (dadosCrm.company?.contactPhones) {
    for (const telefone of dadosCrm.company.contactPhones) {
      const id = extrairIdentificadorTelefone(telefone.phone);
      if (id && id !== "0000000000") {
        identificadores.add(id);
      }
    }
  }
  
  console.log(`[IDENTIFICADOR] Gerados ${identificadores.size} identificadores válidos`);
  
  return Array.from(identificadores);
}

/**
 * Busca ou cria cliente com base nos dados do CRM
 */
async function criarOuAtualizarCliente(dadosCrm: DadosCrm): Promise<{ cliente: any, criado: boolean, atualizado: boolean }> {
  const identificadores = gerarIdentificadoresPossiveis(dadosCrm);

  if (identificadores.length === 0) {
    throw new Error("Nenhum telefone válido encontrado no CRM");
  }

  // Buscar cliente existente por IDENTIFICADOR primeiro (chave de negócio)
  let clienteExistente = null;
  let tipoEncontro = "";

  // PRIORIDADE 1: Buscar por identificador (mais confiável)
  for (const identificador of identificadores) {
    clienteExistente = await prisma.cliente.findFirst({
      where: { identificador },
    });
    if (clienteExistente) {
      tipoEncontro = `identificador ${identificador}`;
      break;
    }
  }

  // PRIORIDADE 2: Se não encontrou por identificador, buscar por crmId (fallback)
  if (!clienteExistente) {
    clienteExistente = await prisma.cliente.findFirst({
      where: { crmId: dadosCrm.id },
    });
    if (clienteExistente) {
      tipoEncontro = `CRM ID ${dadosCrm.id}`;
    }
  }

  // Preparar dados do cliente
  const nomeCliente =
    dadosCrm.person?.name || dadosCrm.company?.name || "Cliente";

  // Priorizar telefone principal
  let telefonePrincipal = "";
  let identificadorPrincipal = identificadores[0];

  if (dadosCrm.person?.contactPhones) {
    const telPrincipal =
      dadosCrm.person.contactPhones.find((t: any) => t.is_main === 1) ||
      dadosCrm.person.contactPhones[0];
    if (telPrincipal) {
      telefonePrincipal = telPrincipal.phone;
      const idPrincipal = extrairIdentificadorTelefone(telPrincipal.phone);
      if (idPrincipal && idPrincipal !== "0000000000")
        identificadorPrincipal = idPrincipal;
    }
  } else if (dadosCrm.company?.contactPhones?.[0]) {
    telefonePrincipal = dadosCrm.company.contactPhones[0].phone;
    const idPrincipal = extrairIdentificadorTelefone(telefonePrincipal);
    if (idPrincipal && idPrincipal !== "0000000000")
      identificadorPrincipal = idPrincipal;
  }
  // Função para obter documento
  const obterDocumento = (oportunidade: DadosCrm): {
    documento?: string;
    tipo?: string;
  } => {
    if (oportunidade.person?.cpf) {
      return { documento: oportunidade.person.cpf, tipo: "CPF" };
    }

    if (oportunidade.company?.cnpj) {
      return { documento: oportunidade.company.cnpj, tipo: "CNPJ" };
    }

    return {};
  }
  // Preparar dados para atualização/criação
  const numeroDocumento = obterDocumento(dadosCrm);

  let dadosCliente: any = {
    nome: nomeCliente,
    crmId: dadosCrm.id,
    numeroDocumento: numeroDocumento.documento || undefined,
    tipoDeDocumento: numeroDocumento.tipo || undefined,
    camposPersonalizados: dadosCrm.customFields || undefined,
  };

  // DECISÃO INTELIGENTE SOBRE IDENTIFICADOR
  if (clienteExistente) {
    if (tipoEncontro.includes("identificador")) {
      // Encontrou por identificador: manter o identificador atual (pode atualizar outros dados)
      dadosCliente.identificador = clienteExistente.identificador;
    } else if (tipoEncontro.includes("CRM ID")) {
      // Encontrou por crmId: PRESERVAR identificador existente se houver
      if (
        clienteExistente.identificador &&
        clienteExistente.identificador !== "0000000000"
      ) {
        dadosCliente.identificador = clienteExistente.identificador;
      } else {
        dadosCliente.identificador = identificadorPrincipal;
      }
    }
  } else {
    // Cliente novo: usar identificador gerado
    dadosCliente.identificador = identificadorPrincipal;
  }

  // Unificar lógica: tratar cliente existente igual a novo
  let cliente: any;
  let criado = false;
  let atualizado = false;

  if (clienteExistente) {
    cliente = await prisma.cliente.upsert({
      where: { id: clienteExistente.id },
      create: dadosCliente, // Não deveria ser executado já que cliente existe
      update: dadosCliente,
    });
    atualizado = true;
  } else {
    // VERIFICAÇÃO FINAL: Garantir que não existe cliente com este identificador
    const verificacaoFinal = await prisma.cliente.findFirst({
      where: { identificador: identificadorPrincipal },
    });

    if (verificacaoFinal) {
      cliente = await prisma.cliente.update({
        where: { id: verificacaoFinal.id },
        data: dadosCliente,
      });
      atualizado = true;
    } else {
      cliente = await prisma.cliente.create({
        data: dadosCliente,
      });
      criado = true;
    }
  }

  // 🆕 LÓGICA DE CONTATOS PRESERVANDO ALTERAÇÕES MANUAIS
  if (criado) {
    // CLIENTE NOVO: Criar contatos com dados do CRM
    console.log(`[PROCESSO] Cliente novo - criando contatos com dados do CRM`);
    
    const emailPrincipal =
      dadosCrm.person?.contactEmails?.find((e: any) => e.is_main === 1)?.email ||
      dadosCrm.person?.contactEmails?.[0]?.email ||
      dadosCrm.company?.contactEmails?.[0]?.email;

    await prisma.contatoCliente.create({
      data: {
        clienteId: cliente.id,
        telefone: telefonePrincipal,
        email: emailPrincipal,
      },
    });

    // Contato adicional se company tem dados diferentes
    if (dadosCrm.person && dadosCrm.company) {
      const telefoneCompany = dadosCrm.company.contactPhones?.[0]?.phone;
      const emailCompany = dadosCrm.company.contactEmails?.[0]?.email;

      if (telefoneCompany && telefoneCompany !== telefonePrincipal) {
        await prisma.contatoCliente.create({
          data: {
            clienteId: cliente.id,
            telefoneSegundario: telefoneCompany,
            email: emailCompany,
          },
        });
      }
    }
    
  } else {
    // CLIENTE EXISTENTE: Verificar se já tem contatos
    const contatosExistentes = await prisma.contatoCliente.findMany({
      where: { clienteId: cliente.id }
    });
    
    if (contatosExistentes.length === 0) {
      // Cliente existe mas não tem contatos - criar com dados do CRM
      console.log(`[PROCESSO] Cliente existente sem contatos - criando com dados do CRM`);
      
      const emailPrincipal =
        dadosCrm.person?.contactEmails?.find((e: any) => e.is_main === 1)?.email ||
        dadosCrm.person?.contactEmails?.[0]?.email ||
        dadosCrm.company?.contactEmails?.[0]?.email;

      await prisma.contatoCliente.create({
        data: {
          clienteId: cliente.id,
          telefone: telefonePrincipal,
          email: emailPrincipal,
        },
      });
      
    } else {
      // Cliente já tem contatos - PRESERVAR (não sobrescrever)
      console.log(`[PROCESSO] Cliente existente com ${contatosExistentes.length} contato(s) - preservando alterações manuais`);
    }
  }

  return { cliente, criado, atualizado };
}

/**
 * Conecta o cliente ao processo existente no banco
 */
async function conectarClienteAoProcesso(clienteId: number, numeroProcesso: string, dadosCrm?: any, dadosTitulares?: any, elementoNominativo?: string | null): Promise<any> {
  // Buscar o processo que já deve existir (criado pelo webhook)
  let processo = await prisma.processo.findUnique({
    where: { numero: numeroProcesso },
    include: {
      titulares: true // Incluir titulares para verificar se já existem
    }
  });
  
  if (!processo) {
    if (dadosCrm) {
      try {
        // Importar a função de criação de processo
        const { criarProcessoAPartirDoCrm } = await import('./criacaoProcessoService');
        
        // Criar o processo a partir dos dados do CRM e titulares
        // 🔧 CORRIGIDO: Passar elementoNominativo para determinação correta da apresentação
        processo = await criarProcessoAPartirDoCrm(dadosCrm, numeroProcesso, dadosTitulares, elementoNominativo);
        
      } catch (criacaoError) {
        throw new Error(`Processo ${numeroProcesso} não encontrado no banco e falha na criação automática: ${criacaoError}`);
      }
      
    } else {
      throw new Error(`Processo ${numeroProcesso} não encontrado no banco. Webhook pode não ter sido processado e não há dados do CRM para criação automática.`);
    }
  } else {
    // 🆕 ATUALIZAR MARCA EXISTENTE SE NECESSÁRIO
    try {
      console.log(`[PROCESSO] Processo ${numeroProcesso} já existe - verificando se marca precisa atualização...`);
      
      // Buscar marca existente
      const marcaExistente = await prisma.marca.findFirst({
        where: { processoId: processo.id }
      });
      
      if (marcaExistente && dadosCrm) {
        // Importar funções auxiliares do criacaoProcessoService
        const { limparNomeMarcaFigurativa, determinarApresentacaoMarca } = await import('./criacaoProcessoService');
        const { fieldMapping } = await import('../utils/crm.utils');
        
        // Extrair dados do CRM
        const extrairCampoCustomizado = (dadosCrm: any, fieldId: number): any => {
          const campo = dadosCrm.customFields?.find((field: any) => field.id === fieldId);
          return campo ? campo.value : null;
        };
        
        const nomeMarcaBruto = String(extrairCampoCustomizado(dadosCrm, fieldMapping.nomeMarca.id) || '');
        const nomeMarcaLimpo = limparNomeMarcaFigurativa(nomeMarcaBruto);
        
        // Determinar apresentação
        const temElementoNominativo = !!(elementoNominativo && elementoNominativo.trim());
        let apresentacaoCrm = extrairCampoCustomizado(dadosCrm, fieldMapping.tipoMarca.id);
        if (typeof apresentacaoCrm === 'string' && apresentacaoCrm.startsWith('[')) {
          try {
            const parsed = JSON.parse(apresentacaoCrm);
            apresentacaoCrm = Array.isArray(parsed) ? parsed[0] : apresentacaoCrm;
          } catch (e) {
            apresentacaoCrm = String(apresentacaoCrm);
          }
        }
        
        const apresentacaoFinal = determinarApresentacaoMarca(
          String(apresentacaoCrm || ''), 
          temElementoNominativo, 
          nomeMarcaLimpo
        );
        
        // Verificar se precisa atualizar
        const nomeEhFigurativo = marcaExistente.nome && limparNomeMarcaFigurativa(marcaExistente.nome) === null;
        const nomeNeedUpdate = nomeEhFigurativo && marcaExistente.nome !== nomeMarcaLimpo;
        
        const apresentacaoNeedUpdate = 
          (!temElementoNominativo && marcaExistente.apresentacao !== 'Figurativa') ||
          (nomeEhFigurativo && marcaExistente.apresentacao !== 'Figurativa');
        
        if (nomeNeedUpdate || apresentacaoNeedUpdate) {
          const dadosParaAtualizar: any = {};
          
          if (nomeNeedUpdate) {
            dadosParaAtualizar.nome = nomeMarcaLimpo;
          }
          
          if (apresentacaoNeedUpdate) {
            dadosParaAtualizar.apresentacao = apresentacaoFinal;
          }
          
          await prisma.marca.update({
            where: { id: marcaExistente.id },
            data: dadosParaAtualizar
          });
          
          console.log(`[PROCESSO] ✅ Marca ATUALIZADA no processo existente:`);
          if (nomeNeedUpdate) {
            console.log(`   Nome: "${marcaExistente.nome}" → ${nomeMarcaLimpo ? `"${nomeMarcaLimpo}"` : 'null'}`);
          }
          if (apresentacaoNeedUpdate) {
            console.log(`   Apresentação: "${marcaExistente.apresentacao}" → "${apresentacaoFinal}"`);
          }
        } else {
          console.log(`[PROCESSO] Marca já está correta no processo existente`);
        }
      }
      
    } catch (marcaError) {
      console.error(`[PROCESSO] Erro ao atualizar marca existente:`, marcaError);
      // Não quebrar o fluxo, apenas logar o erro
    }
    
    // Verificar se o processo tem titulares
    if (!processo.titulares || processo.titulares.length === 0) {
      if (dadosTitulares && dadosTitulares.length > 0) {
        try {
          // Importar função para criar titulares
          const { criarTitularesNoProcesso } = await import('./criacaoProcessoService');
          
          // Criar titulares no processo existente e conectar ao cliente
          await prisma.$transaction(async (tx) => {
            await criarTitularesNoProcesso(processo!.id, dadosTitulares, tx, clienteId);
          });
          
        } catch (titularError) {
          // Não quebrar o fluxo, apenas logar o erro
        }
      }
    } else {
      // Verificar se os titulares já estão conectados ao cliente
      const titularesSemCliente = processo.titulares.filter(t => !t.clienteId);
      if (titularesSemCliente.length > 0) {
        try {
          // Importar função para conectar titulares
          const { conectarTitularesAoCliente } = await import('./criacaoProcessoService');
          
          await prisma.$transaction(async (tx) => {
            await conectarTitularesAoCliente(processo!.id, clienteId, tx);
          });
          
        } catch (conexaoError) {
          // Não quebrar o fluxo, apenas logar o erro
        }
      }
    }
  }
  
  // Verificar se processo foi encontrado/criado
  if (!processo) {
    throw new Error(`Erro crítico: Processo ${numeroProcesso} não pôde ser encontrado nem criado`);
  }
  
  // Se já está conectado ao cliente, retornar
  if (processo.clienteId === clienteId) {
    return processo;
  }
  
  // Conectar o cliente ao processo existente
  const processoAtualizado = await prisma.processo.update({
    where: { id: processo.id },
    data: { clienteId }
  });
  
  return processoAtualizado;
}

/**
 * Gera código curto de 6 caracteres (igual ao script original)
 */
function generateShortCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Gera código único (igual ao script original)
 */
async function generateUniqueShortCode(): Promise<string> {
  let shortCode: string;
  let attempts = 0;
  const maxAttempts = 10;
  
  do {
    shortCode = generateShortCode();
    attempts++;
    
    if (attempts >= maxAttempts) {
      throw new Error('Não foi possível gerar um código único após várias tentativas');
    }
  } while (!(await isShortCodeUnique(shortCode)));
  
  return shortCode;
}

/**
 * Verifica se código já existe
 */
async function isShortCodeUnique(shortCode: string): Promise<boolean> {
  const existing = await prisma.shortUrl.findUnique({
    where: { shortCode }
  });
  return !existing;
}

/**
 * Criptografa credenciais (CÓPIA EXATA do gerarLinksAutoLogin.ts)
 */
function encryptCredentials(identificador: string, numeroDocumento: string): string {
  try {
    // Pegar últimos 3 dígitos do documento como senha (somente números)
    const apenasNumeros = numeroDocumento.replace(/\D/g, ''); // Remove tudo que não é dígito
    const senha = apenasNumeros.slice(-3);
    
    // Criar payload das credenciais
    const payload = {
      identificador,
      senha,
      timestamp: Date.now()
    };
    
    // Criptografar usando AES-256-CBC
    const iv = crypto.randomBytes(16); // 16 bytes = 128 bits
    const cipher = crypto.createCipheriv(ALGORITHM, KEY, iv);
    
    // CRÍTICO: IV deve ser convertido para HEX (não base64)
    const ivHex = iv.toString('hex');
    
    if (ivHex.length !== 32) {
      throw new Error(`IV hex tem tamanho inválido: ${ivHex.length}, esperado: 32`);
    }
    
    // CRÍTICO: Dados criptografados também em HEX (não base64)
    let encrypted = cipher.update(JSON.stringify(payload), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // IMPORTANTE: Concatenar IV(hex) + ':' + dados(hex)
    const combined = ivHex + ':' + encrypted;
    
    // Converter combined string para base64url
    const base64 = Buffer.from(combined, 'utf8').toString('base64');
    const result = base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    
    // TESTE: Verificar se pode decodificar corretamente
    const testDecode = Buffer.from(base64, 'base64').toString('utf8');
    const [testIV, testEncrypted] = testDecode.split(':');
    
    if (testIV?.length !== 32) {
      throw new Error(`ERRO: IV decodificado tem tamanho ${testIV?.length}, esperado: 32`);
    }
    
    return result;
    
  } catch (error) {
    throw new Error('Falha na criptografia das credenciais: ' + (error as Error).message);
  }
}

/**
 * Gera link de auto-login para o cliente (USANDO MESMA LÓGICA DO SCRIPT)
 */
async function gerarLinkAutoLogin(cliente: any): Promise<string | null> {
  try {
    // Verificar se cliente já tem link válido
    const existingShortUrl = await prisma.shortUrl.findFirst({
      where: { 
        clienteId: cliente.id,
        isActive: true
      }
    });
    
    if (existingShortUrl && cliente.autoLoginUrl) {
      return cliente.autoLoginUrl;
    }
    
    // Validar se tem numeroDocumento
    if (!cliente.numeroDocumento) {
      throw new Error('Cliente não possui número de documento para gerar senha');
    }
    
    // Gerar token criptografado (IGUAL AO SCRIPT)
    const longToken = encryptCredentials(cliente.identificador, cliente.numeroDocumento);
    
    // Gerar código curto único (IGUAL AO SCRIPT)
    const shortCode = await generateUniqueShortCode();
    
    // URL final
    const autoLoginUrl = `${BASE_URL}/${shortCode}`;
    
    if (existingShortUrl) {
      // Atualizar link existente
      await prisma.shortUrl.update({
        where: { id: existingShortUrl.id },
        data: {
          shortCode,
          longToken,
          usageCount: 0,
          isActive: true
        }
      });
    } else {
      // Criar novo link
      await prisma.shortUrl.create({
        data: {
          shortCode,
          longToken,
          clienteId: cliente.id,
          expiresAt: null, // Links permanentes
          usageCount: 0,
          isActive: true
        }
      });
    }
    
    // Atualizar cliente com o link
    await prisma.cliente.update({
      where: { id: cliente.id },
      data: { autoLoginUrl }
    });
    
    return autoLoginUrl;
    
  } catch (error) {
    throw error;
  }
}

// Função para reformatar número (copiada do script atualizarCamposChatguru.ts)
function reformatChatNumber(chatNumber: string): string {
  if (!chatNumber || typeof chatNumber !== "string") {
    throw new Error("Número de chat inválido");
  }

  const countryCode = chatNumber.slice(0, 2); // Código do país
  const areaCode = chatNumber.slice(2, 4); // Código de área
  let phoneNumber = chatNumber.slice(4);

  if (phoneNumber.length === 9) {
    phoneNumber = phoneNumber.slice(1);
  } else if (phoneNumber.length === 8) {
    phoneNumber = "9" + phoneNumber;
  }

  return countryCode + areaCode + phoneNumber;
}

// Função para atualizar MÚLTIPLOS campos de uma só vez (SEM EXECUÇÃO DE DIÁLOGO)
async function updateMultipleChatGuruFields(
  chatNumber: string,
  fields: Record<string, string>,
  executeDialog: boolean = false // 🆕 Flag para controlar execução do diálogo
): Promise<any> {
  console.log(`[CHATGURU] Atualizando campos: ${Object.keys(fields).join(', ')} | Tel: ${chatNumber.substring(0, 6)}****`);

  // Remover caracteres especiais e espaços
  let cleanNumber = chatNumber.replace(/\D/g, "");
  
  // Garantir que o número tenha o formato correto (*************)
  if (!cleanNumber.startsWith("55")) {
    cleanNumber = "55" + cleanNumber;
  }

  const key = process.env.CHATGURU_API_KEY;
  const accountId = process.env.CHATGURU_ACCOUNT_ID;
  const phoneId = process.env.CHATGURU_PHONE_ID;
  const action = "chat_update_custom_fields";

  if (!key || !accountId || !phoneId) {
    throw new Error("Credenciais do Chatguru não configuradas");
  }

  // Função para executar diálogo (separada e controlada)
  const executeDialogFunc = async (formattedNumber: string, retryCount: number = 0): Promise<void> => {
    try {
      console.log(`[CHATGURU-DIALOGO] Executando para: ${formattedNumber.substring(0, 6)}****`);
      
      const dialogId = "663e65dfa00a441168e435f4";
      const dialogAction = "dialog_execute";
      
      const chatGuruData = {
        key,
        account_id: accountId,
        phone_id: phoneId,
        dialog_id: dialogId,
        chat_number: formattedNumber,
        action: dialogAction,
      };

      const formData = querystring.stringify(chatGuruData);
      
      const response = await axios.post(
        process.env.CHATGURU_API_ENDPOINT || "https://s16.chatguru.app/api/v1",
        formData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      console.log(`[CHATGURU-DIALOGO] Executado com sucesso: ${response.status}`);

    } catch (error: any) {
      if (error.response && error.response.status === 400 && retryCount < 2) {
        console.log(`[CHATGURU-DIALOGO] Reformatando número (tentativa ${retryCount + 1})`);
        const reformattedChatNumber = reformatChatNumber(formattedNumber);
        if (reformattedChatNumber !== formattedNumber) {
          return await executeDialogFunc(reformattedChatNumber, retryCount + 1);
        }
      }
      console.log(`[CHATGURU-DIALOGO] Erro: ${error.response?.data || error.message}`);
      // Não quebrar o fluxo principal se o diálogo falhar
    }
  };

  const sendRequest = async (formattedNumber: string, retryCount: number = 0): Promise<any> => {
    try {
      const chatGuruData: any = {
        key,
        account_id: accountId,
        phone_id: phoneId,
        chat_number: formattedNumber,
        action,
      };
      
      // Adicionar todos os campos dinamicamente
      Object.entries(fields).forEach(([fieldName, fieldValue]) => {
        chatGuruData[`field__${fieldName}`] = fieldValue;
      });

      const formData = querystring.stringify(chatGuruData);
      
      const response = await axios.post(
        process.env.CHATGURU_API_ENDPOINT || "https://s16.chatguru.app/api/v1",
        formData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      console.log(`[CHATGURU] Campos atualizados com sucesso: ${response.status}`);

      // 🆕 Executar diálogo APENAS se solicitado
      if (executeDialog) {
        console.log(`[CHATGURU] Aguardando 2s antes do diálogo...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
        await executeDialogFunc(formattedNumber, retryCount);
      }

      return response.data;
    } catch (error: any) {
      if (error.response && error.response.status === 400 && retryCount < 2) {
        console.log(`[CHATGURU] Reformatando número (tentativa ${retryCount + 1})`);
        const reformattedChatNumber = reformatChatNumber(formattedNumber);
        if (reformattedChatNumber !== formattedNumber) {
          return await sendRequest(reformattedChatNumber, retryCount + 1);
        }
      }
      console.log(`[CHATGURU] Erro: ${error.response?.data || error.message}`);
      throw error;
    }
  };

  return await sendRequest(cleanNumber);
}

function extrairUltimos3Digitos(documento: string): string {
  const apenasNumeros = documento.replace(/\D/g, '');
  return apenasNumeros.slice(-3);
}

function extrairShortCodeDaUrl(url: string): string {
  if (!url) return '';
  
  // Extrair apenas o código após a última barra
  // Ex: "https://cliente.registre.se/Kk0jjB" → "Kk0jjB"
  const shortCode = url.split('/').pop() || '';
  return shortCode;
}

function obterListaTelefones(contatos: any[]): string[] {
  const telefones: string[] = [];
  
  for (const contato of contatos) {
    if (contato.telefone) {
      telefones.push(contato.telefone);
    }
    if (contato.telefoneSegundario) {
      telefones.push(contato.telefoneSegundario);
    }
  }
  
  // Remover duplicatas e números inválidos
  return telefones.filter((telefone, index, arr) => 
    arr.indexOf(telefone) === index && telefone.replace(/\D/g, '').length >= 10
  );
}

// Função para buscar persons por telefone no CRM
async function buscarPersonsPorTelefone(telefone: string): Promise<number[]> {
  try {
    const crmToken = process.env.CRM_TOKEN;
    if (!crmToken) {
      throw new Error("CRM_TOKEN não configurada");
    }

    // Normalizar telefone para busca (remover caracteres especiais)
    const telefoneNormalizado = telefone.replace(/\D/g, '');

    const url = `https://api.pipe.run/v1/contactPhones?show=200&phone=${telefoneNormalizado}&cursor=1`;
    
    const response = await axios.get(url, {
      headers: {
        token: crmToken,
      },
      timeout: 15000,
    });

    if (!response.data.success) {
      throw new Error(`Erro da API: ${response.data.message}`);
    }

    // Extrair person_ids únicos
    const personIds = new Set<number>();
    
    for (const contact of response.data.data) {
      if (contact.person_id) {
        personIds.add(contact.person_id);
      }
    }

    return Array.from(personIds);

  } catch (error: any) {
    return [];
  }
}

// Função para atualizar campo personalizado de uma person
async function atualizarCampoPersonalizado(personId: number, linkEncurtado: string): Promise<boolean> {
  try {
    const crmToken = process.env.CRM_TOKEN;
    if (!crmToken) {
      throw new Error("CRM_TOKEN não configurada");
    }

    const url = `https://api.pipe.run/v1/persons/${personId}`;
    
    const payload = {
      custom_fields: [
        {
          id: 715633,
          value: linkEncurtado
        }
      ]
    };

    const response = await axios.put(url, payload, {
      headers: {
        'accept': 'application/json',
        'content-type': 'application/json',
        'token': crmToken,
      },
      timeout: 15000,
    });
    
    if (response.status === 200 || response.status === 201) {
      return true;
    } else {
      return false;
    }

  } catch (error: any) {
    return false;
  }
}

// Função principal para atualizar todas as persons relacionadas aos telefones do cliente
async function atualizarPersonsRelacionadas(cliente: any): Promise<{
  sucesso: boolean;
  telefonesProcesados: number;
  personsAtualizadas: number;
  personsComErro: number;
}> {
  try {
    // 1. Obter telefones do cliente
    const contatos = await prisma.contatoCliente.findMany({
      where: { clienteId: cliente.id }
    });
    
    const telefones = obterListaTelefones(contatos);
    
    if (telefones.length === 0) {
      return { sucesso: true, telefonesProcesados: 0, personsAtualizadas: 0, personsComErro: 0 };
    }

    // 2. Extrair link encurtado
    const linkEncurtado = cliente.autoLoginUrl || '';
    
    if (!linkEncurtado) {
      return { sucesso: true, telefonesProcesados: 0, personsAtualizadas: 0, personsComErro: 0 };
    }

    // 3. Coletar todas as persons únicas de todos os telefones
    const todasPersonsUnicas = new Set<number>();
    let telefonesProcesados = 0;

    for (const telefone of telefones) {
      const personIds = await buscarPersonsPorTelefone(telefone);
      
      personIds.forEach(id => todasPersonsUnicas.add(id));
      telefonesProcesados++;

      // Pequena pausa entre buscas para não sobrecarregar API
      if (telefonesProcesados < telefones.length) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    const personsParaAtualizar = Array.from(todasPersonsUnicas);

    if (personsParaAtualizar.length === 0) {
      return { sucesso: true, telefonesProcesados, personsAtualizadas: 0, personsComErro: 0 };
    }

    // 4. Atualizar cada person
    let personsAtualizadas = 0;
    let personsComErro = 0;

    for (let i = 0; i < personsParaAtualizar.length; i++) {
      const personId = personsParaAtualizar[i];
      
      const sucesso = await atualizarCampoPersonalizado(personId, linkEncurtado);
      if (sucesso) {
        personsAtualizadas++;
      } else {
        personsComErro++;
      }

      // Pausa entre atualizações para não sobrecarregar API
      if (i < personsParaAtualizar.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 800));
      }
    }

    return {
      sucesso: true,
      telefonesProcesados,
      personsAtualizadas,
      personsComErro
    };

  } catch (error: any) {
    return { sucesso: false, telefonesProcesados: 0, personsAtualizadas: 0, personsComErro: 0 };
  }
}

/**
 * Busca e concatena os nomes das marcas de todos os processos do cliente
 */
async function obterNomesMarcasCliente(clienteId: number): Promise<string> {
  try {
    // Buscar todos os processos do cliente com suas marcas
    const processos = await prisma.processo.findMany({
      where: { 
        clienteId: clienteId 
      },
      include: {
        marca: true
      }
    });
    
    // Extrair nomes das marcas que têm nome preenchido
    const nomesMarcas: string[] = [];
    
      for (const processo of processos) {
    if (processo.marca) {
      // 🔧 DEBUG: Log do nome como vem do banco
      console.log(`[DEBUG-BANCO] Processo ${processo.numero}: Nome DB="${processo.marca.nome}", Apresentação="${processo.marca.apresentacao}"`);
      
      // 🔧 USAR FUNÇÃO AUXILIAR: Gerar nome apropriado para exibição
      const nomeMarca = gerarNomeMarcaParaExibicao(
        processo.marca.nome, 
        processo.numero, 
        processo.marca.apresentacao
      );
      
      console.log(`[DEBUG-BANCO] Nome para exibição: "${nomeMarca}"`);
      
      if (nomeMarca && !nomesMarcas.includes(nomeMarca)) {
        nomesMarcas.push(nomeMarca);
      }
    }
  }
    
    // Concatenar nomes das marcas com " / "
    const resultado = nomesMarcas.length > 0 ? nomesMarcas.join(' / ') : '';
    
    return resultado;
    
  } catch (error) {
    return ''; // Retornar string vazia em caso de erro
  }
}

/**
 * Atualiza campos no ChatGuru (versão corrigida usando a API real do ChatGuru)
 */
async function atualizarChatGuru(cliente: any, marcasDoGrupo?: string[], executarDialogo: boolean = false): Promise<{ sucesso: boolean, telefone?: string }> {
  console.log(`[CHATGURU] Iniciando para cliente: ${cliente.nome} (ID: ${cliente.id})`);
  
  // Buscar telefones do cliente
  const contatos = await prisma.contatoCliente.findMany({
    where: { clienteId: cliente.id }
  });
  
  // Obter lista de telefones válidos
  const telefones = obterListaTelefones(contatos);
  
  if (telefones.length === 0) {
    throw new Error('Nenhum telefone válido disponível para atualizar ChatGuru');
  }

  console.log(`[CHATGURU] Telefones disponíveis: ${telefones.length}`);

  // Preparar dados dos campos - TODOS DE UMA VEZ
  const senhaCliente = cliente.numeroDocumento ? 
    extrairUltimos3Digitos(cliente.numeroDocumento) : 
    '123';
  
  // 🆕 Usar marcas específicas do grupo
  let nomesMarcas: string;
  if (marcasDoGrupo && marcasDoGrupo.length > 0) {
    // Remover duplicatas e juntar com " / "
    const marcasUnicas = [...new Set(marcasDoGrupo.filter(marca => marca && marca.trim()))];
    nomesMarcas = marcasUnicas.join(' / ');
    console.log(`[CHATGURU] Marcas do grupo: ${nomesMarcas}`);
  } else {
    // Fallback: buscar marcas de todos os processos do cliente
    nomesMarcas = await obterNomesMarcasCliente(cliente.id);
    if (!nomesMarcas) {
      nomesMarcas = "-";
    }
    console.log(`[CHATGURU] Marcas do cliente (fallback): ${nomesMarcas}`);
  }
  
  const camposParaAtualizar = {
    IDC: cliente.identificador,
    Senha_Cliente: senhaCliente,
    Link_Cliente: extrairShortCodeDaUrl(cliente.autoLoginUrl || ''),
    Nome_da_marca: nomesMarcas
  };

  console.log(`[CHATGURU] Campos: IDC=${cliente.identificador}, Senha=***, Link=${extrairShortCodeDaUrl(cliente.autoLoginUrl || '')}, Marcas=${nomesMarcas}`);

  // Tentar atualizar campos para cada telefone até conseguir
  for (let i = 0; i < telefones.length; i++) {
    const telefone = telefones[i];
    console.log(`[CHATGURU] Tentativa ${i + 1}/${telefones.length}: ${telefone.substring(0, 6)}****`);
    
    try {
      // 🆕 Passar flag de execução de diálogo
      await updateMultipleChatGuruFields(telefone, camposParaAtualizar, executarDialogo);
      
      // Se chegou aqui, todos os campos foram atualizados com sucesso
      console.log(`[CHATGURU] Sucesso em ${telefone.substring(0, 6)}**** | Diálogo: ${executarDialogo ? 'SIM' : 'NAO'}`);
      return { sucesso: true, telefone };
      
    } catch (error: any) {
      console.log(`[CHATGURU] Falha em ${telefone.substring(0, 6)}****: ${error.message}`);
      
      // Se não é o último telefone, continuar tentando
      if (i < telefones.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
  }
  
  throw new Error(`Falha ao atualizar ChatGuru em todos os ${telefones.length} telefones`);
}

/**
 * Processa um protocolo da fila
 */
export async function processarProtocolo(protocolo: any): Promise<void> {
  console.log(`🚀 Iniciando processamento do protocolo do processo: ${protocolo.numeroProcesso}`);
  
  try {
    // 1. Atualizar status para PROCESSANDO
    await prisma.processamentoProtocolo.update({
      where: { id: protocolo.id },
      data: { 
        status: 'PROCESSANDO',
        tentativas: protocolo.tentativas + 1
      }
    });
    
    // 2. Buscar dados no CRM
    let dadosCrm: DadosCrm | null = null;
    try {
      dadosCrm = await buscarDadosCrm(protocolo.numeroProcesso);
      
      if (!dadosCrm) {
        throw new Error('Protocolo não encontrado no CRM');
      }
      
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: {
          crmDealId: dadosCrm.id,
          crmPersonId: dadosCrm.person?.id,
          crmCompanyId: dadosCrm.company?.id,
          dadosCrm: dadosCrm as any
        }
      });
      
    } catch (error) {
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: { 
          status: 'FALHA_CRM',
          ultimoErro: error instanceof Error ? error.message : 'Erro no CRM'
        }
      });
      throw error;
    }
    
    // 3. Criar/atualizar cliente
    let cliente: any;
    let clienteCriado = false;
    let clienteAtualizado = false;
    
    try {
      const resultado = await criarOuAtualizarCliente(dadosCrm);
      cliente = resultado.cliente;
      clienteCriado = resultado.criado;
      clienteAtualizado = resultado.atualizado;
      
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: {
          clienteId: cliente.id,
          clienteCriado,
          clienteAtualizado
        }
      });
      
    } catch (error) {
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: { 
          status: 'FALHA_CLIENTE',
          ultimoErro: error instanceof Error ? error.message : 'Erro ao criar cliente'
        }
      });
      throw error;
    }
    
    // 4. Conectar cliente ao processo existente
    let processo: any;
    try {
      processo = await conectarClienteAoProcesso(
        cliente.id, 
        protocolo.numeroProcesso,
        dadosCrm, // Dados do CRM para análise caso o processo não exista
        protocolo.dadosTitulares, // Dados dos titulares já extraídos do PDF
        protocolo.elementoNominativo // Elemento nominativo do protocolo para determinação da apresentação
      );
      
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: {
          processoId: processo.id,
          processoVinculado: true
        }
      });
      
    } catch (error) {
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: { 
          status: 'FALHA_PROCESSO',
          ultimoErro: error instanceof Error ? error.message : 'Erro ao criar processo'
        }
      });
      throw error;
    }
    
    // 5. Gerar link de auto-login
    let linkGerado = false;
    let autoLoginUrl: string | null = null;
    
    try {
      autoLoginUrl = await gerarLinkAutoLogin(cliente);
      
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: {
          linkGerado: !!autoLoginUrl,
          autoLoginUrl
        }
      });
      
      linkGerado = !!autoLoginUrl;
      
      // IMPORTANTE: Atualizar o objeto cliente com o link gerado
      if (autoLoginUrl) {
        cliente.autoLoginUrl = autoLoginUrl;
        console.log(`🔄 Cliente atualizado com autoLoginUrl: ${autoLoginUrl}`);
      }
      
    } catch (error) {
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: { 
          status: 'FALHA_LINK',
          ultimoErro: error instanceof Error ? error.message : 'Erro ao gerar link'
        }
      });
      throw error;
    }
    
    // 6. Atualizar ChatGuru
    let chatguruAtualizado = false;
    let telefoneChatguru: string | undefined;
    
    try {
      const resultado = await atualizarChatGuru(cliente);
      chatguruAtualizado = resultado.sucesso;
      telefoneChatguru = resultado.telefone;
      
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: {
          chatguruAtualizado,
          telefoneChatguru
        }
      });
      
    } catch (error) {
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: { 
          status: 'FALHA_CHATGURU',
          ultimoErro: error instanceof Error ? error.message : 'Erro no ChatGuru'
        }
      });
      throw error;
    }

    // 6.5. Atualizar persons relacionadas no CRM
    try {
      console.log('🔄 Iniciando atualização de persons relacionadas no CRM...');
      
      const resultadoPersons = await atualizarPersonsRelacionadas(cliente);
      const personsAtualizadas = resultadoPersons.personsAtualizadas;
      const personsComErro = resultadoPersons.personsComErro;
      
      console.log(`✅ Persons CRM: ${personsAtualizadas} atualizadas, ${personsComErro} com erro`);
      
    } catch (error) {
      console.error('❌ Erro na atualização de persons do CRM:', error);
      // Não quebrar o fluxo principal, apenas logar o erro
    }

    // 6.6. Atualizar status da oportunidade no CRM para "Ganha"
    if (dadosCrm?.id) {
      try {
        console.log(`🎯 Atualizando oportunidade ${dadosCrm.id} no CRM para status "Ganha"...`);
        
        // Importar funções do CRM
        const { getCRMConfiguration, updateDealStatus } = await import('../utils/crm.utils');
        
        // Obter configuração do CRM
        const { CRM_API_URL, CRM_TOKEN } = await getCRMConfiguration();
        
        // Atualizar status para "Ganha"
        await updateDealStatus(dadosCrm.id.toString(), CRM_API_URL, CRM_TOKEN);
        
        console.log(`✅ Oportunidade ${dadosCrm.id} marcada como "Ganha" no CRM`);
        
      } catch (crmError) {
        console.error(`❌ Erro ao atualizar status no CRM:`, crmError);
        // Não quebrar o fluxo, apenas logar o erro
        // O processamento continua mesmo se a atualização do CRM falhar
      }
    } else {
      console.log(`ℹ️ Nenhum ID de oportunidade CRM disponível para atualizar`);
    }
    
    // 7. Finalizar com sucesso
    await prisma.processamentoProtocolo.update({
      where: { id: protocolo.id },
      data: {
        status: 'SUCESSO',
        processadoEm: new Date(),
        ultimoErro: null
      }
    });
    
    console.log(`✅ Protocolo ${protocolo.numeroProcesso} processado com sucesso!`);
    
  } catch (error) {
    console.error(`❌ Erro no processamento do protocolo ${protocolo.numeroProcesso}:`, error);
    
    // Se não foi um erro específico (CRM, Cliente, etc), marcar como falha geral
    const protocoloAtual = await prisma.processamentoProtocolo.findUnique({
      where: { id: protocolo.id }
    });
    
    if (protocoloAtual?.status === 'PROCESSANDO') {
      await prisma.processamentoProtocolo.update({
        where: { id: protocolo.id },
        data: { 
          status: 'FALHA_GERAL',
          ultimoErro: error instanceof Error ? error.message : 'Erro desconhecido'
        }
      });
    }
    
    throw error;
  }
}

/**
 * Job principal que processa protocolos pendentes
 */
export async function processarFilaProtocolos(): Promise<void> {
  console.log('🔄 Iniciando job de processamento de protocolos...');
  
  try {
    // Buscar protocolos pendentes ou com falha (para retry)
    const agora = new Date();
    const protocolos = await prisma.processamentoProtocolo.findMany({
      where: {
        OR: [
          { status: 'PENDENTE' },
          {
            AND: [
              { status: { in: ['FALHA_CRM', 'FALHA_CLIENTE', 'FALHA_PROCESSO', 'FALHA_LINK', 'FALHA_CHATGURU', 'FALHA_GERAL'] } },
              { tentativas: { lt: 3 } },
              {
                OR: [
                  { proximaTentativa: { lte: agora } },
                  { proximaTentativa: null }
                ]
              }
            ]
          }
        ]
      },
      orderBy: { criadoEm: 'asc' },
      take: 50 // Processar máximo 50 por vez
    });
    
    if (protocolos.length === 0) {
      console.log('📋 Nenhum protocolo pendente encontrado');
      return;
    }
    
    console.log(`📋 Encontrados ${protocolos.length} protocolos para processar`);
    
    // Processar cada protocolo
    for (const protocolo of protocolos) {
      try {
        await processarProtocolo(protocolo);
        
        // Delay entre processamentos para não sobrecarregar APIs
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        // Definir próxima tentativa se ainda pode tentar
        if (protocolo.tentativas < 2) {
          const proximaTentativa = new Date();
          proximaTentativa.setMinutes(proximaTentativa.getMinutes() + (protocolo.tentativas + 1) * 5);
          
          await prisma.processamentoProtocolo.update({
            where: { id: protocolo.id },
            data: { proximaTentativa }
          });
        }
        
        console.error(`❌ Falha no protocolo ${protocolo.numeroProcesso}:`, error);
        continue;
      }
    }
    
    console.log('✅ Job de processamento concluído');
    
  } catch (error) {
    console.error('❌ Erro no job de processamento:', error);
  }
}

// ==================================================================================
// 🆕 NOVAS FUNCIONALIDADES: AGRUPAMENTO E PROCESSAMENTO OTIMIZADO POR CLIENTE
// ==================================================================================

/**
 * Interface para grupo de cliente
 */
interface GrupoCliente {
  clienteKey: string;
  clienteInfo: {
    tipo: 'person' | 'company';
    id: number;
    nome: string;
  };
  protocolos: any[];
  dadosCrm: DadosCrm;
}

/**
 * Interface para resultado do processamento de grupo
 */
interface ResultadoProcessamentoGrupo {
  clienteKey: string;
  clienteNome: string;
  protocolosTotal: number;
  protocolosProcessados: number;
  protocolosComErro: number;
  clienteCriado: boolean;
  clienteAtualizado: boolean;
  linkGerado: boolean;
  chatguruAtualizado: boolean;
  personsAtualizadas: number;
  marcasEncontradas: string[];
  erros: string[];
  sucesso: boolean;
  tempoProcessamento: number;
  // 🆕 Dados para consolidação ChatGuru
  dadosChatGuru?: DadosChatGuruCliente;
}

// 🆕 Interfaces para consolidação ChatGuru por telefone
interface DadosChatGuruCliente {
  clienteId: number;
  clienteNome: string;
  identificador: string;
  senha: string;
  link: string;
  telefones: string[];
  marcas: string[];
}

interface ConsolidacaoChatGuru {
  telefone: string;
  clientes: DadosChatGuruCliente[];
  marcasConsolidadas: string[];
  camposConsolidados: {
    IDC: string; // Usar o primeiro cliente como referência
    Senha_Cliente: string;
    Link_Cliente: string;
    Nome_da_marca: string;
  };
}

/**
 * Consolida dados ChatGuru por telefone único
 */
function consolidarChatGuruPorTelefone(resultados: ResultadoProcessamentoGrupo[]): Map<string, ConsolidacaoChatGuru> {
  console.log(`\n[CHATGURU-CONSOLIDACAO] ========== INICIANDO CONSOLIDAÇÃO ==========`);
  
  const consolidacao = new Map<string, ConsolidacaoChatGuru>();
  
  // Coletar todos os dados ChatGuru dos grupos processados
  for (const resultado of resultados) {
    if (!resultado.dadosChatGuru || !resultado.sucesso) {
      continue;
    }
    
    const dadosCliente = resultado.dadosChatGuru;
    console.log(`[CHATGURU-CONSOLIDACAO] Processando cliente: ${dadosCliente.clienteNome}`);
    console.log(`[CHATGURU-CONSOLIDACAO] Telefones: ${dadosCliente.telefones.join(', ')}`);
    console.log(`[CHATGURU-CONSOLIDACAO] Marcas: ${dadosCliente.marcas.join(', ')}`);
    
    // Para cada telefone do cliente
    for (const telefone of dadosCliente.telefones) {
      const telefoneNormalizado = telefone.replace(/\D/g, '');
      
      if (!consolidacao.has(telefoneNormalizado)) {
        // Primeiro cliente com este telefone
        consolidacao.set(telefoneNormalizado, {
          telefone: telefoneNormalizado,
          clientes: [dadosCliente],
          marcasConsolidadas: [...dadosCliente.marcas],
          camposConsolidados: {
            IDC: dadosCliente.identificador,
            Senha_Cliente: dadosCliente.senha,
            Link_Cliente: dadosCliente.link,
            Nome_da_marca: dadosCliente.marcas.join(' / ')
          }
        });
        
        console.log(`[CHATGURU-CONSOLIDACAO] Novo telefone ${telefoneNormalizado.substring(0, 6)}**** - Cliente: ${dadosCliente.clienteNome}`);
      } else {
        // Adicionar cliente ao telefone existente
        const consolidacaoExistente = consolidacao.get(telefoneNormalizado)!;
        consolidacaoExistente.clientes.push(dadosCliente);
        
        // Consolidar marcas (remover duplicatas)
        const marcasAntes = consolidacaoExistente.marcasConsolidadas.length;
        const novasMarcas = dadosCliente.marcas.filter(marca => 
          !consolidacaoExistente.marcasConsolidadas.includes(marca)
        );
        consolidacaoExistente.marcasConsolidadas.push(...novasMarcas);
        
        // Atualizar campo Nome_da_marca
        consolidacaoExistente.camposConsolidados.Nome_da_marca = 
          consolidacaoExistente.marcasConsolidadas.join(' / ');
        
        console.log(`[CHATGURU-CONSOLIDACAO] Telefone ${telefoneNormalizado.substring(0, 6)}**** já existe - Adicionando cliente: ${dadosCliente.clienteNome}`);
        console.log(`[CHATGURU-CONSOLIDACAO] Marcas consolidadas: ${marcasAntes} → ${consolidacaoExistente.marcasConsolidadas.length}`);
      }
    }
  }
  
  // Relatório da consolidação
  console.log(`[CHATGURU-CONSOLIDACAO] ========== RESUMO ==========`);
  console.log(`[CHATGURU-CONSOLIDACAO] Telefones únicos: ${consolidacao.size}`);
  
  for (const [telefone, dados] of consolidacao) {
    const clientesNomes = dados.clientes.map(c => c.clienteNome).join(', ');
    console.log(`[CHATGURU-CONSOLIDACAO] ${telefone.substring(0, 6)}****: ${dados.clientes.length} cliente(s) [${clientesNomes}]`);
    console.log(`[CHATGURU-CONSOLIDACAO] Marcas finais: ${dados.marcasConsolidadas.join(' / ')}`);
  }
  
  console.log(`[CHATGURU-CONSOLIDACAO] ========== FIM ==========\n`);
  
  return consolidacao;
}

/**
 * Registra comunicado de protocolo no banco após ChatGuru bem-sucedido
 */
async function registrarComunicadoProtocolo(dados: ConsolidacaoChatGuru, protocolosIds: string[]): Promise<void> {
  console.log(`[COMUNICADO] ========== REGISTRANDO COMUNICADO DE PROTOCOLO ==========`);
  console.log(`[COMUNICADO] IDs dos protocolos desta execução: ${protocolosIds.join(', ')}`);
  
  try {
    // Para cada cliente no telefone consolidado
    for (const cliente of dados.clientes) {
      console.log(`[COMUNICADO] Processando cliente: ${cliente.clienteNome} (ID: ${cliente.clienteId})`);
      
      // 🆕 Buscar protocolos desta execução específica
      let whereClause: any = {
        clienteId: cliente.clienteId,
        status: 'SUCESSO',
        chatguruAtualizado: true
      };

      if (protocolosIds.length > 0) {
        // 🎯 Se temos IDs específicos, usar apenas eles
        whereClause.id = { in: protocolosIds };
        console.log(`[COMUNICADO] Usando IDs específicos: ${protocolosIds.length} protocolos`);
      } else {
        // 📅 Fallback: buscar protocolos processados recentemente (últimos 10 minutos)
        whereClause.processadoEm = {
          gte: new Date(Date.now() - 10 * 60 * 1000) // 10 minutos
        };
        console.log(`[COMUNICADO] Usando filtro temporal: últimos 10 minutos`);
      }

      const protocolosProcessados = await prisma.processamentoProtocolo.findMany({
        where: whereClause,
        include: {
          processo: {
            include: {
              marca: {
                select: {
                  id: true,
                  nome: true
                }
              }
            }
          }
        }
      });
      
      if (protocolosProcessados.length === 0) {
        console.log(`[COMUNICADO] Nenhum protocolo recente encontrado para ${cliente.clienteNome}`);
        continue;
      }
      
      // Extrair IDs únicos de processos e marcas
      const processosIds: string[] = [];
      const marcasIds: string[] = [];
      const marcasNomes: string[] = [];
      
      for (const protocolo of protocolosProcessados) {
        if (protocolo.processo?.id && !processosIds.includes(protocolo.processo.id)) {
          processosIds.push(protocolo.processo.id);
        }
        
        if (protocolo.processo?.marca?.id && !marcasIds.includes(protocolo.processo.marca.id)) {
          marcasIds.push(protocolo.processo.marca.id);
          marcasNomes.push(protocolo.processo.marca.nome || 'Marca sem nome');
        }
      }
      
      console.log(`[COMUNICADO] Protocolos: ${protocolosProcessados.length}`);
      console.log(`[COMUNICADO] Processos únicos: ${processosIds.length}`);
      console.log(`[COMUNICADO] Marcas únicas: ${marcasIds.length} (${marcasNomes.join(', ')})`);
      
      // Gerar título dinâmico baseado na quantidade
      const titulo = protocolosProcessados.length === 1 
        ? `Protocolo registrado - ${marcasNomes[0] || 'Marca'}`
        : `${protocolosProcessados.length} protocolos registrados`;
      
      // Gerar descrição detalhada
      const descricao = protocolosProcessados.length === 1
        ? `Protocolo da marca "${marcasNomes[0]}" foi registrado com sucesso junto ao INPI.`
        : `${protocolosProcessados.length} protocolos das marcas ${formatarMarcasParaRD(marcasNomes)} foram registrados com sucesso junto ao INPI.`;
      
      // Gerar ID único para o comunicado
      const comunicadoId = `protocolo-${cliente.clienteId}-${Date.now()}`;
      
             // Criar comunicado
       await prisma.comunicado.create({
         data: {
           id: comunicadoId,
           tipo: 'PROTOCOLO_MARCA',
           titulo,
           descricao,
           clienteId: cliente.clienteId,
           processosIds,
           marcasIds,
           metodoComunicacao: 'WHATSAPP',
           status: 'ENVIADO',
           dataEnvio: new Date(),
           updatedAt: new Date(),
           dadosAdicionais: {
             telefone: dados.telefone,
             quantidadeProtocolos: protocolosProcessados.length,
             marcasConsolidadas: dados.marcasConsolidadas,
             protocolosIds: protocolosProcessados.map(p => p.id),
             chatguruConsolidado: true
           }
         }
       });
      
      console.log(`[COMUNICADO] ✅ Comunicado registrado: ${comunicadoId}`);
      console.log(`[COMUNICADO] Título: ${titulo}`);
      console.log(`[COMUNICADO] Processos: ${processosIds.length} | Marcas: ${marcasIds.length}`);
    }
    
  } catch (error) {
    console.log(`[COMUNICADO] ❌ Erro ao registrar comunicado: ${error}`);
    // Não quebrar o fluxo principal - apenas logar o erro
  }
  
  console.log(`[COMUNICADO] ========== FIM REGISTRO ==========\n`);
}

/**
 * Executa ChatGuru consolidado para todos os telefones únicos
 */
async function executarChatGuruConsolidado(consolidacao: Map<string, ConsolidacaoChatGuru>): Promise<{
  telefonesProcessados: number;
  telefonesComSucesso: number;
  telefonesComErro: number;
  detalhes: Array<{
    telefone: string;
    sucesso: boolean;
    clientesAfetados: string[];
    marcas: string[];
    erro?: string;
  }>;
}> {
  console.log(`[CHATGURU-EXECUCAO] ========== EXECUTANDO CHATGURU CONSOLIDADO ==========`);
  
  const resultado = {
    telefonesProcessados: 0,
    telefonesComSucesso: 0,
    telefonesComErro: 0,
    detalhes: [] as Array<{
      telefone: string;
      sucesso: boolean;
      clientesAfetados: string[];
      marcas: string[];
      erro?: string;
    }>
  };
  
  for (const [telefone, dados] of consolidacao) {
    resultado.telefonesProcessados++;
    
    const clientesAfetados = dados.clientes.map(c => c.clienteNome);
    
    console.log(`[CHATGURU-EXECUCAO] Telefone ${resultado.telefonesProcessados}/${consolidacao.size}: ${telefone.substring(0, 6)}****`);
    console.log(`[CHATGURU-EXECUCAO] Clientes afetados: ${clientesAfetados.join(', ')}`);
    console.log(`[CHATGURU-EXECUCAO] Marcas consolidadas: ${dados.marcasConsolidadas.join(' / ')}`);
    
    try {
      // Executar ChatGuru com dados consolidados
      await updateMultipleChatGuruFields(
        telefone, 
        dados.camposConsolidados, 
        true // Executar diálogo
      );
      
      resultado.telefonesComSucesso++;
      resultado.detalhes.push({
        telefone,
        sucesso: true,
        clientesAfetados,
        marcas: dados.marcasConsolidadas
      });
      
      console.log(`[CHATGURU-EXECUCAO] ✅ Sucesso para ${telefone.substring(0, 6)}**** | Clientes: ${clientesAfetados.length} | Marcas: ${dados.marcasConsolidadas.length}`);
      
      // 🆕 REGISTRAR COMUNICADO DE PROTOCOLO
      try {
        // TODO: Passar IDs específicos dos protocolos desta execução
        // Por enquanto, busca será baseada no cliente + status + tempo
        await registrarComunicadoProtocolo(dados, []);
      } catch (comunicadoError) {
        console.log(`[CHATGURU-EXECUCAO] ⚠️ Erro ao registrar comunicado: ${comunicadoError}`);
        // Não quebrar o fluxo principal
      }
      
    } catch (error) {
      resultado.telefonesComErro++;
      resultado.detalhes.push({
        telefone,
        sucesso: false,
        clientesAfetados,
        marcas: dados.marcasConsolidadas,
        erro: error instanceof Error ? error.message : String(error)
      });
      
      console.log(`[CHATGURU-EXECUCAO] ❌ Erro para ${telefone.substring(0, 6)}****: ${error}`);
    }
    
    // Delay entre execuções
    if (resultado.telefonesProcessados < consolidacao.size) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log(`[CHATGURU-EXECUCAO] ========== RESUMO FINAL ==========`);
  console.log(`[CHATGURU-EXECUCAO] Telefones processados: ${resultado.telefonesProcessados}`);
  console.log(`[CHATGURU-EXECUCAO] Sucessos: ${resultado.telefonesComSucesso}`);
  console.log(`[CHATGURU-EXECUCAO] Erros: ${resultado.telefonesComErro}`);
  console.log(`[CHATGURU-EXECUCAO] ========== FIM ==========\n`);
  
  return resultado;
}

/**
 * Formata lista de marcas para o RD Station Marketing
 * Exemplos:
 * - 1 marca: "PROTEON TECNOLOGIA"
 * - 2 marcas: "PROTEON TECNOLOGIA e SILITÁ EMPREENDIMENTOS"  
 * - 3 marcas: "PROTEON TECNOLOGIA, SILITÁ EMPREENDIMENTOS e AURIS ENERGIA"
 */
function formatarMarcasParaRD(marcas: string[]): string {
  if (marcas.length === 0) {
    return "";
  }
  
  if (marcas.length === 1) {
    return marcas[0];
  }
  
  if (marcas.length === 2) {
    return `${marcas[0]} e ${marcas[1]}`;
  }
  
  // 3 ou mais marcas: "Marca1, Marca2, Marca3 e MarcaN"
  const todasMenosUltima = marcas.slice(0, -1).join(', ');
  const ultimaMarca = marcas[marcas.length - 1];
  
  return `${todasMenosUltima} e ${ultimaMarca}`;
}

/**
 * Atualiza contato no RD Station Marketing
 */
async function atualizarContatoRDStation(
  email: string,
  linkCliente: string,
  idc: string,
  senhaCliente: string,
  marcas: string[]
): Promise<{ sucesso: boolean; erro?: string }> {
  try {
    console.log(`[RD-STATION] Atualizando contato: ${email}`);
    console.log(`[RD-STATION] Marcas: ${marcas.join(', ')}`);
    
    // Formatar marcas para RD Station
    const marcasFormatadas = formatarMarcasParaRD(marcas);
    const multiplasMarcas = marcas.length > 1 ? "Sim" : "Não";
    
    console.log(`[RD-STATION] Marcas formatadas: "${marcasFormatadas}"`);
    console.log(`[RD-STATION] Múltiplas marcas: ${multiplasMarcas}`);
    
    // Payload para RD Station
    const payload = {
      email: email,
      cf_link_cliente: linkCliente,
      cf_idc: idc,
      cf_senha_cliente: senhaCliente,
      cf_protocolo_realizado: "Sim",
      cf_protocolo_multiplas_marcas: multiplasMarcas,
      cf_protocolo_marcas: marcasFormatadas
    };
    
    console.log(`[RD-STATION] Payload:`, JSON.stringify(payload, null, 2));
    
    // Fazer requisição para RD Station
    const response = await axios.post(
      'https://rd.registrese.app.br/contacts/update-fields',
      payload,
      {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 15000
      }
    );
    
    console.log(`[RD-STATION] ✅ Sucesso para ${email}: ${response.status}`);
    return { sucesso: true };
    
  } catch (error: any) {
    const mensagemErro = error.response?.data?.message || error.message || 'Erro desconhecido';
    console.log(`[RD-STATION] ❌ Erro para ${email}: ${mensagemErro}`);
    return { sucesso: false, erro: mensagemErro };
  }
}

/**
 * Atualiza todos os contatos do RD Station após consolidação ChatGuru bem-sucedida
 */
async function atualizarRDStationConsolidado(consolidacao: Map<string, ConsolidacaoChatGuru>): Promise<{
  contatosProcessados: number;
  contatosComSucesso: number;
  contatosComErro: number;
  detalhes: Array<{
    email: string;
    clienteNome: string;
    sucesso: boolean;
    marcas: string[];
    erro?: string;
  }>;
}> {
  console.log(`[RD-STATION] ========== ATUALIZANDO CONTATOS RD STATION ==========`);
  
  const resultado = {
    contatosProcessados: 0,
    contatosComSucesso: 0,
    contatosComErro: 0,
    detalhes: [] as Array<{
      email: string;
      clienteNome: string;
      sucesso: boolean;
      marcas: string[];
      erro?: string;
    }>
  };
  
  // Coletar todos os clientes únicos e suas marcas consolidadas
  const clientesParaAtualizar = new Map<number, {
    cliente: DadosChatGuruCliente;
    marcasConsolidadas: string[];
  }>();
  
  // Consolidar marcas por cliente (mesmo cliente pode aparecer em múltiplos telefones)
  for (const [telefone, dados] of consolidacao) {
    for (const cliente of dados.clientes) {
      if (clientesParaAtualizar.has(cliente.clienteId)) {
        // Cliente já existe, consolidar marcas
        const existente = clientesParaAtualizar.get(cliente.clienteId)!;
        const novasMarcas = dados.marcasConsolidadas.filter(marca => 
          !existente.marcasConsolidadas.includes(marca)
        );
        existente.marcasConsolidadas.push(...novasMarcas);
      } else {
        // Novo cliente
        clientesParaAtualizar.set(cliente.clienteId, {
          cliente,
          marcasConsolidadas: [...dados.marcasConsolidadas]
        });
      }
    }
  }
  
  console.log(`[RD-STATION] Clientes únicos para atualizar: ${clientesParaAtualizar.size}`);
  
  // Atualizar cada cliente no RD Station
  for (const [clienteId, { cliente, marcasConsolidadas }] of clientesParaAtualizar) {
    resultado.contatosProcessados++;
    
    try {
      // Buscar email principal do cliente
      const contatos = await prisma.contatoCliente.findMany({
        where: { clienteId: clienteId },
        orderBy: { id: 'asc' } // Primeiro contato = principal
      });
      
      if (!contatos || contatos.length === 0 || !contatos[0].email) {
        resultado.contatosComErro++;
        resultado.detalhes.push({
          email: 'N/A',
          clienteNome: cliente.clienteNome,
          sucesso: false,
          marcas: marcasConsolidadas,
          erro: 'Email principal não encontrado'
        });
        
        console.log(`[RD-STATION] ⚠️ Cliente ${cliente.clienteNome}: Email não encontrado`);
        continue;
      }
      
      const emailPrincipal = contatos[0].email;
      const linkCompleto = `https://cliente.registre.se/${cliente.link}`;
      
      console.log(`[RD-STATION] Cliente ${resultado.contatosProcessados}/${clientesParaAtualizar.size}: ${cliente.clienteNome}`);
      console.log(`[RD-STATION] Email: ${emailPrincipal}`);
      console.log(`[RD-STATION] Link: ${linkCompleto}`);
      
      // Atualizar RD Station
      const resultadoRD = await atualizarContatoRDStation(
        emailPrincipal,
        linkCompleto,
        cliente.identificador,
        cliente.senha,
        marcasConsolidadas
      );
      
      if (resultadoRD.sucesso) {
        resultado.contatosComSucesso++;
        resultado.detalhes.push({
          email: emailPrincipal,
          clienteNome: cliente.clienteNome,
          sucesso: true,
          marcas: marcasConsolidadas
        });
      } else {
        resultado.contatosComErro++;
        resultado.detalhes.push({
          email: emailPrincipal,
          clienteNome: cliente.clienteNome,
          sucesso: false,
          marcas: marcasConsolidadas,
          erro: resultadoRD.erro
        });
      }
      
    } catch (error) {
      resultado.contatosComErro++;
      resultado.detalhes.push({
        email: 'N/A',
        clienteNome: cliente.clienteNome,
        sucesso: false,
        marcas: marcasConsolidadas,
        erro: error instanceof Error ? error.message : String(error)
      });
      
      console.log(`[RD-STATION] ❌ Erro no cliente ${cliente.clienteNome}: ${error}`);
    }
    
    // Delay entre atualizações
    if (resultado.contatosProcessados < clientesParaAtualizar.size) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log(`[RD-STATION] ========== RESUMO FINAL ==========`);
  console.log(`[RD-STATION] Contatos processados: ${resultado.contatosProcessados}`);
  console.log(`[RD-STATION] Sucessos: ${resultado.contatosComSucesso}`);
  console.log(`[RD-STATION] Erros: ${resultado.contatosComErro}`);
  console.log(`[RD-STATION] ========== FIM ==========\n`);
  
  return resultado;
}

/**
 * Agrupa protocolos por cliente baseado nos dados do CRM
 * REUTILIZA a função buscarDadosCrm() existente
 */
export async function agruparProtocolosPorCliente(protocolos: any[]): Promise<Map<string, GrupoCliente>> {
  console.log(`\n[AGRUPAMENTO] ========== INICIANDO COM ${protocolos.length} PROTOCOLOS ==========`);
  
  const grupos = new Map<string, GrupoCliente>();
  const protocolosSemCliente: any[] = [];
  
  for (let i = 0; i < protocolos.length; i++) {
    const protocolo = protocolos[i];
    console.log(`[AGRUPAMENTO] Analisando ${i + 1}/${protocolos.length}: ${protocolo.numeroProcesso}`);
    
    try {
      // REUTILIZAR função existente para buscar dados no CRM
      const dadosCrm = await buscarDadosCrm(protocolo.numeroProcesso);
      
      if (!dadosCrm) {
        console.log(`[AGRUPAMENTO] Protocolo ${protocolo.numeroProcesso} não encontrado no CRM - órfão`);
        protocolosSemCliente.push(protocolo);
        continue;
      }
      
      // Identificar cliente (person ou company)
      let clienteInfo: GrupoCliente['clienteInfo'];
      let clienteKey: string;
      
      if (dadosCrm.person) {
        clienteInfo = {
          tipo: 'person',
          id: dadosCrm.person.id,
          nome: dadosCrm.person.name
        };
        clienteKey = `person-${dadosCrm.person.id}`;
      } else if (dadosCrm.company) {
        clienteInfo = {
          tipo: 'company',
          id: dadosCrm.company.id,
          nome: dadosCrm.company.name
        };
        clienteKey = `company-${dadosCrm.company.id}`;
      } else {
        console.log(`[AGRUPAMENTO] Protocolo ${protocolo.numeroProcesso} sem person/company - órfão`);
        protocolosSemCliente.push(protocolo);
        continue;
      }
      
      console.log(`[AGRUPAMENTO] Cliente: ${clienteInfo.nome} (${clienteInfo.tipo} ID: ${clienteInfo.id})`);
      
      // Adicionar ao grupo existente ou criar novo grupo
      if (grupos.has(clienteKey)) {
        const grupo = grupos.get(clienteKey)!;
        grupo.protocolos.push({ ...protocolo, dadosCrm });
        console.log(`[AGRUPAMENTO] Adicionado ao grupo existente - Total: ${grupo.protocolos.length} protocolos`);
      } else {
        const novoGrupo: GrupoCliente = {
          clienteKey,
          clienteInfo,
          protocolos: [{ ...protocolo, dadosCrm }],
          dadosCrm
        };
        grupos.set(clienteKey, novoGrupo);
        console.log(`[AGRUPAMENTO] Novo grupo criado para: ${clienteInfo.nome}`);
      }
      
    } catch (error) {
      console.log(`[AGRUPAMENTO] Erro no protocolo ${protocolo.numeroProcesso}: ${error}`);
      protocolosSemCliente.push(protocolo);
    }
    
    // Pequeno delay para não sobrecarregar API do CRM
    if (i < protocolos.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
  
  // Adicionar protocolos sem cliente como grupos individuais
  if (protocolosSemCliente.length > 0) {
    console.log(`[AGRUPAMENTO] Criando grupos individuais para ${protocolosSemCliente.length} órfãos`);
    
    for (let i = 0; i < protocolosSemCliente.length; i++) {
      const protocolo = protocolosSemCliente[i];
      const clienteKey = `orphan-${protocolo.numeroProcesso}`;
      
      const grupoOrfao: GrupoCliente = {
        clienteKey,
        clienteInfo: {
          tipo: 'person', // Default para orphans
          id: 0,
          nome: `Processo ${protocolo.numeroProcesso} (sem cliente)`
        },
        protocolos: [protocolo],
        dadosCrm: {} as DadosCrm // Vazio para orphans
      };
      
      grupos.set(clienteKey, grupoOrfao);
    }
  }
  
  // Relatório final do agrupamento
  console.log(`[AGRUPAMENTO] ========== RESUMO ==========`);
  console.log(`[AGRUPAMENTO] Total de grupos: ${grupos.size}`);
  console.log(`[AGRUPAMENTO] Órfãos: ${protocolosSemCliente.length}`);
  
  let totalProtocolosAgrupados = 0;
  for (const [key, grupo] of grupos) {
    console.log(`[AGRUPAMENTO] ${grupo.clienteInfo.nome}: ${grupo.protocolos.length} protocolo(s)`);
    totalProtocolosAgrupados += grupo.protocolos.length;
  }
  
  console.log(`[AGRUPAMENTO] Total agrupados: ${totalProtocolosAgrupados}`);
  console.log(`[AGRUPAMENTO] ========== FIM ==========\n`);
  
  return grupos;
}

/**
 * Processa um grupo de cliente de forma otimizada
 * REUTILIZA todas as funções existentes sem modificá-las
 */
export async function processarGrupoCliente(grupo: GrupoCliente): Promise<ResultadoProcessamentoGrupo> {
  const inicioProcessamento = Date.now();
  const resultado: ResultadoProcessamentoGrupo = {
    clienteKey: grupo.clienteKey,
    clienteNome: grupo.clienteInfo.nome,
    protocolosTotal: grupo.protocolos.length,
    protocolosProcessados: 0,
    protocolosComErro: 0,
    clienteCriado: false,
    clienteAtualizado: false,
    linkGerado: false,
    chatguruAtualizado: false,
    personsAtualizadas: 0,
    marcasEncontradas: [],
    erros: [],
    sucesso: false,
    tempoProcessamento: 0
  };
  
  console.log(`\n[GRUPO] ========== PROCESSANDO: ${grupo.clienteInfo.nome} (${grupo.protocolos.length} protocolos) ==========`);
  
  try {
    // PARA GRUPOS ÓRFÃOS: Processar individualmente usando função existente
    if (grupo.clienteKey.startsWith('orphan-')) {
      console.log(`[GRUPO] Órfão detectado - processando individualmente`);
      
      for (const protocolo of grupo.protocolos) {
        try {
          await processarProtocolo(protocolo); // FUNÇÃO EXISTENTE
          resultado.protocolosProcessados++;
        } catch (error) {
          resultado.protocolosComErro++;
          resultado.erros.push(`Protocolo ${protocolo.numeroProcesso}: ${error}`);
        }
      }
      
      resultado.sucesso = resultado.protocolosProcessados > 0;
      resultado.tempoProcessamento = Date.now() - inicioProcessamento;
      return resultado;
    }
    
    // PARA GRUPOS COM CLIENTE IDENTIFICADO: Processamento otimizado
    const dadosCrmPrincipal = grupo.dadosCrm;
    
    // 1. CRIAR/ATUALIZAR CLIENTE (1x por grupo) - FUNÇÃO EXISTENTE
    console.log(`[GRUPO] ETAPA 1: Criando/atualizando cliente ${grupo.clienteInfo.nome}...`);
    let cliente: any;
    
    try {
      const resultadoCliente = await criarOuAtualizarCliente(dadosCrmPrincipal); // FUNÇÃO EXISTENTE
      cliente = resultadoCliente.cliente;
      resultado.clienteCriado = resultadoCliente.criado;
      resultado.clienteAtualizado = resultadoCliente.atualizado;
      
      console.log(`[GRUPO] Cliente: ${cliente.nome} (ID: ${cliente.id}) - ${resultado.clienteCriado ? 'CRIADO' : 'ATUALIZADO'}`);
    } catch (error) {
      resultado.erros.push(`Erro ao criar/atualizar cliente: ${error}`);
      throw error;
    }
    
    // 2. PROCESSAR TODOS OS PROTOCOLOS DO GRUPO (conectar processos)
    console.log(`[GRUPO] ETAPA 2: Conectando ${grupo.protocolos.length} processo(s)...`);
    
    for (let i = 0; i < grupo.protocolos.length; i++) {
      const protocolo = grupo.protocolos[i];
      console.log(`[GRUPO] Protocolo ${i + 1}/${grupo.protocolos.length}: ${protocolo.numeroProcesso}`);
      
      try {
        // Atualizar status para PROCESSANDO
        await prisma.processamentoProtocolo.update({
          where: { id: protocolo.id },
          data: { 
            status: 'PROCESSANDO',
            tentativas: protocolo.tentativas + 1,
            clienteId: cliente.id,
            crmDealId: protocolo.dadosCrm.id,
            crmPersonId: protocolo.dadosCrm.person?.id,
            crmCompanyId: protocolo.dadosCrm.company?.id,
            dadosCrm: protocolo.dadosCrm as any
          }
        });
        
        // Conectar cliente ao processo - FUNÇÃO EXISTENTE
        const processo = await conectarClienteAoProcesso(
          cliente.id, 
          protocolo.numeroProcesso,
          protocolo.dadosCrm,
          protocolo.dadosTitulares,
          protocolo.elementoNominativo // Para determinação correta da apresentação da marca
        );
        
        // Atualizar registro com sucesso na conexão
        await prisma.processamentoProtocolo.update({
          where: { id: protocolo.id },
          data: {
            processoId: processo.id,
            processoVinculado: true
          }
        });
        
        // Marcar oportunidade como "Ganha" no CRM - FUNÇÃO EXISTENTE
        if (protocolo.dadosCrm?.id) {
          try {
            const { getCRMConfiguration, updateDealStatus } = await import('../utils/crm.utils');
            const { CRM_API_URL, CRM_TOKEN } = await getCRMConfiguration();
            await updateDealStatus(protocolo.dadosCrm.id.toString(), CRM_API_URL, CRM_TOKEN);
            console.log(`[GRUPO] CRM oportunidade ${protocolo.dadosCrm.id} marcada como Ganha`);
          } catch (crmError) {
            console.log(`[GRUPO] CRM erro ao marcar oportunidade: ${crmError}`);
          }
        }
        
        resultado.protocolosProcessados++;
        console.log(`[GRUPO] Protocolo ${protocolo.numeroProcesso} conectado OK`);
        
      } catch (error) {
        resultado.protocolosComErro++;
        resultado.erros.push(`Protocolo ${protocolo.numeroProcesso}: ${error}`);
        console.log(`[GRUPO] Protocolo ${protocolo.numeroProcesso} ERRO: ${error}`);
        
        // Marcar protocolo com erro
        await prisma.processamentoProtocolo.update({
          where: { id: protocolo.id },
          data: { 
            status: 'FALHA_PROCESSO',
            ultimoErro: error instanceof Error ? error.message : 'Erro ao conectar processo'
          }
        });
      }
    }
    
    // 3. GERAR LINK AUTO-LOGIN (1x por grupo) - FUNÇÃO EXISTENTE
    console.log(`[GRUPO] ETAPA 3: Gerando link de auto-login...`);
    
    try {
      const autoLoginUrl = await gerarLinkAutoLogin(cliente); // FUNÇÃO EXISTENTE
      resultado.linkGerado = !!autoLoginUrl;
      
      if (autoLoginUrl) {
        cliente.autoLoginUrl = autoLoginUrl;
        console.log(`[GRUPO] Link gerado: ${autoLoginUrl}`);
      }
    } catch (error) {
      resultado.erros.push(`Erro ao gerar link: ${error}`);
      console.log(`[GRUPO] Link ERRO: ${error}`);
    }
    
    // 4. 🆕 COLETAR DADOS CHATGURU (sem executar ainda)
    console.log(`\n[GRUPO] ETAPA 4: Coletando dados ChatGuru para ${grupo.clienteInfo.nome}...`);
    
    try {
      // Extrair marcas específicas dos protocolos do grupo
      const marcasDoGrupo: string[] = [];
      
      for (const protocolo of grupo.protocolos) {
        // Opção 1: Usar elementoNominativo (extraído no upload)
        if (protocolo.elementoNominativo) {
          marcasDoGrupo.push(protocolo.elementoNominativo);
          console.log(`[GRUPO] Marca extraída do elementoNominativo: ${protocolo.elementoNominativo}`);
        } else {
          // 🔧 CORRIGIDO: Para marcas figurativas, tentar extrair do CRM
          console.log(`[GRUPO] Protocolo ${protocolo.numeroProcesso} - marca figurativa (sem elemento nominativo)`);
          
          // Opção 2: Extrair do CRM se elementoNominativo não estiver disponível  
          if (protocolo.dadosCrm?.customFields) {
            const { fieldMapping } = await import('../utils/crm.utils');
            
            // Função auxiliar local para extrair campo customizado
            const extrairCampoCustomizado = (dadosCrm: any, fieldId: number): any => {
              const campo = dadosCrm.customFields?.find((field: any) => field.id === fieldId);
              return campo ? campo.value : null;
            };
            
                         const nomeMarcaCrm = String(extrairCampoCustomizado(protocolo.dadosCrm, fieldMapping.nomeMarca.id) || '');
            
            // 🔧 DEBUG: Log do nome como vem do CRM vs como vai para processamento
            console.log(`[DEBUG-NOME-MARCA] CRM BRUTO: "${nomeMarcaCrm}"`);
            console.log(`[DEBUG-NOME-MARCA] CRM TRIMMED: "${nomeMarcaCrm.trim()}"`);
            
            // 🔧 USAR FUNÇÃO AUXILIAR: Obter apresentação do CRM para contexto
            let apresentacaoCrm = extrairCampoCustomizado(protocolo.dadosCrm, fieldMapping.tipoMarca.id);
            if (typeof apresentacaoCrm === 'string' && apresentacaoCrm.startsWith('[')) {
              try {
                const parsed = JSON.parse(apresentacaoCrm);
                apresentacaoCrm = Array.isArray(parsed) ? parsed[0] : apresentacaoCrm;
              } catch (e) {
                // Manter valor original se falhar o parse
              }
            }
            
            // Gerar nome apropriado para ChatGuru/RD
            const nomeMarcaParaExibicao = gerarNomeMarcaParaExibicao(
              nomeMarcaCrm.trim() || null, 
              protocolo.numeroProcesso, 
              String(apresentacaoCrm || 'Figurativa')
            );
            
            console.log(`[DEBUG-NOME-MARCA] PARA EXIBIÇÃO: "${nomeMarcaParaExibicao}"`);
            
            marcasDoGrupo.push(nomeMarcaParaExibicao);
            console.log(`[GRUPO] Marca para exibição: ${nomeMarcaParaExibicao}`);
            if (nomeMarcaCrm && isMarcaFigurativa(nomeMarcaCrm)) {
              console.log(`[GRUPO] Nome CRM "${nomeMarcaCrm}" filtrado por ser figurativo`);
            }
           } else {
             // Fallback para marcas figurativas sem dados do CRM
             const marcaFigurativa = gerarNomeMarcaParaExibicao(null, protocolo.numeroProcesso, 'Figurativa');
             marcasDoGrupo.push(marcaFigurativa);
             console.log(`[GRUPO] Marca figurativa sem dados CRM: ${marcaFigurativa}`);
           }
        }
      }
      
      // Remover duplicatas e valores vazios
      const marcasUnicasDoGrupo = [...new Set(marcasDoGrupo.filter(marca => marca && marca.trim()))];
      
      console.log(`[GRUPO] Marcas finais: [${marcasUnicasDoGrupo.join(', ')}]`);
        
      // 🆕 Obter telefones do cliente
      const contatos = await prisma.contatoCliente.findMany({
        where: { clienteId: cliente.id }
      });
      
      const telefones = obterListaTelefones(contatos);
      
      if (telefones.length > 0) {
        // 🆕 Preparar dados ChatGuru para consolidação posterior
        const senhaCliente = cliente.numeroDocumento ? 
          extrairUltimos3Digitos(cliente.numeroDocumento) : 
          '123';
        
        resultado.dadosChatGuru = {
          clienteId: cliente.id,
          clienteNome: cliente.nome,
          identificador: cliente.identificador,
          senha: senhaCliente,
          link: extrairShortCodeDaUrl(cliente.autoLoginUrl || ''),
          telefones: telefones,
          marcas: marcasUnicasDoGrupo
        };
        
        console.log(`[GRUPO] Dados ChatGuru coletados - Telefones: ${telefones.length}, Marcas: ${marcasUnicasDoGrupo.length}`);
        console.log(`[GRUPO] Telefones: ${telefones.map(t => t.substring(0, 6) + '****').join(', ')}`);
        
        // Marcar como "preparado" para ChatGuru (será executado posteriormente)
        resultado.chatguruAtualizado = true; // Temporário - será atualizado na execução consolidada
      } else {
        console.log(`[GRUPO] Nenhum telefone válido encontrado - ChatGuru não será executado`);
        resultado.chatguruAtualizado = false;
      }
      
      // Usar as marcas do grupo para o relatório
      resultado.marcasEncontradas = marcasUnicasDoGrupo;
      
    } catch (error) {
      resultado.erros.push(`Erro ao coletar dados ChatGuru: ${error}`);
      console.log(`[GRUPO] Erro ao coletar dados ChatGuru: ${error}`);
      resultado.chatguruAtualizado = false;
    }
    
    // 5. ATUALIZAR PERSONS RELACIONADAS (1x por grupo) - FUNÇÃO EXISTENTE
    console.log(`\n👥 ETAPA 5: Atualizando persons relacionadas no CRM...`);
    
    try {
      const resultadoPersons = await atualizarPersonsRelacionadas(cliente); // FUNÇÃO EXISTENTE
      resultado.personsAtualizadas = resultadoPersons.personsAtualizadas;
      
      console.log(`✅ Persons atualizadas: ${resultado.personsAtualizadas}`);
    } catch (error) {
      resultado.erros.push(`Erro nas persons: ${error}`);
      console.error(`❌ Erro nas persons:`, error);
    }
    
    // 6. FINALIZAR PROTOCOLOS PROCESSADOS COM SUCESSO
    console.log(`\n✅ ETAPA 6: Finalizando protocolos processados...`);
    
    const protocolosProcessadosComSucesso = grupo.protocolos.filter((p, index) => index < resultado.protocolosProcessados);
    
    for (const protocolo of protocolosProcessadosComSucesso) {
      try {
        await prisma.processamentoProtocolo.update({
          where: { id: protocolo.id },
          data: {
            status: 'SUCESSO',
            processadoEm: new Date(),
            ultimoErro: null,
            linkGerado: resultado.linkGerado,
            autoLoginUrl: cliente.autoLoginUrl,
            chatguruAtualizado: resultado.chatguruAtualizado
          }
        });
      } catch (error) {
        console.error(`⚠️ Erro ao finalizar protocolo ${protocolo.numeroProcesso}:`, error);
      }
    }
    
    resultado.sucesso = resultado.protocolosProcessados > 0;
    resultado.tempoProcessamento = Date.now() - inicioProcessamento;
    
    console.log(`[GRUPO] ========== FINALIZADO: ${grupo.clienteInfo.nome} ==========`);
    console.log(`[GRUPO] Sucessos: ${resultado.protocolosProcessados}, Erros: ${resultado.protocolosComErro}`);
    console.log(`[GRUPO] ChatGuru: ${resultado.chatguruAtualizado ? 'OK' : 'FALHA'}, Persons: ${resultado.personsAtualizadas}`);
    console.log(`[GRUPO] Tempo: ${resultado.tempoProcessamento}ms`);
    
    return resultado;
    
  } catch (error) {
    resultado.sucesso = false;
    resultado.tempoProcessamento = Date.now() - inicioProcessamento;
    resultado.erros.push(`Erro geral do grupo: ${error}`);
    
    console.log(`[GRUPO] ========== ERRO: ${grupo.clienteInfo.nome} ==========`);
    console.log(`[GRUPO] Erro: ${error}`);
    
    return resultado;
  }
}

/**
 * Processa fila de protocolos com agrupamento por cliente
 * NOVA versão otimizada que usa agrupamento
 */
export async function processarFilaComAgrupamento(): Promise<{
  totalProtocolos: number;
  totalGrupos: number;
  gruposProcessados: number;
  gruposComErro: number;
  tempoTotal: number;
  resultados: ResultadoProcessamentoGrupo[];
}> {
  const inicioGeral = Date.now();
  console.log('\n[FILA] ========== INICIANDO PROCESSAMENTO COM AGRUPAMENTO ==========');
  
  try {
    // Buscar protocolos pendentes (mesma lógica da função original)
    const agora = new Date();
    const protocolos = await prisma.processamentoProtocolo.findMany({
      where: {
        OR: [
          { status: 'PENDENTE' },
          {
            AND: [
              { status: { in: ['FALHA_CRM', 'FALHA_CLIENTE', 'FALHA_PROCESSO', 'FALHA_LINK', 'FALHA_CHATGURU', 'FALHA_GERAL'] } },
              { tentativas: { lt: 3 } },
              {
                OR: [
                  { proximaTentativa: { lte: agora } },
                  { proximaTentativa: null }
                ]
              }
            ]
          }
        ]
      },
      orderBy: { criadoEm: 'asc' },
      take: 50 // Processar máximo 50 por vez
    });
    
    if (protocolos.length === 0) {
      console.log('[FILA] Nenhum protocolo pendente encontrado');
      return {
        totalProtocolos: 0,
        totalGrupos: 0,
        gruposProcessados: 0,
        gruposComErro: 0,
        tempoTotal: Date.now() - inicioGeral,
        resultados: []
      };
    }
    
    console.log(`[FILA] Encontrados ${protocolos.length} protocolos para processar`);
    
    // Agrupar protocolos por cliente
    const grupos = await agruparProtocolosPorCliente(protocolos);
    
    // Processar cada grupo (SEM ChatGuru ainda)
    const resultados: ResultadoProcessamentoGrupo[] = [];
    let gruposProcessados = 0;
    let gruposComErro = 0;
    
    console.log(`[FILA] ========== PROCESSANDO GRUPOS (SEM CHATGURU) ==========`);
    
    for (const [clienteKey, grupo] of grupos) {
      try {
        const resultado = await processarGrupoCliente(grupo);
        resultados.push(resultado);
        
        if (resultado.sucesso) {
          gruposProcessados++;
        } else {
          gruposComErro++;
        }
        
        // Delay entre grupos para não sobrecarregar APIs
        await new Promise(resolve => setTimeout(resolve, 3000));
        
      } catch (error) {
        gruposComErro++;
        console.log(`[FILA] Erro fatal no grupo ${grupo.clienteInfo.nome}: ${error}`);
        
        // Criar resultado de erro para o grupo
        resultados.push({
          clienteKey: grupo.clienteKey,
          clienteNome: grupo.clienteInfo.nome,
          protocolosTotal: grupo.protocolos.length,
          protocolosProcessados: 0,
          protocolosComErro: grupo.protocolos.length,
          clienteCriado: false,
          clienteAtualizado: false,
          linkGerado: false,
          chatguruAtualizado: false,
          personsAtualizadas: 0,
          marcasEncontradas: [],
          erros: [`Erro fatal: ${error}`],
          sucesso: false,
          tempoProcessamento: 0
        });
      }
    }
    
    // 🆕 EXECUTAR CHATGURU CONSOLIDADO POR TELEFONE
    console.log(`[FILA] ========== EXECUTANDO CHATGURU CONSOLIDADO ==========`);
    
    try {
      // Consolidar dados ChatGuru por telefone único
      const consolidacao = consolidarChatGuruPorTelefone(resultados);
      
      if (consolidacao.size > 0) {
        // Executar ChatGuru consolidado
        const resultadoChatGuru = await executarChatGuruConsolidado(consolidacao);
        
        // Atualizar resultados dos grupos com status real do ChatGuru
        for (const resultado of resultados) {
          if (resultado.dadosChatGuru && resultado.sucesso) {
            // Verificar se algum telefone do cliente teve sucesso
            let chatGuruSucesso = false;
            
            for (const telefone of resultado.dadosChatGuru.telefones) {
              const telefoneNormalizado = telefone.replace(/\D/g, '');
              const detalhe = resultadoChatGuru.detalhes.find(d => d.telefone === telefoneNormalizado);
              
              if (detalhe && detalhe.sucesso) {
                chatGuruSucesso = true;
                break;
              }
            }
            
            resultado.chatguruAtualizado = chatGuruSucesso;
            
            // Atualizar protocolos do grupo com status real do ChatGuru
            const protocolosParaAtualizar = await prisma.processamentoProtocolo.findMany({
              where: {
                clienteId: resultado.dadosChatGuru.clienteId,
                status: 'SUCESSO'
              }
            });
            
            for (const protocolo of protocolosParaAtualizar) {
              await prisma.processamentoProtocolo.update({
                where: { id: protocolo.id },
                data: { chatguruAtualizado: chatGuruSucesso }
              });
            }
          }
        }
        
        console.log(`[FILA] ChatGuru consolidado concluído: ${resultadoChatGuru.telefonesComSucesso}/${resultadoChatGuru.telefonesProcessados} telefones`);
        
        // 🆕 ATUALIZAR RD STATION MARKETING após ChatGuru bem-sucedido
        if (resultadoChatGuru.telefonesComSucesso > 0) {
          console.log(`[FILA] ========== ATUALIZANDO RD STATION MARKETING ==========`);
          
          try {
            const resultadoRD = await atualizarRDStationConsolidado(consolidacao);
            console.log(`[FILA] RD Station concluído: ${resultadoRD.contatosComSucesso}/${resultadoRD.contatosProcessados} contatos`);
          } catch (rdError) {
            console.log(`[FILA] Erro na atualização RD Station: ${rdError}`);
          }
        } else {
          console.log(`[FILA] Pulando RD Station - nenhum ChatGuru bem-sucedido`);
        }
        
      } else {
        console.log(`[FILA] Nenhum dado ChatGuru para consolidar`);
      }
      
    } catch (error) {
      console.log(`[FILA] Erro na consolidação ChatGuru: ${error}`);
      
      // Marcar todos os grupos como falha no ChatGuru
      for (const resultado of resultados) {
        if (resultado.dadosChatGuru) {
          resultado.chatguruAtualizado = false;
          resultado.erros.push(`Erro na consolidação ChatGuru: ${error}`);
        }
      }
    }
    
    const tempoTotal = Date.now() - inicioGeral;
    
    console.log(`[FILA] ========== PROCESSAMENTO CONCLUÍDO ==========`);
    console.log(`[FILA] Total de protocolos: ${protocolos.length}`);
    console.log(`[FILA] Total de grupos: ${grupos.size}`);
    console.log(`[FILA] Grupos processados: ${gruposProcessados}`);
    console.log(`[FILA] Grupos com erro: ${gruposComErro}`);
    console.log(`[FILA] Tempo total: ${tempoTotal}ms`);
    
    // 🆕 Resumo final detalhado por cliente
    console.log(`[FILA] ========== RESUMO POR CLIENTE ==========`);
    for (const resultado of resultados) {
      if (resultado.sucesso) {
        const statusChatGuru = resultado.chatguruAtualizado ? 'OK' : 'FALHA';
        const telefonesInfo = resultado.dadosChatGuru ? 
          ` | Telefones: ${resultado.dadosChatGuru.telefones.length}` : '';
        
        console.log(`[FILA] ✅ ${resultado.clienteNome}: ${resultado.protocolosProcessados}/${resultado.protocolosTotal} protocolos | ChatGuru: ${statusChatGuru}${telefonesInfo} | Marcas: ${resultado.marcasEncontradas.join(' / ') || 'nenhuma'}`);
      } else {
        console.log(`[FILA] ❌ ${resultado.clienteNome}: ${resultado.protocolosComErro}/${resultado.protocolosTotal} com erro`);
      }
    }
    console.log(`[FILA] ========== FIM RESUMO ==========`);
    
    return {
      totalProtocolos: protocolos.length,
      totalGrupos: grupos.size,
      gruposProcessados,
      gruposComErro,
      tempoTotal,
      resultados
    };
    
  } catch (error) {
    console.log(`[FILA] Erro no processamento com agrupamento: ${error}`);
    throw error;
  }
} 