import axios from 'axios';

export interface FastApiResponse {
  success: boolean;
  message?: string;
  processedCount?: number;
  errors?: string[];
}

/**
 * Envia lista de processos para a FastAPI fazer download das logos
 */
export async function enviarProcessosParaDownload(
  numerosProcessos: string[],
  fastapiUrl: string = 'http://localhost:8000',
  endpoint: string = '/download-logos'
): Promise<FastApiResponse> {
  
  if (numerosProcessos.length === 0) {
    return {
      success: true,
      message: 'Nenhum processo para enviar',
      processedCount: 0
    };
  }

  const url = `${fastapiUrl}${endpoint}`;
  const payload = {
    processos: numerosProcessos
  };

  try {
    console.log(`📡 Enviando ${numerosProcessos.length} processos para FastAPI: ${url}`);
    console.log(`📦 Payload:`, JSON.stringify(payload, null, 2));

    const response = await axios.post(url, payload, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 120000, // 2 minutos de timeout (download pode demorar)
    });

    if (response.status === 200) {
      console.log(`✅ FastAPI respondeu com sucesso:`, response.data);
      return {
        success: true,
        message: response.data.message || 'Processos enviados com sucesso',
        processedCount: response.data.processedCount || numerosProcessos.length,
        ...response.data
      };
    } else {
      return {
        success: false,
        message: `FastAPI retornou status ${response.status}: ${response.statusText}`
      };
    }

  } catch (error: any) {
    let errorMessage = 'Erro desconhecido ao comunicar com FastAPI';
    
    if (error.code === 'ECONNREFUSED') {
      errorMessage = 'Não foi possível conectar com a FastAPI. Verifique se está rodando em localhost:8000';
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = 'Timeout na requisição para FastAPI';
    } else if (error.response) {
      errorMessage = `FastAPI retornou erro ${error.response.status}: ${error.response.data?.detail || error.response.statusText}`;
    } else if (error.message) {
      errorMessage = error.message;
    }

    console.error(`❌ Erro ao comunicar com FastAPI:`, errorMessage);
    return {
      success: false,
      message: errorMessage
    };
  }
}

/**
 * Verifica se a FastAPI está online e respondendo
 */
export async function verificarFastApiOnline(
  fastapiUrl: string = 'http://localhost:8000'
): Promise<boolean> {
  try {
    const response = await axios.get(`${fastapiUrl}/health`, {
      timeout: 5000
    });
    return response.status === 200;
  } catch (error) {
    try {
      // Tentar rota raiz se /health não existir
      const response = await axios.get(fastapiUrl, {
        timeout: 5000
      });
      return response.status === 200;
    } catch (rootError) {
      return false;
    }
  }
} 