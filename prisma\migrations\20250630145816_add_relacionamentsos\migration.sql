-- AlterEnum
ALTER TYPE "TipoComunicado" ADD VALUE 'DESPACHO_PROCESSO';

-- AlterTable
ALTER TABLE "Comunicado" ADD COLUMN     "despachoElegivelId" TEXT;

-- CreateIndex
CREATE INDEX "Comunicado_despachoElegivelId_idx" ON "Comunicado"("despachoElegivelId");

-- AddForeignKey
ALTER TABLE "Comunicado" ADD CONSTRAINT "Comunicado_despachoElegivelId_fkey" FOREIGN KEY ("despachoElegivelId") REFERENCES "DespachoElegivel"("id") ON DELETE SET NULL ON UPDATE CASCADE;
