import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

interface ProcuradorAnalise {
  id: string;
  nome: string;
  totalProcessos: number;
  processosMonitorados: number;
  variacoesNome: string[];
}

async function analisarProcuradoresRegistreSe() {
  console.log("🔍 Analisando procuradores REGISTRE-SE...\n");

  try {
    // Buscar todos os procuradores que contenham "REGISTRE-SE" (case insensitive)
    const procuradores = await prisma.procurador.findMany({
      where: {
        nome: {
          contains: "REGISTRE-SE",
          mode: "insensitive",
        },
      },
      include: {
        _count: {
          select: {
            processos: true,
          },
        },
        processos: {
          select: {
            monitorado: true,
          },
        },
      },
      orderBy: {
        nome: "asc",
      },
    });

    if (procuradores.length === 0) {
      console.log("❌ Nenhum procurador encontrado com 'REGISTRE-SE' no nome.");
      return;
    }

    console.log(`📊 Total de procuradores encontrados: ${procuradores.length}\n`);

    // Analisar cada procurador
    const analises: ProcuradorAnalise[] = [];

    for (const procurador of procuradores) {
      const processosMonitorados = procurador.processos.filter(
        (p) => p.monitorado === true
      ).length;

      const analise: ProcuradorAnalise = {
        id: procurador.id,
        nome: procurador.nome,
        totalProcessos: procurador._count.processos,
        processosMonitorados,
        variacoesNome: [procurador.nome],
      };

      analises.push(analise);
    }

    // Exibir resultados detalhados
    console.log("📋 DETALHAMENTO DOS PROCURADORES:\n");
    console.log("=" .repeat(80));

    analises.forEach((analise, index) => {
      console.log(`\n${index + 1}. PROCURADOR:`);
      console.log(`   ID: ${analise.id}`);
      console.log(`   Nome: "${analise.nome}"`);
      console.log(`   Total de Processos: ${analise.totalProcessos}`);
      console.log(`   Processos Monitorados: ${analise.processosMonitorados}`);
      
      // Verificar se o nome bate exatamente com as variações conhecidas
      const nomeUpper = analise.nome.toUpperCase();
      if (nomeUpper.includes("REGISTRE-SE LTDA")) {
        console.log("   ✅ Contém 'REGISTRE-SE LTDA'");
      }
      if (nomeUpper.includes("REGISTRE-SE")) {
        console.log("   ✅ Contém 'REGISTRE-SE'");
      }
      
      console.log("   " + "-".repeat(50));
    });

    // Estatísticas resumidas
    const totalProcessos = analises.reduce((sum, a) => sum + a.totalProcessos, 0);
    const totalMonitorados = analises.reduce((sum, a) => sum + a.processosMonitorados, 0);

    console.log("\n" + "=".repeat(80));
    console.log("📈 ESTATÍSTICAS RESUMIDAS:");
    console.log(`   Total de procuradores REGISTRE-SE: ${analises.length}`);
    console.log(`   Total de processos: ${totalProcessos}`);
    console.log(`   Total de processos monitorados: ${totalMonitorados}`);

    // Identificar possíveis candidatos principais
    console.log("\n" + "=".repeat(80));
    console.log("🎯 ANÁLISE DE CANDIDATOS PRINCIPAIS:");

    const candidatoPrincipal = analises.reduce((prev, current) => {
      if (current.totalProcessos > prev.totalProcessos) return current;
      if (current.totalProcessos === prev.totalProcessos && 
          current.processosMonitorados > prev.processosMonitorados) return current;
      return prev;
    });

    console.log(`\n🏆 CANDIDATO PRINCIPAL:`);
    console.log(`   ID: ${candidatoPrincipal.id}`);
    console.log(`   Nome: "${candidatoPrincipal.nome}"`);
    console.log(`   Processos: ${candidatoPrincipal.totalProcessos} (${candidatoPrincipal.processosMonitorados} monitorados)`);

    // Verificar duplicatas potenciais
    const possiveisDuplicatas = analises.filter(
      (a) => a.id !== candidatoPrincipal.id && a.totalProcessos > 0
    );

    if (possiveisDuplicatas.length > 0) {
      console.log(`\n⚠️  POSSÍVEIS DUPLICATAS (${possiveisDuplicatas.length}):`);
      possiveisDuplicatas.forEach((dup) => {
        console.log(`   - ID: ${dup.id} | "${dup.nome}" | ${dup.totalProcessos} processos`);
      });
      
      console.log("\n💡 RECOMENDAÇÃO:");
      console.log("   Considere consolidar os procuradores duplicados no principal.");
    }

    // Verificar nomes exatos para regex
    console.log("\n" + "=".repeat(80));
    console.log("🔧 PARA IMPLEMENTAÇÃO NO CÓDIGO:");
    
    const nomesUnicos = [...new Set(analises.map(a => a.nome))];
    console.log("\n📝 Nomes exatos encontrados:");
    nomesUnicos.forEach(nome => {
      console.log(`   - "${nome}"`);
    });

    console.log(`\n💻 Sugestão de regex para detecção:`);
    console.log(`   if (procJson.procurador.toUpperCase().includes("REGISTRE-SE")) {`);
    console.log(`     // Usar ID fixo: ${candidatoPrincipal.id}`);
    console.log(`   }`);

    // Verificar se há processos com monitorado=true mas procurador diferente
    console.log("\n" + "=".repeat(80));
    console.log("🔍 VERIFICANDO CONSISTÊNCIA...");

    const processosMonitoradosSemRegistreSe = await prisma.processo.count({
      where: {
        monitorado: true,
        AND: {
          NOT: {
            procuradorId: {
              in: analises.map(a => a.id)
            }
          }
        }
      }
    });

    if (processosMonitoradosSemRegistreSe > 0) {
      console.log(`\n⚠️  INCONSISTÊNCIA DETECTADA:`);
      console.log(`   ${processosMonitoradosSemRegistreSe} processos marcados como 'monitorado=true'`);
      console.log(`   mas não têm procurador REGISTRE-SE associado.`);
      console.log(`\n   Execute este query para investigar:`);
      console.log(`   SELECT p.numero, p.monitorado, pr.nome as procurador_nome`);
      console.log(`   FROM "Processo" p`);
      console.log(`   LEFT JOIN "Procurador" pr ON p."procuradorId" = pr.id`);
      console.log(`   WHERE p.monitorado = true AND p."procuradorId" NOT IN ('${analises.map(a => a.id).join("', '")}')`);
    } else {
      console.log(`\n✅ CONSISTÊNCIA OK: Todos os processos monitorados têm procurador REGISTRE-SE.`);
    }

  } catch (error) {
    console.error("❌ Erro durante a análise:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar análise
analisarProcuradoresRegistreSe()
  .then(() => {
    console.log("\n✅ Análise concluída!");
  })
  .catch((error) => {
    console.error("❌ Erro:", error);
    process.exit(1);
  }); 