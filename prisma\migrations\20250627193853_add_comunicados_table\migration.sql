-- CreateEnum
CREATE TYPE "TipoComunicado" AS ENUM ('PROTOCOLO_MARCA', 'ATUALIZ<PERSON>AO_PROCESSO', 'NOTIFICACAO_PRAZO', 'COMUNICADO_GERAL', 'BOAS_VINDAS');

-- CreateEnum
CREATE TYPE "StatusComunicado" AS ENUM ('ENVIADO', 'PENDENTE', 'FALHA', 'CANCELADO');

-- CreateTable
CREATE TABLE "Comunicado" (
    "id" TEXT NOT NULL,
    "tipo" "TipoComunicado" NOT NULL,
    "titulo" TEXT NOT NULL,
    "descricao" TEXT,
    "clienteId" INTEGER NOT NULL,
    "processosIds" TEXT[],
    "marcasIds" TEXT[],
    "rdStationDataId" TEXT,
    "rdStationEventId" TEXT,
    "email" TEXT,
    "metodoComunicacao" "metodoComunicacao" NOT NULL DEFAULT 'EMAIL',
    "status" "StatusComunicado" NOT NULL DEFAULT 'ENVIADO',
    "dataEnvio" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dadosAdicionais" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Comunicado_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Comunicado_clienteId_idx" ON "Comunicado"("clienteId");

-- CreateIndex
CREATE INDEX "Comunicado_tipo_idx" ON "Comunicado"("tipo");

-- CreateIndex
CREATE INDEX "Comunicado_status_idx" ON "Comunicado"("status");

-- CreateIndex
CREATE INDEX "Comunicado_dataEnvio_idx" ON "Comunicado"("dataEnvio");

-- CreateIndex
CREATE INDEX "Comunicado_rdStationDataId_idx" ON "Comunicado"("rdStationDataId");

-- AddForeignKey
ALTER TABLE "Comunicado" ADD CONSTRAINT "Comunicado_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE CASCADE ON UPDATE CASCADE;
