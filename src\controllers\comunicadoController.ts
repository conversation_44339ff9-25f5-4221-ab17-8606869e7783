import { Request, Response, NextFunction } from 'express';
import { PrismaClient, TipoComunicado, StatusComunicado, metodoComunicacao } from '@prisma/client';
import { chatGuruLogger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';
import prisma from '../dbClient';

const db = prisma;

/**
 * Lista comunicados de um cliente específico
 * 
 * @route GET /api/comunicados/cliente/:clienteId
 */
export const listarComunicadosCliente = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const clienteId = parseInt(req.params.clienteId);
    
    // Validação do clienteId
    if (isNaN(clienteId)) {
      res.status(400).json({
        success: false,
        error: 'clienteId deve ser um número válido'
      });
      return;
    }

    // Parâmetros de paginação
    const page = parseInt(req.query.page as string) || 1;
    const limit = Math.min(parseInt(req.query.limit as string) || 10, 50); // Máximo 50 por página
    const skip = (page - 1) * limit;

    // Parâmetros de filtro
    const tipo = req.query.tipo as TipoComunicado;
    const status = req.query.status as StatusComunicado;
    const dataInicio = req.query.dataInicio ? new Date(req.query.dataInicio as string) : undefined;
    const dataFim = req.query.dataFim ? new Date(req.query.dataFim as string) : undefined;

    // Verificar se o cliente existe
    const cliente = await prisma.cliente.findUnique({
      where: { id: clienteId },
      select: {
        id: true,
        nome: true,
        identificador: true
      }
    });

    if (!cliente) {
      res.status(404).json({
        success: false,
        error: 'Cliente não encontrado'
      });
      return;
    }

    // Construir filtros
    const whereClause: any = {
      clienteId: clienteId
    };

    if (tipo) whereClause.tipo = tipo;
    if (status) whereClause.status = status;
    if (dataInicio || dataFim) {
      whereClause.dataEnvio = {};
      if (dataInicio) whereClause.dataEnvio.gte = dataInicio;
      if (dataFim) whereClause.dataEnvio.lte = dataFim;
    }

    // Buscar comunicados com paginação
    const [comunicados, totalComunicados] = await Promise.all([
      prisma.comunicado.findMany({
        where: whereClause,
        orderBy: { dataEnvio: 'desc' },
        skip,
        take: limit,
        select: {
          id: true,
          tipo: true,
          titulo: true,
          descricao: true,
          status: true,
          metodoComunicacao: true,
          email: true,
          processosIds: true,
          marcasIds: true,
          dataEnvio: true,
          createdAt: true,
          dadosAdicionais: true,
          rdStationDataId: true,
          rdStationEventId: true
        }
      }),
      prisma.comunicado.count({ where: whereClause })
    ]);

    // Obter informações dos processos e marcas para enriquecer os dados
    const todosProcessosIds = [...new Set(comunicados.flatMap(c => c.processosIds))];
    const todasMarcasIds = [...new Set(comunicados.flatMap(c => c.marcasIds))];

    const [processos, marcas] = await Promise.all([
      todosProcessosIds.length > 0 ? prisma.processo.findMany({
        where: { id: { in: todosProcessosIds } },
        select: {
          id: true,
          numero: true,
          marca: {
            select: {
              id: true,
              nome: true,
              apresentacao: true
            }
          }
        }
      }) : [],
      todasMarcasIds.length > 0 ? prisma.marca.findMany({
        where: { id: { in: todasMarcasIds } },
        select: {
          id: true,
          nome: true,
          apresentacao: true
        }
      }) : []
    ]);

    // Criar mapas para facilitar o lookup
    const processosMap = new Map(processos.map(p => [p.id, p]));
    const marcasMap = new Map(marcas.map(m => [m.id, m]));

    // Enriquecer dados dos comunicados
    const comunicadosEnriquecidos = comunicados.map(comunicado => {
      // Obter informações dos processos relacionados
      const processosRelacionados = comunicado.processosIds
        .map(id => processosMap.get(id))
        .filter((p): p is NonNullable<typeof p> => p !== undefined);

      // Obter informações das marcas relacionadas
      const marcasRelacionadas = comunicado.marcasIds
        .map(id => marcasMap.get(id))
        .filter((m): m is NonNullable<typeof m> => m !== undefined);

      // Combinar marcas dos processos e marcas diretas
      const todasMarcas = [
        ...marcasRelacionadas,
        ...processosRelacionados
          .map(p => p.marca)
          .filter((marca): marca is NonNullable<typeof marca> => marca !== null && marca !== undefined)
      ];

      // Remover duplicatas de marcas
      const marcasUnicas = todasMarcas.filter((marca, index, array) => 
        array.findIndex(m => m && m.id === marca.id) === index
      );

      const marcasNomes = marcasUnicas.map(marca => 
        marca?.nome || marca?.apresentacao || `Marca ID: ${marca?.id}`
      );

      return {
        id: comunicado.id,
        tipo: comunicado.tipo,
        titulo: comunicado.titulo,
        descricao: comunicado.descricao,
        status: comunicado.status,
        metodoComunicacao: comunicado.metodoComunicacao,
        email: comunicado.email,
        dataEnvio: comunicado.dataEnvio,
        criadoEm: comunicado.createdAt,
        processos: {
          total: processosRelacionados.length,
          numeros: processosRelacionados.map(p => p?.numero).filter(Boolean),
          marcasNomes,
          marcasTexto: marcasNomes.join(', ') || 'Nenhuma marca'
        },
        dadosAdicionais: comunicado.dadosAdicionais,
        rdStationDataId: comunicado.rdStationDataId,
        rdStationEventId: comunicado.rdStationEventId
      };
    });

    // Calcular estatísticas do cliente
    const [estatisticasTipo, estatisticasStatus] = await Promise.all([
      prisma.comunicado.groupBy({
        by: ['tipo'],
        where: { clienteId },
        _count: { id: true }
      }),
      prisma.comunicado.groupBy({
        by: ['status'],
        where: { clienteId },
        _count: { id: true }
      })
    ]);

    const totalComunicadosCliente = await prisma.comunicado.count({
      where: { clienteId }
    });

    const porTipo = estatisticasTipo.reduce((acc, item) => {
      acc[item.tipo] = item._count.id;
      return acc;
    }, {} as Record<string, number>);

    const porStatus = estatisticasStatus.reduce((acc, item) => {
      acc[item.status] = item._count.id;
      return acc;
    }, {} as Record<string, number>);

    // Resposta final
    const response = {
      success: true,
      message: `${totalComunicados} comunicado(s) encontrado(s)`,
      data: {
        cliente: {
          id: cliente.id,
          nome: cliente.nome,
          identificador: cliente.identificador
        },
        comunicados: comunicadosEnriquecidos,
        estatisticas: {
          totalComunicados: totalComunicadosCliente,
          porTipo,
          porStatus
        },
        paginacao: {
          paginaAtual: page,
          limitePorPagina: limit,
          totalItems: totalComunicados,
          totalPaginas: Math.ceil(totalComunicados / limit)
        },
        filtros: {
          tipo: tipo || null,
          status: status || null,
          dataInicio: dataInicio?.toISOString().split('T')[0] || null,
          dataFim: dataFim?.toISOString().split('T')[0] || null
        }
      }
    };

    chatGuruLogger.info(`Comunicados listados para cliente ${clienteId}`, {
      clienteId,
      total: totalComunicados,
      filtros: { tipo, status, dataInicio, dataFim },
      paginacao: { page, limit }
    });

    res.status(200).json(response);

  } catch (error) {
    console.error('❌ Erro ao listar comunicados do cliente:', error);
    chatGuruLogger.error('Erro ao listar comunicados do cliente:', error);
    
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor ao listar comunicados'
    });
  }
};

/**
 * Recebe dados do RD Station e loga no console para análise
 * Este endpoint será chamado pelo RD Station Marketing no final do fluxo de automação
 * 
 * @route POST /api/comunicados/protocolo/:tipoProtocolo
 */
export const registrarComunicadoProtocolo = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { tipoProtocolo } = req.params;
    const dadosRecebidos = req.body;
    const timestamp = new Date().toISOString();

    // Validação básica do parâmetro da URL
    if (!tipoProtocolo || !['unica', 'multiplas'].includes(tipoProtocolo)) {
      console.log('❌ Erro: tipoProtocolo inválido:', tipoProtocolo);
      res.status(400).json({
        success: false,
        error: 'tipoProtocolo deve ser "unica" ou "multiplas"'
      });
      return;
    }

    // Log detalhado dos dados recebidos
    console.log('\n🎯 ===== COMUNICADO RD STATION RECEBIDO =====');
    console.log('📅 Timestamp:', timestamp);
    console.log('🏷️  Tipo de Protocolo:', tipoProtocolo.toUpperCase());
    console.log('📋 Dados completos recebidos:');
    console.log(JSON.stringify(dadosRecebidos, null, 2));
    console.log('🔍 Headers da requisição:');
    console.log(JSON.stringify(req.headers, null, 2));
    console.log('🌐 IP do cliente:', req.ip || req.connection.remoteAddress);
    console.log('👤 User-Agent:', req.get('User-Agent'));
    console.log('==========================================\n');

    // Log específico para facilitar análise
    chatGuruLogger.info('Dados RD Station recebidos', {
      tipoProtocolo,
      timestamp,
      dadosRecebidos,
      headers: req.headers,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    // **NOVA LÓGICA DE PROCESSAMENTO DO RD STATION**
    
    // Validar se recebemos os dados necessários do RD Station
    if (!dadosRecebidos.leads || !dadosRecebidos.leads[0] || !dadosRecebidos.leads[0].custom_fields) {
      console.log('❌ Estrutura de dados RD Station inválida, apenas logando...');
      console.log('🔍 Estrutura esperada: { leads: [{ custom_fields: {...} }] }');
      console.log('🔍 Estrutura recebida:', {
        temLeads: !!dadosRecebidos.leads,
        temPrimeiroLead: !!(dadosRecebidos.leads && dadosRecebidos.leads[0]),
        temCustomFields: !!(dadosRecebidos.leads && dadosRecebidos.leads[0] && dadosRecebidos.leads[0].custom_fields)
      });
      
    res.status(200).json({
      success: true,
        message: `Dados do protocolo ${tipoProtocolo} recebidos e logados (estrutura inválida)`,
      timestamp,
      tipoProtocolo,
      dadosRecebidos: Object.keys(dadosRecebidos).length
      });
      return;
    }

    const leadData = dadosRecebidos.leads[0];
    const { custom_fields } = leadData;
    const {
      protocolo_realizado,
      protocolo_marca,
      link_cliente,
      idc,
      protocolo_multiplas_marcas
    } = custom_fields;

    console.log('🔍 Dados do lead RD Station:', {
      leadId: leadData.id,
      nome: leadData.name,
      email: leadData.email,
      uuid: leadData.uuid
    });
    
    console.log('🔍 Campos extraídos do custom_fields:', {
      protocolo_realizado,
      protocolo_marca,
      link_cliente,
      idc,
      protocolo_multiplas_marcas
    });

    // Verificar se é um protocolo realizado
    if (protocolo_realizado !== 'Sim') {
      console.log('❌ Protocolo não realizado, apenas logando...');
      res.status(200).json({
        success: true,
        message: `Dados recebidos mas protocolo não foi realizado (${protocolo_realizado})`,
        timestamp,
        tipoProtocolo
      });
      return;
    }

    // Validar campos obrigatórios
    if (!link_cliente || !protocolo_marca) {
      console.log('❌ Campos obrigatórios ausentes:', { link_cliente, protocolo_marca });
      res.status(400).json({
        success: false,
        error: 'Campos obrigatórios ausentes',
        details: 'link_cliente e protocolo_marca são obrigatórios'
      });
      return;
    }

    console.log('✅ Iniciando processamento do protocolo realizado...');

    // Buscar cliente pelo autoLoginUrl
    const cliente = await db.cliente.findFirst({
      where: {
        autoLoginUrl: link_cliente
      },
      select: {
        id: true,
        nome: true,
        identificador: true,
        autoLoginUrl: true,
        contatos: {
          select: {
            email: true,
            telefone: true
          }
        }
      }
    });

    if (!cliente) {
      console.log('❌ Cliente não encontrado para link:', link_cliente);
      res.status(404).json({
        success: false,
        error: 'Cliente não encontrado',
        link_cliente
      });
      return;
    }

    console.log('✅ Cliente encontrado:', {
      id: cliente.id,
      nome: cliente.nome,
      identificador: cliente.identificador
    });

    // 🆕 NOVA ABORDAGEM: Buscar protocolos processados recentemente (últimos 30 minutos)
    // Unificando com a abordagem do ChatGuru, mas com janela maior para email
    const janelaTemporal = 30 * 60 * 1000; // 30 minutos para emails (vs 10 min do WhatsApp)
    const agora = new Date();
    const tempoLimite = new Date(agora.getTime() - janelaTemporal);

    console.log('🔍 Buscando protocolos processados nos últimos 30 minutos:', {
      clienteId: cliente.id,
      tempoLimite: tempoLimite.toISOString(),
      agora: agora.toISOString()
    });

    // Buscar protocolos processados recentemente com sucesso
    const protocolosProcessados = await db.processamentoProtocolo.findMany({
      where: {
        clienteId: cliente.id,
        status: 'SUCESSO',
        processadoEm: {
          gte: tempoLimite
        }
      },
      include: {
        processo: {
          include: {
            marca: {
              select: {
                id: true,
                nome: true,
                apresentacao: true,
                createdAt: true
              }
            }
          }
        }
      },
      orderBy: {
        processadoEm: 'desc'
      }
    });

    if (protocolosProcessados.length === 0) {
      console.log('❌ Nenhum protocolo processado recentemente encontrado');
      res.status(404).json({
        success: false,
        error: 'Nenhum protocolo processado nos últimos 30 minutos foi encontrado para este cliente',
        cliente: {
          id: cliente.id,
          nome: cliente.nome,
          identificador: cliente.identificador
        },
        buscaPeriodo: {
          inicio: tempoLimite,
          fim: agora,
          janela: '30 minutos'
        }
      });
      return;
    }

    console.log('✅ Protocolos processados encontrados:', protocolosProcessados.length);

    // Extrair IDs únicos de processos e marcas
    const processosIds: string[] = [];
    const marcasIds: string[] = [];
    const marcasNomes: string[] = [];

    for (const protocolo of protocolosProcessados) {
      if (protocolo.processo?.id && !processosIds.includes(protocolo.processo.id)) {
        processosIds.push(protocolo.processo.id);
      }
      
      if (protocolo.processo?.marca?.id && !marcasIds.includes(protocolo.processo.marca.id)) {
        marcasIds.push(protocolo.processo.marca.id);
        marcasNomes.push(protocolo.processo.marca.nome || protocolo.processo.marca.apresentacao || 'Marca sem nome');
      }
    }

    console.log('📊 Dados extraídos:', {
      totalProtocolos: protocolosProcessados.length,
      processosUnicos: processosIds.length,
      marcasUnicas: marcasIds.length,
      marcasNomes: marcasNomes.join(', ')
    });

    // Determinar email do cliente
    const emailCliente = cliente.contatos.find((c: any) => c.email)?.email || null;

    // Gerar ID único para o comunicado
    const comunicadoId = uuidv4();

    // 🆕 Gerar título dinâmico baseado na quantidade (igual ao ChatGuru)
    const titulo = protocolosProcessados.length === 1 
      ? `Protocolo registrado - ${marcasNomes[0] || 'Marca'}`
      : `${protocolosProcessados.length} protocolos registrados`;

    // 🆕 Gerar descrição detalhada (igual ao ChatGuru)
    const formatarMarcasParaDescricao = (marcas: string[]): string => {
      if (marcas.length === 0) return "marcas";
      if (marcas.length === 1) return marcas[0];
      if (marcas.length === 2) return `${marcas[0]} e ${marcas[1]}`;
      const todasMenosUltima = marcas.slice(0, -1).join(', ');
      return `${todasMenosUltima} e ${marcas[marcas.length - 1]}`;
    };

    const descricao = protocolosProcessados.length === 1
      ? `Protocolo da marca "${marcasNomes[0]}" foi registrado com sucesso junto ao INPI.`
      : `${protocolosProcessados.length} protocolos das marcas ${formatarMarcasParaDescricao(marcasNomes)} foram registrados com sucesso junto ao INPI.`;

    console.log('💾 Criando comunicado:', {
      id: comunicadoId,
      titulo,
      clienteId: cliente.id,
      totalProtocolos: protocolosProcessados.length,
      totalProcessos: processosIds.length,
      totalMarcas: marcasIds.length
    });

    // Criar comunicado
    const comunicado = await db.comunicado.create({
      data: {
        id: comunicadoId,
        tipo: TipoComunicado.PROTOCOLO_MARCA,
        titulo,
        descricao,
        clienteId: cliente.id,
        processosIds,
        marcasIds,
        rdStationDataId: idc || null,
        rdStationEventId: leadData.uuid || null,
        email: emailCliente,
        metodoComunicacao: metodoComunicacao.EMAIL,
        status: StatusComunicado.ENVIADO,
        dataEnvio: agora,
        updatedAt: agora,
        dadosAdicionais: {
          rdStationData: dadosRecebidos,
          tipoProtocolo,
          protocolo_marca,
          protocolo_multiplas_marcas,
          leadInfo: {
            rdId: leadData.id,
            nome: leadData.name,
            email: leadData.email,
            uuid: leadData.uuid,
            criadoEm: leadData.created_at
          },
          processosDetalhes: protocolosProcessados.map((p: any) => ({
            numero: p.processo?.numero,
            marcaNome: p.processo?.marca?.nome || p.processo?.marca?.apresentacao,
            marcaCriadaEm: p.processo?.marca?.createdAt
          }))
        }
      }
    });

    console.log('✅ Comunicado criado com sucesso:', comunicado.id);

    // Log detalhado do sucesso
    chatGuruLogger.info('Comunicado de protocolo criado com sucesso', {
      comunicadoId: comunicado.id,
      clienteId: cliente.id,
      tipoProtocolo,
      totalProcessos: processosIds.length,
      totalMarcas: marcasIds.length,
      rdStationDataId: idc,
      leadRdId: leadData.id,
      leadNome: leadData.name,
      leadEmail: leadData.email
    });

    // Resposta de sucesso com dados do comunicado criado
    res.status(201).json({
      success: true,
      message: 'Comunicado de protocolo registrado com sucesso',
      data: {
        comunicado: {
          id: comunicado.id,
          tipo: comunicado.tipo,
          titulo: comunicado.titulo,
          status: comunicado.status,
          dataEnvio: comunicado.dataEnvio,
          totalProcessos: processosIds.length,
          totalMarcas: marcasIds.length
        },
        cliente: {
          id: cliente.id,
          nome: cliente.nome,
          identificador: cliente.identificador
        },
        processamento: {
          tipoProtocolo,
          timestamp,
          marcasEncontradas: protocolosProcessados.length
        },
        rdStationLead: {
          id: leadData.id,
          nome: leadData.name,
          email: leadData.email,
          uuid: leadData.uuid
        }
      }
    });

  } catch (error) {
    console.error('❌ Erro ao processar dados do RD Station:', error);
    chatGuruLogger.error('Erro ao processar dados do RD Station:', error);
    
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor ao processar dados'
    });
  }
};

/**
 * Endpoint de teste para verificar se a API está funcionando
 * 
 * @route GET /api/comunicados/health
 */
export const healthCheck = async (
  req: Request,
  res: Response
): Promise<void> => {
  res.status(200).json({
    success: true,
    message: 'API de comunicados funcionando corretamente',
    timestamp: new Date().toISOString()
  });
};

/**
 * Lista comunicados de protocolo para um cliente específico ou todos
 */
export const listarComunicadosProtocolo = async (req: Request, res: Response): Promise<any> => {
  try {
    const { 
      clienteId, 
      tipo = 'PROTOCOLO_MARCA',
      page = 1, 
      limit = 20,
      dataInicio,
      dataFim
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const take = Number(limit);

    // Construir filtros
    const where: any = {
      tipo: tipo as string
    };
    
    if (clienteId && typeof clienteId === 'string') {
      where.clienteId = parseInt(clienteId);
    }
    
    if (dataInicio && dataFim) {
      where.dataEnvio = {
        gte: new Date(dataInicio as string),
        lte: new Date(dataFim as string)
      };
    }

    // Buscar comunicados
    const [comunicados, total] = await Promise.all([
      prisma.comunicado.findMany({
        where,
        skip,
        take,
        orderBy: { dataEnvio: 'desc' },
        include: {
          Cliente: {
            select: {
              id: true,
              nome: true,
              identificador: true,
              autoLoginUrl: true
            }
          }
        }
      }),
      prisma.comunicado.count({ where })
    ]);

    // Enriquecer dados com informações dos processos e marcas
    const comunicadosEnriquecidos = await Promise.all(
      comunicados.map(async (comunicado) => {
        // Buscar processos relacionados
        const processos = comunicado.processosIds.length > 0 
          ? await prisma.processo.findMany({
              where: {
                id: { in: comunicado.processosIds }
              },
              select: {
                id: true,
                numero: true,
                dataDeposito: true
              }
            })
          : [];

        // Buscar marcas relacionadas
        const marcas = comunicado.marcasIds.length > 0
          ? await prisma.marca.findMany({
              where: {
                id: { in: comunicado.marcasIds }
              },
              select: {
                id: true,
                nome: true,
                apresentacao: true,
                temLogomarca: true
              }
            })
          : [];

        // Dados adicionais do comunicado
        const dadosAdicionais = comunicado.dadosAdicionais as any;

        return {
          id: comunicado.id,
          tipo: comunicado.tipo,
          titulo: comunicado.titulo,
          descricao: comunicado.descricao,
          metodoComunicacao: comunicado.metodoComunicacao,
          status: comunicado.status,
          dataEnvio: comunicado.dataEnvio,
          cliente: comunicado.Cliente,
          processos: processos.map(p => ({
            id: p.id,
            numero: p.numero,
            dataDeposito: p.dataDeposito
          })),
          marcas: marcas.map(m => ({
            id: m.id,
            nome: m.nome,
            apresentacao: m.apresentacao,
            temLogomarca: m.temLogomarca
          })),
          estatisticas: {
            quantidadeProtocolos: dadosAdicionais?.quantidadeProtocolos || processos.length,
            quantidadeProcessos: processos.length,
            quantidadeMarcas: marcas.length,
            chatguruConsolidado: dadosAdicionais?.chatguruConsolidado || false,
            telefone: dadosAdicionais?.telefone ? 
              `${dadosAdicionais.telefone.substring(0, 6)}****` : null
          }
        };
      })
    );

    return res.json({
      success: true,
      message: `${comunicadosEnriquecidos.length} comunicado(s) encontrado(s)`,
      data: comunicadosEnriquecidos,
      paginacao: {
        total,
        pagina: Number(page),
        limite: Number(limit),
        totalPaginas: Math.ceil(total / Number(limit))
      },
      filtros: {
        tipo: tipo,
        clienteId: clienteId || 'todos',
        periodo: dataInicio && dataFim ? `${dataInicio} a ${dataFim}` : 'todos'
      }
    });

  } catch (error) {
    console.error('Erro ao listar comunicados de protocolo:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro ao listar comunicados de protocolo',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
};

/**
 * Busca estatísticas de comunicados de protocolo
 */
export const estatisticasComunicadosProtocolo = async (req: Request, res: Response): Promise<any> => {
  try {
    const { clienteId, dataInicio, dataFim } = req.query;

    // Construir filtros
    const where: any = {
      tipo: 'PROTOCOLO_MARCA'
    };
    
    if (clienteId && typeof clienteId === 'string') {
      where.clienteId = parseInt(clienteId);
    }
    
    if (dataInicio && dataFim) {
      where.dataEnvio = {
        gte: new Date(dataInicio as string),
        lte: new Date(dataFim as string)
      };
    }

    // Buscar estatísticas
    const [
      totalComunicados,
      totalWhatsApp,
      totalEmail,
      comunicadosRecentes
    ] = await Promise.all([
      prisma.comunicado.count({ where }),
      prisma.comunicado.count({ 
        where: { ...where, metodoComunicacao: 'WHATSAPP' } 
      }),
      prisma.comunicado.count({ 
        where: { ...where, metodoComunicacao: 'EMAIL' } 
      }),
      prisma.comunicado.findMany({
        where,
        take: 5,
        orderBy: { dataEnvio: 'desc' },
        include: {
          Cliente: {
            select: {
              nome: true
            }
          }
        }
      })
    ]);

    // Calcular totais de processos e marcas comunicados
    const comunicadosCompletos = await prisma.comunicado.findMany({
      where,
      select: {
        processosIds: true,
        marcasIds: true,
        dadosAdicionais: true
      }
    });

    const processosUnicosSet = new Set<string>();
    const marcasUnicasSet = new Set<string>();
    let totalProtocolos = 0;

    comunicadosCompletos.forEach(comunicado => {
      comunicado.processosIds.forEach(id => processosUnicosSet.add(id));
      comunicado.marcasIds.forEach(id => marcasUnicasSet.add(id));
      
      const dadosAdicionais = comunicado.dadosAdicionais as any;
      totalProtocolos += dadosAdicionais?.quantidadeProtocolos || comunicado.processosIds.length;
    });

    return res.json({
      success: true,
      data: {
        resumo: {
          totalComunicados,
          totalProtocolos,
          totalProcessosUnicos: processosUnicosSet.size,
          totalMarcasUnicas: marcasUnicasSet.size
        },
        porMetodo: {
          whatsapp: totalWhatsApp,
          email: totalEmail
        },
        recentes: comunicadosRecentes.map(c => ({
          id: c.id,
          titulo: c.titulo,
          cliente: c.Cliente?.nome,
          metodoComunicacao: c.metodoComunicacao,
          dataEnvio: c.dataEnvio,
          quantidadeProcessos: c.processosIds.length,
          quantidadeMarcas: c.marcasIds.length
        }))
      }
    });

  } catch (error) {
    console.error('Erro ao buscar estatísticas de comunicados:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro ao buscar estatísticas de comunicados',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
}; 


