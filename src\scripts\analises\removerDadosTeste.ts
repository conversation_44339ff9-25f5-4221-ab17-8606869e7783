import { PrismaClient } from '@prisma/client';
import cliProgress from 'cli-progress';

const prisma = new PrismaClient();

// Identificadores dos clientes de teste criados pelos scripts
const CLIENTES_TESTE = ['00000001', '00000002', '00000003'];

// Prefixos dos números de processo de teste
const PREFIXOS_PROCESSO_TESTE = [
  'TESTE-00000001-',
  'TESTE-00000002-', 
  'TESTE-00000003-',
  'TESTE',
];

// Ranges de números de RPI fictícios criados pelos scripts
const RANGES_RPI_FICTICIOS = [
  { min: 999900000, max: 999999999 }, // criarDadosTesteFluxos.ts
  { min: 888800000, max: 888899999 }, // criarDadosTesteCliente002.ts
  { min: 777700000, max: 777799999 }  // criarDadosTesteCliente003.ts
];

// Códigos de despacho dos dados de teste
const CODIGOS_DESPACHO_TESTE = ['TESTE', 'FLUXO_ESP', 'FLUXO_DT3'];

/**
 * Remove todos os dados de teste criados pelos scripts anteriores
 */
async function removerDadosTeste() {
  try {
    console.log('🧹 Iniciando remoção de dados de teste...\n');

    // 1. Buscar processos de teste
    console.log('📋 Identificando processos de teste...');
    const processosConditions = PREFIXOS_PROCESSO_TESTE.map(prefixo => ({
      numero: { startsWith: prefixo }
    }));

    const processosTeste = await prisma.processo.findMany({
      where: { OR: processosConditions },
      select: { id: true, numero: true }
    });

    console.log(`   Encontrados ${processosTeste.length} processos de teste`);

    // 2. Buscar RPIs fictícias
    console.log('📋 Identificando RPIs fictícias...');
    const rpisConditions = RANGES_RPI_FICTICIOS.map(range => ({
      numero: { gte: range.min, lte: range.max }
    }));

    const rpisFicticias = await prisma.rPI.findMany({
      where: { OR: rpisConditions },
      select: { id: true, numero: true }
    });

    console.log(`   Encontradas ${rpisFicticias.length} RPIs fictícias`);

    // 3. Buscar clientes de teste
    console.log('📋 Identificando clientes de teste...');
    const clientesTeste = await prisma.cliente.findMany({
      where: { identificador: { in: CLIENTES_TESTE } },
      select: { id: true, identificador: true, nome: true }
    });

    console.log(`   Encontrados ${clientesTeste.length} clientes de teste`);

    if (processosTeste.length === 0 && rpisFicticias.length === 0 && clientesTeste.length === 0) {
      console.log('✅ Nenhum dado de teste encontrado para remover.');
      return;
    }

    // Confirmar remoção
    console.log('\n⚠️  RESUMO DOS DADOS QUE SERÃO REMOVIDOS:');
    console.log(`   • ${processosTeste.length} processos de teste`);
    console.log(`   • ${rpisFicticias.length} RPIs fictícias`);
    console.log(`   • ${clientesTeste.length} clientes de teste`);
    console.log('   • Todos os dados relacionados (despachos, marcas, etc.)\n');

    // Configurar barra de progresso
    const totalOperacoes = 10; // Número de etapas de deleção
    const barraProgresso = new cliProgress.SingleBar({
      format: 'Removendo dados |{bar}| {percentage}% | {value}/{total} Etapas',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });
    barraProgresso.start(totalOperacoes, 0);

    const processosIds = processosTeste.map(p => p.id);
    const rpisIds = rpisFicticias.map(r => r.id);
    const clientesIds = clientesTeste.map(c => c.id);

    // Iniciar transação para garantir atomicidade
    await prisma.$transaction(async (tx) => {
      // 1. Remover ProtocoloDespacho (referencia despacho)
      if (processosIds.length > 0) {
        await tx.protocoloDespacho.deleteMany({
          where: {
            despacho: {
              processoId: { in: processosIds }
            }
          }
        });
      }
      barraProgresso.increment();

      // 2. Remover Despachos (referencia processo e RPI)
      if (processosIds.length > 0 || rpisIds.length > 0) {
        const despachoConditions = [];
        if (processosIds.length > 0) {
          despachoConditions.push({ processoId: { in: processosIds } });
        }
        if (rpisIds.length > 0) {
          despachoConditions.push({ rpiId: { in: rpisIds } });
        }
        // Também remover por código de despacho
        despachoConditions.push({ codigo: { in: CODIGOS_DESPACHO_TESTE } });

        await tx.despacho.deleteMany({
          where: { OR: despachoConditions }
        });
      }
      barraProgresso.increment();

      // 3. Remover HistoricoComunicadoPrazoMerito e ComunicadoPrazoMerito
      if (processosIds.length > 0) {
        await tx.historicoComunicadoPrazoMerito.deleteMany({
          where: {
            comunicado: {
              processoId: { in: processosIds }
            }
          }
        });
        await tx.comunicadoPrazoMerito.deleteMany({
          where: { processoId: { in: processosIds } }
        });
      }
      barraProgresso.increment();

      // 4. Remover CrmProcessoControle
      if (processosIds.length > 0) {
        await tx.crmProcessoControle.deleteMany({
          where: { processoId: { in: processosIds } }
        });
      }
      barraProgresso.increment();

      // 5. Remover ProcessoScrapingControl
      if (processosIds.length > 0) {
        await tx.processoScrapingControl.deleteMany({
          where: { processoId: { in: processosIds } }
        });
      }
      barraProgresso.increment();

      // 6. Remover ProcessoInteresse
      if (processosIds.length > 0) {
        await tx.processoInteresse.deleteMany({
          where: { processoId: { in: processosIds } }
        });
      }
      barraProgresso.increment();

      // 7. Remover Sobrestadores
      if (processosIds.length > 0) {
        await tx.sobrestador.deleteMany({
          where: { processoId: { in: processosIds } }
        });
      }
      barraProgresso.increment();

      // 8. Remover Titulares
      if (processosIds.length > 0 || rpisIds.length > 0) {
        const titularConditions = [];
        if (processosIds.length > 0) {
          titularConditions.push({ processoId: { in: processosIds } });
        }
        if (rpisIds.length > 0) {
          titularConditions.push({ rpiId: { in: rpisIds } });
        }

        await tx.titular.deleteMany({
          where: { OR: titularConditions }
        });
      }
      barraProgresso.increment();

      // 9. Remover NCL, CFE e Marcas (NCL e CFE têm cascade delete)
      if (processosIds.length > 0) {
        await tx.marca.deleteMany({
          where: { processoId: { in: processosIds } }
        });
      }
      barraProgresso.increment();

      // 10. Remover Processos
      if (processosIds.length > 0) {
        await tx.processo.deleteMany({
          where: { id: { in: processosIds } }
        });
      }
      barraProgresso.increment();

      // 11. Remover RPIs (agora que não há mais referências)
      if (rpisIds.length > 0) {
        await tx.rPI.deleteMany({
          where: { id: { in: rpisIds } }
        });
      }

      // 12. Remover ContatoCliente
      if (clientesIds.length > 0) {
        await tx.contatoCliente.deleteMany({
          where: { clienteId: { in: clientesIds } }
        });
      }

      // 13. Remover Clientes de teste
      if (clientesIds.length > 0) {
        await tx.cliente.deleteMany({
          where: { id: { in: clientesIds } }
        });
      }
    });

    barraProgresso.stop();
    
    console.log('\n✅ Remoção de dados de teste concluída com sucesso!');
    console.log('📊 Dados removidos:');
    console.log(`   • ${processosTeste.length} processos`);
    console.log(`   • ${rpisFicticias.length} RPIs`);
    console.log(`   • ${clientesTeste.length} clientes`);
    console.log('   • Todos os registros relacionados (despachos, marcas, etc.)');

  } catch (error: any) {
    console.error('\n❌ Erro ao remover dados de teste:', error.message);
    if (error.code) {
      console.error('Código do erro:', error.code);
    }
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script
console.log('🚀 Script de remoção de dados de teste iniciado...');
removerDadosTeste(); 