import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
import fs from "fs";

dotenv.config();

const prisma = new PrismaClient();

interface ProcessoComCliente {
  id: string;
  numero: string;
  procuradorNome: string;
  cliente: {
    id: number;
    nome: string | null;
    identificador: string | null;
    numeroDocumento: string | null;
    crmId: number | null;
    contatos: {
      telefone: string | null;
      telefoneSegundario: string | null;
    }[];
  };
}

interface ProcessoSemCliente {
  id: string;
  numero: string;
  procuradorNome: string;
  titulares: {
    id: string;
    nomeRazaoSocial: string;
    numeroDocumento: string | null;
  }[];
}

interface EstatisticasAcesso {
  totalProcessos: number;
  comClienteComIdentificador: number;
  comClienteSemIdentificador: number;
  semCliente: number;
  clientesSemIdentificadorMasComContatos: number;
  clientesSemIdentificadorSemContatos: number;
  processosSemClienteMasComTitularCpf: number;
  processosSemClienteSemTitularCpf: number;
}

function isIdentificadorValido(identificador: string | null): boolean {
  if (!identificador) return false;
  
  const identificadorLimpo = identificador.trim();
  
  // Verifica se não é vazio, "null", ou só zeros
  if (!identificadorLimpo || 
      identificadorLimpo.toLowerCase() === "null" || 
      identificadorLimpo === "0000000000" ||
      identificadorLimpo.length !== 10) {
    return false;
  }
  
  return true;
}

async function analisarAcessosRegistreSe() {
  const startTime = Date.now();
  
  try {
    console.log("🔍 Analisando acessos aos processos REGISTRE-SE LTDA...\n");
    
    // 1. Buscar processos COM cliente
    console.log("📡 Buscando processos com cliente...");
    const processosComCliente = await prisma.processo.findMany({
      where: {
        procurador: {
          nome: {
            contains: "REGISTRE-SE LTDA",
            mode: "insensitive"
          }
        },
        clienteId: {
          not: null
        }
      },
      include: {
        procurador: {
          select: {
            nome: true
          }
        },
        cliente: {
          include: {
            contatos: {
              select: {
                telefone: true,
                telefoneSegundario: true
              }
            }
          }
        }
      },
      orderBy: {
        numero: 'asc'
      }
    });
    
    // 2. Buscar processos SEM cliente
    console.log("📡 Buscando processos sem cliente...");
    const processosSemCliente = await prisma.processo.findMany({
      where: {
        procurador: {
          nome: {
            contains: "REGISTRE-SE LTDA",
            mode: "insensitive"
          }
        },
        clienteId: null
      },
      include: {
        procurador: {
          select: {
            nome: true
          }
        },
        titulares: {
          select: {
            id: true,
            nomeRazaoSocial: true,
            numeroDocumento: true
          }
        }
      },
      orderBy: {
        numero: 'asc'
      }
    });
    
    console.log(`✅ Processos com cliente: ${processosComCliente.length}`);
    console.log(`✅ Processos sem cliente: ${processosSemCliente.length}`);
    
    // 3. Classificar processos com cliente
    const processosComClienteComIdentificador: ProcessoComCliente[] = [];
    const processosComClienteSemIdentificador: ProcessoComCliente[] = [];
    let clientesSemIdentificadorMasComContatos = 0;
    let clientesSemIdentificadorSemContatos = 0;
    
    for (const processo of processosComCliente) {
      if (!processo.cliente) continue;
      
      const processoInfo: ProcessoComCliente = {
        id: processo.id,
        numero: processo.numero,
        procuradorNome: processo.procurador?.nome || "N/A",
        cliente: {
          id: processo.cliente.id,
          nome: processo.cliente.nome,
          identificador: processo.cliente.identificador,
          numeroDocumento: processo.cliente.numeroDocumento,
          crmId: processo.cliente.crmId,
          contatos: processo.cliente.contatos || []
        }
      };
      
      if (isIdentificadorValido(processo.cliente.identificador)) {
        processosComClienteComIdentificador.push(processoInfo);
      } else {
        processosComClienteSemIdentificador.push(processoInfo);
        
        // Verificar se tem contatos
        const temContatos = processo.cliente.contatos && 
                           processo.cliente.contatos.length > 0 &&
                           processo.cliente.contatos.some(c => c.telefone && c.telefone !== "Não informado");
        
        if (temContatos) {
          clientesSemIdentificadorMasComContatos++;
        } else {
          clientesSemIdentificadorSemContatos++;
        }
      }
    }
    
    // 4. Analisar processos sem cliente
    let processosSemClienteMasComTitularCpf = 0;
    let processosSemClienteSemTitularCpf = 0;
    
    const processosSemClienteInfo: ProcessoSemCliente[] = [];
    
    for (const processo of processosSemCliente) {
      const processoInfo: ProcessoSemCliente = {
        id: processo.id,
        numero: processo.numero,
        procuradorNome: processo.procurador?.nome || "N/A",
        titulares: processo.titulares || []
      };
      
      processosSemClienteInfo.push(processoInfo);
      
      // Verificar se tem titular com CPF
      const temTitularComCpf = processo.titulares && 
                              processo.titulares.some(t => t.numeroDocumento && t.numeroDocumento.trim() !== "");
      
      if (temTitularComCpf) {
        processosSemClienteMasComTitularCpf++;
      } else {
        processosSemClienteSemTitularCpf++;
      }
    }
    
    // 5. Consolidar estatísticas
    const totalProcessos = processosComCliente.length + processosSemCliente.length;
    
    const estatisticas: EstatisticasAcesso = {
      totalProcessos,
      comClienteComIdentificador: processosComClienteComIdentificador.length,
      comClienteSemIdentificador: processosComClienteSemIdentificador.length,
      semCliente: processosSemCliente.length,
      clientesSemIdentificadorMasComContatos,
      clientesSemIdentificadorSemContatos,
      processosSemClienteMasComTitularCpf,
      processosSemClienteSemTitularCpf
    };
    
    // 6. Gerar relatórios
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const endTime = Date.now();
    const executionTime = (endTime - startTime) / 1000;
    
    // Relatório resumo principal
    const resumo = [
      '='.repeat(80),
      'ANÁLISE DE ACESSOS - PROCESSOS REGISTRE-SE LTDA',
      '='.repeat(80),
      `Data da análise: ${new Date().toLocaleString('pt-BR')}`,
      `Tempo de execução: ${executionTime.toFixed(2)} segundos`,
      '',
      'PANORAMA GERAL:',
      `📊 Total de processos REGISTRE-SE LTDA: ${totalProcessos}`,
      '',
      'PROCESSOS COM CLIENTE:',
      `✅ Com cliente E identificador válido: ${estatisticas.comClienteComIdentificador} (${((estatisticas.comClienteComIdentificador / totalProcessos) * 100).toFixed(1)}%)`,
      `⚠️  Com cliente MAS sem identificador: ${estatisticas.comClienteSemIdentificador} (${((estatisticas.comClienteSemIdentificador / totalProcessos) * 100).toFixed(1)}%)`,
      `   └─ Com contatos de telefone: ${estatisticas.clientesSemIdentificadorMasComContatos}`,
      `   └─ Sem contatos de telefone: ${estatisticas.clientesSemIdentificadorSemContatos}`,
      '',
      'PROCESSOS SEM CLIENTE:',
      `❌ Sem cliente vinculado: ${estatisticas.semCliente} (${((estatisticas.semCliente / totalProcessos) * 100).toFixed(1)}%)`,
      `   └─ Com titular que tem CPF: ${estatisticas.processosSemClienteMasComTitularCpf}`,
      `   └─ Sem titular com CPF: ${estatisticas.processosSemClienteSemTitularCpf}`,
      '',
      'CAPACIDADE DE ACESSO ATUAL:',
      `🔓 Processos acessíveis: ${estatisticas.comClienteComIdentificador} (${((estatisticas.comClienteComIdentificador / totalProcessos) * 100).toFixed(1)}%)`,
      `🔒 Processos inacessíveis: ${totalProcessos - estatisticas.comClienteComIdentificador} (${(((totalProcessos - estatisticas.comClienteComIdentificador) / totalProcessos) * 100).toFixed(1)}%)`,
      '',
      'POTENCIAL DE MELHORIA:',
      `📞 Corrigir identificadores: +${estatisticas.clientesSemIdentificadorMasComContatos} processos`,
      `👤 Criar clientes para titulares: +${estatisticas.processosSemClienteMasComTitularCpf} processos`,
      `🎯 Total potencial acessível: ${estatisticas.comClienteComIdentificador + estatisticas.clientesSemIdentificadorMasComContatos + estatisticas.processosSemClienteMasComTitularCpf} (${(((estatisticas.comClienteComIdentificador + estatisticas.clientesSemIdentificadorMasComContatos + estatisticas.processosSemClienteMasComTitularCpf) / totalProcessos) * 100).toFixed(1)}%)`,
      '',
      '='.repeat(80),
      ''
    ].join('\n');
    
    fs.writeFileSync(`analise-acessos-registre-se-${timestamp}.txt`, resumo, 'utf8');
    
    // CSV 1: Processos com cliente SEM identificador
    if (processosComClienteSemIdentificador.length > 0) {
      const csvClientesSemIdentificador = [
        'processo_id,numero_processo,cliente_id,nome_cliente,crm_id,numero_documento,tem_contatos,telefones',
        ...processosComClienteSemIdentificador.map(p => {
          const temContatos = p.cliente.contatos.length > 0 && p.cliente.contatos.some(c => c.telefone && c.telefone !== "Não informado");
          const telefones = p.cliente.contatos
            .filter(c => c.telefone && c.telefone !== "Não informado")
            .map(c => c.telefone)
            .join('; ');
          
          return [
            `"${p.id}"`,
            `"${p.numero}"`,
            `"${p.cliente.id}"`,
            `"${p.cliente.nome || 'N/A'}"`,
            `"${p.cliente.crmId || 'N/A'}"`,
            `"${p.cliente.numeroDocumento || 'N/A'}"`,
            `"${temContatos ? 'SIM' : 'NÃO'}"`,
            `"${telefones}"`
          ].join(',');
        })
      ].join('\n');
      
      fs.writeFileSync(`clientes-sem-identificador-${timestamp}.csv`, csvClientesSemIdentificador, 'utf8');
    }
    
    // CSV 2: Processos SEM cliente
    if (processosSemClienteInfo.length > 0) {
      const csvProcessosSemCliente = [
        'processo_id,numero_processo,tem_titular_com_cpf,titulares_info',
        ...processosSemClienteInfo.map(p => {
          const titularesComCpf = p.titulares.filter(t => t.numeroDocumento && t.numeroDocumento.trim() !== "");
          const temTitularComCpf = titularesComCpf.length > 0;
          
          const titularesInfo = p.titulares.map(t => 
            `${t.nomeRazaoSocial}${t.numeroDocumento ? ` (CPF: ${t.numeroDocumento})` : ' (Sem CPF)'}`
          ).join(' | ');
          
          return [
            `"${p.id}"`,
            `"${p.numero}"`,
            `"${temTitularComCpf ? 'SIM' : 'NÃO'}"`,
            `"${titularesInfo}"`
          ].join(',');
        })
      ].join('\n');
      
      fs.writeFileSync(`processos-sem-cliente-${timestamp}.csv`, csvProcessosSemCliente, 'utf8');
    }
    
    // CSV 3: Panorama completo (resumido)
    const csvPanorama = [
      'categoria,quantidade,percentual,descricao',
      `"Acessíveis","${estatisticas.comClienteComIdentificador}","${((estatisticas.comClienteComIdentificador / totalProcessos) * 100).toFixed(1)}%","Com cliente e identificador válido"`,
      `"Cliente sem ID","${estatisticas.comClienteSemIdentificador}","${((estatisticas.comClienteSemIdentificador / totalProcessos) * 100).toFixed(1)}%","Com cliente mas sem identificador"`,
      `"Sem cliente","${estatisticas.semCliente}","${((estatisticas.semCliente / totalProcessos) * 100).toFixed(1)}%","Sem cliente vinculado"`,
      `"ID fix potencial","${estatisticas.clientesSemIdentificadorMasComContatos}","${((estatisticas.clientesSemIdentificadorMasComContatos / totalProcessos) * 100).toFixed(1)}%","Clientes com contatos para corrigir ID"`,
      `"Cliente novo potencial","${estatisticas.processosSemClienteMasComTitularCpf}","${((estatisticas.processosSemClienteMasComTitularCpf / totalProcessos) * 100).toFixed(1)}%","Processos sem cliente mas com titular CPF"`
    ].join('\n');
    
    fs.writeFileSync(`panorama-acessos-${timestamp}.csv`, csvPanorama, 'utf8');
    
    // Relatório final no console
    console.log("\n🎯 ANÁLISE DE ACESSOS CONCLUÍDA:");
    console.log("=".repeat(60));
    console.log(`📊 Total de processos: ${totalProcessos}`);
    console.log(`✅ Acessíveis: ${estatisticas.comClienteComIdentificador} (${((estatisticas.comClienteComIdentificador / totalProcessos) * 100).toFixed(1)}%)`);
    console.log(`⚠️ Com cliente sem ID: ${estatisticas.comClienteSemIdentificador} (${((estatisticas.comClienteSemIdentificador / totalProcessos) * 100).toFixed(1)}%)`);
    console.log(`❌ Sem cliente: ${estatisticas.semCliente} (${((estatisticas.semCliente / totalProcessos) * 100).toFixed(1)}%)`);
    
    console.log("\n🔧 POTENCIAL DE MELHORIA:");
    console.log(`📞 Corrigir IDs: +${estatisticas.clientesSemIdentificadorMasComContatos} processos`);
    console.log(`👤 Criar clientes: +${estatisticas.processosSemClienteMasComTitularCpf} processos`);
    console.log(`🎯 Total alcançável: ${estatisticas.comClienteComIdentificador + estatisticas.clientesSemIdentificadorMasComContatos + estatisticas.processosSemClienteMasComTitularCpf} (${(((estatisticas.comClienteComIdentificador + estatisticas.clientesSemIdentificadorMasComContatos + estatisticas.processosSemClienteMasComTitularCpf) / totalProcessos) * 100).toFixed(1)}%)`);
    
    console.log(`\n⏱️ Tempo de execução: ${executionTime.toFixed(2)} segundos`);
    
    console.log("\n📁 ARQUIVOS GERADOS:");
    console.log(`   📋 Relatório completo: analise-acessos-registre-se-${timestamp}.txt`);
    console.log(`   📊 Panorama geral: panorama-acessos-${timestamp}.csv`);
    
    if (processosComClienteSemIdentificador.length > 0) {
      console.log(`   ⚠️ Clientes sem ID: clientes-sem-identificador-${timestamp}.csv`);
    }
    
    if (processosSemClienteInfo.length > 0) {
      console.log(`   ❌ Processos sem cliente: processos-sem-cliente-${timestamp}.csv`);
    }
    
    console.log(`\n📂 Localização: ${process.cwd()}/`);
    
    return estatisticas;
    
  } catch (error: any) {
    console.error("❌ Erro durante a análise:", error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Função principal
async function main() {
  console.log("🚀 ANÁLISE DE ACESSOS - REGISTRE-SE LTDA\n");
  
  try {
    const resultado = await analisarAcessosRegistreSe();
    
    console.log("\n✅ Análise concluída com sucesso!");
    
    const totalInacessiveis = resultado.comClienteSemIdentificador + resultado.semCliente;
    
    if (totalInacessiveis > 0) {
      console.log(`\n💡 Próximos passos sugeridos:`);
      console.log(`   • Corrigir identificadores de ${resultado.clientesSemIdentificadorMasComContatos} clientes com contatos`);
      console.log(`   • Criar clientes para ${resultado.processosSemClienteMasComTitularCpf} processos com titular CPF`);
      console.log(`   • Investigar ${resultado.clientesSemIdentificadorSemContatos} clientes sem contatos`);
      console.log(`   • Enriquecer ${resultado.processosSemClienteSemTitularCpf} processos sem titular CPF`);
    }
    
  } catch (error: any) {
    console.error("❌ Erro durante a execução:", error.message);
    process.exit(1);
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  main();
}

export { analisarAcessosRegistreSe }; 