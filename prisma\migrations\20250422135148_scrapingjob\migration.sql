-- CreateEnum
CREATE TYPE "ScrapingJobStatus" AS ENUM ('PENDING', 'SENT', 'PROCESSING', 'COMPLETED', 'FAILED');

-- CreateTable
CREATE TABLE "ScrapingJob" (
    "id" TEXT NOT NULL,
    "jobId" TEXT NOT NULL,
    "processoNumeros" TEXT[],
    "status" "ScrapingJobStatus" NOT NULL DEFAULT 'PENDING',
    "errorMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ScrapingJob_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ScrapingJob_jobId_key" ON "ScrapingJob"("jobId");

-- CreateIndex
CREATE INDEX "ScrapingJob_jobId_idx" ON "ScrapingJob"("jobId");

-- CreateIndex
CREATE INDEX "ScrapingJob_status_idx" ON "ScrapingJob"("status");

-- CreateIndex
CREATE INDEX "ScrapingJob_createdAt_idx" ON "ScrapingJob"("createdAt");
