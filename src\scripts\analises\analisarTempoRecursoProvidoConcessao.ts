import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

const prisma = new PrismaClient();

// Array com todas as RPIs a serem consideradas para a CONCESSÃO
const RPIS_ANALISE = Array.from({length: 30}, (_, i) => 2800 + i); // Gera array de 2800 a 2829

/**
 * <PERSON><PERSON><PERSON> o tempo médio entre o despacho "Recurso provido (decisão reformada para: Deferimento)"
 * e o despacho subsequente de "Concessão de registro".
 * Considera apenas processos onde a concessão ocorre diretamente após o recurso provido,
 * sem outros despachos intermediários.
 */
async function analisarTempoRecursoProvidoConcessao() {
  try {
    console.log('Iniciando análise do tempo entre Recurso Provido (Deferido) e Concessão...');
    console.log(`Filtrando por Concessões de Registro nas RPIs ${RPIS_ANALISE[0]} a ${RPIS_ANALISE[RPIS_ANALISE.length-1]}\n`);

    const dispatchRecursoProvido =
      "Recurso provido (decisão reformada para: Deferimento)";
    const dispatchConcessao = 'Concessão de registro';

    let conteudoArquivo = 'ANÁLISE DO TEMPO ENTRE RECURSO PROVIDO (DEFERIDO) E CONCESSÃO DE REGISTRO\n';
    conteudoArquivo += `RPIs da Concessão Analisadas: ${RPIS_ANALISE[0]} a ${RPIS_ANALISE[RPIS_ANALISE.length-1]}\n`;
    conteudoArquivo += 'Considerando apenas processos sem despachos intermediários entre os dois eventos.\n';
    conteudoArquivo += '='.repeat(80) + '\n\n';

    // Buscar processos que têm Recurso Provido (Deferido) e Concessão
    const processos = await prisma.processo.findMany({
      where: {
        despachos: {
          some: {
            nome: { contains: dispatchRecursoProvido, mode: 'insensitive' },
          }
        },
        AND: {
          despachos: {
            some: {
              // A concessão deve estar em uma das RPIs alvo
              AND: [
                {
                  nome: { contains: dispatchConcessao, mode: 'insensitive' }
                },
                {
                  rpi: {
                    numero: {
                      in: RPIS_ANALISE
                    }
                  }
                }
              ]
            }
          }
        }
      },
      include: {
        despachos: {
          include: {
            rpi: true
          },
          orderBy: {
            rpi: {
              dataPublicacao: 'asc'
            }
          }
        }
      }
    });

    console.log(`Encontrados ${processos.length} processos com '${dispatchRecursoProvido} (...Deferimento)' e '${dispatchConcessao}'.`);
    conteudoArquivo += `Total de processos encontrados com ambos os despachos: ${processos.length}\n\n`;

    let tempoTotal = 0;
    let tempoMinimo = Number.MAX_VALUE;
    let tempoMaximo = 0;
    let processosAnalisados = 0;
    let processosDescartados = 0;
    const tempos: number[] = [];
    const detalhesProcessos: string[] = [];

    for (const processo of processos) {
      // Encontrar índices dos despachos relevantes
      const indexRecursoProvido = processo.despachos.findIndex(d => 
        d.nome?.toLowerCase() === dispatchRecursoProvido.toLowerCase()
      );
      
      // Encontrar a primeira concessão *após* o recurso provido E DENTRO DAS RPIS ALVO
      const indexConcessao = processo.despachos.findIndex((d, index) => 
        index > indexRecursoProvido &&
        d.nome?.toLowerCase().includes(dispatchConcessao.toLowerCase()) &&
        RPIS_ANALISE.includes(d.rpi?.numero || 0) // Garantir que a RPI está no array alvo
      );

      // Verificar se encontrou ambos os despachos na ordem correta e com a RPI correta
      if (indexRecursoProvido !== -1 && indexConcessao !== -1) {
        const recursoProvido = processo.despachos[indexRecursoProvido];
        const concessao = processo.despachos[indexConcessao];

        // Verificar se há despachos *entre* o recurso provido e a concessão
        const temDespachoEntre = processo.despachos.some((d, index) => {
          return index > indexRecursoProvido && 
                 index < indexConcessao && 
                 d.id !== recursoProvido.id && // Comparar por ID para garantir que não é o mesmo despacho
                 d.id !== concessao.id;
        });

        if (!temDespachoEntre && recursoProvido?.rpi?.dataPublicacao && concessao?.rpi?.dataPublicacao) {
          const dataRecursoProvido = new Date(recursoProvido.rpi.dataPublicacao);
          const dataConcessao = new Date(concessao.rpi.dataPublicacao);
          
          // Garantir que a concessão é posterior ao recurso provido
          // E que a RPI da concessão está na lista (verificação redundante, mas segura)
          if (dataConcessao <= dataRecursoProvido || !RPIS_ANALISE.includes(concessao.rpi.numero)) {
            processosDescartados++; // Contar como descartado se a ordem estiver errada ou RPI fora
            continue;
          }

          const diffTime = Math.abs(dataConcessao.getTime() - dataRecursoProvido.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          tempos.push(diffDays);
          tempoTotal += diffDays;
          tempoMinimo = Math.min(tempoMinimo, diffDays);
          tempoMaximo = Math.max(tempoMaximo, diffDays);
          processosAnalisados++;

          detalhesProcessos.push(
            `Processo: ${processo.numero}\n` +
            `Data Recurso Provido (Deferido): ${dataRecursoProvido.toLocaleDateString('pt-BR')} (RPI: ${recursoProvido.rpi.numero})\n` +
            `Data da Concessão: ${dataConcessao.toLocaleDateString('pt-BR')} (RPI: ${concessao.rpi.numero})\n` +
            `Tempo decorrido: ${diffDays} dias\n`
          );
        } else if (temDespachoEntre) {
          // Contar como descartado se houver despacho intermediário
          processosDescartados++;
        }
      }
    }

    if (processosAnalisados === 0) {
      const mensagem = `Nenhum processo encontrado com os critérios para análise (Recurso Provido seguido diretamente por Concessão nas RPIs ${RPIS_ANALISE[0]}-${RPIS_ANALISE[RPIS_ANALISE.length-1]}).`;
      console.log(mensagem);
      conteudoArquivo += mensagem;
      const nomeArquivoZero = `analise_recurso_provido_concessao_rpi${RPIS_ANALISE[0]}_${RPIS_ANALISE[RPIS_ANALISE.length-1]}_${new Date().toISOString().split('T')[0]}.txt`;
      fs.writeFileSync(nomeArquivoZero, conteudoArquivo);
      console.log(`Arquivo de análise (sem resultados) salvo em: ${nomeArquivoZero}`);
      return;
    }

    // Calcular média, desvio padrão e mediana
    const media = tempoTotal / processosAnalisados;
    const somaDiferencasQuadrado = tempos.reduce((acc, tempo) => acc + (tempo - media) ** 2, 0);
    const desvioPadrao = Math.sqrt(somaDiferencasQuadrado / processosAnalisados);
    tempos.sort((a, b) => a - b);
    const mediana = processosAnalisados % 2 === 0
      ? (tempos[processosAnalisados / 2 - 1] + tempos[processosAnalisados / 2]) / 2
      : tempos[Math.floor(processosAnalisados / 2)];

    // Adicionar resultados estatísticos ao arquivo
    conteudoArquivo += 'RESULTADOS ESTATÍSTICOS\n';
    conteudoArquivo += '-'.repeat(30) + '\n';
    conteudoArquivo += `Total de processos analisados: ${processosAnalisados}\n`;
    conteudoArquivo += `Processos descartados (despachos intermediários ou ordem incorreta): ${processosDescartados}\n`;
    conteudoArquivo += `Tempo médio: ${media.toFixed(2)} dias\n`;
    conteudoArquivo += `Desvio padrão: ${desvioPadrao.toFixed(2)} dias\n`;
    conteudoArquivo += `Mediana: ${mediana.toFixed(2)} dias\n`;
    conteudoArquivo += `Tempo mínimo: ${tempoMinimo} dias\n`;
    conteudoArquivo += `Tempo máximo: ${tempoMaximo} dias\n\n`;

    // Adicionar distribuição dos tempos
    conteudoArquivo += 'DISTRIBUIÇÃO DOS TEMPOS\n';
    conteudoArquivo += '-'.repeat(30) + '\n';
    const intervalos = [30, 60, 90, 120, 150, 180, 365, 730]; // Intervalos ajustados
    
    let temposAnteriores = 0;
    for (const intervalo of intervalos) {
      const quantidade = tempos.filter(t => t > temposAnteriores && t <= intervalo).length;
      const percentual = ((quantidade / processosAnalisados) * 100).toFixed(2);
      conteudoArquivo += `${temposAnteriores + 1} a ${intervalo} dias: ${quantidade} processos (${percentual}%)\n`;
      temposAnteriores = intervalo;
    }
    
    const quantidadeRestante = tempos.filter(t => t > intervalos[intervalos.length - 1]).length;
    const percentualRestante = ((quantidadeRestante / processosAnalisados) * 100).toFixed(2);
    conteudoArquivo += `Mais de ${intervalos[intervalos.length - 1]} dias: ${quantidadeRestante} processos (${percentualRestante}%)\n\n`;

    // Adicionar detalhes dos processos
    conteudoArquivo += 'DETALHES DOS PROCESSOS ANALISADOS\n';
    conteudoArquivo += '-'.repeat(30) + '\n';
    detalhesProcessos.forEach((detalhe, index) => {
      conteudoArquivo += detalhe + (index < detalhesProcessos.length - 1 ? '\n' : '');
    });

    // Salvar arquivo
    const nomeArquivo = `analise_recurso_provido_concessao_rpi${RPIS_ANALISE[0]}_${RPIS_ANALISE[RPIS_ANALISE.length-1]}_${new Date().toISOString().split('T')[0]}.txt`;
    fs.writeFileSync(nomeArquivo, conteudoArquivo);
    
    console.log(`\nAnálise completa! Resultados salvos em: ${nomeArquivo}`);
    console.log(`Total de processos analisados: ${processosAnalisados}`);
    console.log(`Processos descartados: ${processosDescartados}`);
    console.log(`Tempo médio: ${media.toFixed(2)} dias`);

  } catch (error) {
    console.error('Erro ao analisar tempo entre Recurso Provido (Deferido) e Concessão:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar a análise
analisarTempoRecursoProvidoConcessao(); 