import { Router } from 'express';
import * as comunicadoController from '../controllers/comunicadoController';

const router = Router();

/**
 * @route   GET /api/comunicados/health
 * @desc    Verifica se a API de comunicados está funcionando
 * @access  Público
 */
router.get('/health', comunicadoController.healthCheck);

/**
 * @route   GET /api/comunicados/cliente/:clienteId
 * @desc    Lista comunicados de um cliente específico
 * @access  Privado
 * @params  clienteId: ID do cliente
 * @query   page: Página (padrão: 1)
 * @query   limit: Limite por página (padrão: 10, máximo: 50)
 * @query   tipo: Filtro por tipo de comunicado (TipoComunicado)
 * @query   status: Filtro por status (StatusComunicado)
 * @query   dataInicio: Data inicial (YYYY-MM-DD)
 * @query   dataFim: Data final (YYYY-MM-DD)
 */
router.get('/cliente/:clienteId', comunicadoController.listarComunicadosCliente);

/**
 * @route   POST /api/comunicados/protocolo/:tipoProtocolo
 * @desc    Recebe dados do RD Station e loga no console (sem salvar no banco)
 * @access  Público (webhook do RD Station)
 * @params  tipoProtocolo: 'unica' | 'multiplas'
 * @body    Qualquer JSON (será logado no console)
 */
router.post('/protocolo/:tipoProtocolo', comunicadoController.registrarComunicadoProtocolo);

/**
 * @route GET /api/comunicados/protocolos
 * @desc Listar comunicados de protocolo (WhatsApp/Email)
 * @access Public
 */
router.get('/protocolos', comunicadoController.listarComunicadosProtocolo);

/**
 * @route GET /api/comunicados/protocolos/estatisticas
 * @desc Estatísticas de comunicados de protocolo
 * @access Public
 */
router.get('/protocolos/estatisticas', comunicadoController.estatisticasComunicadosProtocolo);

export default router; 