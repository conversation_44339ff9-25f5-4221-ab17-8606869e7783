import { PrismaClient } from '@prisma/client';
import { dialogoMapping } from '../../utils/chatguru.utils';

// Inicializar o cliente Prisma
const prisma = new PrismaClient();

// Definir tipos para o retorno do kanban
export type KanbanColuna = {
  nome: string;
  chave: string;
  dialogId?: string;
  processos: any[];
  total: number;
};

export type KanbanResponse = {
  colunas: KanbanColuna[];
  totalProcessos: number;
};

/**
 * Converte dias para meses (baseado em múltiplos exatos de 30 dias)
 */
function diasParaMeses(dias: number): number {
  return Math.floor(dias / 30);
}

/**
 * Obtém as colunas de prazo a partir do dialogoMapping
 * Filtra apenas as entradas relacionadas a prazos mensais e organiza em ordem decrescente
 */
function obterColunasPrazo(): KanbanColuna[] {
  const colunas: KanbanColuna[] = [];
  const prazoRegex = /^faltam (\d+) meses$/;
  const umMesRegex = /^falta cerca de 1 mês$/;
  
  // Adiciona colunas para os prazos em meses (do maior para o menor)
  Object.entries(dialogoMapping).forEach(([chave, config]) => {
    const match = chave.match(prazoRegex);
    if (match) {
      const meses = parseInt(match[1]);
      colunas.push({
        nome: `${meses} meses`,
        chave: meses.toString(),
        dialogId: config.dialogoChatguru,
        processos: [],
        total: 0
      });
    } else if (umMesRegex.test(chave)) {
      // Caso especial para "falta cerca de 1 mês"
      colunas.push({
        nome: "1 mês",
        chave: "1",
        dialogId: config.dialogoChatguru,
        processos: [],
        total: 0
      });
    }
  });
  
  // Adiciona coluna "Vencendo" para processos com menos de 30 dias
  colunas.push({
    nome: "Vencendo",
    chave: "0",
    dialogId: dialogoMapping["Falta pouco"]?.dialogoChatguru,
    processos: [],
    total: 0
  });
  
  // Ordena as colunas do maior prazo para o menor
  return colunas.sort((a, b) => parseInt(b.chave) - parseInt(a.chave));
}

// Gerar colunas do kanban a partir do dialogoMapping
const COLUNAS_KANBAN_PRAZO = obterColunasPrazo();

/**
 * Obtém os processos agrupados por meses restantes até o mérito para visualização em kanban
 */
export async function obterKanbanPorPrazo(filtros: {
  clienteId?: number;
  tipoProcesso?: string;
  elegivelParaComunicados?: boolean;
  limite?: number;
}): Promise<KanbanResponse> {
  try {
    // Definir limite padrão de 50 processos por coluna
    const limite = filtros.limite || 50;
    
    // Construir condições WHERE para os filtros
    const where: any = {
      dataMeritoEstimada: { not: null },  // Processos com data de mérito estimada
      dataMerito: null                    // Processos que ainda não possuem mérito
    };
    
    // Adicionar filtros conforme necessário
    if (filtros.clienteId) {
      where.clienteId = filtros.clienteId;
    }
    
    if (filtros.tipoProcesso) {
      where.tipo = filtros.tipoProcesso;
    }
    
    if (filtros.elegivelParaComunicados !== undefined) {
      where.elegivelParaComunicados = filtros.elegivelParaComunicados;
    }
    
    // Contar total de processos que atendem aos filtros
    const totalProcessos = await prisma.processo.count({ where });
    
    // Buscar todos os processos com data de mérito estimada
    const todosProcessos = await prisma.processo.findMany({
      where,
      include: {
        cliente: {
          select: {
            id: true,
            nome: true,
            crmId: true
          }
        },
        despachos: {
          take: 1,
          orderBy: {
            rpi: {
              dataPublicacao: 'desc'
            }
          },
          include: {
            rpi: true
          }
        }
      },
      orderBy: {
        dataMeritoEstimada: 'asc'
      }
    });
    
    // Log para debug
    console.log(`Total de processos encontrados: ${todosProcessos.length}`);
    
    // Inicializar as colunas do kanban
    const colunasMap = new Map<string, KanbanColuna>();
    
    // Inicializar as colunas com arrays vazios
    for (const coluna of COLUNAS_KANBAN_PRAZO) {
      colunasMap.set(coluna.chave, {
        ...coluna,
        processos: []
      });
    }
    
    // Para cada processo, calcular os meses restantes e classificar na coluna apropriada
    for (const processo of todosProcessos) {
      // Calcular dias restantes com base na data atual e na data estimada
      const hoje = new Date();
      const dataEstimada = processo.dataMeritoEstimada ? new Date(processo.dataMeritoEstimada) : hoje;
      const diasRestantes = Math.ceil(
        (dataEstimada.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      // Calcular meses restantes
      const mesesRestantes = diasParaMeses(diasRestantes);
      
      // Classificar o processo na coluna apropriada
      let colunaEncontrada = false;
      
      // Tenta encontrar a coluna exata
      if (colunasMap.has(mesesRestantes.toString())) {
        const coluna = colunasMap.get(mesesRestantes.toString());
        if (coluna) {
          coluna.processos.push(processo);
          colunaEncontrada = true;
        }
      }
      
      // Se não encontrou a coluna exata, usa a regra geral
      if (!colunaEncontrada) {
        let colunaChave: string;
        
        if (mesesRestantes <= 0) {
          colunaChave = "0"; // Vencendo
        } else {
          // Encontrar a coluna mais próxima (menor ou igual ao número de meses)
          const chaves = Array.from(colunasMap.keys())
            .map(k => parseInt(k))
            .filter(k => k <= mesesRestantes)
            .sort((a, b) => b - a);
          
          colunaChave = chaves.length > 0 ? chaves[0].toString() : "0";
        }
        
        // Adicionar o processo à coluna correspondente
        const coluna = colunasMap.get(colunaChave);
        if (coluna) {
          coluna.processos.push(processo);
        }
      }
    }
    
    // Converter o Map para um array de colunas e aplicar o limite por coluna
    const colunas: KanbanColuna[] = COLUNAS_KANBAN_PRAZO.map(colunaConfig => {
      const coluna = colunasMap.get(colunaConfig.chave);
      const processos = coluna ? coluna.processos.slice(0, limite) : [];
      
      return {
        nome: colunaConfig.nome,
        chave: colunaConfig.chave,
        dialogId: colunaConfig.dialogId,
        processos,
        total: coluna ? coluna.processos.length : 0
      };
    });
    
    return {
      colunas,
      totalProcessos
    };
  } catch (error) {
    console.error("Erro ao obter kanban por prazo:", error);
    throw error;
  }
} 