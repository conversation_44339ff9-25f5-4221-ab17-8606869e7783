import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';
import * as cliProgress from 'cli-progress';

const prisma = new PrismaClient();

interface AnaliseResult {
  processosAguardandoMerito: {
    total: number;
    dataDepositoMaisAntiga: Date | null;
    dataDepositoMaisRecente: Date | null;
    distribuicaoDespachos: Record<string, number>;
  };
  processosMeritoRPIs: {
    total: number;
    dataDepositoMaisAntiga: Date | null;
    dataDepositoMaisRecente: Date | null;
    distribuicaoTipoMerito: Record<string, number>;
    distribuicaoRPIs: Record<string, number>;
  };
}

interface AnaliseResultSql {
  processosAguardandoMerito: {
    total: number;
    dataDepositoMaisAntiga: Date | null;
    dataDepositoMaisRecente: Date | null;
    distribuicaoDespachos: Record<string, number>;
  };
  processosMeritoRPIs: {
    total: number;
    dataDepositoMaisAntiga: Date | null;
    dataDepositoMaisRecente: Date | null;
    distribuicaoTipoMerito: Record<string, number>;
    distribuicaoRPIs: Record<string, number>;
  };
}

// Opções de configuração
interface ConfigAnalise {
  modoTeste: boolean;
  limiteTeste: number;
}

function log(message: string) {
  const timestamp = new Date().toLocaleTimeString('pt-BR');
  console.log(`[${timestamp}] ${message}`);
}

async function analisarProcessosEsperaMeritoPerformance(config: ConfigAnalise = { modoTeste: false, limiteTeste: 1000 }): Promise<AnaliseResult> {
  log('🚀 Iniciando análise otimizada de processos aguardando mérito...');
  
  if (config.modoTeste) {
    log(`🧪 MODO TESTE ATIVADO - Limitando a ${config.limiteTeste.toLocaleString('pt-BR')} registros`);
  }

  // Configurar barra de progresso
  const progressBar = new cliProgress.SingleBar({
    format: '📊 Progresso |{bar}| {percentage}% | {value}/{total} | Etapa: {etapa}',
    barCompleteChar: '\u2588',
    barIncompleteChar: '\u2591',
    hideCursor: true
  });

  progressBar.start(100, 0, { etapa: 'Iniciando...' });

  try {
    // ETAPA 1: Buscar TODOS os processos com despachos de publicação e TODOS seus despachos
    progressBar.update(20, { etapa: 'Buscando processos com publicação...' });
    log('🔍 Buscando todos os processos com despachos de publicação e seus despachos...');

    const nomesDespachosPublicacao = [
      'Publicação de pedido de registro para oposição (exame formal concluído)',
      'Republicação de pedido (por perda da prioridade)', 
      'Republicação de pedido',
      'Publicação de pedido de registro para oposição (exame formal de designação concluído)'
    ];

    const processosComPublicacao = await prisma.processo.findMany({
      where: {
        despachos: {
          some: {
            nome: {
              in: nomesDespachosPublicacao
            }
          }
        }
      },
      take: config.modoTeste ? config.limiteTeste : undefined,
      select: {
        id: true,
        numero: true,
        dataDeposito: true,
        despachos: {
          select: {
            nome: true
          }
        }
      }
    });

    log(`✅ Encontrados ${processosComPublicacao.length} processos com despachos de publicação`);
    progressBar.update(50, { etapa: 'Filtrando em memória...' });

    // ETAPA 2: Filtrar em memória os processos que ainda aguardam mérito
    log('🧠 Filtrando em memória os processos que ainda aguardam exame de mérito...');

    // Lista de nomes de despachos que indicam finalização do processo
    const nomesDespachosFim = [
      // Despachos de mérito
      'Deferimento do pedido',
      'Indeferimento do pedido',
      
      // Decisões de pedido inexistente  
      'Decisão de considerar pedido inexistente por exigência de pagamento não respondida',
      'Decisão de considerar pedido inexistente por falta de pagamento',
      'Decisão de considerar pedido inexistente por exigência de pagamento não cumprida',
      'Decisão de considerar pedido inexistente por exigência formal não respondida',
      'Decisão de considerar pedido inexistente por exigência formal não cumprida',
      
      // Arquivamentos definitivos
      'Arquivamento definitivo de pedido de registro por falta de procuração',
      'Arquivamento definitivo de pedido de registro por falta de cumprimento de exigência de mérito',
      'Arquivamento definitivo de pedido de registro por falta de documentos de marca de certificação',
      'Arquivamento definitivo de pedido de registro por falta de documentos de marca coletiva', 
      'Arquivamento definitivo de designação por falta de docs. de marca coletiva ou de certificação',
      'Arquivamento definitivo de designação por falta de cumprimento de exigência de mérito',
      
      // Outros arquivamentos e desistências
      'Desistência de pedido de registro de marca',
      'Arquivamento de ofício de pedido de registro de marca',
      'Cancelamento de ofício de registro de marca',
      'Arquivamento do pedido',
      'Desistência do pedido'
    ];

    const processosAguardandoMerito = processosComPublicacao.filter(processo => {
      // Verificar se o processo tem algum despacho de finalização
      const temDespachoFim = processo.despachos.some(despacho => 
        nomesDespachosFim.some(nomeFim => 
          despacho.nome?.includes(nomeFim)
        )
      );
      
      // Se não tem despacho de fim, ainda está aguardando mérito
      return !temDespachoFim;
    });

    log(`✅ ${processosAguardandoMerito.length} processos estão aguardando mérito (${processosComPublicacao.length - processosAguardandoMerito.length} já finalizados)`);

    // ETAPA 3: Processar estatísticas dos processos aguardando mérito
    progressBar.update(70, { etapa: 'Processando estatísticas...' });
    log('📈 Processando estatísticas dos processos aguardando mérito...');

    const distribuicaoDespachos: Record<string, number> = {};
    const datasDeposito: Date[] = [];

    processosAguardandoMerito.forEach(processo => {
      // Encontrar o despacho de publicação
      const despachoPublicacao = processo.despachos.find(despacho => 
        nomesDespachosPublicacao.some(nomePubl => 
          despacho.nome?.includes(nomePubl)
        )
      );
      
      if (despachoPublicacao?.nome) {
        distribuicaoDespachos[despachoPublicacao.nome] = (distribuicaoDespachos[despachoPublicacao.nome] || 0) + 1;
      }
      
      if (processo.dataDeposito) {
        datasDeposito.push(processo.dataDeposito);
      }
    });

    const dataDepositoMaisAntigaAguardando = datasDeposito.length > 0 
      ? new Date(Math.min(...datasDeposito.map(d => d.getTime()))) 
      : null;
    const dataDepositoMaisRecenteAguardando = datasDeposito.length > 0 
      ? new Date(Math.max(...datasDeposito.map(d => d.getTime()))) 
      : null;

    log(`📊 Distribuição de despachos processada: ${Object.keys(distribuicaoDespachos).length} tipos`);

    // ETAPA 4: Buscar processos com mérito nas RPIs específicas (mantém a consulta otimizada)
    progressBar.update(85, { etapa: 'Buscando mérito nas RPIs...' });
    log('⚖️ Buscando processos com mérito nas RPIs [2837, 2838, 2839, 2840]...');

    const processosMeritoRPIs = await prisma.processo.findMany({
      where: {
        despachos: {
          some: {
            AND: [
              {
                nome: {
                  in: ['Deferimento do pedido', 'Indeferimento do pedido']
                }
              },
              {
                rpi: {
                  numero: {
                    in: [2837, 2838, 2839, 2840]
                  }
                }
              }
            ]
          }
        }
      },
      take: config.modoTeste ? config.limiteTeste : undefined,
      select: {
        numero: true,
        dataDeposito: true,
        despachos: {
          where: {
            AND: [
              {
                nome: {
                  in: ['Deferimento do pedido', 'Indeferimento do pedido']
                }
              },
              {
                rpi: {
                  numero: {
                    in: [2837, 2838, 2839, 2840]
                  }
                }
              }
            ]
          },
          select: {
            nome: true,
            rpi: {
              select: {
                numero: true,
                dataPublicacao: true
              }
            }
          }
        }
      }
    });

    log(`✅ Encontrados ${processosMeritoRPIs.length} processos com mérito nas RPIs específicas`);

    // ETAPA 5: Processar dados dos processos com mérito
    progressBar.update(95, { etapa: 'Finalizando estatísticas...' });
    log('📈 Processando estatísticas dos processos com mérito...');

    const distribuicaoTipoMerito: Record<string, number> = {};
    const distribuicaoRPIs: Record<string, number> = {};
    const datasDepositoMerito: Date[] = [];

    processosMeritoRPIs.forEach(processo => {
      const despacho = processo.despachos[0];
      if (despacho?.nome) {
        const tipoMerito = despacho.nome.includes('Deferimento') ? 'Deferimento' : 'Indeferimento';
        distribuicaoTipoMerito[tipoMerito] = (distribuicaoTipoMerito[tipoMerito] || 0) + 1;
        
        if (despacho.rpi?.numero) {
          const rpiStr = despacho.rpi.numero.toString();
          distribuicaoRPIs[rpiStr] = (distribuicaoRPIs[rpiStr] || 0) + 1;
        }
      }
      
      if (processo.dataDeposito) {
        datasDepositoMerito.push(processo.dataDeposito);
      }
    });

    const dataDepositoMaisAntigaMerito = datasDepositoMerito.length > 0 
      ? new Date(Math.min(...datasDepositoMerito.map(d => d.getTime()))) 
      : null;
    const dataDepositoMaisRecenteMerito = datasDepositoMerito.length > 0 
      ? new Date(Math.max(...datasDepositoMerito.map(d => d.getTime()))) 
      : null;

    log(`📊 Processamento concluído - Tipos: ${Object.keys(distribuicaoTipoMerito).length}, RPIs: ${Object.keys(distribuicaoRPIs).length}`);

    // ETAPA 6: Finalizar
    progressBar.update(100, { etapa: 'Concluído!' });
    progressBar.stop();

    log('✅ Análise concluída com sucesso!');
    log(`📋 Processos aguardando mérito: ${processosAguardandoMerito.length.toLocaleString('pt-BR')}`);
    log(`⚖️ Processos com mérito nas RPIs: ${processosMeritoRPIs.length.toLocaleString('pt-BR')}`);

    return {
      processosAguardandoMerito: {
        total: processosAguardandoMerito.length,
        dataDepositoMaisAntiga: dataDepositoMaisAntigaAguardando,
        dataDepositoMaisRecente: dataDepositoMaisRecenteAguardando,
        distribuicaoDespachos,
      },
      processosMeritoRPIs: {
        total: processosMeritoRPIs.length,
        dataDepositoMaisAntiga: dataDepositoMaisAntigaMerito,
        dataDepositoMaisRecente: dataDepositoMaisRecenteMerito,
        distribuicaoTipoMerito,
        distribuicaoRPIs,
      }
    };

  } catch (error) {
    progressBar.stop();
    log(`❌ Erro durante a análise: ${error}`);
    throw error;
  }
}

async function analisarProcessosComSqlRaw(config: ConfigAnalise = { modoTeste: false, limiteTeste: 10000 }): Promise<AnaliseResultSql> {
  log('🚀 Iniciando análise SQL RAW otimizada para big data...');
  
  if (config.modoTeste) {
    log(`🧪 MODO TESTE ATIVADO - Limitando a ${config.limiteTeste.toLocaleString('pt-BR')} registros`);
  }

  // Configurar barra de progresso
  const progressBar = new cliProgress.SingleBar({
    format: '📊 Progresso |{bar}| {percentage}% | {value}/{total} | Etapa: {etapa}',
    barCompleteChar: '\u2588',
    barIncompleteChar: '\u2591',
    hideCursor: true
  });

  progressBar.start(100, 0, { etapa: 'Iniciando...' });

  try {
    // ETAPA 1: Contar processos aguardando mérito usando SQL direto
    progressBar.update(25, { etapa: 'Contando processos aguardando mérito...' });
    log('🔍 Contando processos aguardando mérito com SQL agregado...');

    const limitClause = config.modoTeste ? `LIMIT ${config.limiteTeste}` : '';

    const sqlAguardandoMerito = `
      WITH processos_publicacao AS (
        SELECT DISTINCT p.id, p.numero, p."dataDeposito"
        FROM "Processo" p 
        INNER JOIN "Despacho" d ON d."processoId" = p.id
        WHERE d.nome IN (
          'Publicação de pedido de registro para oposição (exame formal concluído)',
          'Republicação de pedido (por perda da prioridade)',
          'Republicação de pedido',
          'Publicação de pedido de registro para oposição (exame formal de designação concluído)'
        )
        ${limitClause}
      ),
      processos_finalizados AS (
        SELECT DISTINCT p.id
        FROM "Processo" p 
        INNER JOIN "Despacho" d ON d."processoId" = p.id
        WHERE d.nome LIKE '%Deferimento do pedido%'
           OR d.nome LIKE '%Indeferimento do pedido%'
           OR d.nome LIKE '%Decisão de considerar pedido inexistente%'
           OR d.nome LIKE '%Arquivamento definitivo%'
           OR d.nome LIKE '%Desistência%'
           OR d.nome LIKE '%Arquivamento de ofício%'
           OR d.nome LIKE '%Cancelamento de ofício%'
      ),
      processos_aguardando AS (
        SELECT pp.*
        FROM processos_publicacao pp
        LEFT JOIN processos_finalizados pf ON pp.id = pf.id
        WHERE pf.id IS NULL
      )
      SELECT 
        COUNT(*) as total,
        MIN("dataDeposito") as data_mais_antiga,
        MAX("dataDeposito") as data_mais_recente
      FROM processos_aguardando;
    `;

    const resultadoAguardando = await prisma.$queryRawUnsafe(sqlAguardandoMerito) as any[];
    const estatisticasAguardando = resultadoAguardando[0];

    log(`✅ Encontrados ${Number(estatisticasAguardando.total).toLocaleString('pt-BR')} processos aguardando mérito`);

    // ETAPA 2: Distribuição de despachos de publicação para processos aguardando
    progressBar.update(50, { etapa: 'Analisando distribuição de despachos...' });
    log('📊 Analisando distribuição de tipos de despacho...');

    const sqlDistribuicaoDespachos = `
      WITH processos_publicacao AS (
        SELECT DISTINCT p.id, d.nome as despacho_nome
        FROM "Processo" p 
        INNER JOIN "Despacho" d ON d."processoId" = p.id
        WHERE d.nome IN (
          'Publicação de pedido de registro para oposição (exame formal concluído)',
          'Republicação de pedido (por perda da prioridade)',
          'Republicação de pedido',
          'Publicação de pedido de registro para oposição (exame formal de designação concluído)'
        )
        ${limitClause}
      ),
      processos_finalizados AS (
        SELECT DISTINCT p.id
        FROM "Processo" p 
        INNER JOIN "Despacho" d ON d."processoId" = p.id
        WHERE d.nome LIKE '%Deferimento do pedido%'
           OR d.nome LIKE '%Indeferimento do pedido%'
           OR d.nome LIKE '%Decisão de considerar pedido inexistente%'
           OR d.nome LIKE '%Arquivamento definitivo%'
           OR d.nome LIKE '%Desistência%'
           OR d.nome LIKE '%Arquivamento de ofício%'
           OR d.nome LIKE '%Cancelamento de ofício%'
      ),
      processos_aguardando AS (
        SELECT pp.*
        FROM processos_publicacao pp
        LEFT JOIN processos_finalizados pf ON pp.id = pf.id
        WHERE pf.id IS NULL
      )
      SELECT 
        despacho_nome,
        COUNT(*) as quantidade
      FROM processos_aguardando
      GROUP BY despacho_nome
      ORDER BY quantidade DESC;
    `;

    const distribuicaoDespachos = await prisma.$queryRawUnsafe(sqlDistribuicaoDespachos) as any[];
    const distribuicaoObj = distribuicaoDespachos.reduce((acc, row) => {
      acc[row.despacho_nome] = Number(row.quantidade);
      return acc;
    }, {} as Record<string, number>);

    log(`📊 Distribuição processada: ${distribuicaoDespachos.length} tipos de despacho`);

    // ETAPA 3: Processos com mérito nas RPIs específicas
    progressBar.update(75, { etapa: 'Analisando mérito nas RPIs...' });
    log('⚖️ Analisando processos com mérito nas RPIs [2837, 2838, 2839, 2840]...');

    const sqlMeritoRPIs = `
      SELECT 
        CASE 
          WHEN d.nome LIKE '%Deferimento%' THEN 'Deferimento'
          WHEN d.nome LIKE '%Indeferimento%' THEN 'Indeferimento'
          ELSE 'Outro'
        END as tipo_merito,
        r.numero as rpi_numero,
        COUNT(*) as quantidade,
        MIN(p."dataDeposito") as data_mais_antiga,
        MAX(p."dataDeposito") as data_mais_recente
      FROM "Processo" p
      INNER JOIN "Despacho" d ON d."processoId" = p.id
      INNER JOIN "RPI" r ON d."rpiId" = r.id
      WHERE (d.nome LIKE '%Deferimento do pedido%' OR d.nome LIKE '%Indeferimento do pedido%')
        AND r.numero IN (2837, 2838, 2839, 2840)
      GROUP BY tipo_merito, r.numero
      ORDER BY rpi_numero DESC, tipo_merito;
    `;

    const resultadoMeritoRPIs = await prisma.$queryRawUnsafe(sqlMeritoRPIs) as any[];
    
    // Processar resultados do mérito
    let totalMerito = 0;
    const distribuicaoTipoMerito: Record<string, number> = {};
    const distribuicaoRPIs: Record<string, number> = {};
    let dataAntiga: Date | null = null;
    let dataRecente: Date | null = null;

    resultadoMeritoRPIs.forEach(row => {
      const quantidade = Number(row.quantidade);
      totalMerito += quantidade;
      
      // Distribuição por tipo
      distribuicaoTipoMerito[row.tipo_merito] = (distribuicaoTipoMerito[row.tipo_merito] || 0) + quantidade;
      
      // Distribuição por RPI
      const rpiStr = row.rpi_numero.toString();
      distribuicaoRPIs[rpiStr] = (distribuicaoRPIs[rpiStr] || 0) + quantidade;
      
      // Datas extremas
      if (row.data_mais_antiga && (!dataAntiga || row.data_mais_antiga < dataAntiga)) {
        dataAntiga = row.data_mais_antiga;
      }
      if (row.data_mais_recente && (!dataRecente || row.data_mais_recente > dataRecente)) {
        dataRecente = row.data_mais_recente;
      }
    });

    log(`✅ Encontrados ${totalMerito.toLocaleString('pt-BR')} processos com mérito nas RPIs específicas`);

    // ETAPA 4: Finalizar
    progressBar.update(100, { etapa: 'Concluído!' });
    progressBar.stop();

    log('✅ Análise SQL otimizada concluída com sucesso!');
    log(`📋 Processos aguardando mérito: ${Number(estatisticasAguardando.total).toLocaleString('pt-BR')}`);
    log(`⚖️ Processos com mérito nas RPIs: ${totalMerito.toLocaleString('pt-BR')}`);

    return {
      processosAguardandoMerito: {
        total: Number(estatisticasAguardando.total),
        dataDepositoMaisAntiga: estatisticasAguardando.data_mais_antiga,
        dataDepositoMaisRecente: estatisticasAguardando.data_mais_recente,
        distribuicaoDespachos: distribuicaoObj,
      },
      processosMeritoRPIs: {
        total: totalMerito,
        dataDepositoMaisAntiga: dataAntiga,
        dataDepositoMaisRecente: dataRecente,
        distribuicaoTipoMerito,
        distribuicaoRPIs,
      }
    };

  } catch (error) {
    progressBar.stop();
    log(`❌ Erro durante a análise: ${error}`);
    throw error;
  }
}

function gerarRelatorioMarkdown(resultado: AnaliseResult | AnaliseResultSql, config: ConfigAnalise): string {
  const agora = new Date().toLocaleString('pt-BR', { 
    timeZone: 'America/Sao_Paulo',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
  
  const modoTesteInfo = config.modoTeste ? `\n> **⚠️ MODO TESTE:** Resultados limitados a ${config.limiteTeste.toLocaleString('pt-BR')} registros por consulta` : '';
  
  const relatorio = `# Relatório de Análise de Processos - Mérito
*Gerado em: ${agora}*${modoTesteInfo}

## ⚙️ Parâmetros da Análise

### Processos Aguardando Mérito
- **Despachos de Publicação Considerados:**
  - Publicação de pedido de registro para oposição (exame formal concluído)
  - Republicação de pedido (por perda da prioridade)
  - Republicação de pedido
  - Publicação de pedido de registro para oposição (exame formal de designação concluído)

- **Critérios de Exclusão (Processos Finalizados):**
  - **Despachos de Mérito:** Deferimento do pedido, Indeferimento do pedido
  - **Decisões de Pedido Inexistente:** Por exigência de pagamento/formal não respondida ou não cumprida, por falta de pagamento
  - **Arquivamentos Definitivos:** Por falta de procuração, cumprimento de exigência de mérito, documentos de marca coletiva/certificação
  - **Outros:** Desistência de pedido, Arquivamento/Cancelamento de ofício

### Processos com Mérito nas RPIs Específicas
- **RPIs Analisadas:** 2837, 2838, 2839, 2840
- **Despachos de Mérito:** Deferimento do pedido, Indeferimento do pedido

---

## 📊 Resultados

### 📋 Processos Aguardando Exame de Mérito
- **Total:** ${resultado.processosAguardandoMerito.total.toLocaleString('pt-BR')} processos
- **Data de Depósito Mais Antiga:** ${resultado.processosAguardandoMerito.dataDepositoMaisAntiga?.toLocaleDateString('pt-BR') || 'N/A'}
- **Data de Depósito Mais Recente:** ${resultado.processosAguardandoMerito.dataDepositoMaisRecente?.toLocaleDateString('pt-BR') || 'N/A'}

#### 📈 Distribuição por Tipo de Despacho de Publicação:
${Object.entries(resultado.processosAguardandoMerito.distribuicaoDespachos)
  .sort(([,a], [,b]) => b - a)
  .map(([despacho, quantidade]) => `- **${despacho}:** ${quantidade.toLocaleString('pt-BR')} processos`)
  .join('\n')}

### ⚖️ Processos com Mérito nas RPIs [2837-2840]
- **Total:** ${resultado.processosMeritoRPIs.total.toLocaleString('pt-BR')} processos
- **Data de Depósito Mais Antiga:** ${resultado.processosMeritoRPIs.dataDepositoMaisAntiga?.toLocaleDateString('pt-BR') || 'N/A'}
- **Data de Depósito Mais Recente:** ${resultado.processosMeritoRPIs.dataDepositoMaisRecente?.toLocaleDateString('pt-BR') || 'N/A'}

#### 📈 Distribuição por Tipo de Mérito:
${Object.entries(resultado.processosMeritoRPIs.distribuicaoTipoMerito)
  .sort(([,a], [,b]) => b - a)
  .map(([tipo, quantidade]) => `- **${tipo}:** ${quantidade.toLocaleString('pt-BR')} processos`)
  .join('\n')}

#### 📈 Distribuição por RPI:
${Object.entries(resultado.processosMeritoRPIs.distribuicaoRPIs)
  .sort(([a], [b]) => parseInt(b) - parseInt(a))
  .map(([rpi, quantidade]) => `- **RPI ${rpi}:** ${quantidade.toLocaleString('pt-BR')} processos`)
  .join('\n')}

---

## 📝 Observações Técnicas

- **Performance:** Consultas otimizadas SQL RAW para lidar com big data
- **Método de Análise:** Agregações diretas no banco para evitar sobrecarga de memória
- **Tratamento de Dados:** CTEs e JOINS otimizados para máxima performance
- **Monitoramento:** Logs com timestamps para acompanhar o progresso em tempo real

## 💡 Insights

- Os processos aguardando mérito representam o backlog atual do INPI para análise
- As RPIs analisadas (2837-2840) mostram a produtividade recente de decisões de mérito
- A diferença temporal entre as datas de depósito indica o tempo de tramitação no sistema

---

*Relatório gerado automaticamente pelo sistema de análise SQL otimizado para big data*
`;

  return relatorio;
}

async function executarAnalisePerformance() {
  const tempoInicio = Date.now();
  
  try {
    log('🚀 === ANÁLISE OTIMIZADA PARA BIG DATA ===');
    
    // Detectar se deve usar modo teste baseado em argumentos da linha de comando
    const args = process.argv.slice(2);
    const modoTeste = args.includes('--teste') || args.includes('-t');
    const usarSqlRaw = args.includes('--sql') || args.includes('--raw');
    const limiteTeste = 10000; // Limite maior para teste com SQL
    
    const config: ConfigAnalise = { modoTeste, limiteTeste };
    
    let resultado: AnaliseResult | AnaliseResultSql;
    
    if (usarSqlRaw) {
      log('🔧 Usando análise SQL RAW para big data...');
      resultado = await analisarProcessosComSqlRaw(config);
    } else {
      log('🔧 Usando análise Prisma ORM (recomendado apenas para testes)...');
      resultado = await analisarProcessosEsperaMeritoPerformance(config);
    }
    
    const tempoExecucao = ((Date.now() - tempoInicio) / 1000).toFixed(2);
    log(`⏱️ Tempo de execução: ${tempoExecucao}s`);
    
    // Gerar relatório em markdown
    const relatorioMarkdown = gerarRelatorioMarkdown(resultado, config);
    
    // Salvar arquivo
    const timestamp = new Date().toISOString().split('T')[0];
    const sufixoTeste = config.modoTeste ? '-teste' : '';
    const sufixoMetodo = usarSqlRaw ? '-sql' : '';
    const nomeArquivo = `relatorio-analise-merito${sufixoTeste}${sufixoMetodo}-${timestamp}.md`;
    
    fs.writeFileSync(nomeArquivo, relatorioMarkdown, 'utf8');
    
    log(`📄 Relatório salvo em: ${nomeArquivo}`);
    log('=== 📈 RESUMO EXECUTIVO ===');
    log(`📋 Processos aguardando mérito: ${resultado.processosAguardandoMerito.total.toLocaleString('pt-BR')}`);
    log(`⚖️ Processos com mérito (RPIs 2837-2840): ${resultado.processosMeritoRPIs.total.toLocaleString('pt-BR')}`);
    
    // Mostrar estatísticas de performance
    log(`⚡ Performance:`);
    log(`- Tempo total: ${tempoExecucao}s`);
    log(`- Modo: ${config.modoTeste ? 'TESTE' : 'PRODUÇÃO'}`);
    log(`- Método: ${usarSqlRaw ? 'SQL RAW' : 'Prisma ORM'}`);
    
    if (config.modoTeste && !usarSqlRaw) {
      log(`🧪 Para execução completa, execute sem o parâmetro --teste`);
    }
    
    if (!usarSqlRaw) {
      log(`💡 Para big data, recomenda-se usar: npm run dev src/scripts/analiseProcessosEsperaMerito.ts --sql`);
    }
    
  } catch (error) {
    log(`❌ Erro durante a análise: ${error}`);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  executarAnalisePerformance();
}

export { executarAnalisePerformance, analisarProcessosEsperaMeritoPerformance, analisarProcessosComSqlRaw, gerarRelatorioMarkdown }; 