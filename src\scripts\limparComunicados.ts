import { PrismaClient } from '@prisma/client';

async function limparComunicados() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Iniciando limpeza da tabela de comunicados...');
    
    // Obter contagem atual para referência
    const contagemAnterior = await prisma.comunicadoPrazoMerito.count();
    console.log(`Encontrados ${contagemAnterior} comunicados na tabela.`);
    
    // Executar a limpeza
    const resultado = await prisma.comunicadoPrazoMerito.deleteMany({});
    
    console.log(`Operação concluída! ${resultado.count} registros foram removidos.`);
    console.log('A tabela de comunicados foi limpa com sucesso.');
    
    return resultado;
  } catch (error) {
    console.error('Erro ao limpar tabela de comunicados:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script
limparComunicados()
  .then(() => {
    console.log('Script finalizado.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Falha no script:', error);
    process.exit(1);
  }); 