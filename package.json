{"name": "registre_sys_backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "start": "ts-node src/server.ts", "testar-comunicados": "ts-node src/scripts/testarComunicadosPrazo.ts", "testar-mapeamento-dialogos": "ts-node src/scripts/testarMapeamentoDialogos.ts", "testar-relatorio-comunicados": "ts-node src/scripts/testarRelatorioComunicados.ts", "teste-comunicados": "ts-node src/scripts/testeComunicados.ts", "comparar-processos": "ts-node src/scripts/compararProcessosCrmBanco.ts", "analisar-registre-se": "ts-node src/scripts/analisarClientesRegistreSe.ts", "analisar-processos-registre-se": "ts-node src/scripts/analisarProcessosRegistreSe.ts", "analisar-acessos-registre-se": "ts-node src/scripts/analisarAcessosRegistreSe.ts", "limpar-clientes": "ts-node src/scripts/limparClientes.ts", "orquestrar-crm": "ts-node src/scripts/orquestrarAcoesCrmToBD.ts", "processar-crm-banco": "ts-node src/scripts/processarDadosCrmParaBanco.ts", "analisar-crm-detalhado": "ts-node src/scripts/analisarDadosCrmDetalhado.ts", "testar-funcao-telefone": "ts-node src/scripts/testarFuncaoTelefone.ts", "criar-clientes-crm": "ts-node src/scripts/criarClientesCrmCompleto.ts", "enriquecer-titulares": "ts-node src/scripts/enriquecerTitularesComCpf.ts", "gerar-links-auto-login": "ts-node src/scripts/gerarLinksAutoLogin.ts", "gerar-csv-marcas-links": "ts-node src/scripts/gerarCsvMarcasLinks.ts", "atualizar-campos-chatguru": "ts-node src/scripts/atualizarCamposChatguru.ts", "listar-clientes-sem-identificador": "ts-node scripts/listarClientesSemIdentificador.ts", "atualizar-identificadores": "ts-node scripts/listarClientesSemIdentificador.ts --atualizar", "atualizar-nomes-marca": "ts-node scripts/atualizarNomeMarcaClientes.ts", "limpar-todos-comunicados": "ts-node src/scripts/limparTodosComunicados.ts", "limpar-comunicados-seletivo": "ts-node src/scripts/limparComunicadosSeletivo.ts", "atualizar-nomes-marca-exec": "ts-node scripts/atualizarNomeMarcaClientes.ts --atualizar", "gerar-csv-clientes": "ts-node scripts/gerarCsvClientes.ts", "pm2:start": "node scripts/pm2-manager.js start", "pm2:stop": "node scripts/pm2-manager.js stop", "pm2:restart": "node scripts/pm2-manager.js restart", "pm2:status": "node scripts/pm2-manager.js status", "pm2:logs": "node scripts/pm2-manager.js logs", "pm2:logs-node": "node scripts/pm2-manager.js logs-node", "pm2:logs-python": "node scripts/pm2-manager.js logs-python", "pm2:install": "node scripts/pm2-manager.js install", "pm2:help": "node scripts/pm2-manager.js help"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.5.0", "@types/adm-zip": "^0.5.7", "@types/pdf-parse": "^1.1.5", "adm-zip": "^0.5.16", "axios": "^1.9.0", "better-sqlite3": "^11.9.0", "cli-progress": "^3.12.0", "cors": "^2.8.5", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "fast-csv": "^5.0.2", "form-data": "^4.0.3", "fp-ts": "^2.16.9", "jimp": "^1.6.0", "JSONStream": "^1.3.5", "multer": "^2.0.1", "node-cron": "^3.0.3", "p-limit": "^6.2.0", "ts-node-dev": "^2.0.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.12", "@types/cli-progress": "^3.11.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/multer": "^1.4.12", "@types/node": "^22.13.10", "@types/node-cron": "^3.0.11", "@types/uuid": "^10.0.0", "prisma": "^6.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}