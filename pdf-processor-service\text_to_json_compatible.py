import re
import os
import sys
import json
from collections import OrderedDict
from datetime import datetime
from pathlib import Path
from multiprocessing import Pool, cpu_count
from typing import List, Tuple

# --- Configurações e caminhos ---
CURRENT_DIR = Path(__file__).parent              # extractors/
PROJECT_ROOT = CURRENT_DIR.parent.parent         # RgsysBaseConstructor/
BASE_DIR = CURRENT_DIR
OUTPUT_DIR = BASE_DIR

# --- Padrões e dicionários de regex ---
chaves_blocos = OrderedDict([
    ("numeroProcesso", r'^(\d{9})'),
    # Aceita protocolo com 10, 11 ou 12 dígitos
    ("numeroProtocolo", r'^(\d{10,12})'),
    ("dataProtocolo", r'^(\d{2}/\d{2}/\d{4})'),
    ("tipoDespacho", r'^([^0-9\n][^\n]+?)(?=\n|$)'),
    ("tipoPeticao", r'Petição \(tipo\):\s*([^(]+)\((\d+\.\d+)\)'),
    ("processoAfetado",
     r'(?:Processo afetado:|Processo de base:|Número do processo:)\s*(\d{9})-?([^\n]*)'),
    ("requerente", r'Requerente:\s*(.+?)(?=\n|$)'),
    ("titular", r'Titular(?: do registro)?:\s*((?:(?!\n\S+:\s).)+)'),
    ("procurador", r'Procurador:\s*(.+?)(?=\n|$)'),
    ("procuradorTitular", r'Procurador do titular\s*:\s*(.+?)(?=\n|$)'),
    ("dataDeDeposito", r'Data de depósito:\s*(\d{2}/\d{2}/\d{4})'),
    ("dataDeConcessao", r'Data de concessão:\s*(\d{2}/\d{2}/\d{4})'),
    ("dataDaNovaVigencia", r'Data da nova vigência:\s*(\d{2}/\d{2}/\d{4})'),
    ("apresentacao", r'Apresentação:\s*(.+?)(?=\n|$)'),
    ("natureza", r'Natureza:\s*(.+?)(?=\n|$)'),
    ("elementoNominativo", r'Elemento nominativo:\s*(.+?)(?=\n|$)'),
    ("cfe", r'CFE:\s*(.+?)(?=\n|$)'),
    ("ncl", r'NCL\(?(\d+)?\)?:\s*(\d+)'),
    # Os campos "especificacao" e "detalhesDespacho" serão tratados separadamente
    ("apostila", r'Apostila:\s*(.+?)(?=\n|$)'),
    ("sobrestadores", r'Sobrestadores:\s*((?:(?!\n\S+:\s).)+)'),
    # Novo campo para capturar o tipo de comunicação (utilizado em protocolos)
    ("tipoComunicacao", r'^Tipo de Comunicação:\s*(.+)$'),
])

# Padrões para cabeçalho
revista_num_pattern = re.compile(r'N[º°]\s*(\d+)', re.IGNORECASE)
revista_data_pattern = re.compile(
    r'(\d{1,2})\s+de\s+([a-zç]+)\s+de\s+(\d{4})', re.IGNORECASE)
meses = {
    "janeiro": "01",
    "fevereiro": "02",
    "março": "03",
    "abril": "04",
    "maio": "05",
    "junho": "06",
    "julho": "07",
    "agosto": "08",
    "setembro": "09",
    "outubro": "10",
    "novembro": "11",
    "dezembro": "12"
}

# Função para carregar o conteúdo de um arquivo TXT
def load_text_file(file_path: str) -> str:
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

# Função para segmentar o texto em blocos
def segmentar_blocos(text: str) -> list:
    if not text:
        return []
    pattern = re.compile(r'^--- BLOCO \d+ ---$', re.MULTILINE)
    splits = pattern.split(text)
    return [s.strip() for s in splits if s.strip()]

# ✅ NOVA FUNÇÃO: Converte data ISO para formato brasileiro
def convert_date_to_brazilian(iso_date: str) -> str:
    """Converte data de formato YYYY-MM-DD para DD/MM/YYYY"""
    if not iso_date:
        return None
    try:
        # Se já estiver no formato brasileiro, retorna como está
        if '/' in iso_date:
            return iso_date
        # Converte de ISO para brasileiro
        year, month, day = iso_date.split('-')
        return f"{day}/{month}/{year}"
    except:
        return iso_date

# Função para extrair informações do cabeçalho
def parse_header(header_text: str) -> dict:
    numero = None
    data_publicacao = None
    for line in header_text.splitlines():
        line = line.strip()
        if not numero:
            m = revista_num_pattern.search(line)
            if m:
                numero = m.group(1)
        if not data_publicacao:
            m = revista_data_pattern.search(line)
            if m:
                dia, mes_nome, ano = m.groups()
                mes = meses.get(mes_nome.lower())
                if mes:
                    # ✅ FORMATO BRASILEIRO: DD/MM/YYYY
                    data_publicacao = f"{dia.zfill(2)}/{mes}/{ano}"
    return {"numero": numero, "data": data_publicacao}

# Função para processar sobrestadores
def parse_sobrestadores(sobrestadores_str: str) -> list:
    """✅ SEMPRE RETORNA ARRAY (nunca null) para compatibilidade com merged.json"""
    if not sobrestadores_str:
        return []  # ✅ Array vazio em vez de null
    
    print("String original dos sobrestadores:", repr(sobrestadores_str))
    sobrestadores_str = re.sub(r'\s+', ' ', sobrestadores_str.strip())
    sobrestadores_str = sobrestadores_str.replace(' e ', ', ')
    print("String processada:", repr(sobrestadores_str))
    sobrestadores = []
    pattern = re.compile(r'Processo\s*(\d+)\s*\(([^)]+?)\)')
    matches = list(pattern.finditer(sobrestadores_str))
    print(f"Número de matches encontrados: {len(matches)}")
    for match in matches:
        processo, marca = match.groups()
        sobrestador = {"processo": processo.strip(), "marca": marca.strip()}
        print("Encontrado sobrestador:", sobrestador)
        sobrestadores.append(sobrestador)
    print("Total de sobrestadores encontrados:", len(sobrestadores))
    return sobrestadores

# Função para extração multilinha de campos como "Detalhes do despacho" e "Especificação"
def extrair_campo_multilinha(bloco: str, chave: str, chaves_delimitadoras: list) -> str:
    """
    Extrai o conteúdo de um campo que pode se estender por múltiplas linhas.
    A captura inicia após 'chave:' e termina ao encontrar qualquer uma das chaves delimitadoras
    ou o início de um novo bloco (linha iniciando com '--- BLOCO').
    """
    linhas = bloco.splitlines()
    capturando = False
    conteudo = []
    for linha in linhas:
        l_stripped = linha.strip()
        if not capturando:
            if l_stripped.startswith(chave + ":"):
                # Inicia a captura removendo o rótulo e os dois pontos
                conteudo.append(l_stripped[len(chave)+1:].strip())
                capturando = True
        else:
            # Se encontrar alguma chave delimitadora ou o início de um novo bloco, interrompe a captura
            if any(l_stripped.startswith(delim) for delim in chaves_delimitadoras) or l_stripped.startswith("--- BLOCO"):
                break
            else:
                conteudo.append(linha)
    return "\n".join(conteudo).strip()

# Função para extrair as especificações do bloco, para compor o objeto NCL
def extrair_especificacoes(bloco: str) -> dict:
    """
    Extrai as especificações do bloco e as organiza em um dicionário,
    com as chaves: 'Especificação alterada para', 'Especificação' e 'Especificação traduzida'.
    """
    especificacoes = {}
    chaves_espec = [
        "Especificação alterada para",
        "Especificação",
        "Especificação traduzida"
    ]
    # Delimitadores comuns para a extração de especificações
    delimitadores = [
        "Procurador:",
        "Procurador do titular:",
        "Titular:",
        "Apostila:",
        "--- BLOCO"
    ]
    for chave in chaves_espec:
        if chave + ":" in bloco:
            valor = extrair_campo_multilinha(bloco, chave, delimitadores)
            if valor:
                especificacoes[chave] = valor
    return especificacoes

# Função para parse do bloco de processo
def parse_process_block(block: str) -> dict:
    registro = {}
    bloco_texto = block
    # Processa os campos definidos no dicionário, exceto "detalhesDespacho" e "especificacao"
    for chave, padrao in chaves_blocos.items():
        if chave in ["detalhesDespacho", "especificacao"]:
            continue
        try:
            if chave == "numeroProtocolo":
                flags = re.IGNORECASE | re.DOTALL
            else:
                flags = re.IGNORECASE | re.MULTILINE | re.DOTALL

            if isinstance(padrao, tuple):
                regex, custom_flags = padrao
                flags = custom_flags
            else:
                regex = padrao

            m = re.search(regex, bloco_texto, flags)
            if m:
                if chave == "sobrestadores":
                    registro[chave] = parse_sobrestadores(m.group(1))
                elif chave == "processoAfetado":
                    registro[chave] = {
                        "numero": m.group(1).strip(),
                        "marca": m.group(2).strip()
                    }
                elif m.lastindex and m.lastindex > 1:
                    valores = m.groups()
                    if chave == "ncl":
                        registro[chave] = {
                            "edicao": valores[0],
                            "codigo": valores[1]
                        }
                    elif chave == "tipoPeticao":
                        registro[chave] = {
                            "nome": valores[0].strip(),
                            "codigo": valores[1]
                        }
                    else:
                        registro[chave] = valores[0]
                else:
                    valor = m.group(1) if m.lastindex else m.group()
                    registro[chave] = valor.strip() if valor else None
            else:
                registro[chave] = None
        except Exception as e:
            print(f"Erro ao processar campo {chave}: {str(e)}")
            registro[chave] = None

    # Tratamento especial para "Detalhes do despacho"
    delimitadores_detalhes = [
        "Procurador:",
        "Procurador do titular:",
        "Especificação alterada para:",
        "Especificação:",
        "Especificação traduzida:",
        "Titular:",
        "Apostila:"
    ]
    if "Detalhes do despacho:" in bloco_texto:
        detalhes = extrair_campo_multilinha(
            bloco_texto, "Detalhes do despacho", delimitadores_detalhes)
        registro["detalhesDespacho"] = detalhes
    else:
        registro["detalhesDespacho"] = None

    # Tratamento especial para "Especificação" dentro do objeto NCL
    if registro.get("ncl"):
        delimitadores_espec = [
            "Especificação traduzida:",
            "Detalhes do despacho:",
            "Procurador:",
            "Titular:",
            "Elemento nominativo:"
        ]
        espec_text = extrair_campo_multilinha(
            bloco_texto, "Especificação", delimitadores_espec)
        if espec_text:
            if isinstance(registro["ncl"], dict):
                registro["ncl"]["especificacao"] = espec_text
            else:
                registro["ncl"] = {
                    "edicao": None,
                    "codigo": registro["ncl"].strip(),
                    "especificacao": espec_text,
                    "status": None
                }

    return registro

# Função para processar o campo "Titular"
def parse_titular(titular_str: str) -> list:
    """
    Processa o campo 'Titular', que pode conter múltiplos titulares separados por " e ".
    Retorna uma lista de dicionários com 'nome_razao_social', 'pais' e 'uf'.
    """
    if not titular_str:
        return []
    titular_str = " ".join(titular_str.split())
    partes = [p.strip() for p in re.split(
        r'\s+e\s+', titular_str) if p.strip()]
    titulares = []
    for parte in partes:
        m = re.match(r'(.+?)\s*\[([A-Z]{2})(?:/([A-Z]{2}))?\]', parte)
        if m:
            nome, pais, uf = m.groups()
            titulares.append({
                "nome_razao_social": nome.strip(),
                "pais": pais.strip(),
                "uf": uf.strip() if uf else None
            })
        else:
            titulares.append(
                {"nome_razao_social": parte, "pais": None, "uf": None})
    return titulares

# ✅ FUNÇÃO ADAPTADA: Parse CFE compatível com merged.json
def parse_CFE(cfe_str: str) -> list:
    """✅ SEMPRE RETORNA ARRAY para compatibilidade com merged.json"""
    if not cfe_str:
        return []  # ✅ Array vazio sempre
    cfe_str = cfe_str.replace(" e ", ", ")
    codigos = [s.strip() for s in cfe_str.split(",") if s.strip()]
    return [{"codigo": codigo, "edicao": None} for codigo in codigos]

# ✅ FUNÇÃO ADAPTADA: Parse NCL compatível com merged.json
def parse_NCL(ncl_valor) -> list:
    """✅ Formato compatível com merged.json"""
    if ncl_valor:
        if isinstance(ncl_valor, dict):
            # ✅ Adiciona status se não existir (compatibilidade com merged)
            if 'status' not in ncl_valor:
                ncl_valor['status'] = None
            return [ncl_valor]
        return [{"edicao": None, "codigo": ncl_valor.strip(), "especificacao": None, "status": None}]
    return []

# Função para processar o campo Requerente
def parse_requerente(requerente_str: str) -> dict:
    if not requerente_str:
        return {"nome_razao_social": None, "pais": None, "uf": None}
    m = re.match(
        r'(.+?)(?:\s*\[([A-Z]{2})(?:/([A-Z]{2}))?\])?$', requerente_str)
    if m:
        nome, pais, uf = m.groups()
        return {"nome_razao_social": nome.strip(),
                "pais": pais.strip() if pais else None,
                "uf": uf.strip() if uf else None}
    return {"nome_razao_social": requerente_str.strip(), "pais": None, "uf": None}

# ✅ FUNÇÃO ADAPTADA: Normaliza estrutura da marca para compatibilidade
def normalize_marca_structure(nome, apresentacao, natureza) -> dict:
    """
    ✅ Compatibilidade com merged.json:
    - Se todos os campos forem null/empty, retorna objeto vazio {}
    - Senão, retorna objeto com campos
    """
    # Verifica se todos os campos estão vazios/null
    if not nome and not apresentacao and not natureza:
        return {}  # ✅ Objeto vazio como no merged.json
    
    # Retorna objeto com campos
    return {
        "nome": nome,
        "apresentacao": apresentacao,
        "natureza": natureza
    }

# Função para processar os blocos e agrupar os processos e protocolos
def parse_processes(blocos: list) -> Tuple[list, list]:
    """
    Processa os blocos (exceto o cabeçalho) e gera:
      - Uma lista de dicionários representando os processos (agrupando os despachos).
      - Uma lista de protocolos sem processo (protocolosSemProcesso).
    Se um mesmo processo aparecer em blocos diferentes, os despachos são agregados em um único objeto.
    ✅ FORMATO COMPATÍVEL COM MERGED.JSON
    """
    processos_dict = {}  # Agrupamento por número do processo
    protocolos_sem_processo = []  # Para blocos de protocolos sem "processoAfetado"

    for bloco in blocos[1:]:
        try:
            if not bloco:
                continue
            reg = parse_process_block(bloco)

            # Se não há "processoAfetado" mas existe "numeroProtocolo", trata-se de um protocolo isolado.
            if not reg.get("processoAfetado") and reg.get("numeroProtocolo"):
                codigo_servico = None
                if reg.get("tipoPeticao"):
                    # Ajusta o código removendo o ponto
                    codigo_servico = reg.get("tipoPeticao").get(
                        "codigo", "").replace(".", "")
                elif reg.get("tipoComunicacao"):
                    parts = reg.get("tipoComunicacao").split("-")
                    if len(parts) > 1:
                        codigo_servico = parts[1].strip()[:5] + "0"
                
                # ✅ Converte data para formato brasileiro
                data_protocolo = reg.get("dataProtocolo")
                if data_protocolo:
                    data_protocolo = convert_date_to_brazilian(data_protocolo)
                
                protocolo = {
                    "numero": reg.get("numeroProtocolo"),
                    "data": data_protocolo,  # ✅ Formato brasileiro
                    "codigo_servico": codigo_servico,
                    "requerente": parse_requerente(reg.get("requerente")),
                    "procurador": reg.get("procurador")
                }
                protocolos_sem_processo.append(protocolo)
                continue  # pula para o próximo bloco

            # Caso exista "processoAfetado" ou "numeroProcesso" (como fallback)
            if reg.get("processoAfetado"):
                numero = reg.get("processoAfetado")["numero"]
                marca_nome = reg.get("processoAfetado")["marca"]
                if not marca_nome:
                    marca_nome = reg.get("elementoNominativo")
            else:
                numero = reg.get("numeroProcesso")
                marca_nome = reg.get("elementoNominativo")

            # Identifica se o bloco atual é de protocolo
            is_protocol = bool(reg.get("numeroProtocolo")
                               and reg.get("processoAfetado"))

            # Preparar dados do protocolo se for um bloco de protocolo associado ao processo
            protocolos = []
            if is_protocol:
                codigo_servico = None
                if reg.get("tipoPeticao"):
                    codigo_servico = reg.get("tipoPeticao").get(
                        "codigo", "").replace(".", "")
                elif reg.get("tipoComunicacao"):
                    parts = reg.get("tipoComunicacao").split("-")
                    if len(parts) > 1:
                        codigo_servico = parts[1].strip()[:5] + "0"
                
                # ✅ Converte data para formato brasileiro
                data_protocolo = reg.get("dataProtocolo")
                if data_protocolo:
                    data_protocolo = convert_date_to_brazilian(data_protocolo)
                
                protocolo = {
                    "numero": reg.get("numeroProtocolo"),
                    "data": data_protocolo,  # ✅ Formato brasileiro
                    "codigo_servico": codigo_servico,
                    "requerente": parse_requerente(reg.get("requerente")),
                    "procurador": reg.get("procurador")
                }
                protocolos.append(protocolo)

            # Definir código do despacho (exemplo de regra para "tipoDespacho")
            if reg.get("tipoDespacho") and "Anotação de cancelamento" in reg.get("tipoDespacho"):
                codigo_despacho = "IPAS902"
            else:
                codigo_despacho = "PENDENTE"

            despacho = {
                "codigo": codigo_despacho,
                "nome": reg.get("tipoDespacho"),
                "texto_complementar": reg.get("detalhesDespacho"),
                "protocolos": protocolos
            }

            # Agrupar os despachos no processo
            if numero in processos_dict:
                # Se o novo bloco não for de protocolo e o processo ainda não possui procurador, atualiza-o
                if not is_protocol and processos_dict[numero].get("procurador") is None:
                    processos_dict[numero]["procurador"] = (
                        reg.get("procuradorTitular") or reg.get("procurador"))
                processos_dict[numero]["despachos"].append(despacho)
            else:
                # ✅ Converte datas para formato brasileiro
                data_deposito = convert_date_to_brazilian(reg.get("dataDeDeposito"))
                data_concessao = convert_date_to_brazilian(reg.get("dataDeConcessao"))
                data_vigencia = convert_date_to_brazilian(reg.get("dataDaNovaVigencia"))
                
                # ✅ Normaliza estrutura da marca
                marca_estrutura = normalize_marca_structure(
                    marca_nome, 
                    reg.get("apresentacao"), 
                    reg.get("natureza")
                )
                
                processo = {
                    "numero": numero,
                    "data_deposito": data_deposito,      # ✅ Formato brasileiro
                    "data_concessao": data_concessao,    # ✅ Formato brasileiro  
                    "data_vigencia": data_vigencia,      # ✅ Formato brasileiro
                    "marca": marca_estrutura,            # ✅ Compatível com merged
                    "titulares": parse_titular(reg.get("titular")) if reg.get("titular") else [],
                    "despachos": [despacho],
                    "sobrestadores": reg.get("sobrestadores", []),  # ✅ Array vazio em vez de null
                    # Se o bloco é de protocolo, não atribuímos o procurador no nível do processo;
                    # caso contrário, usamos o valor extraído.
                    "procurador": None if is_protocol else (reg.get("procuradorTitular") or reg.get("procurador")),
                    # ✅ REMOVE procuradorTitular - não existe no merged.json
                    "NCL": parse_NCL(reg.get("ncl")),
                    "CFE": parse_CFE(reg.get("cfe")),    # ✅ ADICIONA CFE (sempre array)
                    "apostila": reg.get("apostila")      # ✅ ADICIONA apostila
                }
                processos_dict[numero] = processo

        except Exception as e:
            print(f"ERRO ao processar bloco: {str(e)}")
            continue

    return list(processos_dict.values()), protocolos_sem_processo

# Função para processar um arquivo TXT e gerar o JSON correspondente
def parse_json_from_text_file(txt_file_path: str) -> dict:
    text = load_text_file(txt_file_path)
    blocos = segmentar_blocos(text)
    if not blocos:
        raise ValueError("Nenhum bloco encontrado no arquivo.")
    header_info = parse_header(blocos[0])
    processos, protocolos_sem_processo = parse_processes(blocos)
    return {
        "numero": header_info.get("numero"),
        "data": header_info.get("data"),  # ✅ Já no formato brasileiro
        "processos": processos
        # ✅ REMOVE protocolosSemProcesso - não existe no merged.json
    }

# Função para salvar o JSON em um arquivo
def save_json(json_data: dict, output_file: str):
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=2, ensure_ascii=False)  # ✅ indent=2 como merged
    print(f"Arquivo JSON salvo em: {output_file}")

# Função para processar um único arquivo (para uso com multiprocessing)
def process_single_file(file_tuple: Tuple[Path, Path]) -> None:
    txt_file, output_file = file_tuple
    try:
        print(f"Processando arquivo: {txt_file}")
        json_data = parse_json_from_text_file(str(txt_file))
        save_json(json_data, str(output_file))
        print(f"✓ Arquivo processado com sucesso: {output_file}")
    except Exception as e:
        print(f"✗ Erro ao processar {txt_file}: {str(e)}")

# Função para processar todos os arquivos na pasta de testes
def process_all_files(num_workers: int = 5) -> None:
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    txt_files = list(BASE_DIR.glob("*.txt"))
    if not txt_files:
        print(f"Nenhum arquivo TXT encontrado em {BASE_DIR}")
        return
    file_pairs = [(txt_file, OUTPUT_DIR / (txt_file.stem + "_compatible.json"))  # ✅ Nome diferente
                  for txt_file in txt_files]
    num_workers = min(num_workers, len(file_pairs))
    print(
        f"Iniciando processamento de {len(file_pairs)} arquivos com {num_workers} workers...")
    with Pool(num_workers) as pool:
        pool.map(process_single_file, file_pairs)
    print("Processamento concluído!")

# Função principal
def main():
    if len(sys.argv) > 1:
        txt_filename = sys.argv[1]
        txt_file = BASE_DIR / txt_filename
        if not txt_file.exists():
            print(f"ERRO: O arquivo {txt_file} não foi encontrado.")
            sys.exit(1)
        try:
            json_data = parse_json_from_text_file(str(txt_file))
            os.makedirs(OUTPUT_DIR, exist_ok=True)
            output_file = OUTPUT_DIR / (txt_file.stem + "_compatible.json")  # ✅ Nome diferente
            save_json(json_data, str(output_file))
        except Exception as e:
            print(f"ERRO durante o processamento: {str(e)}")
            sys.exit(1)
    else:
        process_all_files(num_workers=5)


if __name__ == "__main__":
    main() 