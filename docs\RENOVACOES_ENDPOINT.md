# Endpoint de Renovações de Marcas

## Visão Geral

O endpoint `/api/marca/renovacoes` permite listar renovações necessárias de marcas registradas junto ao INPI, seguindo as regras definidas no Manual de Marcas do INPI.

## URL

```
GET /api/marca/renovacoes
```

## Parâmetros de Query

| Parâmetro | Tipo | Padrão | Descrição |
|-----------|------|--------|-----------|
| `page` | number | 1 | Página da listagem |
| `limit` | number | 10 | Quantidade de itens por página |
| `sortBy` | string | 'dataVigencia' | Campo para ordenação ('dataVigencia', 'numeroProcesso', 'marcaNome', 'diasRestantes') |
| `sortOrder` | string | 'asc' | Ordem da listagem ('asc', 'desc') |
| `filtroVencimento` | string | 'todos' | Filtro por status de vencimento |
| `mesesProximos` | number | 12 | Quantidade de meses para filtro 'proximoVencimento' |

### Opções de `filtroVencimento`:

- `todos`: Todos os processos elegíveis
- `vencidas`: Apenas renovações já vencidas
- `proximoVencimento`: Renovações que vencem nos próximos X meses (definido por `mesesProximos`)
- `noAno`: Renovações que vencem no próximo ano

## Critérios de Elegibilidade

O endpoint considera apenas processos que atendem aos seguintes critérios:

1. **Procurador específico**: `procuradorId = '65b3c0c0-aa3b-4d89-85fb-2a144e538800'`
2. **Concessão válida**: Possui despacho de "Concessão de registro"
3. **Sem extinção**: Não possui despachos de extinção/arquivamento posteriores à concessão
4. **Dados de RPI**: Despacho de concessão deve ter RPI válida para cálculo de datas

## Identificação de Renovações Anteriores

O sistema identifica renovações já realizadas através dos seguintes códigos de serviço nos protocolos:

### Prazo Ordinário:
- `3745` ou `374.5`: Prorrogação em prazo ordinário

### Prazo Extraordinário:
- `3755`, `3751`, `375.5` ou `375.1`: Prorrogação em prazo extraordinário

## Cálculo de Prazos

### Vigência Inicial
- **Data de vigência**: Data da concessão + 10 anos
- **Prazo ordinário**: Último ano de vigência (data vigência - 1 ano até data vigência)
- **Prazo extraordinário**: 6 meses após o vencimento (data vigência até data vigência + 6 meses)

### Renovações Subsequentes
Para cada renovação realizada, adiciona-se 10 anos à data de vigência.

## Estrutura da Resposta

```json
{
  "success": true,
  "data": [
    {
      "processoId": "uuid",
      "numeroProcesso": "string",
      "dataDeposito": "ISO Date",
      "dataConcessao": "ISO Date",
      "dataVigencia": "ISO Date",
      "prazoOrdinario": {
        "inicio": "ISO Date",
        "fim": "ISO Date",
        "formatado": "de dd/mm/aaaa até dd/mm/aaaa"
      },
      "prazoExtraordinario": {
        "inicio": "ISO Date",
        "fim": "ISO Date",
        "formatado": "de dd/mm/aaaa até dd/mm/aaaa"
      },
      "tempoRestante": {
        "anos": 0,
        "meses": 0,
        "dias": 0,
        "diasTotal": 0,
        "formatado": "X anos, Y meses e Z dias"
      },
      "marcaNome": "string",
      "renovacoesAnteriores": {
        "quantidade": 0,
        "detalhes": [
          {
            "data": "ISO Date",
            "protocolos": [
              {
                "numero": "string",
                "codigoServico": "string",
                "data": "ISO Date"
              }
            ]
          }
        ]
      },
      "cliente": {
        "identificador": "string",
        "crmId": 0,
        "nome": "string",
        "numeroDocumento": "string",
        "contatos": [
          {
            "endereco": "string",
            "telefone": "string",
            "email": "string",
            "cidade": "string",
            "estado": "string"
          }
        ]
      }
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 10,
    "totalItems": 100,
    "itemsPerPage": 10
  },
  "filtros": {
    "filtroVencimento": "todos",
    "mesesProximos": 12,
    "sortBy": "dataVigencia",
    "sortOrder": "asc"
  }
}
```

## Exemplos de Uso

### Listar todas as renovações
```
GET /api/marca/renovacoes
```

### Renovações que vencem nos próximos 6 meses
```
GET /api/marca/renovacoes?filtroVencimento=proximoVencimento&mesesProximos=6
```

### Ordenar por dias restantes (mais urgente primeiro)
```
GET /api/marca/renovacoes?sortBy=diasRestantes&sortOrder=asc
```

### Paginação e filtros combinados
```
GET /api/marca/renovacoes?page=2&limit=20&filtroVencimento=noAno&sortBy=marcaNome&sortOrder=desc
```

## Tratamento de Erros

### Códigos de Status HTTP:
- `200`: Sucesso
- `500`: Erro interno do servidor

### Estrutura de Erro:
```json
{
  "success": false,
  "message": "Mensagem de erro",
  "error": "Detalhes técnicos do erro"
}
```

## Base Legal

Este endpoint implementa as regras de prorrogação de registro conforme o [Manual de Marcas do INPI](https://manualdemarcas.inpi.gov.br/projects/manual/wiki/06_Concess%C3%A3o_manuten%C3%A7%C3%A3o_e_extin%C3%A7%C3%A3o_do_registro#64-Prorroga%C3%A7%C3%A3o-do-registro), especificamente:

- Vigência de 10 anos a partir da concessão
- Possibilidade de prorrogação no último ano ou nos 6 meses subsequentes
- Identificação de códigos de serviço para renovações anteriores 