import axios from "axios";
import { format } from "date-fns";
import dotenv from "dotenv";
import { format as formatDate } from "date-fns";
import { Logger } from "../services/logger.service";

dotenv.config();

const crmToken = process.env.CRM_TOKEN || "";
const baseUrl = "https://api.pipe.run/v1";
const batchSize = 20; // Tamanho do lote para paralelismo controlado
const personBatchSize = 10;
const maxRetries = 3; // Número máximo de tentativas para requisições com falha


/**
 * Formata a data para o formato específico requerido pela API
 * "yyyy-MM-dd HH:mm:ss"
 */
const formatDateForApi = (date: Date): string => {
  return formatDate(date, "yyyy-MM-dd HH:mm:ss");
};

/**
 * Executa um batch de requisições com lógica de retry
 */
const executeBatchWithRetry = async (
  promises: (() => Promise<any>)[]
): Promise<any[]> => {
  const results: any[] = [];
  for (const promiseFn of promises) {
    let success = false;
    for (let attempt = 1; attempt <= maxRetries && !success; attempt++) {
      try {
        Logger.info(`Tentativa ${attempt} para requisição...`);
        const result = await promiseFn();
        results.push(result);
        success = true;
      } catch (error) {
        Logger.error(
          `Erro na tentativa ${attempt}: ${(error as any).message}`
        );
        if (attempt < maxRetries) {
          const waitTime = attempt * 1000; // Tempo de espera exponencial
          Logger.info(
            `Aguardando ${
              waitTime / 1000
            } segundos antes de tentar novamente...`
          );
          await sleep(waitTime);
        } else {
          Logger.error(`Falha após ${maxRetries} tentativas.`);
          results.push(error); // Armazena o erro para depuração
        }
      }
    }
  }
  return results;
};

/**
 * Executa um batch de requisições com lógica de retry para o lote inteiro
 * Se qualquer requisição falhar, todo o lote é tentado novamente
 */
const executeBatchWithFullLoteRetry = async (
  promiseFunctions: (() => Promise<any>)[]
): Promise<any[]> => {
  const maxLoteRetries = 3; // Número máximo de tentativas para o lote inteiro
  const retryDelay = 10000; // 10 segundos de espera entre tentativas
  let lastError: any = null;
  
  for (let attempt = 1; attempt <= maxLoteRetries; attempt++) {
    try {
      Logger.info(`Tentativa ${attempt} para o lote inteiro com ${promiseFunctions.length} requisições...`);
      
      // Executa todas as promessas em paralelo
      const results = await Promise.all(promiseFunctions.map(fn => fn()));
      
      // Se chegou aqui, todas as requisições tiveram sucesso
      Logger.success(`Lote processado com sucesso na tentativa ${attempt}`);
      return results;
      
    } catch (error: any) {
      lastError = error;
      Logger.error(`Erro na tentativa ${attempt} do lote: ${error.message}`);
      
      // Verifica se é erro 429 (Too Many Requests)
      const isTooManyRequests = error.response?.status === 429 || 
                               error.message.includes('429') || 
                               error.message.toLowerCase().includes('too many requests');
      
      if (isTooManyRequests) {
        Logger.warn(`Erro 429 (Too Many Requests) detectado. Todo o lote será repetido.`);
      }
      
      // Se não for a última tentativa, espera e tenta novamente
      if (attempt < maxLoteRetries) {
        const waitTime = retryDelay;
        Logger.info(`Aguardando ${waitTime / 1000} segundos antes de tentar todo o lote novamente...`);
        await sleep(waitTime);
      } else {
        Logger.error(`Falha após ${maxLoteRetries} tentativas do lote inteiro.`);
      }
    }
  }
  
  // Se chegou aqui, todas as tentativas falharam
  throw lastError || new Error('Falha em todas as tentativas do lote');
};

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const fetchDeals = async (
): Promise<any[]> => {
  const syncType = 'DEALS';
  
  
  const startTime = new Date();
  Logger.section(`🔄 INICIANDO SINCRONIZAÇÃO DE DEALS`);
  Logger.info(`Data/hora de início: ${startTime.toLocaleString()}`);


  const allData: any[] = [];

  // Parâmetros da API
  const baseParams: any = {
    token: crmToken,
    show: 200,
    with: "tags,customFields,owner,persons,city"  
  };

  try {
    Logger.info(`Buscando primeira página de deals...`);
    const firstPageResponse = await axios.get(`${baseUrl}/deals`, {
      params: { ...baseParams, page: 1 },
    });

    if (!firstPageResponse.data.success) {
      throw new Error(
        `Erro na resposta inicial: ${firstPageResponse.data.message}`
      );
    }
    
    const totalPages = firstPageResponse.data.meta.total_pages || 1;
    const totalRecords = firstPageResponse.data.meta.total || 0;
    
    Logger.info(`Total de páginas a processar: ${totalPages}`);
    Logger.info(`Total de registros a buscar: ${totalRecords}`);
    
    // Adiciona os dados da primeira página
    if (firstPageResponse.data.data && firstPageResponse.data.data.length > 0) {
      allData.push(...firstPageResponse.data.data);
      Logger.info(`Primeira página obtida: ${firstPageResponse.data.data.length} registros`);
    }
    
    // Se tiver mais páginas, busca-as em lotes
    if (totalPages > 1) {
      const totalBatches = Math.ceil((totalPages - 1) / batchSize);
      
      Logger.info(`Processando ${totalPages - 1} páginas restantes em ${totalBatches} lotes...`);

      for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
        const startPage = batchIndex * batchSize + 2; // +2 porque a página 1 já foi processada
        const endPage = Math.min(startPage + batchSize - 1, totalPages);
        
        Logger.info(
          `Preparando lote ${batchIndex + 1} de ${totalBatches} (páginas ${startPage} até ${endPage})...`
        );
        
        // Cria as funções de promessa para o lote atual
        const promiseFunctions = [];
        for (let page = startPage; page <= endPage; page++) {
          promiseFunctions.push(() => 
            axios.get(`${baseUrl}/deals`, {
              params: { ...baseParams, page },
            })
          );
        }
        
        // Executa o lote com retry completo
        try {
          Logger.info(`Executando lote ${batchIndex + 1} com ${promiseFunctions.length} requisições...`);
          
          const batchResults = await executeBatchWithFullLoteRetry(promiseFunctions);
          
          let recordsInBatch = 0;
          batchResults.forEach((pageResponse) => {
            if (pageResponse.data && pageResponse.data.success && Array.isArray(pageResponse.data.data)) {
              const dealsNaPagina = pageResponse.data.data;
              const pageRecords = dealsNaPagina.length;
              allData.push(...dealsNaPagina);
              recordsInBatch += pageRecords;
            } else if (pageResponse.data && !pageResponse.data.success) {
              Logger.warn(`Resposta da API para uma página de deals não foi bem-sucedida: ${pageResponse.data.message || JSON.stringify(pageResponse.data)}`);
            }
          });

          Logger.success(`Lote ${batchIndex + 1} concluído: obtidos ${recordsInBatch} registros (total acumulado: ${allData.length})`);
          
        } catch (error: any) {
          Logger.error(`Erro fatal no lote ${batchIndex + 1}: ${error.message}`);
          throw error; // Propaga o erro para ser tratado no bloco catch externo
        }
        
        // Aguarda entre os lotes para evitar erros 429 (rate limit)
        if (batchIndex < totalBatches - 1) {
          Logger.info(`Aguardando 5 segundos antes do próximo lote...`);
          await sleep(5000);
        }
      }
    }
    
    const endTime = new Date();
    const durationMs = endTime.getTime() - startTime.getTime();
    const durationMin = Math.round(durationMs / 60000 * 10) / 10; // Arredonda para 1 casa decimal
    
    Logger.success(`Busca de deals finalizada com sucesso!`);
    Logger.info(`Total de registros obtidos: ${allData.length}`);
    Logger.info(`Duração: ${durationMin} minutos`);
    

    
    return allData;
    
  } catch (error: any) {
    Logger.error(`Erro fatal ao buscar dados de deals:`, error);
    
    return [];
  }
};

export const fetchPersons = async (
): Promise<any[]> => {

  
  // Registra o início da sincronização
  // await updateSyncStatus(syncType, 'RUNNING');
  
  const startTime = new Date();
  Logger.section(`🔄 INICIANDO SINCRONIZAÇÃO DE PERSONS`);
  Logger.info(`Data/hora de início: ${startTime.toLocaleString()}`);

  // Busca o timestamp da última sincronização bem-sucedida
  const allData: any[] = [];

  // Parâmetros da API
  const params: any = {
    token: crmToken,
    show: 200,
    with: "contactPhones,contactEmails",
  };


  try {
    Logger.info(`Buscando primeira página de persons...`);
    const firstPageResponse = await axios.get(`${baseUrl}/persons`, {
      params: { ...params, page: 1 },
    });

    if (!firstPageResponse.data.success) {
      throw new Error(
        `Erro na resposta inicial: ${firstPageResponse.data.message}`
      );
    }

    const totalPages = firstPageResponse.data.meta.total_pages || 1;
    const totalRecords = firstPageResponse.data.meta.total || 0;
    
    Logger.info(`Total de páginas a processar: ${totalPages}`);
    Logger.info(`Total de registros a buscar: ${totalRecords}`);
    
    // Adiciona os dados da primeira página
    if (firstPageResponse.data.data && firstPageResponse.data.data.length > 0) {
      allData.push(...firstPageResponse.data.data);
      Logger.info(`Primeira página obtida: ${firstPageResponse.data.data.length} registros`);
    }
    
    // Se tiver mais páginas, busca-as em lotes usando executeBatchWithRetry
    if (totalPages > 1) {
      const promises: (() => Promise<any>)[] = [];
      const totalBatches = Math.ceil((totalPages - 1) / personBatchSize);
      
      Logger.info(`Processando ${totalPages - 1} páginas restantes em ${totalBatches} lotes...`);

      for (let page = 2; page <= totalPages; page++) {
        promises.push(() =>
          axios.get(`${baseUrl}/persons`, {
            params: { ...params, page },
          })
        );

        if (promises.length === personBatchSize || page === totalPages) {
          const currentBatch = Math.ceil((page - 1) / personBatchSize);
          Logger.info(`Processando lote ${currentBatch} de ${totalBatches}...`);
          
          const batchResults = await executeBatchWithRetry(promises);
          
          let successCount = 0;
          let errorCount = 0;
          let recordsInBatch = 0;
          
          batchResults.forEach((result) => {
            if (result instanceof Error) {
              Logger.error(`Erro ao buscar persons: ${result.message}`);
              errorCount++;
            } else if (result.data && result.data.success) {
              const pageRecords = result.data.data.length;
              allData.push(...result.data.data);
              recordsInBatch += pageRecords;
              successCount++;
              Logger.info(`Página processada com sucesso: ${pageRecords} registros`);
            } else {
              Logger.warn(`Resposta inesperada: ${JSON.stringify(result)}`);
              errorCount++;
            }
          });

          Logger.info(`Lote ${currentBatch} concluído: ${successCount} páginas com sucesso, ${errorCount} com erro, ${recordsInBatch} registros obtidos`);
          
          promises.length = 0;
          
          // Aguarda entre os lotes para evitar rate limit
          if (page < totalPages) {
            Logger.info(`Aguardando 3 segundos antes do próximo lote...`);
            await sleep(3000);
          }
        }
      }
    }
    
    const endTime = new Date();
    const durationMs = endTime.getTime() - startTime.getTime();
    const durationMin = Math.round(durationMs / 60000 * 10) / 10; // Arredonda para 1 casa decimal
    
    Logger.success(`Busca de persons finalizada com sucesso!`);
    Logger.info(`Total de registros obtidos: ${allData.length}`);
    Logger.info(`Duração: ${durationMin} minutos`);
    
    
    
    return allData;
    
  } catch (error: any) {
    Logger.error(`Erro fatal ao buscar dados de persons:`, error);
    
    
    
    return [];
  }
};

export const fetchPipelines = async (): Promise<any[]> => {
  const startTime = new Date().toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' });
  console.log(`[${startTime}] Iniciando busca de pipelines...`);

  const allData: any[] = [];
  const params = {};

  try {
    const firstPageResponse = await axios.get(`${baseUrl}/pipelines`, {
      params: { token: crmToken, show: 200, page: 1, ...params },
    });

    if (!firstPageResponse.data.success) {
      throw new Error(
        `Erro na resposta inicial: ${firstPageResponse.data.message}`
      );
    }

    const totalPages = firstPageResponse.data.meta.total_pages || 1;
    for (let page = 2; page <= totalPages; page++) {
      console.log(`Processando página ${page} de ${totalPages}...`);
      const response = await axios.get(`${baseUrl}/pipelines`, {
        params: { token: crmToken, show: 200, page, ...params },
      });

      if (response.data.success) {
        allData.push(...response.data.data);
      } else {
        console.warn(
          `Erro na página ${page} de pipelines: ${response.data.message}`
        );
      }
    }
    const finalData = [...firstPageResponse.data.data, ...allData];
    const endTime = new Date().toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' });
    console.log(`[${endTime}] Busca de pipelines finalizada. Total de registros: ${finalData.length}`);
    return finalData;
  } catch (error: any) {
    console.error("Erro ao buscar dados de pipelines:", error.message);
    return [];
  }
};

export const fetchStages = async (): Promise<any[]> => {
  const startTime = new Date().toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' });
  console.log(`[${startTime}] Iniciando busca de stages...`);

  const allData: any[] = [];
  const params = {};

  try {
    const firstPageResponse = await axios.get(`${baseUrl}/stages`, {
      params: { token: crmToken, show: 200, page: 1, ...params },
    });

    if (!firstPageResponse.data.success) {
      throw new Error(
        `Erro na resposta inicial: ${firstPageResponse.data.message}`
      );
    }

    const totalPages = firstPageResponse.data.meta.total_pages || 1;

    for (let page = 2; page <= totalPages; page++) {
      console.log(`Processando página ${page} de ${totalPages}...`);
      const response = await axios.get(`${baseUrl}/stages`, {
        params: { token: crmToken, show: 200, page, ...params },
      });

      if (response.data.success) {
        allData.push(...response.data.data);
      } else {
        console.warn(
          `Erro na página ${page} de stages: ${response.data.message}`
        );
      }
    }

    const finalData = [...firstPageResponse.data.data, ...allData];
    const endTime = new Date().toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' });
    console.log(`[${endTime}] Busca de stages finalizada. Total de registros: ${finalData.length}`);
    return finalData;
  } catch (error: any) {
    console.error("Erro ao buscar dados de stages:", error.message);
    return [];
  }
};

export const fetchCompanies = async (
  syncLastModified: boolean = false
): Promise<any[]> => {
  const startTime = new Date().toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' });
  console.log(`[${startTime}] Iniciando busca de companies...`);

  const allData: any[] = [];
  const updatedAtStart = format(new Date(), "yyyy-MM-dd");
  
  const params = {
    with: "contactPhones,contactEmails",
    ...(syncLastModified ? { updated_at_start: updatedAtStart } : {}),
  }

  try {
    const firstPageResponse = await axios.get(`${baseUrl}/companies`, {
      params: { token: crmToken, show: 200, page: 1, ...params },
    });

    if (!firstPageResponse.data.success) {
      throw new Error(
        `Erro na resposta inicial: ${firstPageResponse.data.message}`
      );
    }

    const totalPages = firstPageResponse.data.meta.total_pages || 1;
    console.log(`Total de páginas a processar: ${totalPages}`);

    const promises: (() => Promise<any>)[] = [];

    for (let page = 2; page <= totalPages; page++) {
      promises.push(() =>
        axios.get(`${baseUrl}/companies`, {
          params: { token: crmToken, show: 200, page, ...params },
        })
      );

      if (promises.length === batchSize || page === totalPages) {
        console.log(`Processando lote ${Math.ceil(page/batchSize)} de ${Math.ceil(totalPages/batchSize)}...`);
        const batchResults = await executeBatchWithRetry(promises);
        batchResults.forEach((result) => {
          if (result instanceof Error) {
            console.error(`Erro ao buscar companies: ${result.message}`);
          } else if (result.data && result.data.success) {
            allData.push(...result.data.data);
          } else {
            console.warn(`Resposta inesperada: ${JSON.stringify(result)}`);
          }
        });

        promises.length = 0;
        await sleep(2000);
      }
    }
    const finalData = [...firstPageResponse.data.data, ...allData];
    const endTime = new Date().toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' });
    console.log(`[${endTime}] Busca de companies finalizada. Total de registros: ${finalData.length}`);
    return finalData;
  } catch (error: any) {
    console.error("Erro ao buscar dados de companies:", error.message);
    return [];
  }
};