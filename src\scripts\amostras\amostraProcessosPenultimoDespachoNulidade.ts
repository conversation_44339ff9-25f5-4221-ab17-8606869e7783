import { PrismaClient, Processo, <PERSON>pacho, RPI, ProtocoloDespacho } from "@prisma/client";
import { writeFileSync } from "fs";
import cliProgress from "cli-progress";

const prisma = new PrismaClient({
  log: ["warn", "error"],
});

const NOME_DESPACHO_NULIDADE_CONTAINS = "Requerimento provido (nulo o registro";
const NOMES_DESPACHOS_POS_NULIDADE_RECURSO = new Set([
  "mantida a concessão",
  "recurso provido",
  "decisão reformada",
]);

const TAMANHO_AMOSTRA = 10;
const TAMANHO_LOTE_CANDIDATOS = 100;

interface DespachoAmostra extends Despacho {
  rpi: RPI | null;
  protocolos: ProtocoloDespacho[];
}

interface ProcessoAmostra {
  numeroProcesso: string;
  dataDeposito: Date | null;
  despachos: DespachoAmostra[];
  despachoNulidadeEncontrado?: DespachoAmostra;
  despachoRecursoPosNulidadeEncontrado?: DespachoAmostra;
}

async function buscarAmostraNulidadeUnicaComRecursoPosterior() {
  console.log(
`Buscando ${TAMANHO_AMOSTRA} processos com:
    1. UM ÚNICO despacho contendo "${NOME_DESPACHO_NULIDADE_CONTAINS}"
    2. Seguido por um despacho contendo uma das frases: [${Array.from(NOMES_DESPACHOS_POS_NULIDADE_RECURSO).join(", ")}]`
  );

  const amostraEncontrada: ProcessoAmostra[] = [];
  let skip = 0;
  let processosVerificados = 0;

  const barraProgresso = new cliProgress.SingleBar(
    {
      format: `Verificando processos | {value} verificados | ${TAMANHO_AMOSTRA} amostras alvo | {bar} | ETA: {eta}s`,
    },
    cliProgress.Presets.shades_classic
  );
  barraProgresso.start(TAMANHO_AMOSTRA * 200, 0); // Estimativa pode precisar de ajuste

  while (amostraEncontrada.length < TAMANHO_AMOSTRA) {
    const processosCandidatos = await prisma.processo.findMany({
      where: {
        despachos: {
          some: {
            nome: {
              contains: NOME_DESPACHO_NULIDADE_CONTAINS,
              mode: 'insensitive',
            },
          },
        },
      },
      include: {
        despachos: {
          include: {
            rpi: true,
            protocolos: true,
          },
          orderBy: {
            rpi: {
              dataPublicacao: "asc",
            },
            // id: 'asc', 
          },
        },
      },
      skip: skip,
      take: TAMANHO_LOTE_CANDIDATOS,
    });

    if (processosCandidatos.length === 0) {
      console.log("\nNenhum processo candidato adicional encontrado com o pré-filtro.");
      break;
    }

    for (const processo of processosCandidatos) {
      processosVerificados++;
      barraProgresso.update(processosVerificados);

      const despachosDoProcesso = processo.despachos as DespachoAmostra[];
      let indiceUnicoDespachoNulidade = -1;
      let contagemDespachosNulidade = 0;

      // Contar despachos de nulidade e encontrar o índice do primeiro (se houver apenas um)
      for (let i = 0; i < despachosDoProcesso.length; i++) {
        if (despachosDoProcesso[i].nome?.toLowerCase().includes(NOME_DESPACHO_NULIDADE_CONTAINS.toLowerCase())) {
          contagemDespachosNulidade++;
          if (indiceUnicoDespachoNulidade === -1) { // Pega o primeiro encontrado
            indiceUnicoDespachoNulidade = i;
          }
        }
      }

      // Prosseguir somente se houver EXATAMENTE UM despacho de nulidade
      if (contagemDespachosNulidade === 1) {
        // Verificar se o único despacho de nulidade não é o último despacho do processo
        if (indiceUnicoDespachoNulidade < despachosDoProcesso.length - 1) {
          const despachoNulidade = despachosDoProcesso[indiceUnicoDespachoNulidade];
          let despachoRecursoEncontrado: DespachoAmostra | undefined = undefined;

          // Verificar os despachos subsequentes ao de nulidade
          for (let j = indiceUnicoDespachoNulidade + 1; j < despachosDoProcesso.length; j++) {
            const nomeDespachoSubsequenteLower = despachosDoProcesso[j].nome?.toLowerCase();
            if (nomeDespachoSubsequenteLower) {
              for (const termoBusca of NOMES_DESPACHOS_POS_NULIDADE_RECURSO) {
                if (nomeDespachoSubsequenteLower.includes(termoBusca)) {
                  despachoRecursoEncontrado = despachosDoProcesso[j];
                  break; 
                }
              }
            }
            if (despachoRecursoEncontrado) break;
          }

          if (despachoRecursoEncontrado) {
            amostraEncontrada.push({
              numeroProcesso: processo.numero,
              dataDeposito: processo.dataDeposito || null,
              despachos: despachosDoProcesso,
              despachoNulidadeEncontrado: despachoNulidade,
              despachoRecursoPosNulidadeEncontrado: despachoRecursoEncontrado,
            });

            if (amostraEncontrada.length >= TAMANHO_AMOSTRA) {
              break; 
            }
          }
        }
      }
    }

    if (amostraEncontrada.length >= TAMANHO_AMOSTRA) {
      break; 
    }
    skip += TAMANHO_LOTE_CANDIDATOS;
  }

  barraProgresso.stop();

  if (amostraEncontrada.length === 0) {
    console.log("\nNenhum processo encontrado que satisfaça todos os critérios (incluindo nulidade única).");
    return;
  }

  console.log(`\nEncontrados ${amostraEncontrada.length} processos para a amostra.`);

  const dataHora = new Date().toISOString().replace(/[:.]/g, "-");
  const nomeArquivo = `amostra-nulidade-unica-com-recurso-posterior-${dataHora}.json`;

  try {
    writeFileSync(nomeArquivo, JSON.stringify(amostraEncontrada, null, 2));
    console.log(`Amostra de processos salva em: ${nomeArquivo}`);
  } catch (error) {
    console.error("\nErro ao salvar arquivo JSON da amostra:", error);
  }
}

async function main() {
  try {
    await buscarAmostraNulidadeUnicaComRecursoPosterior(); // Nome da função atualizado
  } catch (error) {
    console.error("\nErro na execução principal:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log("\nConexão com o banco de dados fechada.");
  }
}

if (require.main === module) {
  main();
} 