import { Router } from 'express';
import * as marcaController from '../controllers/marcaController';

const router = Router();

/**
 * @route   GET /api/marca/renovacoes
 * @desc    Lista renovações necessárias de marcas
 * @access  Privado
 * @query   page, limit, sortBy, sortOrder, filtroVencimento, mesesProximos
 */
router.get('/renovacoes', marcaController.listarRenovacoes);

/**
 * @route   PUT /api/marca/:id
 * @desc    Atualiza os dados de uma marca
 * @access  Privado
 */
router.put('/:id', marcaController.atualizarMarca);

export default router;