// src/scheduler/meritoScheduler.ts

import cron from 'node-cron';
import { executarECadastrarEstimativasMerito } from '../services/meritoService';

// Configuração para executar toda terça-feira às 14:00 (fuso horário de Brasília)
export function iniciarAgendadorMerito() {
  console.log('Iniciando agendador de cálculo de estimativas de mérito...');
  
  cron.schedule('0 14 * * 2', async () => {
    const timestamp = new Date().toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' });
    console.log(`[${timestamp}] Executando cálculo programado de estimativas de mérito...`);
    
    try {
      await executarECadastrarEstimativasMerito();
      console.log(`[${new Date().toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' })}] Cálculo concluído com sucesso.`);
    } catch (error) {
      console.error(`[${new Date().toLocaleString('pt-BR', { timeZone: 'America/Sao_Paulo' })}] Erro no cálculo:`, error);
    }
  }, {
    scheduled: true,
    timezone: "America/Sao_Paulo"
  });
  
  console.log('Agendador configurado: Toda terça-feira às 14:00 (horário de Brasília)');
} 