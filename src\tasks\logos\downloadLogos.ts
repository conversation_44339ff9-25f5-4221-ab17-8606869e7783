import { PrismaClient } from '@prisma/client';
import path from 'path';
import fs from 'fs';
import { downloadLogoProcesso, logoExisteLocalmente, DownloadResult } from '../utils/download';
import { enviarProcessosParaDownload, verificarFastApiOnline } from '../utils/fastapi';

const prisma = new PrismaClient();

interface RelatorioExecucao {
  totalProcessosEncontrados: number;
  logosJaExistentes: number;
  tentativasDownload: number;
  downloadsComSucesso: number;
  downloadsFalharam: number;
  processosSemLogo: string[];
  processosEnviadosParaFastApi: number;
  fastapiDisponivel: boolean;
  fastapiResposta?: any;
  tempoExecucao: string;
  erros: string[];
}

const PROCURADOR_FILTRO = 'REGISTRE-SE LTDA';
const OUTPUT_DIR = path.join(__dirname, '..', '..', '..', 'public', 'logos', 'processos');

async function downloadLogosProcessos(): Promise<RelatorioExecucao> {
  const inicioExecucao = Date.now();
  const relatorio: RelatorioExecucao = {
    totalProcessosEncontrados: 0,
    logosJaExistentes: 0,
    tentativasDownload: 0,
    downloadsComSucesso: 0,
    downloadsFalharam: 0,
    processosSemLogo: [],
    processosEnviadosParaFastApi: 0,
    fastapiDisponivel: false,
    tempoExecucao: '',
    erros: []
  };

  try {
    console.log(`🔍 Iniciando busca por processos com procurador: "${PROCURADOR_FILTRO}"`);
    console.log(`📁 Diretório de saída das logos: ${OUTPUT_DIR}`);

    // 1. Buscar processos monitorados no banco de dados
    const processosMonitorados = await prisma.processo.findMany({
      where: {
        procurador: {
          nome: {
            contains: PROCURADOR_FILTRO,
            mode: 'insensitive'
          }
        }
      },
      select: {
        numero: true,
        id: true,
        procurador: {
          select: {
            nome: true
          }
        }
      },
      orderBy: {
        numero: 'asc'
      }
    });

    relatorio.totalProcessosEncontrados = processosMonitorados.length;
    console.log(`📊 Encontrados ${processosMonitorados.length} processos monitorados.`);

    if (processosMonitorados.length === 0) {
      console.log('⚠️ Nenhum processo encontrado. Encerrando execução.');
      return relatorio;
    }

    // 2. Verificar quais logos já existem localmente
    console.log(`\n🔎 Verificando logos existentes...`);
    const processosParaDownload: string[] = [];

    for (const processo of processosMonitorados) {
      if (logoExisteLocalmente(processo.numero, OUTPUT_DIR)) {
        relatorio.logosJaExistentes++;
        console.log(`✅ Logo já existe: ${processo.numero}`);
      } else {
        processosParaDownload.push(processo.numero);
        console.log(`❌ Logo não encontrada: ${processo.numero}`);
      }
    }

    console.log(`\n📈 Resumo inicial:`);
    console.log(`   Total de processos: ${relatorio.totalProcessosEncontrados}`);
    console.log(`   Logos já existentes: ${relatorio.logosJaExistentes}`);
    console.log(`   Precisam download: ${processosParaDownload.length}`);

    // 3. Tentar baixar logos que não existem
    if (processosParaDownload.length > 0) {
      console.log(`\n⬬ Iniciando download de ${processosParaDownload.length} logos...`);
      
      const resultadosDownload: DownloadResult[] = [];
      relatorio.tentativasDownload = processosParaDownload.length;

      for (let i = 0; i < processosParaDownload.length; i++) {
        const numeroProcesso = processosParaDownload[i];
        console.log(`⬬ [${i + 1}/${processosParaDownload.length}] Baixando ${numeroProcesso}...`);
        
        const resultado = await downloadLogoProcesso(numeroProcesso);
        resultadosDownload.push(resultado);

        if (resultado.success) {
          relatorio.downloadsComSucesso++;
          console.log(`   ✅ Sucesso: ${resultado.fileSize} bytes salvos`);
        } else {
          relatorio.downloadsFalharam++;
          relatorio.processosSemLogo.push(numeroProcesso);
          console.log(`   ❌ Falha: ${resultado.error}`);
        }

        // Pequeno delay para não sobrecarregar o servidor
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      console.log(`\n📊 Resultado dos downloads:`);
      console.log(`   Sucessos: ${relatorio.downloadsComSucesso}`);
      console.log(`   Falhas: ${relatorio.downloadsFalharam}`);
    }

    // 4. Enviar processos sem logo para FastAPI
    if (relatorio.processosSemLogo.length > 0) {
      console.log(`\n🚀 Preparando envio para FastAPI...`);
      console.log(`📋 Processos sem logo: ${relatorio.processosSemLogo.join(', ')}`);
      
      // Verificar se FastAPI está disponível
      relatorio.fastapiDisponivel = await verificarFastApiOnline();
      
      if (relatorio.fastapiDisponivel) {
        console.log(`✅ FastAPI está online.`);
        
        const respostaFastApi = await enviarProcessosParaDownload(relatorio.processosSemLogo);
        relatorio.fastapiResposta = respostaFastApi;
        
        if (respostaFastApi.success) {
          relatorio.processosEnviadosParaFastApi = relatorio.processosSemLogo.length;
          console.log(`✅ ${relatorio.processosEnviadosParaFastApi} processos enviados para FastAPI com sucesso.`);
        } else {
          relatorio.erros.push(`Erro ao enviar para FastAPI: ${respostaFastApi.message}`);
          console.error(`❌ Erro ao enviar para FastAPI: ${respostaFastApi.message}`);
        }
      } else {
        relatorio.erros.push('FastAPI não está disponível em localhost:8000');
        console.error(`❌ FastAPI não está disponível em localhost:8000`);
        console.log(`💡 Para processar estes ${relatorio.processosSemLogo.length} processos, inicie a FastAPI e execute novamente.`);
      }
    } else {
      console.log(`\n🎉 Todas as logos foram baixadas com sucesso! Nenhum processo precisa ser enviado para FastAPI.`);
    }

  } catch (error: any) {
    relatorio.erros.push(`Erro geral: ${error.message}`);
    console.error(`❌ Erro durante execução:`, error);
  } finally {
    await prisma.$disconnect();
  }

  // Calcular tempo de execução
  const fimExecucao = Date.now();
  const tempoMs = fimExecucao - inicioExecucao;
  relatorio.tempoExecucao = `${(tempoMs / 1000).toFixed(2)}s`;

  return relatorio;
}

async function salvarRelatorio(relatorio: RelatorioExecucao): Promise<void> {
  const nomeArquivo = `relatorio_download_logos_${new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')}.json`;
  const caminhoRelatorio = path.join(__dirname, '..', '..', '..', 'output', nomeArquivo);
  
  // Garantir que a pasta output existe
  const pastaOutput = path.dirname(caminhoRelatorio);
  if (!fs.existsSync(pastaOutput)) {
    fs.mkdirSync(pastaOutput, { recursive: true });
  }

  fs.writeFileSync(caminhoRelatorio, JSON.stringify(relatorio, null, 2));
  console.log(`📄 Relatório salvo em: ${caminhoRelatorio}`);
}

async function main() {
  console.log(`🚀 === DOWNLOAD DE LOGOS DE PROCESSOS ===`);
  console.log(`⏰ Início: ${new Date().toLocaleString()}`);
  
  const relatorio = await downloadLogosProcessos();
  
  console.log(`\n📊 === RELATÓRIO FINAL ===`);
  console.log(`📈 Processos encontrados: ${relatorio.totalProcessosEncontrados}`);
  console.log(`✅ Logos já existiam: ${relatorio.logosJaExistentes}`);
  console.log(`⬬ Tentativas de download: ${relatorio.tentativasDownload}`);
  console.log(`✅ Downloads bem-sucedidos: ${relatorio.downloadsComSucesso}`);
  console.log(`❌ Downloads com falha: ${relatorio.downloadsFalharam}`);
  console.log(`🚀 Processos enviados para FastAPI: ${relatorio.processosEnviadosParaFastApi}`);
  console.log(`🕐 Tempo de execução: ${relatorio.tempoExecucao}`);
  
  if (relatorio.erros.length > 0) {
    console.log(`❌ Erros encontrados:`);
    relatorio.erros.forEach(erro => console.log(`   - ${erro}`));
  }

  await salvarRelatorio(relatorio);
  
  console.log(`⏰ Fim: ${new Date().toLocaleString()}`);
  console.log(`🏁 Execução concluída.`);
}

// Executar o script
main().catch(console.error); 