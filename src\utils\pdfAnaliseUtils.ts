import fs from 'fs';
import pdfParse from 'pdf-parse';

/**
 * Tipos para análise de PDF
 */
export interface EstruturaPdf {
  info: {
    paginas: number;
    tamanhoArquivo: number;
    criadoEm?: Date;
    modificadoEm?: Date;
    titulo?: string;
    autor?: string;
    assunto?: string;
    criador?: string;
    produtor?: string;
  };
  conteudo: {
    textoCompleto: string;
    numeroCaracteres: number;
    numeroPalavras: number;
    linhas: string[];
    numeroLinhas: number;
  };
  analise: {
    contemNumeroProtocolo: boolean;
    contemClasse: boolean;
    contemMarca: boolean;
    contemData: boolean;
    possiveisNumeros: string[];
    possiveisClasses: string[];
    possiveisDatas: string[];
    palavrasChave: string[];
  };
}

/**
 * Lê e parseia um arquivo PDF
 */
const lerArquivoPdf = async (caminhoArquivo: string): Promise<any> => {
  const dataBuffer = fs.readFileSync(caminhoArquivo);
  return await pdfParse(dataBuffer);
};

/**
 * Extrai informações básicas do PDF
 */
const extrairInfoBasica = (dadosPdf: any, tamanhoArquivo: number): EstruturaPdf['info'] => ({
  paginas: dadosPdf.numpages,
  tamanhoArquivo,
  criadoEm: dadosPdf.info?.CreationDate ? new Date(dadosPdf.info.CreationDate) : undefined,
  modificadoEm: dadosPdf.info?.ModDate ? new Date(dadosPdf.info.ModDate) : undefined,
  titulo: dadosPdf.info?.Title || undefined,
  autor: dadosPdf.info?.Author || undefined,
  assunto: dadosPdf.info?.Subject || undefined,
  criador: dadosPdf.info?.Creator || undefined,
  produtor: dadosPdf.info?.Producer || undefined,
});

/**
 * Processa o conteúdo textual do PDF
 */
const processarConteudo = (texto: string): EstruturaPdf['conteudo'] => {
  const linhas = texto.split('\n').map(linha => linha.trim()).filter(linha => linha.length > 0);
  const palavras = texto.split(/\s+/).filter(palavra => palavra.length > 0);

  return {
    textoCompleto: texto,
    numeroCaracteres: texto.length,
    numeroPalavras: palavras.length,
    linhas,
    numeroLinhas: linhas.length,
  };
};

/**
 * Busca padrões de número de protocolo
 */
const buscarNumerosProtocolo = (texto: string): string[] => {
  // Padrões comuns de protocolos (ajuste conforme necessário)
  const padroes = [
    /\b\d{9,12}\b/g,           // 9-12 dígitos consecutivos
    /\b\d{3}\.\d{3}\.\d{3}\b/g, // XXX.XXX.XXX
    /\b\d{2}\.\d{3}\.\d{3}[-\/]\d{1}\b/g, // XX.XXX.XXX-X ou XX.XXX.XXX/X
  ];

  const numeros = new Set<string>();
  
  padroes.forEach(padrao => {
    const matches = texto.match(padrao);
    if (matches) {
      matches.forEach(match => numeros.add(match));
    }
  });

  return Array.from(numeros);
};

/**
 * Busca padrões de classe
 */
const buscarClasses = (texto: string): string[] => {
  const padroes = [
    /CLASSE\s+(\d{1,2})/gi,
    /CLASS[EÊ]\s*:?\s*(\d{1,2})/gi,
    /NCL\s*:?\s*(\d{1,2})/gi,
  ];

  const classes = new Set<string>();
  
  padroes.forEach(padrao => {
    const matches = [...texto.matchAll(padrao)];
    matches.forEach(match => {
      if (match[1]) classes.add(match[1]);
    });
  });

  return Array.from(classes);
};

/**
 * Busca padrões de data
 */
const buscarDatas = (texto: string): string[] => {
  const padroes = [
    /\b\d{1,2}\/\d{1,2}\/\d{4}\b/g,     // DD/MM/YYYY
    /\b\d{1,2}-\d{1,2}-\d{4}\b/g,      // DD-MM-YYYY
    /\b\d{4}-\d{1,2}-\d{1,2}\b/g,      // YYYY-MM-DD
  ];

  const datas = new Set<string>();
  
  padroes.forEach(padrao => {
    const matches = texto.match(padrao);
    if (matches) {
      matches.forEach(match => datas.add(match));
    }
  });

  return Array.from(datas);
};

/**
 * Identifica palavras-chave importantes
 */
const identificarPalavrasChave = (texto: string): string[] => {
  const palavrasChave = [
    'protocolo', 'requerimento', 'marca', 'patente', 'desenho', 'industrial',
    'deposito', 'depósito', 'inpi', 'propriedade', 'intelectual', 'registro',
    'classe', 'ncl', 'vienna', 'titular', 'procurador', 'representante',
    'reivindicação', 'reivindicacao', 'especificação', 'especificacao',
    'resumo', 'figuras', 'desenhos'
  ];

  const textoLower = texto.toLowerCase();
  const encontradas = palavrasChave.filter(palavra => 
    textoLower.includes(palavra.toLowerCase())
  );

  return encontradas;
};

/**
 * Realiza análise avançada do conteúdo
 */
const analisarConteudo = (texto: string): EstruturaPdf['analise'] => {
  const possiveisNumeros = buscarNumerosProtocolo(texto);
  const possiveisClasses = buscarClasses(texto);
  const possiveisDatas = buscarDatas(texto);
  const palavrasChave = identificarPalavrasChave(texto);

  return {
    contemNumeroProtocolo: possiveisNumeros.length > 0,
    contemClasse: possiveisClasses.length > 0,
    contemMarca: palavrasChave.includes('marca'),
    contemData: possiveisDatas.length > 0,
    possiveisNumeros,
    possiveisClasses,
    possiveisDatas,
    palavrasChave,
  };
};

/**
 * Função principal para analisar estrutura do PDF
 */
export const analisarEstruturaPdf = async (caminhoArquivo: string): Promise<EstruturaPdf> => {
  try {
    // Obter tamanho do arquivo
    const stats = fs.statSync(caminhoArquivo);
    
    // Ler e parsear PDF
    const dadosPdf = await lerArquivoPdf(caminhoArquivo);
    
    // Extrair informações estruturadas
    const info = extrairInfoBasica(dadosPdf, stats.size);
    const conteudo = processarConteudo(dadosPdf.text);
    const analise = analisarConteudo(dadosPdf.text);

    return {
      info,
      conteudo,
      analise
    };

  } catch (error) {
    console.error('Erro ao analisar PDF:', error);
    throw new Error(`Falha na análise do PDF: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
  }
};

/**
 * Cria resumo da análise para resposta da API
 */
export const criarResumoAnalise = (estrutura: EstruturaPdf) => ({
  resumo: {
    paginas: estrutura.info.paginas,
    tamanhoKB: Math.round(estrutura.info.tamanhoArquivo / 1024),
    palavras: estrutura.conteudo.numeroPalavras,
    linhas: estrutura.conteudo.numeroLinhas,
  },
  detectado: {
    protocolos: estrutura.analise.possiveisNumeros,
    classes: estrutura.analise.possiveisClasses,
    datas: estrutura.analise.possiveisDatas,
    palavrasChave: estrutura.analise.palavrasChave,
  },
  flags: {
    contemProtocolo: estrutura.analise.contemNumeroProtocolo,
    contemClasse: estrutura.analise.contemClasse,
    contemMarca: estrutura.analise.contemMarca,
    contemData: estrutura.analise.contemData,
  }
}); 