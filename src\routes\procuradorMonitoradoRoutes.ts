import { Router } from 'express';
import {
  cadastrarProcuradorMonitorado,
  listarProcuradoresMonitorados,
  deletarProcuradorMonitorado,
  listarProcessosPorProcurador
} from '../controllers/procuradorMonitoradoController';

const router = Router();

// Rotas para procuradores monitorados
router.post('/', cadastrarProcuradorMonitorado);
router.get('/', listarProcuradoresMonitorados);
router.get("/procurador", listarProcessosPorProcurador);
router.delete('/:id', deletarProcuradorMonitorado);


export default router; 