import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

const prisma = new PrismaClient();

async function analisarTempoDeferimentoConcessao() {
  try {
    console.log('Iniciando análise do tempo entre deferimento e concessão...');
    console.log('Filtrando processos com concessão nas RPIs 2828 e 2829\n');

    let conteudoArquivo = 'ANÁLISE DO TEMPO ENTRE DEFERIMENTO E CONCESSÃO\n';
    conteudoArquivo += 'RPIs analisadas: 2828 e 2829\n';
    conteudoArquivo += 'Considerando apenas processos sem despachos entre deferimento e concessão\n';
    conteudoArquivo += '='.repeat(50) + '\n\n';

    // Buscar processos que têm tanto deferimento quanto concessão
    const processos = await prisma.processo.findMany({
      where: {
        despachos: {
          some: {
            nome: {
              contains: 'Deferimento do pedido',
              mode: 'insensitive'
            }
          }
        },
        AND: {
          despachos: {
            some: {
              AND: [
                {
                  nome: {
                    contains: 'Concessão de registro',
                    mode: 'insensitive'
                  }
                },
                {
                  rpi: {
                    numero: {
                      in: [2828, 2829]
                    }
                  }
                }
              ]
            }
          }
        }
      },
      include: {
        despachos: {
          include: {
            rpi: true
          },
          orderBy: {
            rpi: {
              dataPublicacao: 'asc'
            }
          }
        }
      }
    });

    console.log(`Encontrados ${processos.length} processos com deferimento e concessão nas RPIs 2828/2829`);
    conteudoArquivo += `Total de processos encontrados: ${processos.length}\n\n`;

    let tempoTotal = 0;
    let tempoMinimo = Number.MAX_VALUE;
    let tempoMaximo = 0;
    let processosAnalisados = 0;
    let processosDescartados = 0;
    const tempos: number[] = [];
    const detalhesProcessos: string[] = [];

    for (const processo of processos) {
      // Encontrar índices dos despachos relevantes
      const indexDeferimento = processo.despachos.findIndex(d => 
        d.nome?.toLowerCase().includes('deferimento do pedido')
      );
      
      const indexConcessao = processo.despachos.findIndex(d => 
        d.nome?.toLowerCase().includes('concessão de registro') &&
        (d.rpi?.numero === 2828 || d.rpi?.numero === 2829)
      );

      // Verificar se encontrou ambos os despachos e se são consecutivos
      if (indexDeferimento !== -1 && indexConcessao !== -1) {
        const deferimento = processo.despachos[indexDeferimento];
        const concessao = processo.despachos[indexConcessao];

        // Verificar se há despachos entre deferimento e concessão
        const temDespachoEntre = processo.despachos.some((d, index) => {
          return index > indexDeferimento && 
                 index < indexConcessao && 
                 d.nome !== deferimento.nome && 
                 d.nome !== concessao.nome;
        });

        if (!temDespachoEntre && deferimento?.rpi?.dataPublicacao && concessao?.rpi?.dataPublicacao) {
          const dataDeferimento = new Date(deferimento.rpi.dataPublicacao);
          const dataConcessao = new Date(concessao.rpi.dataPublicacao);
          
          const diffTime = Math.abs(dataConcessao.getTime() - dataDeferimento.getTime());
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          tempos.push(diffDays);
          tempoTotal += diffDays;
          tempoMinimo = Math.min(tempoMinimo, diffDays);
          tempoMaximo = Math.max(tempoMaximo, diffDays);
          processosAnalisados++;

          detalhesProcessos.push(
            `Processo: ${processo.numero}\n` +
            `RPI da Concessão: ${concessao.rpi.numero}\n` +
            `Data do Deferimento: ${dataDeferimento.toLocaleDateString('pt-BR')}\n` +
            `Data da Concessão: ${dataConcessao.toLocaleDateString('pt-BR')}\n` +
            `Tempo decorrido: ${diffDays} dias\n`
          );
        } else {
          processosDescartados++;
        }
      }
    }

    if (processosAnalisados === 0) {
      const mensagem = 'Nenhum processo encontrado com os critérios especificados.';
      console.log(mensagem);
      conteudoArquivo += mensagem;
      fs.writeFileSync('analise_deferimento_concessao.txt', conteudoArquivo);
      return;
    }

    // Calcular média e desvio padrão
    const media = tempoTotal / processosAnalisados;
    
    // Calcular desvio padrão
    const somaDiferencasQuadrado = tempos.reduce((acc, tempo) => {
      const diferenca = tempo - media;
      return acc + (diferenca * diferenca);
    }, 0);
    const desvioPadrao = Math.sqrt(somaDiferencasQuadrado / processosAnalisados);

    // Calcular mediana
    tempos.sort((a, b) => a - b);
    const mediana = processosAnalisados % 2 === 0
      ? (tempos[processosAnalisados/2 - 1] + tempos[processosAnalisados/2]) / 2
      : tempos[Math.floor(processosAnalisados/2)];

    // Adicionar resultados estatísticos ao arquivo
    conteudoArquivo += 'RESULTADOS ESTATÍSTICOS\n';
    conteudoArquivo += '-'.repeat(30) + '\n';
    conteudoArquivo += `Total de processos analisados: ${processosAnalisados}\n`;
    conteudoArquivo += `Processos descartados (com despachos intermediários): ${processosDescartados}\n`;
    conteudoArquivo += `Tempo médio: ${media.toFixed(2)} dias\n`;
    conteudoArquivo += `Desvio padrão: ${desvioPadrao.toFixed(2)} dias\n`;
    conteudoArquivo += `Mediana: ${mediana.toFixed(2)} dias\n`;
    conteudoArquivo += `Tempo mínimo: ${tempoMinimo} dias\n`;
    conteudoArquivo += `Tempo máximo: ${tempoMaximo} dias\n\n`;

    // Adicionar distribuição dos tempos
    conteudoArquivo += 'DISTRIBUIÇÃO DOS TEMPOS\n';
    conteudoArquivo += '-'.repeat(30) + '\n';
    const intervalos = [30, 60, 90, 120, 150, 180, 365];
    
    let temposAnteriores = 0;
    for (const intervalo of intervalos) {
      const quantidade = tempos.filter(t => t > temposAnteriores && t <= intervalo).length;
      const percentual = ((quantidade / processosAnalisados) * 100).toFixed(2);
      conteudoArquivo += `${temposAnteriores + 1} a ${intervalo} dias: ${quantidade} processos (${percentual}%)\n`;
      temposAnteriores = intervalo;
    }
    
    const quantidadeRestante = tempos.filter(t => t > intervalos[intervalos.length - 1]).length;
    const percentualRestante = ((quantidadeRestante / processosAnalisados) * 100).toFixed(2);
    conteudoArquivo += `Mais de ${intervalos[intervalos.length - 1]} dias: ${quantidadeRestante} processos (${percentualRestante}%)\n\n`;

    // Adicionar detalhes dos processos
    conteudoArquivo += 'DETALHES DOS PROCESSOS\n';
    conteudoArquivo += '-'.repeat(30) + '\n';
    detalhesProcessos.forEach((detalhe, index) => {
      conteudoArquivo += detalhe + (index < detalhesProcessos.length - 1 ? '\n' : '');
    });

    // Salvar arquivo
    const nomeArquivo = `analise_deferimento_concessao_${new Date().toISOString().split('T')[0]}.txt`;
    fs.writeFileSync(nomeArquivo, conteudoArquivo);
    
    console.log(`\nAnálise completa! Resultados salvos em: ${nomeArquivo}`);
    console.log(`Total de processos analisados: ${processosAnalisados}`);
    console.log(`Processos descartados (com despachos intermediários): ${processosDescartados}`);
    console.log(`Tempo médio: ${media.toFixed(2)} dias`);

  } catch (error) {
    console.error('Erro ao analisar tempo entre deferimento e concessão:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar a análise
analisarTempoDeferimentoConcessao(); 