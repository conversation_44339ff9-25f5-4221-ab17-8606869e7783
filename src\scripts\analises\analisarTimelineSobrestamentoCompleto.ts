import { Prisma<PERSON>lient, <PERSON>o, <PERSON><PERSON>cho, RPI } from "@prisma/client";
import { writeFileSync } from "fs";
import cliProgress from "cli-progress";

const prisma = new PrismaClient({
  log: ["warn", "error"],
});

const DESPACHO_PUBLICACAO_PEDIDO = "Publicação de pedido de registro para oposição (exame formal concluído)";
const DESPACHOS_SOBRESTAMENTO_NOMES = [
  "Sobrestamento do exame de mérito",
  "Sobrestamento do exame de mérito (em petição)",
  "Sobrestamento da instrução técnica",
];

interface DespachoTimelineInfo {
  nome: string;
  codigo: string | null;
  dataPublicacaoRPI: Date | null;
}

interface ProcessoTimeline {
  numeroProcesso: string;
  dataDeposito: Date | null;
  despachosRelevantes: DespachoTimelineInfo[];
  todosDespachosDoProcesso: DespachoTimelineInfo[]; // Para contexto completo
}

type ProcessoComDespachosOrdenados = Processo & {
  despachos: (Despacho & { rpi: RPI | null })[];
};

async function analisarTimelineSobrestamentoCompleto() {
  console.log("Iniciando análise da timeline de processos com sobrestamento e publicação completa...");
  console.log(`Despacho de publicação obrigatório: ${DESPACHO_PUBLICACAO_PEDIDO}`);
  console.log("Despachos de sobrestamento monitorados:", DESPACHOS_SOBRESTAMENTO_NOMES.join(', '));

  const resultadosTimeline: ProcessoTimeline[] = [];
  let processedCount = 0;
  const TAMANHO_LOTE = 500; // Ajuste conforme necessário, menor para queries mais complexas

  const whereClause = {
    AND: [
      {
        despachos: {
          some: {
            nome: DESPACHO_PUBLICACAO_PEDIDO,
          },
        },
      },
      {
        despachos: {
          some: {
            nome: {
              in: DESPACHOS_SOBRESTAMENTO_NOMES,
            },
          },
        },
      },
    ],
  };

  console.log("Contando o total de processos que atendem aos critérios...");
  const totalProcessosParaAnalisar = await prisma.processo.count({ where: whereClause });

  if (totalProcessosParaAnalisar === 0) {
    console.log("Nenhum processo encontrado com os critérios especificados.");
    return;
  }
  console.log(`Total de processos a serem analisados: ${totalProcessosParaAnalisar}`);

  const barraProgresso = new cliProgress.SingleBar(
    {
      format:
        "Analisando processos |{bar}| {percentage}% | {value}/{total} Processos",
      barCompleteChar: "\u2588",
      barIncompleteChar: "\u2591",
      hideCursor: true,
    },
    cliProgress.Presets.shades_classic
  );

  barraProgresso.start(totalProcessosParaAnalisar, 0);

  while (processedCount < totalProcessosParaAnalisar) {
    const processosLote = await prisma.processo.findMany({
      where: whereClause,
      include: {
        despachos: {
          include: {
            rpi: true,
          },
          orderBy: {
            rpi: {
              dataPublicacao: "asc",
            },
          },
        },
      },
      skip: processedCount,
      take: TAMANHO_LOTE,
    });

    if (processosLote.length === 0) {
      console.warn("Nenhum processo encontrado no lote atual, encerrando.");
      break;
    }

    for (const processo of processosLote as ProcessoComDespachosOrdenados[]) {
      const todosDespachosFormatados: DespachoTimelineInfo[] = processo.despachos.map(d => ({
        nome: d.nome || "NOME_DESPACHO_NULO",
        codigo: d.codigo || null,
        dataPublicacaoRPI: d.rpi?.dataPublicacao || null,
      }));

      // Filtra para incluir apenas os despachos de interesse ou todos para uma timeline completa
      // Por enquanto, vamos pegar todos e identificar os relevantes
      const despachosRelevantes: DespachoTimelineInfo[] = todosDespachosFormatados.filter(d => 
        d.nome === DESPACHO_PUBLICACAO_PEDIDO || DESPACHOS_SOBRESTAMENTO_NOMES.includes(d.nome)
      );

      resultadosTimeline.push({
        numeroProcesso: processo.numero,
        dataDeposito: processo.dataDeposito || null,
        despachosRelevantes: despachosRelevantes, // Poderíamos adicionar mais lógica aqui para calcular tempos, etc.
        todosDespachosDoProcesso: todosDespachosFormatados
      });
    }

    processedCount += processosLote.length;
    barraProgresso.update(processedCount);
  }

  barraProgresso.stop();

  console.log("\n--- Resultados da Análise de Timeline ---");
  console.log(`Total de processos analisados: ${resultadosTimeline.length}`);

  if (resultadosTimeline.length > 0) {
    const dataHora = new Date().toISOString().replace(/[:.]/g, "-");
    const nomeArquivo = `analise-timeline-sobrestamento-completo-${dataHora}.json`;
    try {
      writeFileSync(nomeArquivo, JSON.stringify(resultadosTimeline, null, 2));
      console.log(`\nResultados detalhados da timeline salvos em: ${nomeArquivo}`);
    } catch (error) {
      console.error("\nErro ao salvar arquivo JSON da timeline:", error);
    }
  } else {
    console.log("Nenhuma timeline para salvar.");
  }
}

async function main() {
  try {
    await analisarTimelineSobrestamentoCompleto();
  } catch (error) {
    console.error("\nErro na execução principal:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log("\nConexão com o banco de dados fechada.");
  }
}

if (require.main === module) {
  main();
} 