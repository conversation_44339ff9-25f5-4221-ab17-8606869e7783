import { Router } from 'express';
import { listarProcessosPublicados, rankingProcuradores, listarProcessosComMerito, rankingProcuradoresPorMerito } from '../../controllers/analises/procuradoresController';

const router = Router();

// Rota para listar processos publicados
router.get('/publicacoes', listarProcessosPublicados);

// Rota para ranking de procuradores (por processos depositados + mérito publicado)
router.get('/ranking', rankingProcuradores);

// Rota para ranking baseado exclusivamente em despachos de mérito publicados
router.get('/ranking-merito', rankingProcuradoresPorMerito);

// Rota para listar processos com despachos de mérito
router.get('/merito', listarProcessosComMerito);

export default router; 