import fs from 'fs';
import path from 'path';
import { parse } from 'csv-parse';
import * as csv from 'fast-csv';
import { updateChatGuruCustomField } from '../utils/chatguru.utils';
import dotenv from 'dotenv';

dotenv.config(); // Carrega variáveis de ambiente do .env

interface CsvRow {
  identificador: string;
  telefones: string;
}

interface FalhaAtualizacao {
  telefone: string;
  identificadorEnviado: string;
  mensagemErro: string;
}

const ARQUIVO_CSV = path.join(__dirname, '..', '..', 'output', 'relatorio_identificadores_telefones.csv');
const VARIAVEL_CAMPO_CHATGURU = 'IDC'; //
const LIMITE_REGISTROS_TESTE = 3000;
const ARQUIVO_FALHAS_CSV = path.join(__dirname, '..', '..', 'output', 'relatorio_falhas_atualizacao_chatguru.csv');

async function testarAtualizacaoCustomFields() {

  console.log(`Iniciando teste de atualização de campos personalizados no ChatGuru.`);
  console.log(`Lendo CSV: ${ARQUIVO_CSV}`);
  console.log(`Variável do Campo no ChatGuru: field__${VARIAVEL_CAMPO_CHATGURU}`);
  console.log(`Limite de registros do CSV para teste: ${LIMITE_REGISTROS_TESTE}`);

  const registros: CsvRow[] = [];
  const falhas: FalhaAtualizacao[] = [];

  const parser = fs.createReadStream(ARQUIVO_CSV)
    .pipe(parse({ columns: true, skip_empty_lines: true }));

  let contadorRegistros = 0;
  for await (const row of parser) {
    if (contadorRegistros < LIMITE_REGISTROS_TESTE) {
      registros.push(row as CsvRow);
      contadorRegistros++;
    } else {
      break; // Interrompe após ler o limite de registros
    }
  }

  if (registros.length === 0) {
    console.log('Nenhum registro encontrado no CSV para testar.');
    return;
  }

  console.log(`Processando ${registros.length} registros do CSV...`);

  for (const registro of registros) {
    const { identificador, telefones: telefonesString } = registro;
    if (!identificador || !telefonesString) {
      console.warn(
        `Registro ignorado: identificador ou telefones ausentes. Identificador: ${identificador}, Telefones: ${telefonesString}`
      );
      continue;
    }

    const listaTelefones = telefonesString.split('|').map(tel => tel.trim()).filter(tel => tel);

    if (listaTelefones.length === 0) {
      console.warn(`Identificador ${identificador} não possui telefones associados após o split/trim.`);
      continue;
    }

    console.log(`
--- Processando Identificador: ${identificador} ---`);
    for (const telefone of listaTelefones) {
      console.log(`  📞 Tentando atualizar telefone: ${telefone}`);
      try {
        const resultado = await updateChatGuruCustomField(
          telefone,
          VARIAVEL_CAMPO_CHATGURU,
          identificador
        );
        console.log(`    ✅ Sucesso para ${telefone}:`, resultado);
      } catch (error: any) {
        console.error(`    ❌ Falha para ${telefone}:`, error.message);
        falhas.push({
          telefone,
          identificadorEnviado: identificador,
          mensagemErro: error.message || 'Erro desconhecido',
        });
        // Continuar com o próximo telefone mesmo se um falhar
      }
      // Adicionar um pequeno delay para não sobrecarregar a API (opcional, mas bom para testes em lote)
      await new Promise(resolve => setTimeout(resolve, 500)); // Delay de 0.5 segundos
    }
  }
  console.log('\nTeste de atualização de campos personalizados concluído.');

  if (falhas.length > 0) {
    console.warn(`
🚨 Encontradas ${falhas.length} falhas durante a atualização.`);
    try {
      const pastaOutput = path.dirname(ARQUIVO_FALHAS_CSV);
      if (!fs.existsSync(pastaOutput)) {
        fs.mkdirSync(pastaOutput, { recursive: true });
      }
      const ws = fs.createWriteStream(ARQUIVO_FALHAS_CSV);
      csv.write(falhas, { headers: true })
        .pipe(ws)
        .on('finish', () => {
          console.log(`Relatório de falhas salvo em: ${ARQUIVO_FALHAS_CSV}`);
        });
    } catch (writeError) {
      console.error('Erro ao salvar o relatório de falhas:', writeError);
    }
  } else {
    console.log('🎉 Nenhuma falha registrada durante a atualização!');
  }
}

async function main() {
  await testarAtualizacaoCustomFields();
}

main().catch(async (e) => {
  console.error('Erro inesperado no script de teste:', e);
  process.exit(1);
}); 