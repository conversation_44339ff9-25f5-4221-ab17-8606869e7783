import { PrismaClient } from "@prisma/client";
import * as readline from "readline";

const prisma = new PrismaClient();

interface DuplicadoInfo {
  processoId: string;
  despachoId: string;
  processoNumero: string;
  despachoNome: string;
  registros: {
    id: string;
    clienteId: number;
    statusComunicado: string;
    dataRegistro: Date;
  }[];
}

async function investigarTodosOsTiposDeDuplicados(): Promise<void> {
  console.log("🔬 INVESTIGAÇÃO COMPLETA DE DUPLICADOS\n");
  console.log("=".repeat(80));

  // 1. Contar registros totais
  const totalRegistros = await prisma.despachoElegivel.count();
  console.log(`📊 Total de registros: ${totalRegistros}\n`);

  // 2. Buscar todos os registros
  const todosRegistros = await prisma.despachoElegivel.findMany({
    include: {
      processo: { select: { numero: true } },
      despacho: { select: { nome: true } }
    },
    orderBy: { dataRegistro: 'asc' }
  });

  console.log("🔍 ANÁLISE 1: Duplicados por ID (se houver)\n");
  const idsUnicos = new Set();
  const idsDuplicados = new Set();
  for (const registro of todosRegistros) {
    if (idsUnicos.has(registro.id)) {
      idsDuplicados.add(registro.id);
      console.log(`❌ ID duplicado encontrado: ${registro.id}`);
    } else {
      idsUnicos.add(registro.id);
    }
  }
  console.log(`📊 IDs duplicados: ${idsDuplicados.size}\n`);

  console.log("🔍 ANÁLISE 2: Duplicados por processoId + despachoId\n");
  const gruposProcessoDespacho = new Map<string, typeof todosRegistros>();
  for (const registro of todosRegistros) {
    const chave = `${registro.processoId}_${registro.despachoId}`;
    if (!gruposProcessoDespacho.has(chave)) {
      gruposProcessoDespacho.set(chave, []);
    }
    gruposProcessoDespacho.get(chave)!.push(registro);
  }

  let duplicadosProcessoDespacho = 0;
  for (const [chave, registros] of gruposProcessoDespacho.entries()) {
    if (registros.length > 1) {
      duplicadosProcessoDespacho++;
      console.log(`❌ Duplicado processoId+despachoId: ${chave} (${registros.length} registros)`);
      registros.forEach((r, index) => {
        console.log(`   ${index + 1}. ID: ${r.id}, Cliente: ${r.clienteId}, Data: ${r.dataRegistro.toISOString()}`);
      });
    }
  }
  console.log(`📊 Grupos duplicados por processo+despacho: ${duplicadosProcessoDespacho}\n`);

  console.log("🔍 ANÁLISE 3: Duplicados por clienteId + processoId + despachoId\n");
  const gruposCompletos = new Map<string, typeof todosRegistros>();
  for (const registro of todosRegistros) {
    const chave = `${registro.clienteId}_${registro.processoId}_${registro.despachoId}`;
    if (!gruposCompletos.has(chave)) {
      gruposCompletos.set(chave, []);
    }
    gruposCompletos.get(chave)!.push(registro);
  }

  let duplicadosCompletos = 0;
  for (const [chave, registros] of gruposCompletos.entries()) {
    if (registros.length > 1) {
      duplicadosCompletos++;
      console.log(`❌ Duplicado completo: ${chave} (${registros.length} registros)`);
      registros.forEach((r, index) => {
        console.log(`   ${index + 1}. ID: ${r.id}, Data: ${r.dataRegistro.toISOString()}`);
      });
    }
  }
  console.log(`📊 Grupos duplicados completos: ${duplicadosCompletos}\n`);

  console.log("🔍 ANÁLISE 4: Registros por processo (mostrando apenas processos com múltiplos registros)\n");
  const gruposPorProcesso = new Map<string, typeof todosRegistros>();
  for (const registro of todosRegistros) {
    if (!gruposPorProcesso.has(registro.processoId)) {
      gruposPorProcesso.set(registro.processoId, []);
    }
    gruposPorProcesso.get(registro.processoId)!.push(registro);
  }

  let processosComMultiplosRegistros = 0;
  for (const [processoId, registros] of gruposPorProcesso.entries()) {
    if (registros.length > 1) {
      processosComMultiplosRegistros++;
      console.log(`🔸 Processo ${registros[0].processo.numero} (${processoId}): ${registros.length} registros`);
      
      // Mostrar despachos diferentes
      const despachosUnicos = new Set(registros.map(r => r.despachoId));
      console.log(`   └── ${despachosUnicos.size} despachos únicos`);
      
      if (despachosUnicos.size < registros.length) {
        console.log(`   ⚠️ POSSÍVEL DUPLICAÇÃO: ${registros.length} registros mas apenas ${despachosUnicos.size} despachos únicos`);
      }
    }
  }
  console.log(`📊 Processos com múltiplos registros: ${processosComMultiplosRegistros}\n`);

  console.log("🔍 ANÁLISE 5: Exibindo amostra dos primeiros 10 registros\n");
  todosRegistros.slice(0, 10).forEach((registro, index) => {
    console.log(`${index + 1}. Processo: ${registro.processo.numero}`);
    console.log(`   ID: ${registro.id}`);
    console.log(`   Cliente: ${registro.clienteId}`);
    console.log(`   Despacho: ${registro.despacho.nome || 'N/A'}`);
    console.log(`   Data: ${registro.dataRegistro.toISOString()}`);
    console.log(`   Status: ${registro.statusComunicado}`);
    console.log("");
  });

  return;
}

async function analisarDuplicados(): Promise<DuplicadoInfo[]> {
  console.log("🔍 Analisando duplicados na tabela despachoElegivel...\n");

  // Usar a investigação mais detalhada
  await investigarTodosOsTiposDeDuplicados();

  // NOVO: Buscar duplicados por nome de despacho + processoId + clienteId
  console.log("🔍 ANÁLISE ESPECIAL: Duplicados por nome de despacho + processo + cliente\n");
  
  const todosRegistros = await prisma.despachoElegivel.findMany({
    include: {
      processo: { select: { numero: true } },
      despacho: { select: { nome: true, codigo: true } }
    },
    orderBy: { dataRegistro: 'asc' }
  });

  // Agrupar por processo + cliente + nome do despacho
  const gruposPorNomeDespacho = new Map<string, typeof todosRegistros>();
  for (const registro of todosRegistros) {
    const chave = `${registro.processoId}_${registro.clienteId}_${registro.despacho.nome}`;
    if (!gruposPorNomeDespacho.has(chave)) {
      gruposPorNomeDespacho.set(chave, []);
    }
    gruposPorNomeDespacho.get(chave)!.push(registro);
  }

  console.log("🔎 Verificando duplicados por nome de despacho...\n");
  const duplicadosDetalhados: DuplicadoInfo[] = [];
  
  for (const [chave, registros] of gruposPorNomeDespacho.entries()) {
    if (registros.length > 1) {
      console.log(`❌ DUPLICADO ENCONTRADO: ${registros.length} registros para ${chave}`);
      console.log(`   Processo: ${registros[0].processo.numero}`);
      console.log(`   Cliente: ${registros[0].clienteId}`);
      console.log(`   Despacho: ${registros[0].despacho.nome}`);
      
      registros.forEach((reg, index) => {
        console.log(`   ${index + 1}. ID: ${reg.id}, Código: ${reg.despacho.codigo}, Data: ${reg.dataRegistro.toISOString()}`);
      });
      console.log("");
      
      duplicadosDetalhados.push({
        processoId: registros[0].processoId,
        despachoId: registros[0].despachoId, // Usar o primeiro despacho ID
        processoNumero: registros[0].processo.numero,
        despachoNome: registros[0].despacho.nome || 'N/A',
        registros: registros.map(r => ({
          id: r.id,
          clienteId: r.clienteId,
          statusComunicado: r.statusComunicado,
          dataRegistro: r.dataRegistro
        }))
      });
    }
  }

  console.log(`📊 Total de grupos duplicados por nome de despacho: ${duplicadosDetalhados.length}\n`);
  return duplicadosDetalhados;
}

function exibirDuplicados(duplicados: DuplicadoInfo[]): void {
  console.log("📋 RELATÓRIO DE DUPLICADOS:\n");
  console.log("=".repeat(80));

  duplicados.forEach((dup, index) => {
    console.log(`\n${index + 1}. Processo: ${dup.processoNumero}`);
    console.log(`   Despacho: ${dup.despachoNome}`);
    console.log(`   Total de registros duplicados: ${dup.registros.length}\n`);
    
    dup.registros.forEach((reg, regIndex) => {
      console.log(`   ${regIndex + 1}. ID: ${reg.id}`);
      console.log(`      Cliente ID: ${reg.clienteId}`);
      console.log(`      Status: ${reg.statusComunicado}`);
      console.log(`      Data: ${reg.dataRegistro.toLocaleString('pt-BR')}`);
      console.log("");
    });
    
    console.log("-".repeat(50));
  });

  console.log(`\n📊 RESUMO: ${duplicados.length} grupos de duplicados encontrados`);
  const totalRegistrosDuplicados = duplicados.reduce((acc, dup) => acc + dup.registros.length, 0);
  const totalRegistrosASeremRemovidos = duplicados.reduce((acc, dup) => acc + (dup.registros.length - 1), 0);
  console.log(`📊 Total de registros duplicados: ${totalRegistrosDuplicados}`);
  console.log(`🗑️ Total de registros a serem removidos: ${totalRegistrosASeremRemovidos}\n`);
}

async function removerDuplicados(duplicados: DuplicadoInfo[], estrategia: 'manter_primeiro' | 'manter_ultimo'): Promise<void> {
  console.log(`\n🧹 Iniciando remoção de duplicados (estratégia: ${estrategia})...\n`);
  console.log(`💡 Critério: Para duplicados por nome de despacho, ${estrategia === 'manter_primeiro' ? 'manter o mais antigo' : 'manter o mais recente'}\n`);

  let totalRemovidos = 0;

  for (const dup of duplicados) {
    console.log(`🔧 Processando grupo: ${dup.processoNumero} - ${dup.despachoNome}`);
    console.log(`   Registros encontrados: ${dup.registros.length}`);
    
    const registrosParaRemover = estrategia === 'manter_primeiro' 
      ? dup.registros.slice(1)  // Remove todos exceto o primeiro (mais antigo)
      : dup.registros.slice(0, -1);  // Remove todos exceto o último (mais recente)

    console.log(`   Registros a remover: ${registrosParaRemover.length}`);
    
    if (registrosParaRemover.length > 0) {
      console.log(`   IDs a remover: ${registrosParaRemover.map(r => r.id).join(', ')}`);
      
      try {
        const resultado = await prisma.despachoElegivel.deleteMany({
          where: {
            id: {
              in: registrosParaRemover.map(r => r.id)
            }
          }
        });

        console.log(`   ✅ Removidos ${resultado.count} registros duplicados do processo ${dup.processoNumero}`);
        totalRemovidos += resultado.count;
      } catch (error) {
        console.error(`   ❌ Erro ao remover duplicados do processo ${dup.processoNumero}:`, error);
      }
    } else {
      console.log(`   ℹ️ Nenhum registro para remover`);
    }
    
    console.log("");
  }

  console.log(`\n🎉 Remoção concluída! Total de registros removidos: ${totalRemovidos}`);
}

function criarInterface(): readline.Interface {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
}

async function perguntarUsuario(pergunta: string, rl: readline.Interface): Promise<string> {
  return new Promise((resolve) => {
    rl.question(pergunta, (resposta) => {
      resolve(resposta.trim());
    });
  });
}

async function main(): Promise<void> {
  try {
    console.log("🚀 Iniciando análise de duplicados em despachoElegivel...\n");

    const duplicados = await analisarDuplicados();

    if (duplicados.length === 0) {
      console.log("✅ Nenhum duplicado encontrado! A tabela está limpa.\n");
      return;
    }

    exibirDuplicados(duplicados);

    const rl = criarInterface();

    const desejaRemover = await perguntarUsuario(
      "❓ Deseja remover os duplicados? (s/n): ",
      rl
    );

    if (desejaRemover.toLowerCase() !== 's' && desejaRemover.toLowerCase() !== 'sim') {
      console.log("⏭️ Operação cancelada pelo usuário.");
      rl.close();
      return;
    }

    console.log("\n📋 Estratégias disponíveis:");
    console.log("1. Manter o PRIMEIRO registro (mais antigo) e remover os demais");
    console.log("2. Manter o ÚLTIMO registro (mais recente) e remover os demais");

    const estrategiaEscolhida = await perguntarUsuario(
      "\n❓ Escolha a estratégia (1 ou 2): ",
      rl
    );

    const estrategia = estrategiaEscolhida === '2' ? 'manter_ultimo' : 'manter_primeiro';

    const confirmacao = await perguntarUsuario(
      `\n⚠️ ATENÇÃO: Esta operação irá remover registros da base de dados!\n` +
      `Estratégia escolhida: ${estrategia === 'manter_primeiro' ? 'Manter PRIMEIRO' : 'Manter ÚLTIMO'}\n` +
      `Confirma a remoção? (CONFIRMO/n): `,
      rl
    );

    if (confirmacao !== 'CONFIRMO') {
      console.log("⏭️ Operação cancelada pelo usuário.");
      rl.close();
      return;
    }

    rl.close();

    await removerDuplicados(duplicados, estrategia);

    // Verificação final
    console.log("\n🔍 Executando verificação final...");
    const duplicadosRestantes = await analisarDuplicados();
    
    if (duplicadosRestantes.length === 0) {
      console.log("✅ Verificação concluída: Nenhum duplicado restante!");
    } else {
      console.log(`⚠️ Ainda existem ${duplicadosRestantes.length} grupos de duplicados. Verifique manualmente.`);
    }

  } catch (error) {
    console.error("❌ Erro durante a execução:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Verificar se o script está sendo executado diretamente
if (require.main === module) {
  main().catch(console.error);
}

export { analisarDuplicados, removerDuplicados }; 