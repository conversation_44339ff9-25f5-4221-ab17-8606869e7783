-- CreateTable
CREATE TABLE "PreferenciaComunicacao" (
    "id" TEXT NOT NULL,
    "clienteId" INTEGER NOT NULL,
    "emails" TEXT[],
    "telefone" TEXT,
    "metodosPreferidos" "metodoComunicacao"[],
    "receberComunicados" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PreferenciaComunicacao_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "PreferenciaComunicacao_clienteId_key" ON "PreferenciaComunicacao"("clienteId");

-- CreateIndex
CREATE INDEX "PreferenciaComunicacao_clienteId_idx" ON "PreferenciaComunicacao"("clienteId");

-- AddForeignKey
ALTER TABLE "PreferenciaComunicacao" ADD CONSTRAINT "PreferenciaComunicacao_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE CASCADE ON UPDATE CASCADE;
