# Acompanhamento de Processamento de Protocolos

Esta funcionalidade permite que o usuário acompanhe em tempo real o processamento dos protocolos que foram enviados através do upload.

## Endpoints Disponíveis

### 1. Upload de Protocolo (Modificado)
**POST** `/api/protocolo/upload`

Agora retorna informações adicionais para acompanhamento:

```json
{
  "success": true,
  "message": "Protocolo processado com sucesso!",
  "data": {
    "numeroProcesso": "830123456",
    "elementoNominativo": "MINHA MARCA",
    "nomeArquivo": "830123456-MINHA MARCA.pdf",
    "logoExtraida": true,
    "dataProcessamento": "2024-12-12T10:30:00.000Z",
    "processamentoId": "cm4r5t8k0000abc123def456",
    "urls": {
      "downloadPdf": "http://localhost:3000/api/protocolo/download/830123456",
      "logoImagem": "http://localhost:3000/api/protocolo/logo/830123456"
    },
    "acompanhamento": {
      "statusUrl": "http://localhost:3000/api/fila-protocolo/status/830123456",
      "filaAtivaUrl": "http://localhost:3000/api/fila-protocolo/fila-ativa",
      "recentesUrl": "http://localhost:3000/api/fila-protocolo/recentes"
    }
  }
}
```

### 2. Status de Protocolo Específico
**GET** `/api/fila-protocolo/status/:numeroProcesso`

Retorna o status detalhado de um protocolo:

```json
{
  "success": true,
  "data": {
    "id": "cm4r5t8k0000abc123def456",
    "numeroProcesso": "830123456",
    "elementoNominativo": "MINHA MARCA",
    "status": "PROCESSANDO",
    "progresso": 20,
    "tentativas": 1,
    "ultimoErro": null,
    "proximaTentativa": null,
    "criadoEm": "2024-12-12T10:30:00.000Z",
    "processadoEm": null,
    "cliente": null,
    "processo": null,
    "etapas": {
      "crmEncontrado": false,
      "clienteCriado": false,
      "clienteAtualizado": false,
      "processoVinculado": false,
      "linkGerado": false,
      "chatguruAtualizado": false
    }
  }
}
```

### 3. Protocolos Recentes
**GET** `/api/fila-protocolo/recentes?limit=10`

Lista os protocolos processados nas últimas 24 horas.

### 4. Status da Fila
**GET** `/api/fila-protocolo/fila-ativa`

Verifica se há protocolos sendo processados:

```json
{
  "success": true,
  "data": {
    "filaAtiva": true,
    "protocolosAtivos": 3,
    "proximoProtocolo": {
      "numeroProcesso": "830123456",
      "criadoEm": "2024-12-12T10:30:00.000Z"
    }
  }
}
```

### 5. Estatísticas da Fila
**GET** `/api/fila-protocolo/estatisticas`

Retorna estatísticas gerais da fila de processamento.

## Estados de Processamento

| Status | Progresso | Descrição |
|--------|-----------|-----------|
| `PENDENTE` | 0% | Aguardando processamento |
| `PROCESSANDO` | 20% | Em processamento |
| `FALHA_CRM` | 10% | Erro ao buscar dados no CRM |
| `FALHA_CLIENTE` | 30% | Erro ao criar/atualizar cliente |
| `FALHA_PROCESSO` | 50% | Erro ao vincular processo |
| `FALHA_LINK` | 70% | Erro ao gerar link de acesso |
| `FALHA_CHATGURU` | 90% | Erro na integração ChatGuru |
| `FALHA_GERAL` | 0% | Erro geral não especificado |
| `SUCESSO` | 100% | Processamento concluído com sucesso |

## Fluxo de Uso

1. **Upload**: O usuário faz upload do PDF
2. **Extração**: Sistema extrai metadados e adiciona à fila
3. **Acompanhamento**: Frontend usa polling para verificar status
4. **Conclusão**: Usuário é notificado quando processamento termina

## Exemplo de Implementação Frontend

### JavaScript Vanilla

```javascript
class ProtocolTracker {
    constructor() {
        this.currentProtocol = null;
        this.pollInterval = null;
        this.apiBase = window.location.origin;
    }
    
    async uploadFile(file) {
        const formData = new FormData();
        formData.append('protocolo', file);
        
        const response = await fetch(`${this.apiBase}/api/protocolo/upload`, {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success && result.data.numeroProcesso) {
            this.startTracking(result.data.numeroProcesso);
        }
    }
    
    startTracking(numeroProcesso) {
        this.pollInterval = setInterval(async () => {
            const response = await fetch(
                `${this.apiBase}/api/fila-protocolo/status/${numeroProcesso}`
            );
            const result = await response.json();
            
            if (result.success) {
                this.updateUI(result.data);
                
                // Parar polling se concluído
                if (result.data.status === 'SUCESSO' || result.data.status.startsWith('FALHA_')) {
                    clearInterval(this.pollInterval);
                }
            }
        }, 3000);
    }
    
    updateUI(protocolData) {
        // Atualizar interface com dados do protocolo
        console.log('Status:', protocolData.status);
        console.log('Progresso:', protocolData.progresso + '%');
    }
}
```

### React Hook

```jsx
import { useState, useEffect } from 'react';

const useProtocolTracker = (numeroProcesso) => {
    const [protocolData, setProtocolData] = useState(null);
    const [loading, setLoading] = useState(false);
    
    useEffect(() => {
        if (!numeroProcesso) return;
        
        const checkStatus = async () => {
            try {
                const response = await fetch(`/api/fila-protocolo/status/${numeroProcesso}`);
                const result = await response.json();
                
                if (result.success) {
                    setProtocolData(result.data);
                    
                    // Parar polling se concluído
                    if (result.data.status === 'SUCESSO' || result.data.status.startsWith('FALHA_')) {
                        setLoading(false);
                        return;
                    }
                }
            } catch (error) {
                console.error('Erro ao verificar status:', error);
                setLoading(false);
            }
        };
        
        setLoading(true);
        const interval = setInterval(checkStatus, 3000);
        checkStatus(); // Verificar imediatamente
        
        return () => clearInterval(interval);
    }, [numeroProcesso]);
    
    return { protocolData, loading };
};

// Componente de exemplo
const ProtocolStatus = ({ numeroProcesso }) => {
    const { protocolData, loading } = useProtocolTracker(numeroProcesso);
    
    if (loading) return <div>🔄 Verificando status...</div>;
    if (!protocolData) return <div>❓ Protocolo não encontrado</div>;
    
    return (
        <div>
            <h3>📋 {protocolData.numeroProcesso}</h3>
            <div style={{ width: '100%', backgroundColor: '#e0e0e0', borderRadius: '4px' }}>
                <div 
                    style={{ 
                        width: `${protocolData.progresso}%`, 
                        backgroundColor: '#4caf50', 
                        height: '20px', 
                        borderRadius: '4px',
                        transition: 'width 0.3s ease'
                    }} 
                />
            </div>
            <p>Status: {protocolData.status}</p>
            {protocolData.ultimoErro && (
                <p style={{ color: 'red' }}>Erro: {protocolData.ultimoErro}</p>
            )}
        </div>
    );
};
```

### Vue.js Composition API

```vue
<template>
  <div v-if="protocolData" class="protocol-status">
    <h3>📋 {{ protocolData.numeroProcesso }}</h3>
    <div class="progress-bar">
      <div 
        class="progress-fill" 
        :style="{ width: protocolData.progresso + '%' }"
      ></div>
    </div>
    <p>Status: {{ getStatusText(protocolData.status) }}</p>
    <div v-if="protocolData.ultimoErro" class="error">
      ❌ {{ protocolData.ultimoErro }}
    </div>
    
    <!-- Detalhes das Etapas -->
    <div class="etapas">
      <div class="etapa">
        <span>CRM:</span>
        <span>{{ protocolData.etapas.crmEncontrado ? '✅' : '⏳' }}</span>
      </div>
      <div class="etapa">
        <span>Cliente:</span>
        <span>{{ protocolData.etapas.clienteCriado ? '✅' : '⏳' }}</span>
      </div>
      <div class="etapa">
        <span>Processo:</span>
        <span>{{ protocolData.etapas.processoVinculado ? '✅' : '⏳' }}</span>
      </div>
      <div class="etapa">
        <span>Link:</span>
        <span>{{ protocolData.etapas.linkGerado ? '✅' : '⏳' }}</span>
      </div>
      <div class="etapa">
        <span>ChatGuru:</span>
        <span>{{ protocolData.etapas.chatguruAtualizado ? '✅' : '⏳' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps(['numeroProcesso'])
const protocolData = ref(null)
let intervalId = null

const checkStatus = async () => {
  try {
    const response = await fetch(`/api/fila-protocolo/status/${props.numeroProcesso}`)
    const result = await response.json()
    
    if (result.success) {
      protocolData.value = result.data
      
      // Parar polling se concluído
      if (result.data.status === 'SUCESSO' || result.data.status.startsWith('FALHA_')) {
        clearInterval(intervalId)
      }
    }
  } catch (error) {
    console.error('Erro ao verificar status:', error)
  }
}

const getStatusText = (status) => {
  const texts = {
    'PENDENTE': '⏳ Pendente',
    'PROCESSANDO': '⚙️ Processando',
    'SUCESSO': '✅ Concluído',
    'FALHA_CRM': '❌ Falha no CRM',
    'FALHA_CLIENTE': '❌ Falha no Cliente',
    'FALHA_PROCESSO': '❌ Falha no Processo',
    'FALHA_LINK': '❌ Falha no Link',
    'FALHA_CHATGURU': '❌ Falha no ChatGuru',
    'FALHA_GERAL': '❌ Falha Geral'
  }
  return texts[status] || status
}

onMounted(() => {
  if (props.numeroProcesso) {
    checkStatus()
    intervalId = setInterval(checkStatus, 3000)
  }
})

onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId)
  }
})
</script>

<style scoped>
.protocol-status {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  margin: 10px 0;
}

.progress-fill {
  height: 100%;
  background-color: #4caf50;
  transition: width 0.3s ease;
}

.etapas {
  margin-top: 16px;
}

.etapa {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  border-bottom: 1px solid #eee;
}

.error {
  color: #f44336;
  font-weight: bold;
  margin: 8px 0;
}
</style>
```

## Considerações Técnicas

### Performance
- Use debounce/throttling para evitar requisições excessivas
- Implemente cache local para dados que não mudam frequentemente
- Considere usar WebSocket para aplicações com muitos usuários simultâneos

### UX/UI
- Mostre indicadores visuais claros do progresso
- Forneça feedback imediato ao usuário
- Permita cancelar/reprocessar protocolos com falha
- Implemente notificações quando o processamento for concluído

### Segurança
- Valide permissões do usuário para acessar status de protocolos
- Implemente rate limiting nos endpoints de polling
- Use HTTPS em produção

### Monitoramento
- Adicione logs para rastrear usage patterns
- Monitore performance dos endpoints
- Implemente alertas para falhas recorrentes

## Melhorias Futuras

1. **WebSocket em Tempo Real**: Substituir polling por WebSocket
2. **Notificações Push**: Notificar usuário quando processamento completar
3. **Dashboard Administrativo**: Interface para administradores monitorarem a fila
4. **Retry Automático**: Tentar reprocessar automaticamente falhas temporárias
5. **Métricas Detalhadas**: Analytics sobre tempo de processamento e taxa de sucesso 