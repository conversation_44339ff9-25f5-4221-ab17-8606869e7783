import { Re<PERSON><PERSON><PERSON><PERSON> } from 'express';
import prisma from '../dbClient';
// import { buscarProcessos, atualizarProcesso } from '../services/processoService'; // Removido - Arquivo não encontrado
import { buscarCitacoesEmDespachos } from '../services/citacoesService';

export const consultarProcessoPorNumero: RequestHandler = async (req, res): Promise<any> => {
  try {
    const { numero } = req.params;

    const processo = await prisma.processo.findUnique({
      where: {
        numero
      },
      include: {
        marca: {
          select:{
            ncl: true,
            cfe: true,
            termoPrincipal: true,
            tipoParaPesquisaGap: true,
          }
        },
        titulares: true,
        despachos: {
          include: {
            rpi: {
              select: {
                numero: true,
                dataPublicacao: true,
              },
            },
            protocolos: {
                include: {
                    procurador: true,
                }
            },
          },
        },
        sobrestadores: true,
        procurador: {
            select:{
                nome: true,
            }
        },
        interesse: true,
        cliente: {
          select:{
            id: true,
            identificador: true,
            crmId: true,
            crmLeadIds: true,
            numeroDocumento: true,
            contatos: true,
            autoLoginUrl: true,
          }
        }
      }
    });

    if (!processo) {
      return res.status(404).json({ 
        mensagem: 'Processo não encontrado' 
      });
    }

    res.json(processo);

  } catch (error) {
    console.error('Erro ao consultar processo:', error);
    res.status(500).json({ 
      erro: 'Erro interno do servidor' 
    });
  }
};
export const listarProcessosMonitorados: RequestHandler = async (_req, res): Promise<any> => {
  try {
    const processos = await prisma.processo.findMany({
      where: {
       procurador:{
        nome:{
          contains: "REGISTRE-SE LTDA"
        }
       },
       clienteId:{
        not:null
       }
      },
      include:{
        despachos:{
          include:{
            rpi: true
          }
        },
        cliente: {
          include:{
            contatos:true
          }
        }
      },
      take: 100
    });

    if (!processos.length) {
      return res.status(404).json({
        mensagem: 'Nenhum processo monitorado encontrado'
      });
    }

    res.json({
      mensagem: 'Processos monitorados encontrados com sucesso',
      data: processos 
    });

  } catch (error) {
    console.error('Erro ao listar processos monitorados:', error);
    res.status(500).json({
      erro: 'Erro interno do servidor'
    });
  }
};

export const buscarCitacoesProcesso: RequestHandler = async (req, res) => {
    const { numero } = req.params;

    if (!numero) {
        res.status(400).json({ message: 'Número do processo é obrigatório.' });
        return;
    }

    try {
        const citacoesDespachos = await buscarCitacoesEmDespachos(numero);

        res.status(200).json({ despachos: citacoesDespachos });
        return;
    } catch (error: any) {
        console.error(`Erro ao buscar citações (despachos) para o processo ${numero}:`, error);
        res.status(500).json({ message: error.message || 'Erro interno ao buscar citações.' });
        return;
    }
};
