import { PrismaClient } from "@prisma/client";
import fs from "fs";
import cliProgress from "cli-progress";

const prisma = new PrismaClient();

interface ClienteComProcessos {
  identificador: string | null;
  nome: string | null;
  numeroDocumento: string | null;
  processos: Array<{ numero: string | null }>;
}

async function gerarRelatorioClientesProcessos() {
  console.log("🔍 Buscando todos os clientes e seus processos...");

  try {
    const clientesComProcessos: ClienteComProcessos[] = await prisma.cliente.findMany({
      select: {
        identificador: true,
        nome: true,
        numeroDocumento: true,
        processos: {
          select: {
            numero: true,
          },
        },
      },
    });

    console.log(`📊 Encontrados ${clientesComProcessos.length} clientes.`);

    if (clientesComProcessos.length === 0) {
      console.log("Nenhum cliente encontrado para gerar o relatório.");
      return;
    }

    // Barra de progresso para processamento e geração do CSV
    const progressBar = new cliProgress.SingleBar({
      format: "Gerando CSV |{bar}| {percentage}% || {value}/{total} Clientes",
      barCompleteChar: "\u2588",
      barIncompleteChar: "\u2591",
      hideCursor: true,
    });

    progressBar.start(clientesComProcessos.length, 0);

    // Cabeçalho do CSV
    const cabecalho = [
      'identificador',
      'nome',
      'documento',
      'quantidade_processos',
      'processos'
    ];

    const linhasCsv = clientesComProcessos.map(cliente => {
      const quantidadeProcessos = cliente.processos.length;
      const numerosProcessos = cliente.processos
        .map(p => p.numero)
        .filter(Boolean)
        .join('; '); // Concatena com ponto e vírgula e espaço

      progressBar.increment();

      return [
        `"${cliente.identificador || ''}"`,
        `"${cliente.nome || ''}"`,
        `"${cliente.numeroDocumento || ''}"`,
        quantidadeProcessos,
        `"${numerosProcessos}"`
      ].join(',');
    });

    progressBar.stop();

    // Combinar cabeçalho e dados
    const conteudoCsv = [cabecalho.join(','), ...linhasCsv].join('\n');
    
    // Nome do arquivo com timestamp
    const nomeArquivo = `relatorio-clientes-processos-${new Date().toISOString().slice(0, 10)}-${Date.now()}.csv`;
    
    // Salvar arquivo
    fs.writeFileSync(nomeArquivo, conteudoCsv, 'utf8');
    
    console.log(`\n✅ Arquivo CSV gerado: ${nomeArquivo}`);
    console.log(`📂 Localização: ${process.cwd()}/${nomeArquivo}`);

  } catch (error) {
    console.error("❌ Erro ao gerar relatório:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar a função principal se este arquivo for executado diretamente
if (require.main === module) {
  gerarRelatorioClientesProcessos().catch(error => {
    console.error("Erro ao executar a função principal:", error);
    process.exit(1);
  });
}

export { gerarRelatorioClientesProcessos }; 