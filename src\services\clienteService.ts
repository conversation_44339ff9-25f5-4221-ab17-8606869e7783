import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface IdentificadorClienteResponse {
  identificador?: string | null;
  nomeCliente?: string | null;
  numeroProcesso?: string;
  numeroDocumento?: string | null;
  error?: string;
}

/**
 * Busca o identificador e nome de um cliente associado a um número de processo.
 * @param numeroProcesso O número do processo a ser buscado.
 * @returns Um objeto contendo o identificador e nome do cliente, ou uma mensagem de erro.
 */
export async function getIdentificadorPorNumeroProcesso(numeroProcesso: string): Promise<IdentificadorClienteResponse> {
  if (!numeroProcesso || numeroProcesso.trim() === '') {
    return { error: 'Número do processo não fornecido ou inválido.' };
  }

  try {
    const processoEncontrado = await prisma.processo.findUnique({
      where: { numero: numeroProcesso },
      select: {
        numero: true, // Para retornar na resposta para confirmação
        cliente: {
          select: {
            identificador: true,
            nome: true,
            numeroDocumento: true,
          },
        },
      },
    });

    if (!processoEncontrado) {
      return { error: `Processo com número "${numeroProcesso}" não encontrado.` };
    }

    if (!processoEncontrado.cliente) {
      return { 
        numeroProcesso: processoEncontrado.numero,
        error: `Processo "${numeroProcesso}" não está associado a nenhum cliente.` 
      };
    }

    // Verifica se o identificador é nulo ou o placeholder de inválido
    if (!processoEncontrado.cliente.identificador || processoEncontrado.cliente.identificador === "0000000000") {
      return { 
        numeroProcesso: processoEncontrado.numero,
        nomeCliente: processoEncontrado.cliente.nome,
        numeroDocumento: processoEncontrado.cliente.numeroDocumento,
        error: `Cliente "${processoEncontrado.cliente.nome || 'N/A'}" (associado ao processo "${numeroProcesso}") não possui um identificador válido.` 
      };
    }

    return {
      numeroProcesso: processoEncontrado.numero,
      identificador: processoEncontrado.cliente.identificador,
      nomeCliente: processoEncontrado.cliente.nome,
      numeroDocumento: processoEncontrado.cliente.numeroDocumento,
    };

  } catch (dbError) {
    console.error(`Erro de banco de dados ao buscar identificador para o processo "${numeroProcesso}":`, dbError);
    return { error: 'Erro interno ao processar a solicitação de busca do identificador.' };
  } finally {
    // A desconexão do Prisma geralmente é gerenciada de forma mais global em uma aplicação web,
    // por exemplo, um único cliente Prisma para toda a aplicação ou por request.
    // Omitindo desconexão aqui para evitar problemas em um contexto de servidor real.
    // await prisma.$disconnect(); 
  }
}

// Placeholder para a função que busca cliente por IDC e seus processos
// TODO: Implementar a lógica correta para buscar pelo IDC (últimos 8 dígitos do telefone)
export async function obterClienteProcessosPorIdc(idc: string): Promise<any | null> {
  console.warn(`[obterClienteProcessosPorIdc] Buscando cliente com IDC (parcial): ${idc}. Lógica de busca por final de telefone precisa ser implementada.`);
  
  // Exemplo de busca simples por um campo 'identificador' que pode ou não ser o IDC direto
  // Ajustar conforme a modelagem e lógica correta do IDC.
  const cliente = await prisma.cliente.findFirst({
    where: {
      // Esta condição provavelmente precisará ser ajustada para buscar pelos últimos 8 dígitos de um telefone
      // ou um campo 'idc' específico se existir.
      identificador: idc, // Supondo que idc seja o identificador completo por agora
    },
    include: {
      processos: true, // Inclui os processos associados
    },
  });

  if (!cliente) {
    console.log(`[obterClienteProcessosPorIdc] Cliente não encontrado com IDC: ${idc}`);
    return null;
  }

  console.log(`[obterClienteProcessosPorIdc] Cliente encontrado: ${cliente.nome} com ${cliente.processos.length} processos.`);
  return cliente;
} 