import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
import cliProgress from "cli-progress";
import axios from "axios";
import fs from "fs";

dotenv.config();

const prisma = new PrismaClient();

// Controle de taxa para API do CRM (mesmo do obterLeadsCrm.ts)
class RateLimiter {
  private queue: (() => Promise<void>)[] = [];
  private running = false;
  private requestCount = 0;
  private resetTime = Date.now() + 30000; // 30 segundos
  private maxRequests: number;
  private timeWindow: number;

  constructor(maxRequests = 120, timeWindow = 30000) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindow;
  }

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await fn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      if (!this.running) {
        this.processQueue();
      }
    });
  }

  private async processQueue() {
    if (this.queue.length === 0) {
      this.running = false;
      return;
    }

    this.running = true;

    // Verificar se precisamos resetar o contador
    const now = Date.now();
    if (now >= this.resetTime) {
      this.requestCount = 0;
      this.resetTime = now + this.timeWindow;
    }

    // Verificar se atingimos o limite
    if (this.requestCount >= this.maxRequests) {
      const waitTime = this.resetTime - now;
      console.log(`Limite de requisições atingido. Aguardando ${waitTime / 1000} segundos...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      this.requestCount = 0;
      this.resetTime = Date.now() + this.timeWindow;
    }

    // Executar a próxima função na fila
    const nextFn = this.queue.shift();
    if (nextFn) {
      this.requestCount++;
      await nextFn();
    }

    // Processar o próximo item na fila
    this.processQueue();
  }
}

// Instância do limitador de taxa
const rateLimiter = new RateLimiter(120, 30000);

interface TitularInfo {
  id: string;
  nomeRazaoSocial: string;
  numeroDocumento?: string;
  processoNumero: string;
  procuradorNome: string;
}

interface PersonCRM {
  id: number;
  name: string;
  cpf?: string;
  cnpj?: string;
  email?: string;
  phone?: string;
}

// Função para buscar pessoa no CRM por nome
async function buscarPessoaNoCrm(nome: string, maxRetries = 3): Promise<PersonCRM[]> {
  const url_base = `https://api.pipe.run/v1/persons?`;
  const crmToken = process.env.CRM_TOKEN || "";
  
  let tentativas = 0;
  
  while (tentativas <= maxRetries) {
    try {
      return await rateLimiter.execute(async () => {
        const nomeEncoded = encodeURIComponent(nome.trim());
        const { data } = await axios.get(
          `${url_base}token=${crmToken}&name=${nomeEncoded}`,
          {
            headers: {
              token: crmToken,
            },
          }
        );
        
        if (data.success === false) {
          throw new Error(`Erro na API: ${data.message}`);
        }
        
        return data.data || [];
      });
    } catch (error: any) {
      tentativas++;
      
      // Se for erro 429, 500, 502, 503, 504
      if (error.response?.status === 429 || 
          error.response?.status === 500 ||
          error.response?.status === 502 ||
          error.response?.status === 503 ||
          error.response?.status === 504 ||
          error.message.includes("Too Many Attempts") || 
          error.message.includes("timeout") ||
          error.message.includes("network error")) {
        
        const tempoEspera = Math.min(Math.pow(2, tentativas) * 1000, 60000);
        console.log(`Erro ao buscar pessoa "${nome}" (tentativa ${tentativas}/${maxRetries}): ${error.message}`);
        console.log(`Aguardando ${tempoEspera/1000} segundos antes de tentar novamente...`);
        
        await new Promise(resolve => setTimeout(resolve, tempoEspera));
      } else if (tentativas >= maxRetries) {
        console.error(`Erro ao buscar pessoa "${nome}" após ${maxRetries} tentativas: ${error.message}`);
        return [];
      } else {
        const tempoEspera = 1000 * tentativas;
        console.log(`Erro ao buscar pessoa "${nome}" (tentativa ${tentativas}/${maxRetries}): ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, tempoEspera));
      }
    }
  }
  
  return [];
}

// Função para normalizar e extrair CPF/CNPJ
function normalizarDocumento(documento: string): string {
  return documento.replace(/\D/g, '');
}

// Função para validar CPF básico (11 dígitos)
function isValidCPF(cpf: string): boolean {
  const cpfLimpo = normalizarDocumento(cpf);
  return cpfLimpo.length === 11 && !/^(\d)\1{10}$/.test(cpfLimpo);
}

// Função para validar CNPJ básico (14 dígitos)
function isValidCNPJ(cnpj: string): boolean {
  const cnpjLimpo = normalizarDocumento(cnpj);
  return cnpjLimpo.length === 14 && !/^(\d)\1{13}$/.test(cnpjLimpo);
}

async function enriquecerTitularesComCpf() {
  const startTime = Date.now();
  
  try {
    console.log("🔍 Buscando titulares dos processos REGISTRE-SE LTDA...\n");
    
    // Buscar todos os titulares de processos da REGISTRE-SE LTDA que não têm numeroDocumento
    const titulares = await prisma.titular.findMany({
      where: {
        numeroDocumento: null, // Só processar os que não têm CPF ainda
        processo: {
          procurador: {
            nome: {
              contains: "REGISTRE-SE LTDA",
              mode: "insensitive"
            }
          }
        }
      },
      include: {
        processo: {
          include: {
            procurador: {
              select: {
                nome: true
              }
            }
          }
        }
      },
      orderBy: {
        nomeRazaoSocial: 'asc'
      }
    });
    
    console.log(`✅ Encontrados ${titulares.length} titulares sem CPF/CNPJ`);
    
    if (titulares.length === 0) {
      console.log("🎉 Todos os titulares já possuem documentos cadastrados!");
      return;
    }

    // Estatísticas
    let encontrados = 0;
    let atualizados = 0;
    let naoEncontrados = 0;
    let erros = 0;
    
    const resultados: {
      encontrados: TitularInfo[];
      naoEncontrados: TitularInfo[];
      erros: { titular: TitularInfo; erro: string }[];
    } = {
      encontrados: [],
      naoEncontrados: [],
      erros: []
    };

    // Inicializar a barra de progresso
    const progressBar = new cliProgress.SingleBar({
      format: "Progresso |{bar}| {percentage}% || {value}/{total} Titulares",
      barCompleteChar: "\u2588",
      barIncompleteChar: "\u2591",
      hideCursor: true,
    });

    progressBar.start(titulares.length, 0);

    for (const titular of titulares) {
      const titularInfo: TitularInfo = {
        id: titular.id,
        nomeRazaoSocial: titular.nomeRazaoSocial,
        numeroDocumento: titular.numeroDocumento || undefined,
        processoNumero: titular.processo.numero,
        procuradorNome: titular.processo.procurador?.nome || "N/A"
      };

      try {
        // Buscar no CRM
        const pessoasEncontradas = await buscarPessoaNoCrm(titular.nomeRazaoSocial);
        
        if (pessoasEncontradas.length > 0) {
          // Buscar a pessoa com melhor match de nome e que tenha CPF/CNPJ
          let melhorPessoa: PersonCRM | null = null;
          
          for (const pessoa of pessoasEncontradas) {
            // Priorizar match exato de nome
            const nomesSimilares = pessoa.name.toLowerCase().trim() === titular.nomeRazaoSocial.toLowerCase().trim();
            
            if (pessoa.cpf && isValidCPF(pessoa.cpf)) {
              melhorPessoa = pessoa;
              if (nomesSimilares) break; // Se nome é igual e tem CPF válido, usar este
            } else if (pessoa.cnpj && isValidCNPJ(pessoa.cnpj) && !melhorPessoa) {
              melhorPessoa = pessoa;
              if (nomesSimilares) break; // Se nome é igual e tem CNPJ válido, usar este
            }
          }
          
          if (melhorPessoa) {
            const documento = melhorPessoa.cpf || melhorPessoa.cnpj;
            const documentoLimpo = normalizarDocumento(documento!);
            
            // Atualizar no banco
            await prisma.titular.update({
              where: { id: titular.id },
              data: { numeroDocumento: documentoLimpo }
            });
            
            encontrados++;
            atualizados++;
            titularInfo.numeroDocumento = documentoLimpo;
            resultados.encontrados.push(titularInfo);
            
            // Log a cada 10 encontrados
            if (encontrados % 10 === 0) {
              console.log(`\n✅ ${encontrados} titulares com CPF/CNPJ encontrados até agora...`);
            }
          } else {
            naoEncontrados++;
            resultados.naoEncontrados.push(titularInfo);
          }
        } else {
          naoEncontrados++;
          resultados.naoEncontrados.push(titularInfo);
        }
        
      } catch (error: any) {
        erros++;
        resultados.erros.push({
          titular: titularInfo,
          erro: error.message
        });
        console.error(`\nErro ao processar titular ${titular.nomeRazaoSocial}: ${error.message}`);
      }
      
      progressBar.increment();
    }
    
    progressBar.stop();

    // Gerar relatórios
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const endTime = Date.now();
    const executionTime = (endTime - startTime) / 1000;
    
    // Relatório resumo
    const resumo = [
      '='.repeat(80),
      'ENRIQUECIMENTO DE TITULARES COM CPF/CNPJ VIA CRM',
      '='.repeat(80),
      `Data da execução: ${new Date().toLocaleString('pt-BR')}`,
      `Tempo de execução: ${executionTime.toFixed(2)} segundos`,
      '',
      'RESUMO:',
      `• Total de titulares processados: ${titulares.length}`,
      `• CPF/CNPJ encontrados e atualizados: ${atualizados}`,
      `• Não encontrados no CRM: ${naoEncontrados}`,
      `• Erros durante o processamento: ${erros}`,
      '',
      'TAXA DE SUCESSO:',
      `• ${((atualizados / titulares.length) * 100).toFixed(1)}% dos titulares foram enriquecidos`,
      '',
      '='.repeat(80),
      ''
    ].join('\n');
    
    fs.writeFileSync(`enriquecimento-titulares-resumo-${timestamp}.txt`, resumo, 'utf8');
    
    // CSV com resultados encontrados
    if (resultados.encontrados.length > 0) {
      const csvEncontrados = [
        'titular_id,nome_titular,numero_documento,processo_numero,procurador_nome',
        ...resultados.encontrados.map(r => [
          `"${r.id}"`,
          `"${r.nomeRazaoSocial}"`,
          `"${r.numeroDocumento}"`,
          `"${r.processoNumero}"`,
          `"${r.procuradorNome}"`
        ].join(','))
      ].join('\n');
      
      fs.writeFileSync(`titulares-enriquecidos-${timestamp}.csv`, csvEncontrados, 'utf8');
    }
    
    // CSV com não encontrados
    if (resultados.naoEncontrados.length > 0) {
      const csvNaoEncontrados = [
        'titular_id,nome_titular,processo_numero,procurador_nome',
        ...resultados.naoEncontrados.map(r => [
          `"${r.id}"`,
          `"${r.nomeRazaoSocial}"`,
          `"${r.processoNumero}"`,
          `"${r.procuradorNome}"`
        ].join(','))
      ].join('\n');
      
      fs.writeFileSync(`titulares-nao-encontrados-${timestamp}.csv`, csvNaoEncontrados, 'utf8');
    }
    
    // Relatório final
    console.log("\n🎯 RESULTADOS DO ENRIQUECIMENTO:");
    console.log("=".repeat(60));
    console.log(`📊 Total processados: ${titulares.length}`);
    console.log(`✅ CPF/CNPJ encontrados: ${atualizados} (${((atualizados / titulares.length) * 100).toFixed(1)}%)`);
    console.log(`❌ Não encontrados: ${naoEncontrados} (${((naoEncontrados / titulares.length) * 100).toFixed(1)}%)`);
    console.log(`⚠️ Erros: ${erros}`);
    console.log(`⏱️ Tempo de execução: ${executionTime.toFixed(2)} segundos`);
    
    console.log("\n📁 ARQUIVOS GERADOS:");
    console.log(`   📋 Relatório resumo: enriquecimento-titulares-resumo-${timestamp}.txt`);
    
    if (resultados.encontrados.length > 0) {
      console.log(`   ✅ Titulares enriquecidos: titulares-enriquecidos-${timestamp}.csv`);
    }
    
    if (resultados.naoEncontrados.length > 0) {
      console.log(`   ❌ Não encontrados: titulares-nao-encontrados-${timestamp}.csv`);
    }
    
    console.log(`\n📂 Localização: ${process.cwd()}/`);
    
    return {
      totalProcessados: titulares.length,
      encontrados: atualizados,
      naoEncontrados,
      erros
    };
    
  } catch (error: any) {
    console.error("❌ Erro durante o enriquecimento:", error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Função principal
async function main() {
  console.log("🚀 ENRIQUECIMENTO DE TITULARES COM CPF/CNPJ\n");
  
  try {
    const resultado = await enriquecerTitularesComCpf();
    
    console.log("\n✅ Enriquecimento concluído com sucesso!");
    
    if (resultado && resultado.encontrados && resultado.encontrados > 0) {
      console.log(`\n💡 Próximos passos sugeridos:`);
      console.log(`   • Testar login com CPF dos titulares enriquecidos`);
      console.log(`   • Revisar os ${resultado.naoEncontrados} titulares não encontrados`);
      console.log(`   • Executar novamente para processar novos titulares`);
    }
    
  } catch (error: any) {
    console.error("❌ Erro durante a execução:", error.message);
    process.exit(1);
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  main();
}

export { enriquecerTitularesComCpf }; 