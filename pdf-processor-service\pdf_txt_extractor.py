from pathlib import Path
import fitz
import re
import os
import sys
from collections import OrderedDict
from glob import glob
from multiprocessing import Pool
from io import StringIO

# Flag de depuração (opcional)
DEBUG = False

# Caminhos atualizados
CURRENT_DIR = Path(__file__).parent              # extractors/
PROJECT_ROOT = CURRENT_DIR.parent.parent         # RgsysBaseConstructor/
BASE_DIR = CURRENT_DIR
pdf_dir = BASE_DIR       # Diretório com os  PDFs
# Diretório para os TXT extraídos
OUTPUT_DIR = BASE_DIR

# Dicionário de regex para extração de campos (mantido para referência futura)
chaves_blocos = OrderedDict([
    ("numeroProtocolo", r'^\d{10,12}'),
    ("numeroProcesso", r'^\d{9}'),
    ("processoAfetado",
     r'(?:Processo afetado:|Processo de base:)\s*(\d{9})-([^\n]*)'),
    ("numeroProcessoAfetado", r'Número do processo:'),
    ("titular", r'Titular:\s*(.+)'),
    ("procurador", r'Procurador:\s*(.+)'),
    ("dataDeDeposito", r'Data de depósito:\s*(\d{2}/\d{2}/\d{4})'),
    ("dataDeConcessao", r'Data de concessão:\s*(\d{2}/\d{2}/\d{4})'),
    ("dataDaNovaVigencia", r'Data da nova vigência:\s*(\d{2}/\d{2}/\d{4})'),
    ("elementoNominativo", r'(?:Elemento nominativo:|(?<=\d{9}-))\s*(.+)'),
    ("requerente", r'Requerente:\s*(.+)'),
    ("apresentacao", r'Apresentação:\s*(.+)'),
    ("natureza", r'Natureza:\s*(.+)'),
    ("cfe", r'CFE:\s*(.+)'),
    ("ncl", r'NCL(?:\((\d{1,2})\))?:\s*(\d+)'),
    ("especificacao", r'Especificação:\s*(.+)'),
    ("apostila", r'Apostila:\s*(.+)'),
    ("detalhesDespacho", r'Detalhes do despacho:\s*(.+)'),
    ("sobrestadores", r'Sobrestadores:\s*(.+)'),
    ("peticoes", r'Petições:\s*(.+)'),
    ("peticaoTipo", r'Petição \(tipo\):\s*(.+)'),
    ("peticaoAfetada", r'Petição afetada:\s*(.+)')
])

cabecalho_pattern = re.compile(
    r'\bMARCAS\s*-\s*RPI\s*\d+\s*de\s*\d{2}/\d{2}/\d{4}\s*\d+\b',
    re.IGNORECASE
)

# Atualizado: inicia bloco se a linha começar com 9, 10, 11 ou 12 dígitos
start_block_pattern = re.compile(r'^(?:\d{9}|\d{10}|\d{11}|\d{12})(?:\s.*)?$')


def extract_clean_text_from_pdf(file_path: str) -> str:
    """
    Extrai o texto de todas as páginas do PDF usando PyMuPDF,
    realizando a limpeza de linhas vazias e cabeçalhos em stream.
    """
    pdf_document = fitz.open(file_path)
    buffer = StringIO()
    for page_num in range(pdf_document.page_count):
        page = pdf_document.load_page(page_num)
        page_text = page.get_text()
        for line in page_text.splitlines():
            line_stripped = line.strip()
            if not line_stripped:
                continue
            if re.fullmatch(cabecalho_pattern, line_stripped):
                continue
            buffer.write(line_stripped + "\n")
    return buffer.getvalue()


def segmentar_blocos(text: str) -> list:
    """
    Segmenta o texto em blocos. Inicia um novo bloco sempre que encontra uma linha 
    que corresponda ao padrão de 9, 10, 11 ou 12 dígitos.
    Para evitar separar linhas que são continuação do campo "Titular:" ou dos "Detalhes do despacho:",
    se o bloco atual já contém "titular:" ou "detalhes do despacho:" (ignorando case) e a linha candidata
    consiste exclusivamente de dígitos seguidos de conteúdo entre colchetes, ela é mesclada ao bloco atual.
    """
    lines = text.splitlines()
    blocks = []
    current_block = []
    for line in lines:
        line_stripped = line.strip()
        if start_block_pattern.match(line_stripped):
            current_text = "\n".join(
                current_block).lower() if current_block else ""
            if current_block and (("titular:" in current_text) or ("detalhes do despacho:" in current_text)):
                if re.fullmatch(r'\d+\s*(?:\[.*\)|\(.*\))', line_stripped):
                    current_block.append(line_stripped)
                    continue
            if current_block:
                blocks.append("\n".join(current_block))
                current_block = []
            current_block.append(line_stripped)
        else:
            current_block.append(line_stripped)
    if current_block:
        blocks.append("\n".join(current_block))
    return blocks


def ajustar_detalhes(bloco: str) -> str:
    """
    Se o bloco contém "Detalhes do despacho:" (ignora maiúsculas/minúsculas),
    captura o conteúdo a partir dessa label até que seja detectada uma linha vazia 
    seguida por uma linha que não comece com uma chave importante.
    """
    linhas = bloco.splitlines()
    resultado = []
    in_detalhes = False
    chaves_parada = ["titular:", "procurador:", "processo afetado:", "data de depósito:",
                     "data de concessão:", "apresentação:", "requerente:"]
    for linha in linhas:
        l_lower = linha.lower().strip()
        if "detalhes do despacho:" in l_lower:
            in_detalhes = True
            resultado.append(linha)
            continue
        if in_detalhes:
            if l_lower == "":
                break
            if any(l_lower.startswith(chave) for chave in chaves_parada):
                break
        resultado.append(linha)
    return "\n".join(resultado).strip()


def limpar_bloco(bloco: str) -> str:
    """
    Aplica a limpeza interna do bloco:
      - Ajusta os detalhes do despacho usando ajustar_detalhes.
      - Remove ocorrências de cabeçalhos repetitivos.
    """
    bloco_ajustado = ajustar_detalhes(bloco)
    bloco_limpo = re.sub(cabecalho_pattern, ' ', bloco_ajustado)
    return bloco_limpo.strip()


def ajustar_blocos(blocks: list) -> list:
    """
    Ajusta a segmentação dos blocos, mesclando blocos erroneamente separados.
    
    Heurísticas de mesclagem (se alguma for verdadeira, mescla o próximo bloco ao atual):
      - (Cond1) Se o próximo bloco for curto (<150 caracteres) e iniciar com um número
               (9 a 12 dígitos) seguido de parênteses ou colchetes.
      - (Cond2) Se o bloco atual contém "detalhes do despacho:" ou "sobrestadores:" e o próximo bloco
               inicia com "Processo" seguido de números, ou com números seguidos de parênteses/colchetes,
               ou com números seguidos de "e" ou "referentes".
      - (Cond3) Se o bloco atual termina com vírgula e o próximo bloco indica continuação
               (por exemplo, a primeira linha inicia com um protocolo e contém vírgula).
      - (Cond4) Se o bloco atual contém "detalhes do despacho:" e termina com "e" ou "ou"
               (exceto se o próximo bloco for um despacho novo com data).
      - (Cond5) Se o bloco atual contém "titular:" e o próximo bloco inicia com uma linha exclusiva
               que pareça ser parte do campo (um protocolo com parênteses ou colchetes).
      - (Cond6) Se o bloco atual contém "titular:" e a primeira linha do próximo bloco for exatamente
               um número seguido de colchetes.
      - (Cond7) Se o bloco atual contém chaves como "sobrestador" ou "petição afetada:" e o próximo bloco
               inicia com um protocolo seguido de detalhes entre parênteses e da expressão "Referente ao processo".
      - (Cond8) Se o bloco atual termina com um indicador de continuação, por exemplo, "petição nº".
      - (Cond9) Se a primeira linha do próximo bloco inicia com um protocolo e, após algum texto,
               há uma vírgula seguida de outro protocolo.
      - (Cond10) Se a primeira linha do próximo bloco inicia com um protocolo e contém uma vírgula.
      - (Cond11) Se o bloco atual contém "detalhes do despacho:" e sua última linha termina com ";" 
               e o próximo bloco inicia com um protocolo seguido de hífen.
      - (Cond12) Se o bloco atual contém "detalhes do despacho:" e sua última linha termina com ";" 
               e o próximo bloco inicia com um protocolo seguido de um ponto.
      - (Cond13) Se o bloco atual contém "detalhes do despacho:" e a primeira linha do próximo bloco
               inicia com um protocolo e contém um ponto‑e‑vírgula.
      - (Cond14) Se a primeira linha do próximo bloco consiste em pelo menos dois números (9 a 12 dígitos)
               separados por espaço (opcionalmente terminando com um ponto).
      - (Cond15) Se os últimos 50 caracteres do bloco atual contiverem "referente ao processo"
               (ignorando caixa) e não terminarem com ponto, interrogação ou exclamação, e a primeira
               linha do próximo bloco iniciar com um protocolo.
      - (Cond16) Se o bloco atual contém "sobrestador" ou "detalhes do despacho:" e a primeira linha do próximo bloco
               inicia com um protocolo seguido de parênteses.
    
    Condição de NÃO MESCLAGEM – (Cond17):
      Se o bloco atual contém "processo afetado:" e (na última linha ou trecho de anterioridades) aparece a palavra "petição"
      e a primeira linha do próximo bloco consiste exclusivamente em um número (9 a 12 dígitos) sem delimitadores,
      então trata-se de um novo despacho e NÃO se deve mesclar.
    """
    if not blocks:
        return blocks
    novos_blocos = []
    i = 0
    while i < len(blocks):
        bloco_atual = blocks[i]
        # Avalia a condição de NÃO mesclagem (Cond17) primeiro:
        nao_mesclar = False
        if "processo afetado:" in bloco_atual.lower():
            linhas_atual = [l.strip()
                            for l in bloco_atual.splitlines() if l.strip()]
            if linhas_atual:
                last_line = linhas_atual[-1]
                # Se a última linha contém "petição" (indicando que é uma lista de anterioridades)
                # e não contém nenhum delimitador (vírgula, parênteses, hífen) além dos dígitos,
                # então, se a primeira linha do próximo bloco for apenas dígitos, NÃO mescla.
                if re.search(r'petição', last_line, re.IGNORECASE):
                    # Checa a primeira linha do próximo bloco, se existir
                    if i + 1 < len(blocks):
                        proximo = blocks[i+1].strip()
                        first_line_proximo = proximo.splitlines(
                        )[0].strip() if proximo.splitlines() else ""
                        # Se a linha contém apenas dígitos (9 a 12) sem espaços ou outros caracteres:
                        if re.fullmatch(r'\d{9,12}', first_line_proximo):
                            nao_mesclar = True

        # Se a condição de não mesclagem foi satisfeita, não mesclamos o próximo bloco.
        if nao_mesclar:
            novos_blocos.append(bloco_atual)
            i += 1
            continue

        # Caso contrário, avalia as condições de mesclagem:
        while i + 1 < len(blocks):
            proximo = blocks[i+1].strip()

            # Condição 1:
            cond1 = (len(proximo) < 150 and bool(
                re.match(r'^(?:\d{9}|\d{10}|\d{11}|\d{12})\s*(?:\(|\[).*', proximo)))

            # Condição 2:
            cond2 = False
            if any(campo in bloco_atual.lower() for campo in ["detalhes do despacho:", "sobrestadores:"]):
                padroes_continuacao = [
                    r'^Processo\s+\d+',
                    r'^\d{9,12}\s*(?:\(|\[)',
                    r'^\d{9,12}\s*(?:e|referentes)\b'
                ]
                if any(re.match(padrao, proximo, re.IGNORECASE) for padrao in padroes_continuacao):
                    cond2 = True

            # Condição 3:
            cond3 = False
            if bloco_atual.rstrip().endswith(','):
                first_line_proximo = proximo.splitlines(
                )[0].strip() if proximo.splitlines() else ""
                if re.match(r'^\d{9,12}', first_line_proximo) and (',' in first_line_proximo):
                    cond3 = True
                else:
                    padroes_continuacao_virgula = [
                        r'^\d{9,12}\s*-',
                        r'^\d{9,12}\s*(?:\(|\[)',
                        r'^[A-Za-z]'
                    ]
                    if any(re.match(padrao, proximo) for padrao in padroes_continuacao_virgula):
                        cond3 = True

            # Condição 4:
            cond4 = False
            if "detalhes do despacho:" in bloco_atual.lower():
                if re.search(r'\b(e|ou)\s*$', bloco_atual, re.IGNORECASE):
                    next_lines = proximo.splitlines()
                    if len(next_lines) >= 2:
                        first_line = next_lines[0].strip()
                        second_line = next_lines[1].strip()
                        if re.fullmatch(r'\d{9,12}', first_line) and re.match(r'\d{2}/\d{2}/\d{4}', second_line):
                            cond4 = False
                        else:
                            cond4 = True
                    else:
                        cond4 = True

            # Condição 5:
            cond5 = False
            if "titular:" in bloco_atual.lower():
                first_line_proximo = proximo.splitlines(
                )[0].strip() if proximo.splitlines() else ""
                if re.fullmatch(r'\d+\s*(?:\(.*\)|\[.*\])', first_line_proximo):
                    cond5 = True

            # Condição 6:
            cond6 = False
            if "titular:" in bloco_atual.lower():
                first_line_proximo = proximo.splitlines(
                )[0].strip() if proximo.splitlines() else ""
                if re.fullmatch(r'^\d+\s*\[[^\]]+\]\s*$', first_line_proximo):
                    cond6 = True

            # Condição 7:
            cond7 = False
            if any(chave in bloco_atual.lower() for chave in ["sobrestador", "petição afetada:"]):
                first_line_proximo = proximo.splitlines(
                )[0].strip() if proximo.splitlines() else ""
                if re.fullmatch(r'\d+\s*\(.*\)\s+Referente\s+ao\s+processo.*', first_line_proximo, re.IGNORECASE):
                    cond7 = True

            # Condição 8:
            cond8 = False
            if re.search(r'petição\s*n[ºo]\s*$', bloco_atual, re.IGNORECASE):
                cond8 = True

            # Condição 9:
            cond9 = False
            first_line_proximo = proximo.splitlines(
            )[0].strip() if proximo.splitlines() else ""
            if re.search(r'^\d{9,12}\b.*,\s*\d{9,12}\b', first_line_proximo):
                cond9 = True

            # Condição 10:
            cond10 = False
            first_line_proximo = proximo.splitlines(
            )[0].strip() if proximo.splitlines() else ""
            if re.match(r'^\d{9,12}', first_line_proximo) and (',' in first_line_proximo):
                cond10 = True

            # Condição 11:
            cond11 = False
            if "detalhes do despacho:" in bloco_atual.lower():
                linhas_atual = [l.strip()
                                for l in bloco_atual.splitlines() if l.strip()]
                if linhas_atual:
                    last_line = linhas_atual[-1]
                    if last_line.endswith(';'):
                        first_line_proximo = proximo.splitlines(
                        )[0].strip() if proximo.splitlines() else ""
                        if re.match(r'^\d{9,12}\s*-\s*', first_line_proximo):
                            cond11 = True

            # Condição 12:
            cond12 = False
            if "detalhes do despacho:" in bloco_atual.lower():
                linhas_atual = [l.strip()
                                for l in bloco_atual.splitlines() if l.strip()]
                if linhas_atual:
                    last_line = linhas_atual[-1]
                    if last_line.endswith(';'):
                        first_line_proximo = proximo.splitlines(
                        )[0].strip() if proximo.splitlines() else ""
                        if re.match(r'^\d{9,12}\s*\.\s*', first_line_proximo):
                            cond12 = True

            # Condição 13:
            cond13 = False
            if "detalhes do despacho:" in bloco_atual.lower():
                first_line_proximo = proximo.splitlines(
                )[0].strip() if proximo.splitlines() else ""
                if re.match(r'^\d{9,12}', first_line_proximo) and (';' in first_line_proximo):
                    cond13 = True

            # Condição 14:
            cond14 = False
            if "detalhes do despacho:" in bloco_atual.lower():
                first_line_proximo = proximo.splitlines(
                )[0].strip() if proximo.splitlines() else ""
                if re.fullmatch(r'^(?:\d+\s+){1,}\d+\.?$', first_line_proximo):
                    cond14 = True

            # Condição 15:
            cond15 = False
            if "detalhes do despacho:" in bloco_atual.lower():
                tail = bloco_atual[-50:].lower()
                if "referente ao processo" in tail and not tail.strip().endswith(('.', '?', '!')):
                    first_line_proximo = proximo.splitlines(
                    )[0].strip() if proximo.splitlines() else ""
                    if re.match(r'^\d{9,12}', first_line_proximo):
                        cond15 = True

            # Condição 16:
            cond16 = False
            if any(chave in bloco_atual.lower() for chave in ["sobrestador", "detalhes do despacho:"]):
                first_line_proximo = proximo.splitlines(
                )[0].strip() if proximo.splitlines() else ""
                if re.match(r'^\d{9,12}\s*\(.*\)', first_line_proximo):
                    cond16 = True

            # Condição 17: Se o bloco atual contém "processo afetado:" e na última linha há "petição"
            # e a primeira linha do próximo bloco consiste exclusivamente de um número (9 a 12 dígitos),
            # então trata-se de um novo despacho e NÃO se deve mesclar.
            cond17 = False
            if "processo afetado:" in bloco_atual.lower():
                linhas_atual = [l.strip()
                                for l in bloco_atual.splitlines() if l.strip()]
                if linhas_atual:
                    last_line = linhas_atual[-1]
                    if re.search(r'petição', last_line, re.IGNORECASE):
                        first_line_proximo = proximo.splitlines(
                        )[0].strip() if proximo.splitlines() else ""
                        if re.fullmatch(r'\d{9,12}', first_line_proximo):
                            cond17 = True

            # Se Condição 17 for satisfeita, NÃO mesclamos e interrompemos a mesclagem para este bloco.
            if cond17:
                break

            # Condição 18: Se o próximo bloco for curto (<150 caracteres), iniciar com um protocolo (9-12 dígitos)
            # e contiver um parêntese "(" ou ")" na mesma linha.
            cond18 = False
            if len(proximo) < 150:
                if re.match(r'^\d{9,12}.*[\(\)]', proximo):
                    cond18 = True

            # Condição 19: O bloco atual termina com uma lista de números, e o próximo bloco começa com um número seguido de um ponto "."
            cond19 = False
            if re.search(r'\d{9,12},\s*$', bloco_atual):
                if re.match(r'^\d{9,12}\s*\.', proximo):
                    cond19 = True

            # Se qualquer condição for verdadeira, mesclar os blocos
            if any([cond1, cond2, cond3, cond4, cond5, cond6, cond7, cond8, cond9, cond10,
                    cond11, cond12, cond13, cond14, cond15, cond16, cond18, cond19]):
                bloco_atual += "\n" + proximo
                i += 1
            else:
                break
        novos_blocos.append(bloco_atual)
        i += 1
    return novos_blocos

def salvar_blocos_texto(file_path: str, blocks: list):
    """
    Salva os blocos segmentados em um arquivo de texto.
    Cada arquivo será salvo no diretório OUTPUT_DIR com o mesmo nome base do PDF.
    """
    base_name = os.path.splitext(os.path.basename(file_path))[0]
    out_path = os.path.join(OUTPUT_DIR, base_name + "_blocks.txt")
    with open(out_path, 'w', encoding='utf-8') as f:
        for i, block in enumerate(blocks, start=1):
            bloco_limpo = limpar_bloco(block)
            f.write(f"--- BLOCO {i} ---\n")
            f.write(bloco_limpo + "\n\n")
    print(f"Blocos salvos em: {out_path}")


def process_pdf(pdf_file_path: str):
    """
    Processa um único PDF: extrai o texto (com limpeza em stream), segmenta, ajusta e salva os blocos.
    Retorna o nome do arquivo e o status.
    """
    try:
        # Extração e limpeza do texto em stream (linha a linha)
        text_limpo = extract_clean_text_from_pdf(pdf_file_path)
        if not text_limpo:
            print(f"[{pdf_file_path}] Nenhum texto extraído.")
            return pdf_file_path, False
        blocks = segmentar_blocos(text_limpo)
        blocks_ajustados = ajustar_blocos(blocks)
        salvar_blocos_texto(pdf_file_path, blocks_ajustados)
        print(f"[{pdf_file_path}] Processado com sucesso.")
        return pdf_file_path, True
    except Exception as e:
        print(f"[{pdf_file_path}] ERRO: {str(e)}")
        return pdf_file_path, False


def process_in_parallel(pdf_dir: str, num_processes: int = 5):
    """
    Processa todos os PDFs encontrados em pdf_dir em paralelo, usando num_processes.
    """
    pdf_files = glob(os.path.join(str(pdf_dir), '*.pdf'))
    print(f"\nForam encontrados {len(pdf_files)} arquivos PDF.")

    with Pool(processes=num_processes) as pool:
        results = pool.map(process_pdf, pdf_files)

    total = len(results)
    sucesso = sum(1 for _, status in results if status)
    falhas = total - sucesso
    print(
        f"Total: {total} PDFs; Processados com sucesso: {sucesso}; Falhas: {falhas}")


def main():
    print(f"Diretório de PDFs: {pdf_dir}")
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    process_in_parallel(pdf_dir, num_processes=5)


if __name__ == "__main__":
    main()
