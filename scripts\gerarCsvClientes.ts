// scripts/gerarCsvClientes.ts

import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import cliProgress from 'cli-progress';

const prisma = new PrismaClient();

interface DadosCliente {
  id: number;
  identificador: string;
  numeroDocumento: string;
  nomeDaMarca: string;
  telefone: string;
  email: string;
  nome: string;
  crmId: string;
  quantidadeProcessos: number;
  quantidadeContatos: number;
}

async function buscarDadosClientes(): Promise<DadosCliente[]> {
  try {
    console.log('🔍 Buscando dados dos clientes...\n');

    const clientes = await prisma.cliente.findMany({
      include: {
        contatos: true,
        _count: {
          select: {
            processos: true,
            contatos: true
          }
        }
      },
      orderBy: {
        id: 'asc'
      }
    });

    console.log(`📊 Total de clientes encontrados: ${clientes.length}\n`);

    const progressBar = new cliProgress.SingleBar({
      format: "Processando clientes |{bar}| {percentage}% || {value}/{total}",
      barCompleteChar: "\u2588",
      barIncompleteChar: "\u2591",
      hideCursor: true,
    });

    progressBar.start(clientes.length, 0);

    const dadosProcessados: DadosCliente[] = [];

    for (const cliente of clientes) {
      try {
        // Buscar telefone (preferencialmente principal)
        let telefone = '';
        if (cliente.contatos && cliente.contatos.length > 0) {
          // Procurar por telefone principal primeiro
          const contatoPrincipal = cliente.contatos.find(contato => 
            contato.telefone && 
            contato.telefone !== 'Não informado' && 
            contato.telefone.trim() !== ''
          );
          
          if (contatoPrincipal) {
            telefone = contatoPrincipal.telefone || '';
          }
        }

        // Buscar email
        let email = '';
        if (cliente.contatos && cliente.contatos.length > 0) {
          const contatoComEmail = cliente.contatos.find(contato => 
            contato.email && 
            contato.email !== 'Email não informado' && 
            contato.email !== 'Não informado' &&
            contato.email.trim() !== '' &&
            contato.email.includes('@')
          );
          
          if (contatoComEmail) {
            email = contatoComEmail.email || '';
          }
        }

        const dadosCliente: DadosCliente = {
          id: cliente.id,
          identificador: cliente.identificador || '',
          numeroDocumento: cliente.numeroDocumento || '',
          nomeDaMarca: cliente.nomeDaMarca || '',
          telefone: telefone,
          email: email,
          nome: cliente.nome || '',
          crmId: cliente.crmId?.toString() || '',
          quantidadeProcessos: cliente._count.processos,
          quantidadeContatos: cliente._count.contatos
        };

        dadosProcessados.push(dadosCliente);

      } catch (error: any) {
        console.error(`\n❌ Erro ao processar cliente ID ${cliente.id}: ${error.message}`);
      }

      progressBar.increment();
    }

    progressBar.stop();

    console.log(`\n✅ ${dadosProcessados.length} clientes processados com sucesso!`);
    return dadosProcessados;

  } catch (error) {
    console.error('❌ Erro ao buscar dados dos clientes:', error);
    throw error;
  }
}

function gerarCsv(dados: DadosCliente[], nomeArquivo?: string): string {
  console.log('\n📊 Gerando arquivo CSV...');

  // Definir cabeçalho do CSV
  const cabecalho = [
    'id_cliente',
    'identificador',
    'numero_documento',
    'nome_cliente',
    'nome_da_marca',
    'telefone',
    'email',
    'crm_id',
    'quantidade_processos',
    'quantidade_contatos'
  ];

  // Função auxiliar para escapar valores CSV
  const escaparCsv = (valor: string): string => {
    if (!valor) return '';
    
    // Se contém vírgula, aspas ou quebra de linha, envolver em aspas
    if (valor.includes(',') || valor.includes('"') || valor.includes('\n') || valor.includes('\r')) {
      // Escapar aspas duplicando elas
      return `"${valor.replace(/"/g, '""')}"`;
    }
    
    return valor;
  };

  // Gerar linhas do CSV
  const linhas = dados.map(cliente => [
    cliente.id.toString(),
    escaparCsv(cliente.identificador),
    escaparCsv(cliente.numeroDocumento),
    escaparCsv(cliente.nome),
    escaparCsv(cliente.nomeDaMarca),
    escaparCsv(cliente.telefone),
    escaparCsv(cliente.email),
    escaparCsv(cliente.crmId),
    cliente.quantidadeProcessos.toString(),
    cliente.quantidadeContatos.toString()
  ].join(','));

  // Combinar cabeçalho e dados
  const conteudoCsv = [cabecalho.join(','), ...linhas].join('\n');

  // Definir nome do arquivo
  const arquivo = nomeArquivo || `clientes-dados-completos-${new Date().toISOString().slice(0, 10)}-${Date.now()}.csv`;

  // Escrever arquivo
  fs.writeFileSync(arquivo, conteudoCsv, 'utf8');

  console.log(`✅ Arquivo CSV gerado: ${arquivo}`);
  console.log(`📂 Localização: ${process.cwd()}/${arquivo}`);
  console.log(`📊 Total de registros: ${dados.length}`);

  return arquivo;
}

function gerarEstatisticas(dados: DadosCliente[]): void {
  console.log('\n📈 Estatísticas dos dados:');
  
  const comIdentificador = dados.filter(c => c.identificador && c.identificador !== '').length;
  const comDocumento = dados.filter(c => c.numeroDocumento && c.numeroDocumento !== '').length;
  const comMarca = dados.filter(c => c.nomeDaMarca && c.nomeDaMarca !== '').length;
  const comTelefone = dados.filter(c => c.telefone && c.telefone !== '').length;
  const comEmail = dados.filter(c => c.email && c.email !== '').length;
  const comCrmId = dados.filter(c => c.crmId && c.crmId !== '').length;
  const comProcessos = dados.filter(c => c.quantidadeProcessos > 0).length;

  console.log(`   • Total de clientes: ${dados.length}`);
  console.log(`   • Com identificador: ${comIdentificador} (${((comIdentificador / dados.length) * 100).toFixed(1)}%)`);
  console.log(`   • Com número documento: ${comDocumento} (${((comDocumento / dados.length) * 100).toFixed(1)}%)`);
  console.log(`   • Com nome de marca: ${comMarca} (${((comMarca / dados.length) * 100).toFixed(1)}%)`);
  console.log(`   • Com telefone: ${comTelefone} (${((comTelefone / dados.length) * 100).toFixed(1)}%)`);
  console.log(`   • Com email: ${comEmail} (${((comEmail / dados.length) * 100).toFixed(1)}%)`);
  console.log(`   • Com CRM ID: ${comCrmId} (${((comCrmId / dados.length) * 100).toFixed(1)}%)`);
  console.log(`   • Com processos: ${comProcessos} (${((comProcessos / dados.length) * 100).toFixed(1)}%)`);

  // Estatísticas de múltiplas marcas
  const comMultiplasMarcas = dados.filter(c => c.nomeDaMarca && c.nomeDaMarca.includes(',')).length;
  console.log(`   • Com múltiplas marcas: ${comMultiplasMarcas}`);

  // Top 5 clientes com mais processos
  const topProcessos = dados
    .filter(c => c.quantidadeProcessos > 0)
    .sort((a, b) => b.quantidadeProcessos - a.quantidadeProcessos)
    .slice(0, 5);

  if (topProcessos.length > 0) {
    console.log(`\n🏆 Top 5 clientes com mais processos:`);
    topProcessos.forEach((cliente, index) => {
      console.log(`   ${index + 1}. ID ${cliente.id}: ${cliente.quantidadeProcessos} processos - ${cliente.nome || 'Nome não informado'}`);
    });
  }
}

async function executar() {
  try {
    const args = process.argv.slice(2);
    const nomeArquivoCustom = args.find(arg => arg.startsWith('--arquivo='))?.split('=')[1];
    const apenasEstatisticas = args.includes('--estatisticas') || args.includes('-s');

    console.log('📋 Script para geração de CSV com dados dos clientes\n');

    // Buscar dados dos clientes
    const dadosClientes = await buscarDadosClientes();

    if (apenasEstatisticas) {
      // Mostrar apenas estatísticas
      gerarEstatisticas(dadosClientes);
    } else {
      // Gerar estatísticas
      gerarEstatisticas(dadosClientes);

      // Gerar CSV
      const nomeArquivo = gerarCsv(dadosClientes, nomeArquivoCustom);

      console.log('\n💡 O arquivo CSV foi gerado com as seguintes colunas:');
      console.log('   • id_cliente - ID único do cliente');
      console.log('   • identificador - Identificador baseado em telefone');
      console.log('   • numero_documento - CPF/CNPJ do cliente');
      console.log('   • nome_cliente - Nome do cliente');
      console.log('   • nome_da_marca - Nome(s) da(s) marca(s)');
      console.log('   • telefone - Telefone principal do cliente');
      console.log('   • email - Email do cliente');
      console.log('   • crm_id - ID no sistema CRM');
      console.log('   • quantidade_processos - Número de processos');
      console.log('   • quantidade_contatos - Número de contatos');

      console.log('\n📝 Exemplos de uso:');
      console.log('   npm run gerar-csv-clientes');
      console.log('   npm run gerar-csv-clientes -- --arquivo=meus_clientes.csv');
      console.log('   npm run gerar-csv-clientes -- --estatisticas');
    }

    console.log('\n✅ Execução concluída com sucesso!');
  } catch (error) {
    console.error('\n❌ Erro durante a execução:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executa o script apenas se for chamado diretamente
if (require.main === module) {
  executar();
}

export { buscarDadosClientes, gerarCsv, gerarEstatisticas }; 