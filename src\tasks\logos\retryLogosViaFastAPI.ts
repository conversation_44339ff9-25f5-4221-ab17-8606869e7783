import { PrismaClient } from '@prisma/client';
import path from 'path';
import fs from 'fs';
import { logoExisteLocalmente } from '../utils/download';
import { enviarProcessosParaDownload, verificarFastApiOnline } from '../utils/fastapi';

const prisma = new PrismaClient();

interface RelatorioRetry {
  totalProcessosMonitorados: number;
  logosJaExistentes: number;
  processosSemLogo: string[];
  fastapiDisponivel: boolean;
  processosEnviadosParaFastApi: number;
  fastapiResposta?: any;
  tempoExecucao: string;
  erros: string[];
}

const PROCURADOR_FILTRO = 'REGISTRE-SE LTDA';
const OUTPUT_DIR = path.join(__dirname, '..', '..', '..', 'public', 'logos', 'processos');

async function retryLogosViaFastAPI(): Promise<RelatorioRetry> {
  const inicioExecucao = Date.now();
  const relatorio: RelatorioRetry = {
    totalProcessosMonitorados: 0,
    logosJaExistentes: 0,
    processosSemLogo: [],
    fastapiDisponivel: false,
    processosEnviadosParaFastApi: 0,
    tempoExecucao: '',
    erros: []
  };

  try {
    console.log(`🔄 === RETRY: ENVIO PARA FASTAPI ===`);
    console.log(`🔍 Verificando processos sem logo para envio à FastAPI...`);
    console.log(`📁 Diretório de logos: ${OUTPUT_DIR}`);

    // 1. Verificar se a FastAPI está disponível ANTES de fazer qualquer busca
    console.log(`🔎 Verificando disponibilidade da FastAPI...`);
    relatorio.fastapiDisponivel = await verificarFastApiOnline();
    
    if (!relatorio.fastapiDisponivel) {
      relatorio.erros.push('FastAPI não está disponível em localhost:8000');
      console.error(`❌ FastAPI não está disponível em localhost:8000`);
      console.log(`💡 Inicie a FastAPI em localhost:8000 e execute este script novamente.`);
      return relatorio;
    }
    
    console.log(`✅ FastAPI está online e disponível.`);

    // 2. Buscar todos os processos monitorados
    const processosMonitorados = await prisma.processo.findMany({
      where: {
        procurador: {
          nome: {
            contains: PROCURADOR_FILTRO,
            mode: 'insensitive'
          }
        }
      },
      select: {
        numero: true,
        id: true
      },
      orderBy: {
        numero: 'asc'
      }
    });

    relatorio.totalProcessosMonitorados = processosMonitorados.length;
    console.log(`📊 Total de processos monitorados: ${processosMonitorados.length}`);

    if (processosMonitorados.length === 0) {
      console.log('⚠️ Nenhum processo monitorado encontrado.');
      return relatorio;
    }

    // 3. Identificar processos que ainda não têm logo
    console.log(`\n🔎 Verificando quais logos ainda não existem localmente...`);
    
    for (const processo of processosMonitorados) {
      if (logoExisteLocalmente(processo.numero, OUTPUT_DIR)) {
        relatorio.logosJaExistentes++;
      } else {
        relatorio.processosSemLogo.push(processo.numero);
      }
    }

    console.log(`📈 Situação atual:`);
    console.log(`   ✅ Logos já existem: ${relatorio.logosJaExistentes}`);
    console.log(`   ❌ Processos sem logo: ${relatorio.processosSemLogo.length}`);

    // 4. Enviar processos sem logo para FastAPI
    if (relatorio.processosSemLogo.length === 0) {
      console.log(`\n🎉 Todas as logos já foram baixadas! Nenhuma ação necessária.`);
      return relatorio;
    }

    console.log(`\n🚀 Enviando ${relatorio.processosSemLogo.length} processos para FastAPI...`);
    console.log(`📋 Processos: ${relatorio.processosSemLogo.slice(0, 10).join(', ')}${relatorio.processosSemLogo.length > 10 ? ` (e mais ${relatorio.processosSemLogo.length - 10}...)` : ''}`);
    
    const respostaFastApi = await enviarProcessosParaDownload(relatorio.processosSemLogo);
    relatorio.fastapiResposta = respostaFastApi;
    
    if (respostaFastApi.success) {
      relatorio.processosEnviadosParaFastApi = relatorio.processosSemLogo.length;
      console.log(`✅ ${relatorio.processosEnviadosParaFastApi} processos enviados para FastAPI com sucesso!`);
      console.log(`📝 Resposta da FastAPI: ${respostaFastApi.message}`);
    } else {
      relatorio.erros.push(`Erro ao enviar para FastAPI: ${respostaFastApi.message}`);
      console.error(`❌ Erro ao enviar para FastAPI: ${respostaFastApi.message}`);
    }

  } catch (error: any) {
    relatorio.erros.push(`Erro geral: ${error.message}`);
    console.error(`❌ Erro durante execução:`, error);
  } finally {
    await prisma.$disconnect();
  }

  // Calcular tempo de execução
  const fimExecucao = Date.now();
  const tempoMs = fimExecucao - inicioExecucao;
  relatorio.tempoExecucao = `${(tempoMs / 1000).toFixed(2)}s`;

  return relatorio;
}

async function salvarRelatorioRetry(relatorio: RelatorioRetry): Promise<void> {
  const nomeArquivo = `relatorio_retry_fastapi_${new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-')}.json`;
  const caminhoRelatorio = path.join(__dirname, '..', '..', '..', 'output', nomeArquivo);
  
  // Garantir que a pasta output existe
  const pastaOutput = path.dirname(caminhoRelatorio);
  if (!fs.existsSync(pastaOutput)) {
    fs.mkdirSync(pastaOutput, { recursive: true });
  }

  fs.writeFileSync(caminhoRelatorio, JSON.stringify(relatorio, null, 2));
  console.log(`📄 Relatório de retry salvo em: ${caminhoRelatorio}`);
}

async function main() {
  console.log(`🔄 === RETRY: LOGOS VIA FASTAPI ===`);
  console.log(`⏰ Início: ${new Date().toLocaleString()}`);
  
  const relatorio = await retryLogosViaFastAPI();
  
  console.log(`\n📊 === RELATÓRIO FINAL DO RETRY ===`);
  console.log(`📈 Processos monitorados: ${relatorio.totalProcessosMonitorados}`);
  console.log(`✅ Logos já existentes: ${relatorio.logosJaExistentes}`);
  console.log(`❌ Processos sem logo: ${relatorio.processosSemLogo.length}`);
  console.log(`🚀 Processos enviados para FastAPI: ${relatorio.processosEnviadosParaFastApi}`);
  console.log(`🕐 Tempo de execução: ${relatorio.tempoExecucao}`);
  
  if (relatorio.erros.length > 0) {
    console.log(`❌ Erros encontrados:`);
    relatorio.erros.forEach(erro => console.log(`   - ${erro}`));
  }

  await salvarRelatorioRetry(relatorio);
  
  console.log(`⏰ Fim: ${new Date().toLocaleString()}`);
  console.log(`🏁 Retry concluído.`);
  
  if (relatorio.processosEnviadosParaFastApi > 0) {
    console.log(`\n💡 Dica: Após a FastAPI processar os downloads, você pode executar novamente para verificar se todas as logos foram baixadas.`);
  }
}

// Executar o script
main().catch(console.error); 