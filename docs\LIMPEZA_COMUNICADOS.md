# Scripts de Limpeza de Comunicados

Este documento descreve como usar os scripts para deletar comunicados do banco de dados de forma segura.

## ⚠️ **IMPORTANTE - LEIA ANTES DE USAR**

- **Todas as operações são IRREVERSÍVEIS**
- **Sempre faça backup do banco antes de executar**
- **Use em ambiente de desenvolvimento primeiro**
- **Teste com dados pequenos antes de usar em produção**

## 📋 Scripts Disponíveis

### 1. Limpeza Completa (Deletar Todos)

Remove **TODOS** os comunicados do banco de dados.

```bash
npm run limpar-todos-comunicados
```

**O que é deletado:**
- Tabel<PERSON> `Comunicado` (comunicados gerais)
- Ta<PERSON><PERSON> `ComunicadoPrazoMerito` (comunicados de prazo/mérito)  
- Tabela `HistoricoComunicadoPrazoMerito` (histórico dos comunicados)

**Confirmação necessária:** `SIM, DELETAR TODOS`

### 2. Limpeza Seletiva (Deletar com Filtros)

Remove comunicados com base em filtros específicos.

```bash
npm run limpar-comunicados-seletivo
```

**Filtros disponíveis:**
- **Cliente ID**: Deletar apenas comunicados de um cliente específico
- **Identificador do Cliente**: Usar identificador em vez de ID
- **Data Inicial**: Deletar comunicados a partir de uma data
- **Data Final**: Deletar comunicados até uma data

**Confirmação necessária:** `SIM, DELETAR`

## 🔍 Exemplos de Uso

### Exemplo 1: Deletar comunicados de um cliente específico
```bash
npm run limpar-comunicados-seletivo
# Quando perguntado:
# 1. ID do Cliente: 123
# 2. Identificador do Cliente: [vazio]
# 3. Data inicial: [vazio]  
# 4. Data final: [vazio]
```

### Exemplo 2: Deletar comunicados de um período específico
```bash
npm run limpar-comunicados-seletivo
# Quando perguntado:
# 1. ID do Cliente: [vazio]
# 2. Identificador do Cliente: [vazio]
# 3. Data inicial: 2024-01-01
# 4. Data final: 2024-12-31
```

### Exemplo 3: Deletar comunicados de um cliente por identificador
```bash
npm run limpar-comunicados-seletivo
# Quando perguntado:
# 1. ID do Cliente: [vazio]
# 2. Identificador do Cliente: CLIENT_001
# 3. Data inicial: [vazio]
# 4. Data final: [vazio]
```

## 🛡️ Segurança dos Scripts

### Validações Implementadas:
- ✅ Contagem prévia dos registros a serem deletados
- ✅ Confirmação explícita necessária
- ✅ Transações atômicas (tudo ou nada)
- ✅ Tratamento de erros com rollback automático
- ✅ Verificação pós-operação
- ✅ Logs detalhados de cada etapa

### Ordem de Deleção Segura:
1. `HistoricoComunicadoPrazoMerito` (tabela dependente)
2. `ComunicadoPrazoMerito` (tabela pai)
3. `Comunicado` (tabela independente)

## 🚨 Cenários de Erro Comuns

### 1. "Erro ao criar comunicado"
Pode indicar que o script anterior deixou dados inconsistentes.

**Solução:**
```bash
# Execute primeiro a limpeza completa
npm run limpar-todos-comunicados
```

### 2. "Constraint violation" 
Significa que há dependências não resolvidas.

**Solução:**
- Verifique se não há processos ou clientes dependentes
- Execute os scripts na ordem correta

### 3. "Transaction timeout"
Operação muito grande para ser executada de uma vez.

**Solução:**
- Use o script seletivo com filtros menores
- Execute em lotes menores por data

## 📊 Scripts de Verificação

### Verificar quantos comunicados existem:
```sql
-- No banco de dados diretamente
SELECT 
  'Comunicado' as tabela, COUNT(*) as total FROM "Comunicado"
UNION ALL
SELECT 
  'ComunicadoPrazoMerito' as tabela, COUNT(*) as total FROM "ComunicadoPrazoMerito"  
UNION ALL
SELECT 
  'HistoricoComunicadoPrazoMerito' as tabela, COUNT(*) as total FROM "HistoricoComunicadoPrazoMerito";
```

### Verificar comunicados por cliente:
```sql
-- Comunicados gerais por cliente
SELECT c.identificador, c.nome, COUNT(com.id) as total_comunicados
FROM "Cliente" c
LEFT JOIN "Comunicado" com ON c.id = com."clienteId"
GROUP BY c.id, c.identificador, c.nome
HAVING COUNT(com.id) > 0
ORDER BY total_comunicados DESC;
```

## 🔄 Fluxo Recomendado para Limpeza

1. **Análise**: Verifique quantos comunicados existem
2. **Backup**: Faça backup do banco de dados
3. **Teste**: Execute em ambiente de desenvolvimento primeiro
4. **Seletivo**: Use filtros específicos se possível
5. **Completo**: Só use limpeza completa quando necessário
6. **Verificação**: Confirme que a operação foi bem-sucedida

## 📝 Logs e Monitoramento

Os scripts geram logs detalhados:
- ✅ Contagem inicial de registros
- ✅ Progresso de cada etapa
- ✅ Número de registros deletados por tabela
- ✅ Verificação final
- ❌ Erros com detalhes específicos

## 🆘 Em Caso de Problemas

1. **Pare imediatamente** se algo não parecer certo
2. **Verifique os logs** para entender o que aconteceu
3. **Restaure o backup** se necessário
4. **Execute novamente** com configurações diferentes

---

## 📞 Suporte

Se encontrar problemas ou comportamentos inesperados, documente:
- Comando executado
- Mensagens de erro completas  
- Estado do banco antes da operação
- Logs gerados pelo script 