import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function analisarDuplicadosRpi2843() {
  console.log("🚨 ANÁLISE DE EMERGÊNCIA - RPI 2843\n");
  console.log("=".repeat(80));

  // 1. Encontrar a RPI 2843
  const rpi2843 = await prisma.rPI.findFirst({
    where: { numero: 2843 },
    select: { id: true, numero: true, dataPublicacao: true }
  });

  if (!rpi2843) {
    console.log("❌ RPI 2843 não encontrada!");
    return;
  }

  console.log(`📊 RPI 2843 encontrada:`);
  console.log(`   ID: ${rpi2843.id}`);
  console.log(`   Data: ${rpi2843.dataPublicacao.toISOString()}\n`);

  // 2. Buscar TODOS os despachos da RPI 2843
  const todosDespachos = await prisma.despacho.findMany({
    where: { rpiId: rpi2843.id },
    include: {
      processo: { select: { numero: true } }
    },
    orderBy: [
      { processoId: 'asc' },
      { nome: 'asc' },
      { codigo: 'asc' }
    ]
  });

  console.log(`📊 Total de despachos na RPI 2843: ${todosDespachos.length}\n`);

  // 3. Agrupar por processo + nome do despacho
  const gruposPorProcessoENome = new Map<string, typeof todosDespachos>();
  
  for (const despacho of todosDespachos) {
    const chave = `${despacho.processoId}_${despacho.nome}`;
    if (!gruposPorProcessoENome.has(chave)) {
      gruposPorProcessoENome.set(chave, []);
    }
    gruposPorProcessoENome.get(chave)!.push(despacho);
  }

  // 4. Identificar duplicados
  let duplicadosEncontrados = 0;
  let totalDespachosDuplicados = 0;
  const processosAfetados = new Set<string>();

  console.log("🔍 ANALISANDO DUPLICADOS...\n");

  for (const [chave, despachos] of gruposPorProcessoENome.entries()) {
    if (despachos.length > 1) {
      duplicadosEncontrados++;
      totalDespachosDuplicados += despachos.length;
      processosAfetados.add(despachos[0].processoId);

      console.log(`❌ DUPLICADO ${duplicadosEncontrados}:`);
      console.log(`   Processo: ${despachos[0].processo.numero}`);
      console.log(`   Despacho: ${despachos[0].nome}`);
      console.log(`   Registros: ${despachos.length}`);
      
      despachos.forEach((desp, index) => {
        console.log(`   ${index + 1}. ID: ${desp.id}, Código: ${desp.codigo}`);
      });
      console.log("");

      // Mostrar apenas os primeiros 10 para não sobrecarregar
      if (duplicadosEncontrados >= 10) {
        console.log("⚠️ Mostrando apenas os primeiros 10 duplicados...\n");
        break;
      }
    }
  }

  // 5. Estatísticas finais
  console.log("📊 ESTATÍSTICAS FINAIS:");
  console.log(`   Grupos de duplicados: ${duplicadosEncontrados}`);
  console.log(`   Total de despachos duplicados: ${totalDespachosDuplicados}`);
  console.log(`   Processos afetados: ${processosAfetados.size}`);
  console.log(`   Despachos que seriam removidos: ${totalDespachosDuplicados - duplicadosEncontrados}`);

  // 6. Verificar padrão PENDENTE vs IPAS009
  console.log("\n🔍 VERIFICANDO PADRÃO DE CÓDIGOS:");
  let pendenteCount = 0;
  let ipas009Count = 0;
  let outrosCount = 0;

  for (const despacho of todosDespachos) {
    if (despacho.codigo === 'PENDENTE') pendenteCount++;
    else if (despacho.codigo === 'IPAS009') ipas009Count++;
    else outrosCount++;
  }

  console.log(`   Códigos "PENDENTE": ${pendenteCount}`);
  console.log(`   Códigos "IPAS009": ${ipas009Count}`);
  console.log(`   Outros códigos: ${outrosCount}`);

  // 7. Verificar despachos elegíveis relacionados
  const despachosElegiveis = await prisma.despachoElegivel.count({
    where: {
      despacho: {
        rpiId: rpi2843.id
      }
    }
  });

  console.log(`\n📊 Despachos elegíveis relacionados à RPI 2843: ${despachosElegiveis}`);

  await prisma.$disconnect();
}

analisarDuplicadosRpi2843().catch(console.error); 