import axios from "axios";
import fs from "fs";
import dotenv from "dotenv";
import { Logger } from "../services/logger.service";

dotenv.config();

interface ContactPhone {
  id: number;
  account_id: number;
  company_id: number | null;
  person_id: number | null;
  type: string;
  phone: string;
  is_main: number;
}

interface ApiResponse {
  success: boolean;
  message: string;
  data: ContactPhone[];
  meta: {
    cursor: {
      current: string | null;
      prev: string | null;
      next: string | null;
      count: number | null;
    };
  };
}

interface TelefoneDuplicado {
  ids: number[];
}

// Função para normalizar telefone - detecta números iguais em formatos diferentes
function normalizarTelefoneCompleto(telefone: string): string {
  if (!telefone || typeof telefone !== 'string') return '';
  
  // Remover todos os caracteres não numéricos
  let limpo = telefone.replace(/\D/g, '');
  
  // Se tem menos de 10 dígitos, é inválido
  if (limpo.length < 10) return '';
  
  // Casos possíveis:
  // 1. 55*********** (13 dígitos - código país + DDD + 9 dígitos)
  // 2. ************ (12 dígitos - código país + DDD + 8 dígitos) 
  // 3. *********** (11 dígitos - DDD + 9 dígitos)
  // 4. 1991131354 (10 dígitos - DDD + 8 dígitos)
  
  let codigoPais = '';
  let ddd = '';
  let numero = '';
  
  if (limpo.length === 13 && limpo.startsWith('55')) {
    // Formato: 55***********
    codigoPais = '55';
    ddd = limpo.substring(2, 4);
    numero = limpo.substring(4);
  } else if (limpo.length === 12 && limpo.startsWith('55')) {
    // Formato: ************ - adicionar 9 na frente do número
    codigoPais = '55';
    ddd = limpo.substring(2, 4);
    numero = '9' + limpo.substring(4);
  } else if (limpo.length === 11) {
    // Formato: ***********
    codigoPais = '55';
    ddd = limpo.substring(0, 2);
    numero = limpo.substring(2);
  } else if (limpo.length === 10) {
    // Formato: 1991131354 - adicionar 9 na frente
    codigoPais = '55';
    ddd = limpo.substring(0, 2);
    numero = '9' + limpo.substring(2);
  } else {
    // Outros formatos - tentar extrair os últimos 8-9 dígitos como número
    if (limpo.length >= 10) {
      codigoPais = '55';
      if (limpo.length >= 11) {
        ddd = limpo.substring(limpo.length - 10, limpo.length - 8);
        numero = limpo.substring(limpo.length - 8);
        if (numero.length === 8) {
          numero = '9' + numero;
        }
      } else {
        ddd = limpo.substring(0, 2);
        numero = limpo.substring(2);
        if (numero.length === 8) {
          numero = '9' + numero;
        }
      }
    } else {
      return ''; // Número inválido
    }
  }
  
  // Garantir que o número tenha 9 dígitos
  if (numero.length === 8) {
    numero = '9' + numero;
  } else if (numero.length !== 9) {
    return ''; // Número inválido
  }
  
  // Formato final padronizado: 55*********** (sempre 13 dígitos)
  return codigoPais + ddd + numero;
}

// Função para buscar todos os contatos com paginação
async function buscarTodosContatos(): Promise<ContactPhone[]> {
  Logger.section("📞 BUSCANDO TODOS OS CONTATOS DO CRM");
  
  const crmToken = process.env.CRM_TOKEN;
  if (!crmToken) {
    throw new Error("CRM_TOKEN não configurada no .env");
  }
  
  const baseUrl = "https://api.pipe.run/v1/contactPhones";
  const todosContatos: ContactPhone[] = [];
  let cursor : string | null = "1"; // Primeira página
  let paginaAtual = 1;
  
  Logger.info("🔄 Iniciando busca paginada...");
  
  while (cursor) {
    try {
      Logger.info(`📄 Buscando página ${paginaAtual} (cursor: ${cursor === "1" ? "inicial" : "token"})...`);
      
      const url: string = `${baseUrl}?show=200&cursor=${cursor}`;
      
      const response = await axios.get<ApiResponse>(url, {
        headers: {
          token: crmToken,
        },
        timeout: 30000,
      });
      
      if (!response.data.success) {
        throw new Error(`Erro da API: ${response.data.message}`);
      }
      
      const dadosPagina = response.data.data;
      todosContatos.push(...dadosPagina);
      
      Logger.success(`✅ Página ${paginaAtual}: ${dadosPagina.length} contatos (total: ${todosContatos.length})`);
      
      // Verificar se há próxima página
      cursor = response.data.meta.cursor.next;
      paginaAtual++;
      
      if (cursor) {
        // Pausa entre requisições para não sobrecarregar a API
        Logger.info("⏳ Aguardando 2 segundos antes da próxima requisição...");
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
    } catch (error: any) {
      Logger.error(`❌ Erro na página ${paginaAtual}: ${error.message}`);
      if (error.response) {
        Logger.error(`Status: ${error.response.status}, Data: ${JSON.stringify(error.response.data)}`);
      }
      
      // Se houve erro, parar a busca
      break;
    }
  }
  
  Logger.success(`🎯 Busca concluída: ${todosContatos.length} contatos obtidos em ${paginaAtual - 1} páginas`);
  return todosContatos;
}

// Função para analisar duplicatas
function analisarDuplicatas(contatos: ContactPhone[]): {
  duplicatas: Record<string, TelefoneDuplicado>;
  estatisticas: {
    totalContatos: number;
    contatosComPerson: number;
    telefonesUnicos: number;
    telefonesComDuplicata: number;
    totalPersonsEnvolvidas: number;
  };
} {
  Logger.section("🔍 ANALISANDO DUPLICATAS DE TELEFONE");
  
  // Filtrar apenas contatos com person_id
  const contatosComPerson = contatos.filter(c => c.person_id !== null);
  Logger.info(`📊 ${contatosComPerson.length} contatos com person_id de ${contatos.length} total`);
  
  // Mapa: telefone normalizado -> Set de person_ids
  const mapaTelefonePersons = new Map<string, Set<number>>();
  
  // Processar cada contato
  for (const contato of contatosComPerson) {
    const telefoneNormalizado = normalizarTelefoneCompleto(contato.phone);
    
    if (!telefoneNormalizado) {
      continue; // Ignorar telefones inválidos
    }
    
    if (!mapaTelefonePersons.has(telefoneNormalizado)) {
      mapaTelefonePersons.set(telefoneNormalizado, new Set());
    }
    
    mapaTelefonePersons.get(telefoneNormalizado)!.add(contato.person_id!);
  }
  
  // Separar telefones únicos vs duplicados
  const duplicatas: Record<string, TelefoneDuplicado> = {};
  let telefonesComDuplicata = 0;
  let totalPersonsEnvolvidas = 0;
  
  for (const [telefone, personIds] of mapaTelefonePersons.entries()) {
    const idsArray = Array.from(personIds).sort((a, b) => a - b);
    
    if (idsArray.length > 1) {
      // Duplicata encontrada
      duplicatas[telefone] = { ids: idsArray };
      telefonesComDuplicata++;
      totalPersonsEnvolvidas += idsArray.length;
    }
  }
  
  Logger.info(`📊 Resultados da análise:`);
  Logger.info(`   • ${mapaTelefonePersons.size} telefones únicos analisados`);
  Logger.info(`   • ${telefonesComDuplicata} telefones COM duplicata`);
  Logger.info(`   • ${mapaTelefonePersons.size - telefonesComDuplicata} telefones sem duplicata`);
  Logger.info(`   • ${totalPersonsEnvolvidas} persons envolvidas em duplicatas`);
  
  return {
    duplicatas,
    estatisticas: {
      totalContatos: contatos.length,
      contatosComPerson: contatosComPerson.length,
      telefonesUnicos: mapaTelefonePersons.size,
      telefonesComDuplicata,
      totalPersonsEnvolvidas
    }
  };
}

// Função para salvar resultados
function salvarResultados(
  duplicatas: Record<string, TelefoneDuplicado>,
  estatisticas: any,
  contatosOriginais: ContactPhone[]
): void {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  
  // 1. JSON principal com duplicatas
  const dadosPrincipais = {
    metadados: {
      dataAnalise: new Date().toISOString(),
      estatisticas
    },
    duplicatas
  };
  
  fs.writeFileSync(
    `telefones-duplicados-crm-${timestamp}.json`,
    JSON.stringify(dadosPrincipais, null, 2),
    'utf8'
  );
  
  // 2. JSON com dados completos para debug
  const dadosCompletos = {
    metadados: {
      dataAnalise: new Date().toISOString(),
      estatisticas,
      totalContatosOriginais: contatosOriginais.length
    },
    duplicatas,
    exemploContatos: contatosOriginais.slice(0, 5) // Alguns exemplos para debug
  };
  
  fs.writeFileSync(
    `telefones-duplicados-detalhado-${timestamp}.json`,
    JSON.stringify(dadosCompletos, null, 2),
    'utf8'
  );
  
  // 3. CSV resumido para análise rápida
  if (Object.keys(duplicatas).length > 0) {
    const csvLinhas = [
      'telefone_normalizado,quantidade_persons,person_ids',
      ...Object.entries(duplicatas).map(([telefone, dados]) => 
        `"${telefone}","${dados.ids.length}","${dados.ids.join(';')}"`
      )
    ];
    
    fs.writeFileSync(
      `telefones-duplicados-resumo-${timestamp}.csv`,
      csvLinhas.join('\n'),
      'utf8'
    );
  }
  
  // 4. Relatório em texto
  const relatorio = [
    '='.repeat(80),
    'ANÁLISE DE TELEFONES DUPLICADOS NO CRM',
    '='.repeat(80),
    `Data da análise: ${new Date().toLocaleString('pt-BR')}`,
    '',
    'ESTATÍSTICAS:',
    `📊 Total de contatos obtidos: ${estatisticas.totalContatos.toLocaleString('pt-BR')}`,
    `👥 Contatos com person_id: ${estatisticas.contatosComPerson.toLocaleString('pt-BR')} (${((estatisticas.contatosComPerson / estatisticas.totalContatos) * 100).toFixed(1)}%)`,
    `📞 Telefones únicos: ${estatisticas.telefonesUnicos.toLocaleString('pt-BR')}`,
    `⚠️ Telefones com duplicata: ${estatisticas.telefonesComDuplicata.toLocaleString('pt-BR')} (${((estatisticas.telefonesComDuplicata / estatisticas.telefonesUnicos) * 100).toFixed(1)}%)`,
    `👤 Persons envolvidas em duplicatas: ${estatisticas.totalPersonsEnvolvidas.toLocaleString('pt-BR')}`,
    '',
    'EXEMPLOS DE DUPLICATAS:',
    ...Object.entries(duplicatas).slice(0, 15).map(([telefone, dados]) => 
      `   📞 ${telefone}: ${dados.ids.length} persons [${dados.ids.slice(0, 10).join(', ')}${dados.ids.length > 10 ? '...' : ''}]`
    ),
    ...(Object.keys(duplicatas).length === 0 ? ['   Nenhuma duplicata encontrada! 🎉'] : []),
    '',
    '='.repeat(80)
  ].join('\n');
  
  fs.writeFileSync(`relatorio-telefones-duplicados-${timestamp}.txt`, relatorio, 'utf8');
  
  Logger.success("📁 Arquivos salvos:");
  Logger.success(`   • telefones-duplicados-crm-${timestamp}.json (principal)`);
  Logger.success(`   • telefones-duplicados-detalhado-${timestamp}.json (completo)`);
  Logger.success(`   • relatorio-telefones-duplicados-${timestamp}.txt (relatório)`);
  if (Object.keys(duplicatas).length > 0) {
    Logger.success(`   • telefones-duplicados-resumo-${timestamp}.csv (CSV)`);
  }
}

// Função principal
async function main() {
  try {
    Logger.section("🎯 ANÁLISE DE TELEFONES DUPLICADOS NO CRM");
    
    // 1. Buscar todos os contatos
    const todosContatos = await buscarTodosContatos();
    
    if (todosContatos.length === 0) {
      Logger.warn("❌ Nenhum contato encontrado. Verifique a API key e conectividade.");
      return;
    }
    
    // 2. Analisar duplicatas
    const { duplicatas, estatisticas } = analisarDuplicatas(todosContatos);
    
    // 3. Salvar resultados
    salvarResultados(duplicatas, estatisticas, todosContatos);
    
    // 4. Exibir resumo final
    console.log('\n🎯 RESUMO FINAL:');
    console.log(`   📊 ${estatisticas.totalContatos.toLocaleString('pt-BR')} contatos analisados`);
    console.log(`   📞 ${estatisticas.telefonesUnicos.toLocaleString('pt-BR')} telefones únicos`);
    console.log(`   ⚠️ ${estatisticas.telefonesComDuplicata.toLocaleString('pt-BR')} telefones com duplicata`);
    console.log(`   👥 ${estatisticas.totalPersonsEnvolvidas.toLocaleString('pt-BR')} persons envolvidas em duplicatas`);
    
    if (estatisticas.telefonesComDuplicata > 0) {
      console.log('\n🎯 Veja o arquivo JSON principal para detalhes completos dos conflitos!');
    } else {
      console.log('\n🎉 Nenhuma duplicata encontrada! CRM está limpo.');
    }
    
  } catch (error: any) {
    Logger.error("❌ Erro durante a execução:", error.message);
    if (error.stack) {
      Logger.error("Stack trace:", error.stack);
    }
    process.exit(1);
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  main();
}

export { main as analisarTelefonesDuplicadosCrm }; 