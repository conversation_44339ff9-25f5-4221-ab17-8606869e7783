import { RequestHandler } from 'express';
import { calcularEstimativasMerito, calcularEstimativasMeritoSimplificado } from '../services/meritoService';

export const obterEstimativasMerito: RequestHandler = async (_req, res) => {
  try {
    const estimativas = await calcularEstimativasMerito();
    res.json(estimativas);
  } catch (error) {
    console.error('Erro ao calcular estimativas de mérito:', error);
    res.status(500).json({
      erro: 'Erro interno do servidor',
      mensagem: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
};

// Controller para retornar apenas o objeto estimativaMerito (usando função otimizada)
export const obterEstimativasMeritoSimplificado: RequestHandler = async (_req, res) => {
  try {
    const estimativas = await calcularEstimativasMeritoSimplificado();
    res.json(estimativas);
  } catch (error) {
    console.error('Erro ao calcular estimativas de mérito simplificadas:', error);
    res.status(500).json({
      erro: 'Erro interno do servidor',
      mensagem: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
}; 