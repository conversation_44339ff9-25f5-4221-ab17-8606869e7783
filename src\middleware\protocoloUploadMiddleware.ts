import multer from 'multer';
import path from 'path';
import fs from 'fs';

// Configuração do storage para salvar PDFs de protocolo
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'protocolos');
    
    // Criar diretório se não existir
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Gerar nome único para o arquivo de protocolo
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const fileName = `protocolo-${uniqueSuffix}${path.extname(file.originalname)}`;
    cb(null, fileName);
  }
});

// Configuração base do multer
const multerConfig = {
  storage,
  fileFilter: (req: any, file: any, cb: any) => {
    // Aceitar apenas arquivos PDF
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Apenas arquivos PDF de protocolo são permitidos!'));
    }
  },
  limits: {
    fileSize: 20 * 1024 * 1024 // Limite de 20MB por arquivo
  }
};

// Configuração para upload único (mantém compatibilidade)
export const uploadProtocolo = multer(multerConfig);

// 🆕 Configuração para batch upload (múltiplos arquivos)
export const uploadProtocoloBatch = multer({
  ...multerConfig,
  limits: {
    fileSize: 20 * 1024 * 1024, // 20MB por arquivo
    files: 20 // Máximo 20 arquivos por batch
  }
});

// 🆕 Configurações do batch
export const BATCH_CONFIG = {
  MAX_FILES_PER_BATCH: 20,
  MAX_FILE_SIZE: 20 * 1024 * 1024, // 20MB
  DELAY_BETWEEN_EXTRACTIONS: 1000, // 1s entre extrações
  DELAY_BETWEEN_GROUPS: 2000, // 2s entre grupos
  CHATGURU_BATCH_DELAY: 5000 // 5s entre atualizações ChatGuru
}; 