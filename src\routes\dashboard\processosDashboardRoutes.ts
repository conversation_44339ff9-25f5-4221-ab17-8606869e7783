import { Router } from 'express';
import * as processosDashboardController from '../../controllers/dashboard/processosDashboardController';

const router = Router();

/**
 * @route   GET /api/dashboard/processos/kanban-prazo
 * @desc    Obter processos agrupados por meses restantes até o mérito (visualização kanban)
 * @access  Privado
 */
router.get('/kanban-prazo', processosDashboardController.getKanbanPorPrazo);

export default router; 