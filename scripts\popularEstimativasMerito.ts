// scripts/popularEstimativasMerito.ts

import { popularBancoComPrimeiraEstimativa } from '../src/services/meritoService';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function executar() {
  try {
    await popularBancoComPrimeiraEstimativa();
    console.log('Processamento concluído com sucesso!');
  } catch (error) {
    console.error('Erro ao popular banco:', error);
  } finally {
    await prisma.$disconnect();
  }
}

executar(); 