import { PrismaClient } from '@prisma/client';
import { chatGuruLogger } from '../utils/logger';

// Importa todas as funcionalidades refatoradas
import * as comunicados from './comunicados';

// Re-exporta as funções principais para manter compatibilidade 
// com código existente que depende desse módulo
export const {
  // API do ChatGuru
  atualizarCampoChatGuru,
  executarDialogo,
  
  // Elegibilidade
  verificarElegibilidadePorDespacho,
  verificarElegibilidadeComunicado,
  atualizarElegibilidadePorEtapaCRM,
  getDialogoInfo,
  diasParaMeses,
  
  // Agendamento
  verificarEEnviarComunicados,
  iniciarVerificacaoDiariaComunicados,
  
  // Relatórios
  gerarRelatorioComunicados
} = comunicados;

// Para compatibilidade com código existente
export const prisma = new PrismaClient();

