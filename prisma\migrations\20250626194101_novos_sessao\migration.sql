/*
  Warnings:

  - You are about to drop the `Comunicado` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "Comunicado" DROP CONSTRAINT "Comunicado_clienteId_fkey";

-- AlterTable
ALTER TABLE "ProtocoloDownloadLog" ADD COLUMN     "isValidDownload" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "sessionStatus" TEXT,
ADD COLUMN     "timeFromLogin" INTEGER;

-- AlterTable
ALTER TABLE "SessionLog" ADD COLUMN     "status" TEXT NOT NULL DEFAULT 'pending',
ADD COLUMN     "testingReason" TEXT,
ADD COLUMN     "timeToFirstAction" INTEGER,
ADD COLUMN     "validatedAt" TIMESTAMP(3),
ADD COLUMN     "wasSessionSwitched" BOOLEAN NOT NULL DEFAULT false;

-- DropTable
DROP TABLE "Comunicado";

-- DropEnum
DROP TYPE "StatusComunicado";

-- DropEnum
DROP TYPE "TipoComunicado";

-- CreateIndex
CREATE INDEX "ProtocoloDownloadLog_isValidDownload_idx" ON "ProtocoloDownloadLog"("isValidDownload");

-- CreateIndex
CREATE INDEX "ProtocoloDownloadLog_sessionStatus_idx" ON "ProtocoloDownloadLog"("sessionStatus");

-- CreateIndex
CREATE INDEX "SessionLog_status_idx" ON "SessionLog"("status");

-- CreateIndex
CREATE INDEX "SessionLog_wasSessionSwitched_idx" ON "SessionLog"("wasSessionSwitched");
