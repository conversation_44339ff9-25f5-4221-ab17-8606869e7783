import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Interface para os dados do CRM (baseado no retorno de buscarDadosCrm)
 */
interface DadosCrmParaProcesso {
  id: number;
  customFields?: any[];
  person?: any;
  company?: any;
  [key: string]: any;
}

/**
 * Mapeamento dos IDs dos campos customizados do CRM
 * (baseado no fieldMapping do crmWebhookService)
 */
const fieldMapping = {
  numeroProcesso: { id: 194250 },
  linkProtocolo: { id: 587550 },
  nomeMarca: { id: 213657 },
  tipoMarca: { id: 194466 },
  estadoEspecificacoes: { id: 225853 },
  classes: { id: 194463 },
};

/**
 * Extrai valor de campo customizado dos dados do CRM
 */
function extrairCampoCustomizado(dadosCrm: DadosCrmParaProcesso, fieldId: number): any {
  if (!dadosCrm.customFields || !Array.isArray(dadosCrm.customFields)) {
    return null;
  }

  const campo = dadosCrm.customFields.find((field: any) => field.id === fieldId);
  
  if (!campo) {
    return null;
  }

  return campo.value;
}

/**
 * Analisa e extrai dados necessários para criação do processo
 * Por enquanto apenas faz logs para verificar se conseguimos acessar os dados
 */
export async function analisarDadosParaCriacaoProcesso(
  dadosCrm: DadosCrmParaProcesso,
  numeroProcesso: string
): Promise<void> {

  const numeroProcessoCrm = extrairCampoCustomizado(dadosCrm, fieldMapping.numeroProcesso.id);

  // --- 2. Link do Protocolo ---
  const linkProtocolo = extrairCampoCustomizado(dadosCrm, fieldMapping.linkProtocolo.id);

  // --- 3. Nome da Marca ---
  const nomeMarca = extrairCampoCustomizado(dadosCrm, fieldMapping.nomeMarca.id);

  // --- 4. Apresentação da Marca (Tipo) ---
  const apresentacaoMarcaRaw = extrairCampoCustomizado(dadosCrm, fieldMapping.tipoMarca.id);
  
  let apresentacaoMarca = apresentacaoMarcaRaw;
  if (typeof apresentacaoMarcaRaw === 'string' && apresentacaoMarcaRaw.startsWith('[') && apresentacaoMarcaRaw.endsWith(']')) {
    try {
      const parsed = JSON.parse(apresentacaoMarcaRaw);
      if (Array.isArray(parsed) && parsed.length > 0) {
        apresentacaoMarca = String(parsed[0]);
      }
    } catch (e) {
      apresentacaoMarca = String(apresentacaoMarcaRaw);
    }
  } else {
    apresentacaoMarca = String(apresentacaoMarcaRaw || '');
  }

  // --- 5. Estado das Especificações ---
  const estadoEspecificacoes = extrairCampoCustomizado(dadosCrm, fieldMapping.estadoEspecificacoes.id);

  // --- 6. Classes NCL ---
  const nclClassesRaw = extrairCampoCustomizado(dadosCrm, fieldMapping.classes.id);
  
  let nclCodes: string[] = [];
  if (nclClassesRaw) {
    let potentialArray: any[] = [];
    
    if (typeof nclClassesRaw === 'string') {
      if (nclClassesRaw.startsWith('[') && nclClassesRaw.endsWith(']')) {
        try {
          const parsed = JSON.parse(nclClassesRaw);
          if (Array.isArray(parsed)) {
            potentialArray = parsed;
          }
        } catch (e) {
          potentialArray = nclClassesRaw.split(/[,\s]+/).map(code => code.trim());
        }
      } else {
        potentialArray = nclClassesRaw.split(/[,\s]+/).map(code => code.trim());
      }
    } else if (Array.isArray(nclClassesRaw)) {
      potentialArray = nclClassesRaw;
    }
    
    nclCodes = potentialArray
      .map(item => String(item).trim())
      .filter(code => {
        const isValid = /^[0-9]{1,2}$/.test(code);
        return isValid;
      });
  }

  // --- Validações ---
  // 🔧 CORRIGIDO: Apenas número do processo é obrigatório (marcas figurativas podem não ter nome)
  const dadosCompletos = !!numeroProcesso;
  
  if (!dadosCompletos) {
    console.log(`[PROCESSO] Dados insuficientes para criar processo - falta número do processo`);
  } else {
    console.log(`[PROCESSO] Dados suficientes para criar processo ${numeroProcesso}`);
    if (!nomeMarca || nomeMarca.trim() === '') {
      console.log(`[PROCESSO] Marca sem nome - provavelmente figurativa`);
    }
  }
}

/**
 * Limpa nome da marca removendo valores que indicam marca figurativa
 */
function limparNomeMarcaFigurativa(nomeMarca: string): string | null {
  if (!nomeMarca || typeof nomeMarca !== 'string') {
    return null;
  }
  
  const nomeNormalizado = nomeMarca.trim().toLowerCase();
  
  // Lista de valores que indicam marca figurativa (devem ser tratados como null)
  const valoresFigurativos = [
    'figurativo',
    'figurativa', 
    'figura',
    'figurative',
    'sem nome',
    'marca figurativa',
    'marca figurativ',
    'logotipo',
    'logo',
    'imagem',
    'simbolo',
    'símbolo'
  ];
  
  // Se o nome é algum desses valores, tratar como null
  if (valoresFigurativos.includes(nomeNormalizado)) {
    console.log(`[PROCESSO] Nome da marca "${nomeMarca}" indica figurativa - convertendo para null`);
    return null;
  }
  
  return nomeMarca.trim();
}

/**
 * Determina se uma marca é figurativa baseado na presença de elemento nominativo
 */
function determinarApresentacaoMarca(
  apresentacaoCrm: string | null,
  temElementoNominativo: boolean,
  nomeMarcaLimpo: string | null
): string {
  // 🎯 REGRA PRINCIPAL: Se não tem elemento nominativo, É FIGURATIVA
  if (!temElementoNominativo) {
    console.log(`[PROCESSO] Sem elemento nominativo - marca é FIGURATIVA (sobrescrevendo CRM)`);
    return 'Figurativa';
  }
  
  // Se tem elemento nominativo mas nome foi limpo (era "figurativo"), ainda é figurativa
  if (temElementoNominativo && !nomeMarcaLimpo && apresentacaoCrm) {
    if (apresentacaoCrm.toLowerCase().includes('mista') || 
        apresentacaoCrm.toLowerCase().includes('figura')) {
      console.log(`[PROCESSO] Nome limpo + apresentação CRM indica figurativa`);
      return 'Figurativa';
    }
  }
  
  // Usar apresentação do CRM ou padrão
  return apresentacaoCrm || 'Nominativa';
}

/**
 * Cria processo no banco de dados a partir dos dados do CRM
 */
export async function criarProcessoAPartirDoCrm(
  dadosCrm: DadosCrmParaProcesso,
  numeroProcesso: string,
  dadosTitulares?: any,
  elementoNominativo?: string | null
): Promise<any> {
  
  // --- Extrair e processar dados do CRM ---
  const linkProtocolo = String(extrairCampoCustomizado(dadosCrm, fieldMapping.linkProtocolo.id) || '');
  const nomeMarcaBruto = String(extrairCampoCustomizado(dadosCrm, fieldMapping.nomeMarca.id) || '');

  // 🔧 DEBUG: Log do processo de extração e limpeza
  console.log(`[DEBUG-CRIACAO] CRM BRUTO: "${nomeMarcaBruto}"`);

  // 🔧 LIMPEZA: Remover nomes que indicam marca figurativa
  const nomeMarcaLimpo = limparNomeMarcaFigurativa(nomeMarcaBruto);
  
  console.log(`[DEBUG-CRIACAO] APÓS LIMPEZA: ${nomeMarcaLimpo ? `"${nomeMarcaLimpo}"` : 'null'}`);
  console.log(`[DEBUG-CRIACAO] ELEMENTO NOMINATIVO: ${elementoNominativo ? `"${elementoNominativo}"` : 'null'}`);

  // Processamento de apresentacaoMarca (do CRM)
  let apresentacaoMarcaCrm = extrairCampoCustomizado(dadosCrm, fieldMapping.tipoMarca.id);
  if (typeof apresentacaoMarcaCrm === 'string' && apresentacaoMarcaCrm.startsWith('[') && apresentacaoMarcaCrm.endsWith(']')) {
    try {
      const parsed = JSON.parse(apresentacaoMarcaCrm);
      if (Array.isArray(parsed) && parsed.length > 0) {
        apresentacaoMarcaCrm = String(parsed[0]);
      }
    } catch (e) {
      apresentacaoMarcaCrm = String(apresentacaoMarcaCrm);
    }
  } else if (Array.isArray(apresentacaoMarcaCrm) && apresentacaoMarcaCrm.length > 0) {
    // Para dados do CRM que vêm como array diretamente
    apresentacaoMarcaCrm = String(apresentacaoMarcaCrm[0]);
  } else {
    apresentacaoMarcaCrm = String(apresentacaoMarcaCrm || '');
  }
  
  // 🎯 DETERMINAÇÃO INTELIGENTE: Usar elemento nominativo como fonte da verdade
  const temElementoNominativo = !!(elementoNominativo && elementoNominativo.trim());
  const apresentacaoFinal = determinarApresentacaoMarca(
    apresentacaoMarcaCrm, 
    temElementoNominativo, 
    nomeMarcaLimpo
  );
  
  // Log das decisões tomadas
  if (nomeMarcaBruto !== nomeMarcaLimpo) {
    console.log(`[PROCESSO] Nome da marca limpo: "${nomeMarcaBruto}" → ${nomeMarcaLimpo ? `"${nomeMarcaLimpo}"` : 'null'}`);
  }
  if (apresentacaoMarcaCrm !== apresentacaoFinal) {
    console.log(`[PROCESSO] Apresentação corrigida: "${apresentacaoMarcaCrm}" → "${apresentacaoFinal}"`);
  }

  const estadoEspecificacoes = String(extrairCampoCustomizado(dadosCrm, fieldMapping.estadoEspecificacoes.id) || '');
  
  // Processamento de Classes NCL (IGUAL ao webhook)
  const nclClassesRaw = extrairCampoCustomizado(dadosCrm, fieldMapping.classes.id);
  let nclCodes: string[] = [];

  if (nclClassesRaw) {
    let potentialArray: any[] = [];
    
    if (typeof nclClassesRaw === 'string') {
      if (nclClassesRaw.startsWith('[') && nclClassesRaw.endsWith(']')) {
        try {
          const parsed = JSON.parse(nclClassesRaw);
          if (Array.isArray(parsed)) {
            potentialArray = parsed;
          }
        } catch (e) {
          potentialArray = nclClassesRaw.split(/[,\s]+/).map(code => code.trim());
        }
      } else {
        potentialArray = nclClassesRaw.split(/[,\s]+/).map(code => code.trim());
      }
    } else if (Array.isArray(nclClassesRaw)) {
      potentialArray = nclClassesRaw;
    }
    
    nclCodes = potentialArray.map(item => String(item).trim()).filter(code => /^[0-9]{1,2}$/.test(code));
  }

  // --- Validar dados mínimos ---
  // 🔧 CORRIGIDO: Apenas número do processo é obrigatório (marcas figurativas podem não ter nome)
  if (!numeroProcesso) {
    throw new Error(`Número do processo é obrigatório para criar processo`);
  }

  // --- Criar no banco de dados (transação) ---
  try {
    const tipoMarca = nomeMarcaLimpo ? nomeMarcaLimpo : `Marca ${apresentacaoFinal}`;
    console.log(`[PROCESSO] Criando processo ${numeroProcesso} com marca: ${tipoMarca}`);
    
    const resultado = await prisma.$transaction(async (tx) => {
      // 1. Criar o Processo
      const processo = await tx.processo.create({
        data: {
          numero: numeroProcesso,
          linkProtocolo: linkProtocolo || null,
          monitorado: true,
          dataDeposito: new Date(new Date().toLocaleString("en-US", { timeZone: "America/Sao_Paulo" })),
          // Conectar ao procurador
          procuradorId: "65b3c0c0-aa3b-4d89-85fb-2a144e538800"
        },
        select: { id: true, numero: true }
      });

      // 2. Criar Controle de Scraping
      const agora = new Date();
      await tx.processoScrapingControl.create({
        data: {
          processo: { connect: { id: processo.id } },
          processoNumero: numeroProcesso,
          stage: 'AGUARDANDO_PUBLICACAO', // ScrapingStage.AGUARDANDO_PUBLICACAO
          isActive: true,
          stageEnteredAt: agora,
          nextScrapeDueAt: null, // Scraping não é ativo nesta fase inicial
        }
      });

      // 3. Criar ou Atualizar a Marca
      // 🔧 CORRIGIDO: Verificar se marca já existe e precisa ser atualizada
      const marcaExistente = await tx.marca.findFirst({
        where: { processoId: processo.id }
      });
      
      let marca: any;
      
      if (marcaExistente) {
        // 🔧 LÓGICA DE ATUALIZAÇÃO REFINADA
        const nomeEhFigurativo = marcaExistente.nome && limparNomeMarcaFigurativa(marcaExistente.nome) === null;
        const nomeNeedUpdate = nomeEhFigurativo && marcaExistente.nome !== nomeMarcaLimpo;
        
        // Só atualizar apresentação se:
        // 1. Não tem elemento nominativo (É FIGURATIVA com certeza)
        // 2. OU o nome atual é figurativo (precisa corrigir)
        const apresentacaoNeedUpdate = 
          (!temElementoNominativo && marcaExistente.apresentacao !== 'Figurativa') ||
          (nomeEhFigurativo && marcaExistente.apresentacao !== 'Figurativa');
        
        const precisaAtualizar = nomeNeedUpdate || apresentacaoNeedUpdate;
        
        if (precisaAtualizar) {
          const dadosParaAtualizar: any = {};
          
          if (nomeNeedUpdate) {
            dadosParaAtualizar.nome = nomeMarcaLimpo;
          }
          
          if (apresentacaoNeedUpdate) {
            dadosParaAtualizar.apresentacao = apresentacaoFinal;
          }
          
          marca = await tx.marca.update({
            where: { id: marcaExistente.id },
            data: dadosParaAtualizar,
            select: { id: true, nome: true }
          });
          
          console.log(`[PROCESSO] Marca ATUALIZADA:`);
          if (nomeNeedUpdate) {
            console.log(`   Nome: "${marcaExistente.nome}" → ${nomeMarcaLimpo ? `"${nomeMarcaLimpo}"` : 'null'}`);
          }
          if (apresentacaoNeedUpdate) {
            console.log(`   Apresentação: "${marcaExistente.apresentacao}" → "${apresentacaoFinal}" (${!temElementoNominativo ? 'sem elemento nominativo' : 'nome figurativo'})`);
          }
        } else {
          marca = marcaExistente;
          console.log(`[PROCESSO] Marca já está correta - Nome: ${marcaExistente.nome ? `"${marcaExistente.nome}"` : 'null'}, Apresentação: "${marcaExistente.apresentacao}"`);
        }
      } else {
        // Criar nova marca
        marca = await tx.marca.create({
          data: {
            processo: { connect: { id: processo.id } },
            nome: nomeMarcaLimpo,
            apresentacao: apresentacaoFinal,
          },
          select: { id: true, nome: true }
        });
        
        console.log(`[PROCESSO] Marca CRIADA - Nome: ${nomeMarcaLimpo ? `"${nomeMarcaLimpo}"` : 'null'}, Apresentação: "${apresentacaoFinal}"`);
        console.log(`[DEBUG-CRIACAO] MARCA SALVA NO BANCO - ID: ${marca.id}, Nome: ${marca.nome ? `"${marca.nome}"` : 'null'}`);
      }

      // 4. Adicionar Classes NCL (IGUAL ao webhook - verifica se já existe)
      if (nclCodes.length > 0) {
        for (const code of nclCodes) {
          const existingNcl = await tx.nCL.findFirst({ 
            where: { marcaId: marca.id, codigo: code } 
          });
          
          if (!existingNcl) {
            await tx.nCL.create({ 
              data: { 
                marca: { connect: { id: marca.id } }, 
                codigo: code, 
                estadoDasEspecificacoes: estadoEspecificacoes || null 
              } 
            });
          } 
        }
      }

      // 5. Criar titulares no processo
      await criarTitularesNoProcesso(processo.id, dadosTitulares, tx);

      return processo;
    });

    console.log(`[PROCESSO] Processo ${numeroProcesso} criado com sucesso (ID: ${resultado.id})`);
    return resultado;

  } catch (error) {
    console.error(`[PROCESSO] Erro ao criar processo ${numeroProcesso}:`, error);
    throw error;
  }
}

/**
 * Cria titulares no banco de dados a partir dos dados extraídos
 */
export async function criarTitularesNoProcesso(
  processoId: string, 
  dadosTitulares: any[], 
  tx: any,
  clienteId?: number
): Promise<void> {
  if (!dadosTitulares || dadosTitulares.length === 0) {
    return;
  }

  for (let i = 0; i < dadosTitulares.length; i++) {
    const titular = dadosTitulares[i];
    
    try {
      // Validar dados mínimos
      if (!titular.nome) {
        continue;
      }

      // Criar titular no banco
      await tx.titular.create({
        data: {
          processoId: processoId,
          nomeRazaoSocial: titular.nome,
          numeroDocumento: titular.numeroDocumento || null,
          pais: titular.pais || null,
          uf: titular.uf || null,
          clienteId: clienteId || null, // Conectar ao cliente se fornecido
          // Campos adicionais que não estão no modelo mas podem ser úteis para logs
          // endereco, cidade, cep são salvos apenas nos logs por enquanto
        }
      });
    } catch (error) {
      console.error(`[PROCESSO] Erro ao criar titular ${i + 1} (${titular.nome}):`, error);
      // Não quebrar o fluxo, apenas logar o erro
    }
  }
}

/**
 * Conecta titulares existentes de um processo ao cliente
 */
export async function conectarTitularesAoCliente(
  processoId: string,
  clienteId: number,
  tx: any
): Promise<void> {
  try {
    await tx.titular.updateMany({
      where: {
        processoId: processoId,
        clienteId: null // Apenas titulares que ainda não estão conectados
      },
      data: {
        clienteId: clienteId
      }
    });
  } catch (error) {
    console.error(`[PROCESSO] Erro ao conectar titulares ao cliente:`, error);
    throw error;
  }
}

// Exportar funções auxiliares para uso em outros serviços
export { limparNomeMarcaFigurativa, determinarApresentacaoMarca };