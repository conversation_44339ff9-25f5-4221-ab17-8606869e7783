import { PrismaClient } from '@prisma/client';
import readline from 'readline';

const prisma = new PrismaClient();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function limparBaseDeClientes() {
  console.warn('\x1b[31m%s\x1b[0m', '!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! ATENÇÃO !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');
  console.warn('\x1b[33m%s\x1b[0m', 'Este script irá DELETAR TODOS OS CLIENTES e seus CONTATOS do banco de dados.');
  console.warn('\x1b[33m%s\x1b[0m', 'Os PROCESSOS NÃO serão deletados, mas perderão a associação com os clientes.');
  console.warn('\x1b[31m%s\x1b[0m', 'Esta ação é IRREVERSÍVEL.');
  console.warn('\x1b[33m%s\x1b[0m', 'Faça um BACKUP do seu banco de dados antes de prosseguir, se necessário.');
  console.warn('\x1b[31m%s\x1b[0m', '!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');

  // Forçar um delay para o usuário ler o aviso
  await new Promise(resolve => setTimeout(resolve, 3000)); 

  rl.question('Você tem certeza que deseja continuar? Digite "SIM, EU TENHO CERTEZA" para confirmar: ', async (answer) => {
    if (answer === 'SIM, EU TENHO CERTEZA') {
      console.log('\nIniciando a limpeza dos dados de clientes...');
      try {
        // 1. Deletar todos os ContatoCliente
        // É preciso fazer isso antes de deletar Cliente por causa da relação
        const { count: contatosDeletados } = await prisma.contatoCliente.deleteMany({});
        console.log(`✅ ${contatosDeletados} registros de ContatoCliente deletados.`);

        // 2. Desvincular titulares dos clientes
        await prisma.titular.updateMany({
          where: { clienteId: { not: null } },
          data: { clienteId: null },
        });
        console.log('ℹ️ Titulares desvinculados de clientes.');

        // 3. Deletar logs de sessão e downloads (dependem de cliente)
        const { count: sessoesDeletadas } = await prisma.sessionLog.deleteMany({});
        console.log(`✅ ${sessoesDeletadas} logs de sessão deletados.`);
        
        const { count: downloadsDeletados } = await prisma.protocoloDownloadLog.deleteMany({});
        console.log(`✅ ${downloadsDeletados} logs de download deletados.`);
        
        const { count: urlsDeletadas } = await prisma.shortUrl.deleteMany({});
        console.log(`✅ ${urlsDeletadas} URLs curtas deletadas.`);

        // 4. Deletar todos os Clientes
        // Antes, precisamos desvincular os processos para não dar erro de constraint
        await prisma.processo.updateMany({
          where: { clienteId: { not: null } },
          data: { clienteId: null },
        });
        console.log('ℹ️ Processos desvinculados de clientes.');
        
        const { count: clientesDeletados } = await prisma.cliente.deleteMany({});
        console.log(`✅ ${clientesDeletados} registros de Cliente deletados.`);

        console.log('\nLimpeza concluída com sucesso! 🔥');
        console.log('\n💡 PRÓXIMOS PASSOS RECOMENDADOS:');
        console.log('1. Executar `npm run enriquecer-titulares` - para garantir CPFs nos titulares');
        console.log('2. Criar script robusto para vincular/criar clientes baseado nos titulares');
        console.log('3. Implementar lógica consistente de identificadores (últimos 3 dígitos do CPF)');
        console.log('\nAgora você tem uma base limpa para reconstruir corretamente! ✨');

      } catch (error) {
        console.error('❌ Erro durante a limpeza:', error);
      } finally {
        await prisma.$disconnect();
        rl.close();
      }
    } else {
      console.log('\nOperação cancelada pelo usuário. Nenhum dado foi alterado.');
      await prisma.$disconnect();
      rl.close();
    }
  });
}

// Executar a função principal se este arquivo for executado diretamente
if (require.main === module) {
  limparBaseDeClientes().catch(error => {
    console.error("Erro ao executar a limpeza:", error);
    process.exit(1);
  });
}

export { limparBaseDeClientes }; 