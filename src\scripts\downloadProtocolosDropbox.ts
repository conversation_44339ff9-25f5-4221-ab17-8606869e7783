import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import csv from 'csv-parser';
import { createWriteStream } from 'fs';
import { promisify } from 'util';
import { exec } from 'child_process';
import AdmZip from 'adm-zip';
import * as readline from 'readline';

const execPromise = promisify(exec);

// Configurações
const BATCH_SIZE = 5; // Downloads simultâneos
const DOWNLOAD_TIMEOUT = 120000; // 2 minutos por arquivo
const MAX_RETRIES = 3;
const DELAY_BETWEEN_DOWNLOADS = 1000; // 1 segundo entre downloads

// Estrutura de diretórios
const BASE_DIR = path.join(process.cwd(), 'temp');
const DOWNLOADS_DIR = path.join(BASE_DIR, 'downloads');
const EXTRACTED_DIR = path.join(BASE_DIR, 'extracted');
const FINAL_PDFS_DIR = path.join(BASE_DIR, 'final_pdfs');

interface LinkInfo {
  url: string;
  tipo: 'pdf' | 'folder' | 'unknown';
  filename: string;
  index: number;
}

interface DownloadResult {
  url: string;
  success: boolean;
  filename?: string;
  filepath?: string;
  error?: string;
  tipo: string;
}

interface ProcessStats {
  total: number;
  processed: number;
  downloads_success: number;
  downloads_failed: number;
  pdfs_extraidos: number;
  zips_processados: number;
  errors: string[];
}

interface MenuOption {
  key: string;
  label: string;
  description: string;
}

type FilterType = 'ALL' | 'PDF_ONLY' | 'FOLDER_ONLY' | 'SKIP_DOWNLOADED';

/**
 * Exibe menu interativo para escolher tipo de processamento
 */
async function exibirMenu(): Promise<FilterType> {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const opcoes: MenuOption[] = [
    {
      key: '1',
      label: 'TODOS os links',
      description: 'Processar PDFs e Folders/ZIPs (modo completo)'
    },
    {
      key: '2', 
      label: 'APENAS PDFs',
      description: 'Processar somente links de PDFs diretos'
    },
    {
      key: '3',
      label: 'APENAS Folders/ZIPs',
      description: 'Processar somente links de pastas/ZIPs'
    },
    {
      key: '4',
      label: 'Continuar (skip baixados)',
      description: 'Pular arquivos já baixados e continuar'
    }
  ];

  console.log('\n' + '='.repeat(60));
  console.log('🎯 MENU DE PROCESSAMENTO - PROTOCOLOS DROPBOX');
  console.log('='.repeat(60));
  
  opcoes.forEach(opcao => {
    console.log(`${opcao.key}. ${opcao.label}`);
    console.log(`   ${opcao.description}`);
    console.log('');
  });

  return new Promise((resolve) => {
    const pergunta = '🔸 Escolha uma opção (1-4): ';
    
    rl.question(pergunta, (resposta) => {
      rl.close();
      
      switch(resposta.trim()) {
        case '1':
          console.log('✅ Selecionado: Processar TODOS os links\n');
          resolve('ALL');
          break;
        case '2':
          console.log('✅ Selecionado: Processar APENAS PDFs\n');
          resolve('PDF_ONLY');
          break;
        case '3':
          console.log('✅ Selecionado: Processar APENAS Folders/ZIPs\n');
          resolve('FOLDER_ONLY');
          break;
        case '4':
          console.log('✅ Selecionado: Continuar (skip baixados)\n');
          resolve('SKIP_DOWNLOADED');
          break;
        default:
          console.log('⚠️ Opção inválida, usando padrão: Todos os links\n');
          resolve('ALL');
      }
    });
  });
}

/**
 * Verifica se arquivo já foi baixado
 */
function arquivoJaBaixado(filename: string): boolean {
  const downloadPath = path.join(DOWNLOADS_DIR, filename);
  const finalPath = path.join(FINAL_PDFS_DIR, filename.replace('.zip', '.pdf'));
  
  return fs.existsSync(downloadPath) || fs.existsSync(finalPath);
}

/**
 * Filtra links baseado na escolha do usuário
 */
function filtrarLinks(links: LinkInfo[], filtro: FilterType): LinkInfo[] {
  console.log(`🔍 Aplicando filtro: ${filtro}`);
  console.log(`📊 Total de links antes do filtro: ${links.length}`);
  
  let linksFiltrados: LinkInfo[];
  
  switch(filtro) {
    case 'PDF_ONLY':
      linksFiltrados = links.filter(link => link.tipo === 'pdf');
      console.log(`📄 Filtrado para PDFs: ${linksFiltrados.length} links`);
      break;
      
    case 'FOLDER_ONLY':
      linksFiltrados = links.filter(link => link.tipo === 'folder');
      console.log(`📁 Filtrado para Folders: ${linksFiltrados.length} links`);
      break;
      
    case 'SKIP_DOWNLOADED':
      linksFiltrados = links.filter(link => !arquivoJaBaixado(link.filename));
      console.log(`⏭️ Filtrado (skip baixados): ${linksFiltrados.length} links restantes`);
      break;
      
    case 'ALL':
    default:
      linksFiltrados = links;
      console.log(`📋 Mantendo todos os links: ${linksFiltrados.length} links`);
      break;
  }
  
  return linksFiltrados;
}

/**
 * Cria estrutura de diretórios necessários
 */
function criarEstruturaDiretorios(): void {
  const dirs = [BASE_DIR, DOWNLOADS_DIR, EXTRACTED_DIR, FINAL_PDFS_DIR];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 Diretório criado: ${dir}`);
    }
  });
}

/**
 * Detecta o tipo de link do Dropbox
 */
function detectarTipoLink(url: string): LinkInfo['tipo'] {
  if (url.includes('/scl/fi/')) {
    return 'pdf'; // Link de arquivo
  } else if (url.includes('/scl/fo/') || url.includes('/s/') && url.includes('?dl=0')) {
    return 'folder'; // Link de pasta/zip
  }
  return 'unknown';
}

/**
 * Converte URL do Dropbox para download direto
 */
function converterParaDownloadDireto(url: string, tipo: LinkInfo['tipo']): string {
  console.log(`    🔗 Convertendo URL para download direto...`);
  
  // Para links de pasta (/scl/fo/), adicionar parâmetro &e=1 antes de &dl=1
  if (tipo === 'folder' && url.includes('/scl/fo/')) {
    console.log(`    📁 Link de pasta detectado - aplicando conversão especial`);
    
    // Verificar se já tem &e=1
    if (url.includes('&e=1')) {
      // Já tem e=1, só converter dl=0 para dl=1
      return url.replace('dl=0', 'dl=1');
    } else {
      // Adicionar &e=1 antes de converter dl
      const urlComE1 = url.replace('&dl=0', '&e=1&dl=1');
      console.log(`    ✅ URL convertida com &e=1: ${urlComE1.substring(0, 80)}...`);
      return urlComE1;
    }
  }
  
  // Para PDFs e outros tipos, converter normalmente
  console.log(`    📄 Link de arquivo - conversão padrão`);
  return url.replace('dl=0', 'dl=1');
}

/**
 * Gera nome de arquivo baseado na URL
 */
function gerarNomeArquivo(url: string, tipo: LinkInfo['tipo'], index: number): string {
  try {
    // Tentar extrair nome do protocolo da URL
    const urlDecoded = decodeURIComponent(url);
    
    // Para PDFs, tentar extrair nome do path
    if (tipo === 'pdf') {
      const match = urlDecoded.match(/\/([^\/]+\.pdf)/i);
      if (match) {
        return match[1].replace(/[<>:"/\\|?*]/g, '_');
      }
    }
    
    // Para folders/zips
    if (tipo === 'folder') {
      // Tentar extrair nome da pasta
      const folderMatch = urlDecoded.match(/\/([^\/\?]+)\?/);
      if (folderMatch) {
        return `folder_${folderMatch[1].replace(/[<>:"/\\|?*]/g, '_')}_${index}.zip`;
      }
      return `folder_${index}.zip`;
    }
    
    return `protocolo_${index}.${tipo === 'pdf' ? 'pdf' : 'zip'}`;
  } catch (error) {
    return `protocolo_${index}.${tipo === 'pdf' ? 'pdf' : 'zip'}`;
  }
}

/**
 * Faz download de um arquivo com retry
 */
async function downloadArquivo(linkInfo: LinkInfo): Promise<DownloadResult> {
  const result: DownloadResult = {
    url: linkInfo.url,
    success: false,
    tipo: linkInfo.tipo
  };

  console.log(`📥 [${linkInfo.index}] Baixando: ${linkInfo.filename}`);
  console.log(`    🔗 URL: ${linkInfo.url.substring(0, 80)}...`);
  console.log(`    📋 Tipo: ${linkInfo.tipo}`);

  const downloadUrl = converterParaDownloadDireto(linkInfo.url, linkInfo.tipo);
  const filepath = path.join(DOWNLOADS_DIR, linkInfo.filename);

  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
      console.log(`    🔄 Tentativa ${attempt}/${MAX_RETRIES}`);
      
      const response = await axios({
        method: 'GET',
        url: downloadUrl,
        responseType: 'stream',
        timeout: DOWNLOAD_TIMEOUT,
        maxRedirects: 5, // Seguir até 5 redirects automaticamente
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      // Verificar se response é válido
      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Stream para arquivo
      const writer = createWriteStream(filepath);
      response.data.pipe(writer);

      await new Promise<void>((resolve, reject) => {
        writer.on('finish', resolve);
        writer.on('error', reject);
        response.data.on('error', reject);
      });

      // Verificar se arquivo foi baixado
      if (fs.existsSync(filepath)) {
        const stats = fs.statSync(filepath);
        
        if (stats.size > 0) {
          console.log(`    ✅ Download concluído: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
          
          result.success = true;
          result.filename = linkInfo.filename;
          result.filepath = filepath;
          return result;
        } else {
          throw new Error('Arquivo baixado está vazio');
        }
      } else {
        throw new Error('Arquivo não foi criado');
      }

    } catch (error: any) {
      console.log(`    ❌ Tentativa ${attempt} falhou: ${error.message}`);
      
      // Remover arquivo parcial se existir
      if (fs.existsSync(filepath)) {
        fs.unlinkSync(filepath);
      }
      
      if (attempt === MAX_RETRIES) {
        result.error = `Todas as tentativas falharam. Último erro: ${error.message}`;
        console.log(`    💥 Falha definitiva: ${result.error}`);
      } else {
        // Aguardar antes de tentar novamente
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }

  return result;
}

/**
 * Verifica se arquivo é um ZIP válido
 */
function isValidZip(filePath: string): boolean {
  try {
    const buffer = fs.readFileSync(filePath);
    
    // Verificar tamanho mínimo
    if (buffer.length < 4) return false;
    
    // Verificar se é HTML (começando com <)
    if (buffer[0] === 60) return false; // 60 = 0x3C = '<'
    
    // Verificar assinatura ZIP (50 4B = "PK")
    if (buffer[0] !== 0x50 || buffer[1] !== 0x4B) return false;
    
    // Tentar abrir com AdmZip para verificar estrutura
    const zip = new AdmZip(buffer);
    const entries = zip.getEntries();
    return entries.length > 0;
  } catch (error) {
    return false;
  }
}

/**
 * Extrai recursivamente arquivos ZIP
 */
async function extrairArquivosZip(zipPath: string, extractDir: string): Promise<string[]> {
  const pdfsEncontrados: string[] = [];
  
  try {
    console.log(`📦 Verificando arquivo: ${path.basename(zipPath)}`);
    
    // Verificar se é ZIP válido
    if (!isValidZip(zipPath)) {
      console.log(`    ⚠️ Arquivo não é ZIP válido - provavelmente link de pasta inválido`);
      
      // Verificar se é HTML (link de pasta do Dropbox que falhou)
      const content = fs.readFileSync(zipPath, 'utf8').substring(0, 200);
      if (content.includes('<html') || content.includes('<!DOCTYPE')) {
        console.log(`    🌐 Conteúdo HTML detectado - link de pasta Dropbox inválido`);
      }
      
      return pdfsEncontrados;
    }
    
    console.log(`    ✅ ZIP válido detectado - iniciando extração`);
    
    const zip = new AdmZip(zipPath);
    const zipEntries = zip.getEntries();
    
    // Criar diretório de extração específico
    const specificExtractDir = path.join(extractDir, path.basename(zipPath, '.zip'));
    if (!fs.existsSync(specificExtractDir)) {
      fs.mkdirSync(specificExtractDir, { recursive: true });
    }
    
    // Extrair todos os arquivos
    zip.extractAllTo(specificExtractDir, true);
    console.log(`    ✅ Extraído ${zipEntries.length} arquivos para: ${specificExtractDir}`);
    
    // Processar arquivos extraídos recursivamente
    await processarDiretorioRecursivo(specificExtractDir, pdfsEncontrados);
    
  } catch (error: any) {
    console.error(`❌ Erro ao extrair ${zipPath}: ${error.message}`);
  }
  
  return pdfsEncontrados;
}

/**
 * Processa diretório recursivamente buscando PDFs e ZIPs
 */
async function processarDiretorioRecursivo(dir: string, pdfsEncontrados: string[]): Promise<void> {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const itemPath = path.join(dir, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      // Processar subdiretório
      await processarDiretorioRecursivo(itemPath, pdfsEncontrados);
      
    } else if (stats.isFile()) {
      const ext = path.extname(item).toLowerCase();
      
      if (ext === '.pdf') {
        // Mover PDF para pasta final
        const finalPath = path.join(FINAL_PDFS_DIR, item);
        let finalFilename = item;
        let counter = 1;
        
        // Resolver conflitos de nome
        while (fs.existsSync(finalPath.replace(path.basename(finalPath), finalFilename))) {
          const baseName = path.basename(item, '.pdf');
          finalFilename = `${baseName}_${counter}.pdf`;
          counter++;
        }
        
        const finalDestination = path.join(FINAL_PDFS_DIR, finalFilename);
        fs.copyFileSync(itemPath, finalDestination);
        pdfsEncontrados.push(finalDestination);
        
        console.log(`    📄 PDF movido: ${finalFilename}`);
        
      } else if (ext === '.zip' || ext === '.rar') {
        // Extrair ZIP/RAR aninhado
        console.log(`    📦 ZIP aninhado encontrado: ${item}`);
        const nestedPdfs = await extrairArquivosZip(itemPath, EXTRACTED_DIR);
        pdfsEncontrados.push(...nestedPdfs);
      }
    }
  }
}

/**
 * Processa um arquivo baixado (PDF ou ZIP)
 */
async function processarArquivoBaixado(result: DownloadResult): Promise<string[]> {
  if (!result.success || !result.filepath) {
    return [];
  }
  
  const pdfsProcessados: string[] = [];
  const ext = path.extname(result.filepath).toLowerCase();
  
  if (ext === '.pdf') {
    // Mover PDF diretamente para pasta final
    const finalPath = path.join(FINAL_PDFS_DIR, result.filename!);
    let finalFilename = result.filename!;
    let counter = 1;
    
    // Resolver conflitos de nome
    while (fs.existsSync(path.join(FINAL_PDFS_DIR, finalFilename))) {
      const baseName = path.basename(result.filename!, '.pdf');
      finalFilename = `${baseName}_${counter}.pdf`;
      counter++;
    }
    
    const finalDestination = path.join(FINAL_PDFS_DIR, finalFilename);
    fs.copyFileSync(result.filepath, finalDestination);
    pdfsProcessados.push(finalDestination);
    
    console.log(`📄 PDF final: ${finalFilename}`);
    
  } else {
    // Tratar como ZIP e extrair
    const extractedPdfs = await extrairArquivosZip(result.filepath, EXTRACTED_DIR);
    pdfsProcessados.push(...extractedPdfs);
  }
  
  return pdfsProcessados;
}

/**
 * Lê links do CSV
 */
async function lerLinksDoCSV(csvPath: string): Promise<LinkInfo[]> {
  return new Promise((resolve, reject) => {
    const links: LinkInfo[] = [];
    let index = 0;
    
    fs.createReadStream(csvPath)
      .pipe(csv())
      .on('data', (row: any) => {
        const url = row.link_protocolo?.trim();
        
        if (url && url.includes('dropbox.com') && !url.includes('ddd.com')) {
          index++;
          const tipo = detectarTipoLink(url);
          const filename = gerarNomeArquivo(url, tipo, index);
          
          links.push({
            url,
            tipo,
            filename,
            index
          });
        }
      })
      .on('end', () => {
        console.log(`📊 Total de links válidos encontrados: ${links.length}`);
        resolve(links);
      })
      .on('error', reject);
  });
}

/**
 * Processa links em lotes
 */
async function processarLoteDeLinks(links: LinkInfo[]): Promise<DownloadResult[]> {
  const resultados: DownloadResult[] = [];
  
  for (let i = 0; i < links.length; i += BATCH_SIZE) {
    const lote = links.slice(i, i + BATCH_SIZE);
    
    console.log(`\n📦 Processando lote ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(links.length / BATCH_SIZE)}`);
    console.log(`📋 Links neste lote: ${lote.length}`);
    
    // Processar lote em paralelo
    const promessasLote = lote.map(link => downloadArquivo(link));
    const resultadosLote = await Promise.allSettled(promessasLote);
    
    // Processar resultados
    for (let j = 0; j < resultadosLote.length; j++) {
      const resultado = resultadosLote[j];
      
      if (resultado.status === 'fulfilled') {
        resultados.push(resultado.value);
        
        // Processar arquivo baixado (extrair se for ZIP)
        if (resultado.value.success) {
          await processarArquivoBaixado(resultado.value);
        }
      } else {
        resultados.push({
          url: lote[j].url,
          success: false,
          error: resultado.reason,
          tipo: lote[j].tipo
        });
      }
    }
    
    // Delay entre lotes
    if (i + BATCH_SIZE < links.length) {
      console.log(`⏸️ Aguardando ${DELAY_BETWEEN_DOWNLOADS}ms antes do próximo lote...`);
      await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_DOWNLOADS));
    }
  }
  
  return resultados;
}

/**
 * Gera relatório final
 */
function gerarRelatorio(stats: ProcessStats, resultados: DownloadResult[]): string {
  const timestamp = new Date().toLocaleString('pt-BR');
  const sucessos = resultados.filter(r => r.success);
  const falhas = resultados.filter(r => !r.success);
  
  let relatorio = `# 📊 Relatório - Download Protocolos Dropbox\n\n`;
  relatorio += `**Data/Hora:** ${timestamp}\n\n`;
  
  // Resumo Geral
  relatorio += `## 📈 Resumo da Operação\n\n`;
  relatorio += `| Métrica | Valor |\n`;
  relatorio += `|---------|-------|\n`;
  relatorio += `| 📊 Links Processados | ${stats.processed} |\n`;
  relatorio += `| ✅ Downloads Sucesso | ${stats.downloads_success} |\n`;
  relatorio += `| ❌ Downloads Falha | ${stats.downloads_failed} |\n`;
  relatorio += `| 📄 PDFs Finais | ${stats.pdfs_extraidos} |\n`;
  relatorio += `| 📦 ZIPs Processados | ${stats.zips_processados} |\n`;
  
  if (stats.processed > 0) {
    const taxaSucesso = ((stats.downloads_success / stats.processed) * 100).toFixed(1);
    relatorio += `| 🎯 Taxa de Sucesso | ${taxaSucesso}% |\n`;
  }
  
  relatorio += `\n`;
  
  // Detalhes das Falhas
  if (falhas.length > 0) {
    relatorio += `## ❌ Falhas de Download\n\n`;
    falhas.forEach((falha, index) => {
      relatorio += `### ${index + 1}. ${falha.tipo.toUpperCase()}\n`;
      relatorio += `- **URL:** ${falha.url.substring(0, 80)}...\n`;
      relatorio += `- **Erro:** ${falha.error}\n\n`;
    });
  }
  
  return relatorio;
}

/**
 * Função principal
 */
async function main(): Promise<void> {
  console.log('🚀 Iniciando download de protocolos do Dropbox...\n');
  
  const stats: ProcessStats = {
    total: 0,
    processed: 0,
    downloads_success: 0,
    downloads_failed: 0,
    pdfs_extraidos: 0,
    zips_processados: 0,
    errors: []
  };
  
  try {
    // 1. Criar estrutura de diretórios
    console.log('📁 Criando estrutura de diretórios...');
    criarEstruturaDiretorios();
    
    // 2. Ler links do CSV
    const csvPath = path.join(process.cwd(), 'links-unicos-protocolo-2025-06-11T22-25-44.csv');
    
    if (!fs.existsSync(csvPath)) {
      throw new Error(`CSV não encontrado: ${csvPath}`);
    }
    
    console.log('📄 Lendo links do CSV...');
    const todosOsLinks = await lerLinksDoCSV(csvPath);
    
    if (todosOsLinks.length === 0) {
      console.log('⚠️ Nenhum link válido encontrado no CSV');
      return;
    }
    
    // 3. Exibir menu e obter filtro do usuário
    const filtroEscolhido = await exibirMenu();
    
    // 4. Aplicar filtro baseado na escolha do usuário
    const links = filtrarLinks(todosOsLinks, filtroEscolhido);
    stats.total = links.length;
    
    if (links.length === 0) {
      console.log('⚠️ Nenhum link encontrado após aplicar filtro');
      return;
    }
    
    // 5. Processar downloads em lotes
    console.log(`\n🎯 Iniciando processamento de ${links.length} links...`);
    const resultados = await processarLoteDeLinks(links);
    
    // 6. Calcular estatísticas
    stats.processed = resultados.length;
    stats.downloads_success = resultados.filter(r => r.success).length;
    stats.downloads_failed = resultados.filter(r => !r.success).length;
    
    // Contar PDFs na pasta final
    if (fs.existsSync(FINAL_PDFS_DIR)) {
      const arquivosPdf = fs.readdirSync(FINAL_PDFS_DIR).filter(f => f.endsWith('.pdf'));
      stats.pdfs_extraidos = arquivosPdf.length;
    }
    
    // Contar ZIPs processados
    stats.zips_processados = resultados.filter(r => r.success && r.tipo === 'folder').length;
    
    // 7. Gerar relatório
    const relatorio = gerarRelatorio(stats, resultados);
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
    const relatorioPath = path.join(process.cwd(), `relatorio-download-protocolos-${timestamp}.md`);
    
    fs.writeFileSync(relatorioPath, relatorio, 'utf8');
    
    // 8. Resumo final
    console.log('\n' + '='.repeat(60));
    console.log('📊 RESUMO FINAL');
    console.log('='.repeat(60));
    console.log(`✅ Downloads bem-sucedidos: ${stats.downloads_success}`);
    console.log(`❌ Downloads falharam: ${stats.downloads_failed}`);
    console.log(`📄 PDFs extraídos: ${stats.pdfs_extraidos}`);
    console.log(`📦 ZIPs processados: ${stats.zips_processados}`);
    console.log(`📁 Pasta final: ${FINAL_PDFS_DIR}`);
    console.log(`📄 Relatório: ${relatorioPath}`);
    console.log('='.repeat(60));
    
  } catch (error) {
    console.error('❌ Erro geral na execução:', error);
  }
}

// Executar script se chamado diretamente
if (require.main === module) {
  main()
    .catch(console.error)
    .finally(() => {
      console.log('\n🏁 Script finalizado!');
      process.exit(0);
    });
}

export { main as downloadProtocolosDropbox }; 