-- AlterTable
ALTER TABLE "Titular" ADD COLUMN     "clienteId" INTEGER,
ADD COLUMN     "numeroDocumento" TEXT;

-- CreateIndex
CREATE INDEX "Titular_numeroDocumento_idx" ON "Titular"("numeroDocumento");

-- CreateIndex
CREATE INDEX "Titular_clienteId_idx" ON "Titular"("clienteId");

-- AddForeignKey
ALTER TABLE "Titular" ADD CONSTRAINT "Titular_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE SET NULL ON UPDATE CASCADE;
