import { PrismaClient } from '@prisma/client';
import { updateChatGuruCustomField } from '../utils/chatguru.utils';
import axios from 'axios';
import querystring from 'querystring';
import * as fs from 'fs';
import * as path from 'path';
import dotenv from 'dotenv';

dotenv.config();
const prisma = new PrismaClient();

// Configurações do script
const BATCH_SIZE = 15; // Processar 15 clientes em paralelo por lote
const DELAY_BETWEEN_PHONE_ATTEMPTS = 500; // 500ms entre tentativas de telefone
const DELAY_BETWEEN_BATCHES = 1500; // 1.5 segundos entre lotes

interface ClienteParaProcessar {
  id: number;
  nome: string;
  identificador: string;
  numeroDocumento: string;
  autoLoginUrl: string;
  contatos: Array<{
    telefone: string | null;
    telefoneSegundario: string | null;
  }>;
}

interface ResultadoProcessamento {
  clienteId: number;
  nomeCliente: string;
  identificador: string;
  sucesso: boolean;
  telefoneUsado?: string;
  erro?: string;
  camposAtualizados?: string[];
}

function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function extrairUltimos3Digitos(documento: string): string {
  const apenasNumeros = documento.replace(/\D/g, '');
  return apenasNumeros.slice(-3);
}

function extrairShortCodeDaUrl(url: string): string {
  if (!url) return '';
  
  // Extrair apenas o código após a última barra
  // Ex: "https://cliente.registre.se/Kk0jjB" → "Kk0jjB"
  const shortCode = url.split('/').pop() || '';
  
  console.log(`🔗 Extraindo short code: "${url}" → "${shortCode}"`);
  return shortCode;
}

// Função para reformatar número (copiada do chatguru.utils.ts)
function reformatChatNumber(chatNumber: string): string {
  if (!chatNumber || typeof chatNumber !== "string") {
    throw new Error("Número de chat inválido");
  }

  const countryCode = chatNumber.slice(0, 2); // Código do país
  const areaCode = chatNumber.slice(2, 4); // Código de área
  let phoneNumber = chatNumber.slice(4);

  if (phoneNumber.length === 9) {
    phoneNumber = phoneNumber.slice(1);
  } else if (phoneNumber.length === 8) {
    phoneNumber = "9" + phoneNumber;
  }

  return countryCode + areaCode + phoneNumber;
}

// Função para atualizar MÚLTIPLOS campos de uma só vez
async function updateMultipleChatGuruFields(
  chatNumber: string,
  fields: Record<string, string> // Ex: { IDC: "valor1", Senha_Cliente: "valor2" }
): Promise<any> {
  console.log(`🔄 Atualizando múltiplos campos para: ${chatNumber}, Campos:`, Object.keys(fields));

  // Remover caracteres especiais e espaços
  let cleanNumber = chatNumber.replace(/\D/g, "");
  
  // Garantir que o número tenha o formato correto (*************)
  if (!cleanNumber.startsWith("55")) {
    cleanNumber = "55" + cleanNumber;
  }

  const key = process.env.CHATGURU_API_KEY;
  const accountId = process.env.CHATGURU_ACCOUNT_ID;
  const phoneId = process.env.CHATGURU_PHONE_ID;
  const action = "chat_update_custom_fields";

  if (!key || !accountId || !phoneId) {
    throw new Error("Credenciais do Chatguru não configuradas");
  }

  const sendRequest = async (formattedNumber: string, retryCount: number = 0): Promise<any> => {
    try {
      console.log(`📤 Tentando atualizar campos para número: ${formattedNumber} (tentativa ${retryCount + 1})`);

      const chatGuruData: any = {
        key,
        account_id: accountId,
        phone_id: phoneId,
        chat_number: formattedNumber,
        action,
      };
      
      // Adicionar todos os campos dinamicamente
      Object.entries(fields).forEach(([fieldName, fieldValue]) => {
        chatGuruData[`field__${fieldName}`] = fieldValue;
      });

      console.log("🔑 Dados da requisição:", {
        ...chatGuruData,
        key: "***",
        account_id: "***",
        phone_id: "***",
      });

      const formData = querystring.stringify(chatGuruData);
      
      const response = await axios.post(
        process.env.CHATGURU_API_ENDPOINT || "https://s16.chatguru.app/api/v1",
        formData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      console.log(`📥 Resposta do Chatguru (${response.status}):`, response.data);
      console.log("✅ Múltiplos campos atualizados com sucesso para:", formattedNumber);

      return response.data;
    } catch (error: any) {
      if (error.response && error.response.status === 400 && retryCount < 2) {
        console.warn(`⚠️ Erro 400, tentando reformatar número (tentativa ${retryCount + 1})`);
        const reformattedChatNumber = reformatChatNumber(formattedNumber);
        if (reformattedChatNumber !== formattedNumber) {
          console.log("🔄 Tentando com número reformatado:", reformattedChatNumber);
          return await sendRequest(reformattedChatNumber, retryCount + 1);
        }
      }
      console.error("❌ Erro na requisição ao Chatguru:", error.response?.data || error.message);
      throw error;
    }
  };

  return await sendRequest(cleanNumber);
}

function gerarRelatorioMarkdown(stats: any, resultados: ResultadoProcessamento[]): string {
  const timestamp = new Date().toLocaleString('pt-BR');
  const falhas = resultados.filter(r => !r.sucesso);
  
  let markdown = `# 📊 Relatório - Atualização Campos ChatGuru\n\n`;
  markdown += `**Data/Hora:** ${timestamp}\n\n`;
  
  // Resumo Geral
  markdown += `## 📈 Resumo da Operação\n\n`;
  markdown += `| Métrica | Valor |\n`;
  markdown += `|---------|-------|\n`;
  markdown += `| 📊 Clientes Analisados | ${stats.clientesAnalisados} |\n`;
  markdown += `| ✅ Clientes Válidos | ${stats.clientesValidos} |\n`;
  markdown += `| 🎉 Sucessos | ${stats.sucessos} |\n`;
  markdown += `| ❌ Falhas | ${stats.falhas} |\n`;
  markdown += `| 📦 Lotes Processados | ${stats.batches} |\n`;
  
  if (stats.clientesValidos > 0) {
    const taxaSucesso = ((stats.sucessos / stats.clientesValidos) * 100).toFixed(1);
    markdown += `| 🎯 Taxa de Sucesso | ${taxaSucesso}% |\n`;
  }
  
  markdown += `\n`;

  // Detalhamento das Falhas (somente se houver falhas)
  if (falhas.length > 0) {
    markdown += `## ❌ Detalhamento das Falhas\n\n`;
    markdown += `Total de falhas: **${falhas.length}**\n\n`;
    
    falhas.forEach((falha, index) => {
      markdown += `### ${index + 1}. Cliente ID: ${falha.clienteId}\n\n`;
      markdown += `- **Nome:** ${falha.nomeCliente}\n`;
      markdown += `- **Identificador:** ${falha.identificador}\n`;
      markdown += `- **Erro:** ${falha.erro}\n`;
      if (falha.telefoneUsado) {
        markdown += `- **Último Telefone Tentado:** ${falha.telefoneUsado.substring(0, 5)}****\n`;
      }
      markdown += `\n---\n\n`;
    });
  } else {
    markdown += `## 🎉 Sem Falhas\n\n`;
    markdown += `Todos os clientes foram processados com sucesso! ✨\n\n`;
  }

  // Estatísticas Adicionais
  markdown += `## 📋 Informações Técnicas\n\n`;
  markdown += `- **Batch Size:** ${BATCH_SIZE} clientes por lote\n`;
  markdown += `- **Delay entre tentativas:** ${DELAY_BETWEEN_PHONE_ATTEMPTS}ms\n`;
  markdown += `- **Delay entre lotes:** ${DELAY_BETWEEN_BATCHES}ms\n`;
  markdown += `- **Campos atualizados:** IDC, Senha_Cliente, Link_Cliente\n`;
  markdown += `- **Modo:** Múltiplos campos em 1 requisição\n\n`;

  return markdown;
}

function obterListaTelefones(contatos: ClienteParaProcessar['contatos']): string[] {
  const telefones: string[] = [];
  
  for (const contato of contatos) {
    if (contato.telefone) {
      telefones.push(contato.telefone);
    }
    if (contato.telefoneSegundario) {
      telefones.push(contato.telefoneSegundario);
    }
  }
  
  // Remover duplicatas e números inválidos
  return telefones.filter((telefone, index, arr) => 
    arr.indexOf(telefone) === index && telefone.replace(/\D/g, '').length >= 10
  );
}

async function atualizarCamposCliente(cliente: ClienteParaProcessar): Promise<ResultadoProcessamento> {
  console.log(`🔄 Processando cliente ID: ${cliente.id} - ${cliente.nome}`);
  
  const resultado: ResultadoProcessamento = {
    clienteId: cliente.id,
    nomeCliente: cliente.nome,
    identificador: cliente.identificador,
    sucesso: false
  };

  // Preparar dados dos campos - TODOS DE UMA VEZ
  const senhaCliente = extrairUltimos3Digitos(cliente.numeroDocumento);
  
  const camposParaAtualizar = {
    IDC: cliente.identificador,
    Senha_Cliente: senhaCliente,
    Link_Cliente: extrairShortCodeDaUrl(cliente.autoLoginUrl)
  };

  console.log(`   📋 Campos a atualizar:`, {
    IDC: cliente.identificador,
    Senha_Cliente: senhaCliente,
    Link_Cliente: extrairShortCodeDaUrl(cliente.autoLoginUrl)
  });

  // Obter lista de telefones para tentar
  const telefones = obterListaTelefones(cliente.contatos);
  
  if (telefones.length === 0) {
    resultado.erro = 'Nenhum telefone válido encontrado';
    console.log(`   ❌ ${resultado.erro}`);
    return resultado;
  }

  console.log(`   📱 Telefones disponíveis: ${telefones.length} (${telefones.map(t => t.substring(0, 5) + '****').join(', ')})`);

  // Tentar atualizar campos para cada telefone até conseguir
  for (let i = 0; i < telefones.length; i++) {
    const telefone = telefones[i];
    console.log(`   📞 Tentando telefone ${i + 1}/${telefones.length}: ${telefone.substring(0, 5)}****`);
    
        try {
      // Atualizar TODOS OS CAMPOS de uma só vez
      console.log(`     🔧 Atualizando todos os campos de uma vez...`);
      
      await updateMultipleChatGuruFields(telefone, camposParaAtualizar);
      
      // Se chegou aqui, todos os campos foram atualizados com sucesso
      resultado.sucesso = true;
      resultado.telefoneUsado = telefone;
      resultado.camposAtualizados = Object.keys(camposParaAtualizar);
      
      console.log(`   🎉 Todos os campos atualizados com sucesso para o telefone: ${telefone.substring(0, 5)}****`);
      break;
      
    } catch (error) {
      console.log(`   ⚠️  Falha no telefone ${telefone.substring(0, 5)}****: ${(error as Error).message}`);
      
      // Se não é o último telefone, continuar tentando
      if (i < telefones.length - 1) {
                 console.log(`   🔄 Tentando próximo telefone...`);
         await delay(DELAY_BETWEEN_PHONE_ATTEMPTS); // Delay antes de tentar próximo telefone
      } else {
        resultado.erro = `Falha em todos os ${telefones.length} telefones. Último erro: ${(error as Error).message}`;
        console.log(`   ❌ ${resultado.erro}`);
      }
    }
  }

  return resultado;
}

async function atualizarCamposChatguru() {
  console.log('🚀 Iniciando atualização de campos ChatGuru...\n');
  
  let stats = {
    clientesAnalisados: 0,
    clientesValidos: 0,
    sucessos: 0,
    falhas: 0,
    batches: 0
  };
  
  const resultados: ResultadoProcessamento[] = [];

  try {
    // Buscar clientes válidos
    console.log('📊 Buscando clientes válidos...');
    const clientes = await prisma.cliente.findMany({
      where: {
        identificador: { not: null },
        numeroDocumento: { not: null },
        autoLoginUrl: { not: null }
      },
      include: {
        contatos: {
          select: {
            telefone: true,
            telefoneSegundario: true
          }
        }
      },
    });

    console.log(`✅ Encontrados ${clientes.length} clientes com campos obrigatórios\n`);
    stats.clientesAnalisados = clientes.length;

    // Filtrar clientes que têm pelo menos um telefone
    const clientesValidos: ClienteParaProcessar[] = clientes
      .filter(cliente => {
        const temTelefone = cliente.contatos.some(contato => 
          contato.telefone || contato.telefoneSegundario
        );
        return temTelefone;
      })
      .map(cliente => ({
        id: cliente.id,
        nome: cliente.nome || `Cliente ${cliente.id}`,
        identificador: cliente.identificador!,
        numeroDocumento: cliente.numeroDocumento!,
        autoLoginUrl: cliente.autoLoginUrl!,
        contatos: cliente.contatos
      }));

    console.log(`✅ ${clientesValidos.length} clientes válidos com telefones encontrados\n`);
    stats.clientesValidos = clientesValidos.length;

    if (clientesValidos.length === 0) {
      console.log('⚠️  Nenhum cliente válido encontrado para processamento');
      return;
    }

    // Processar em lotes
    const totalBatches = Math.ceil(clientesValidos.length / BATCH_SIZE);
    console.log(`📦 Processando em ${totalBatches} lotes de até ${BATCH_SIZE} clientes\n`);

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const startIndex = batchIndex * BATCH_SIZE;
      const endIndex = Math.min(startIndex + BATCH_SIZE, clientesValidos.length);
      const batch = clientesValidos.slice(startIndex, endIndex);
      
      stats.batches++;
      
      console.log(`📦 Lote ${batchIndex + 1}/${totalBatches} (${batch.length} clientes):`);
      console.log('='.repeat(50));

             // Processar clientes do lote EM PARALELO
       const promisesBatch = batch.map(cliente => 
         atualizarCamposCliente(cliente).catch(error => {
           console.error(`❌ Erro inesperado no cliente ${cliente.id}:`, error);
           return {
             clienteId: cliente.id,
             nomeCliente: cliente.nome,
             identificador: cliente.identificador,
             sucesso: false,
             erro: `Erro inesperado: ${(error as Error).message}`
           } as ResultadoProcessamento;
         })
       );

       // Aguardar todos os clientes do lote terminarem
       const resultadosBatch = await Promise.allSettled(promisesBatch);
       
       // Processar resultados
       resultadosBatch.forEach((result, index) => {
         if (result.status === 'fulfilled') {
           const resultado = result.value;
           resultados.push(resultado);
           
           if (resultado.sucesso) {
             stats.sucessos++;
           } else {
             stats.falhas++;
           }
         } else {
           console.error(`❌ Promise rejeitada para cliente ${batch[index].id}:`, result.reason);
           resultados.push({
             clienteId: batch[index].id,
             nomeCliente: batch[index].nome,
             identificador: batch[index].identificador,
             sucesso: false,
             erro: `Promise rejeitada: ${result.reason}`
           });
           stats.falhas++;
         }
       });

      // Delay entre lotes (exceto no último)
      if (batchIndex < totalBatches - 1) {
        console.log(`\n⏸️  Aguardando ${DELAY_BETWEEN_BATCHES}ms antes do próximo lote...\n`);
        await delay(DELAY_BETWEEN_BATCHES);
      }
    }

  } catch (error) {
    console.error('❌ Erro geral na execução:', error);
  } finally {
    await prisma.$disconnect();
  }

  // Gerar relatório em Markdown
  console.log('\n📝 Gerando relatório em Markdown...');
  
  const relatorioMarkdown = gerarRelatorioMarkdown(stats, resultados);
  
  // Salvar arquivo
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
  const fileName = `relatorio-chatguru-${timestamp}.md`;
  const filePath = path.join(process.cwd(), fileName);
  
  try {
    fs.writeFileSync(filePath, relatorioMarkdown, 'utf8');
    console.log(`✅ Relatório salvo: ${fileName}`);
    console.log(`📍 Localização: ${filePath}`);
  } catch (error) {
    console.error('❌ Erro ao salvar relatório:', error);
  }

  // Resumo rápido no console
  console.log('\n' + '='.repeat(60));
  console.log('📊 RESUMO FINAL');
  console.log('='.repeat(60));
  console.log(`✅ Sucessos: ${stats.sucessos} | ❌ Falhas: ${stats.falhas}`);
  
  if (stats.clientesValidos > 0) {
    console.log(`🎯 Taxa de sucesso: ${((stats.sucessos / stats.clientesValidos) * 100).toFixed(1)}%`);
  }

  if (stats.sucessos > 0) {
    console.log('\n🎉 Operação concluída com sucessos!');
  } else {
    console.log('\n⚠️  Nenhuma atualização foi bem-sucedida.');
  }
  
  console.log(`📄 Relatório detalhado: ${fileName}`);
  console.log('='.repeat(60));
}

// Executar o script
if (require.main === module) {
  atualizarCamposChatguru()
    .catch(console.error)
    .finally(() => process.exit());
}

export { atualizarCamposChatguru }; 