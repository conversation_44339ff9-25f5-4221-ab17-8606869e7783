import { PrismaClient } from '@prisma/client';
import cliProgress from 'cli-progress';

const prisma = new PrismaClient();

// --- DADOS DO CLIENTE ---
const identificadorClienteTeste = '00000002';
const numeroDocumentoCliente = '00000002';
const nomeCliente = `Cliente Específico (${identificadorClienteTeste})`;
// -----------------------

// --- FLUXOS ESPECÍFICOS PARA ESTE CLIENTE ---
const fluxosEspecificos: string[][] = [
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Deferimento do pedido",
    "Concessão de registro",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Notificação de oposição",
    "Indeferimento do pedido",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Deferimento do pedido",
    "Arquivamento definitivo de pedido de registro por falta de pagamento da concessão",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Indeferimento do pedido",
    "Notificação de recurso",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Sobrestamento do exame de mérito",
    "Indeferimento do pedido",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Exigência de mérito",
    "Arquivamento definitivo de pedido de registro por falta de cumprimento de exigência de mérito",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Exigência de mérito",
    "Indeferimento do pedido",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Indeferimento do pedido",
    "Deferimento da petição",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Notificação de oposição",
    "Deferimento do pedido",
    "Concessão de registro",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Sobrestamento do exame de mérito",
    "Deferimento do pedido",
    "Concessão de registro",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Notificação de oposição",
    "Exigência de mérito",
    "Indeferimento do pedido",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Indeferimento do pedido",
    "Notificação de recurso",
    "Recurso provido (decisão reformada para: Deferimento)",
    "Concessão de registro",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Deferimento do pedido",
    "Concessão de registro",
    "Ato de prejudicar petição",
    "Notificação de caducidade",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Deferimento do pedido",
    "Concessão de registro",
    "Notificação de instauração de processo de nulidade a requerimento",
    "Requerimento não provido (mantida a concessão)",
    "Deferimento da petição",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Indeferimento do pedido",
    "Notificação de recurso",
    "Recurso provido (decisão reformada para: Deferimento)",
    "Decisão de não conhecer da petição",
    "Arquivamento definitivo de pedido de registro por falta de pagamento da concessão",
  ],
  [
    "Publicação de pedido de registro para oposição (exame formal concluído)",
    "Indeferimento do pedido",
    "Notificação de recurso",
    "Recurso provido (decisão reformada para: Deferimento)",
    "Concessão de registro",
    "Notificação de instauração de processo de nulidade a requerimento",
  ],
];
// -------------------------------------------

/**
 * Cria uma abreviação mais única para um nome de despacho.
 */
function abreviarDespacho(nome: string | null): string {
  if (!nome) return 'N/A';
  const overrides: { [key: string]: string } = {
    'Deferimento do pedido': 'DefPed',
    'Deferimento da petição': 'DefPet',
    'Indeferimento do pedido': 'IndPed',
    'Indeferimento da petição': 'IndPet',
    'Publicação de pedido de registro para oposição': 'PubOpo',
    'Publicação de pedido de registro para oposição (exame formal concluído)': 'PubOpoEx',
    'Notificação de oposição': 'NotOpo',
    'Notificação de recurso': 'NotRec',
    'Notificação de caducidade': 'NotCad',
    'Concessão de registro': 'ConcReg',
    'Arquivamento definitivo de pedido de registro por falta de pagamento da concessão': 'ArqPagConc',
    'Arquivamento definitivo de pedido de registro por falta de cumprimento de exigência de mérito': 'ArqExiMer',
    'Exigência de mérito': 'ExiMer',
    'Exigência formal': 'ExiFor',
    'Sobrestamento do exame de mérito': 'SobExaMer',
    'Recurso não provido (decisão mantida)': 'RecNeg',
    'Recurso provido (decisão reformada para: Deferimento)': 'RecDef',
    'Notificação de instauração de processo de nulidade a requerimento': 'NotNulReq',
    'Requerimento não provido (mantida a concessão)': 'ReqNeg',
    'Ato de prejudicar petição': 'AtoPrejPet',
    'Decisão de não conhecer da petição': 'DecNaoConPet',
  };
  if (overrides[nome]) { return overrides[nome]; }
  const palavras = nome.split(' ');
  const primeiraPalavra = palavras[0] || '';
  const abreviacao = primeiraPalavra.substring(0, 3);
  const palavrasIgnoradas = ['de', 'da', 'do', 'a', 'o', 'em', 'para', 'por'];
  let segundaSigla = '';
  for (let i = 1; i < palavras.length; i++) {
    if (!palavrasIgnoradas.includes(palavras[i].toLowerCase())) {
      segundaSigla = palavras[i].substring(0, 1);
      break;
    }
  }
  return (abreviacao + segundaSigla).substring(0, 4);
}

/**
 * Cria um nome de marca resumido baseado na sequência de despachos.
 */
function criarNomeMarcaResumido(sequencia: string[], limite = 50): string {
  if (!sequencia || sequencia.length === 0) {
    return 'Fluxo Vazio'.substring(0, limite);
  }
  const abreviacoes = sequencia.map(abreviarDespacho);
  const nomeCompletoAbrev = abreviacoes.join('-');
  let nome = `Fluxo(${sequencia.length}): ${nomeCompletoAbrev}`;
  if (nome.length > limite) {
    const primeiraAbrev = abreviacoes[0] || 'Inicio';
    const ultimaAbrev = abreviacoes[abreviacoes.length - 1] || 'Fim';
    nome = `Fluxo(${sequencia.length}): ${primeiraAbrev}...${ultimaAbrev}`;
  }
  return nome.substring(0, limite);
}

/**
 * Função principal para criar o cliente específico e seus processos.
 */
async function criarDadosClienteEspecifico() {
  try {
    console.log(`Iniciando criação de dados para cliente: ${identificadorClienteTeste}`);
    console.log(`Total de fluxos específicos a serem criados: ${fluxosEspecificos.length}`);

    // 1. Criar ou encontrar o cliente de teste
    let cliente = await prisma.cliente.findFirst({
      where: { identificador: identificadorClienteTeste }
    });

    if (!cliente) {
      console.log(`Criando novo cliente com identificador: ${identificadorClienteTeste}`);
      cliente = await prisma.cliente.create({
        data: {
          identificador: identificadorClienteTeste,
          nome: nomeCliente,
          tipoDeDocumento: 'TESTE',
          numeroDocumento: numeroDocumentoCliente
        }
      });
    } else {
      console.log(`Cliente encontrado com ID: ${cliente.id}`);
    }

    // 2. Configurar barra de progresso
    const barraProgresso = new cliProgress.SingleBar({
      format: 'Criando processos |{bar}| {percentage}% | {value}/{total} Fluxos',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });
    barraProgresso.start(fluxosEspecificos.length, 0);

    // 3. Criar processos e despachos para cada fluxo
    let processosCriados = 0;
    for (const [index, fluxo] of fluxosEspecificos.entries()) {
      if (!fluxo || fluxo.length === 0) continue;

      const nomeMarca = criarNomeMarcaResumido(fluxo);
      // Usar um prefixo diferente para este cliente nos números de processo
      const numeroProcesso = `TESTE-${identificadorClienteTeste}-${index + 1}`.padStart(15, '0');

      try {
        const processo = await prisma.processo.create({
          data: {
            numero: numeroProcesso,
            clienteId: cliente.id,
            dataDeposito: new Date(),
            monitorado: true,
            marca: {
              create: {
                nome: nomeMarca,
                apresentacao: 'Nominativa',
                natureza: 'Produto'
              }
            }
          },
          include: { marca: true }
        });

        // Usar RPI diferente para cada processo deste cliente
        const rpi = await prisma.rPI.create({
          data: {
            numero: 888800000 + index, // Sequência diferente de RPIs
            dataPublicacao: new Date()
          }
        });

        await prisma.despacho.createMany({
          data: fluxo.map((nomeDespacho) => ({
            processoId: processo.id,
            rpiId: rpi.id,
            codigo: 'FLUXO_ESP', // Código diferente para identificar
            nome: nomeDespacho
          }))
        });

        processosCriados++;
      } catch (error: any) {
        if (error.code === 'P2002' && error.meta?.target?.includes('numero')) {
          console.warn(`\nAviso: Processo ${numeroProcesso} já existe. Pulando.`);
        } else {
          console.error(`\nErro ao criar processo ${numeroProcesso}:`, error.message);
        }
      }
      barraProgresso.increment();
    }

    barraProgresso.stop();
    console.log(`\nCriação concluída. ${processosCriados} processos criados para o cliente ${identificadorClienteTeste}.`);

  } catch (error: any) {
    console.error('Erro ao criar dados de teste para cliente específico:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar o script
criarDadosClienteEspecifico(); 