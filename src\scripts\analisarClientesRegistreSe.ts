import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
import fs from "fs";

dotenv.config();

const prisma = new PrismaClient();

interface ProcessoSemCliente {
  id: string;
  numero: string;
  procuradorNome: string;
}

interface ClienteSemIdentificador {
  clienteId: number;
  nome: string;
  crmId: number | null;
  numeroDocumento: string | null;
  processos: string[];
}

async function analisarClientesRegistreSe() {
  const startTime = Date.now();
  
  try {
    console.log("🔍 Analisando clientes da REGISTRE-SE LTDA...\n");
    
    // PASSO 1: Buscar todos os processos da REGISTRE-SE LTDA
    console.log("📡 Buscando processos da REGISTRE-SE LTDA...");
    
    const processosRegistreSe = await prisma.processo.findMany({
      where: {
        procurador: {
          nome: {
            contains: "REGISTRE-SE LTDA",
            mode: "insensitive"
          }
        }
      },
      include: {
        procurador: {
          select: {
            nome: true
          }
        },
        cliente: {
          select: {
            id: true,
            nome: true,
            identificador: true,
            crmId: true,
            numeroDocumento: true
          }
        }
      },
      orderBy: {
        numero: 'desc'
      }
    });
    
    console.log(`✅ Encontrados ${processosRegistreSe.length} processos da REGISTRE-SE LTDA`);
    
    // PASSO 2: Separar processos sem cliente
    const processosSemCliente: ProcessoSemCliente[] = [];
    const processosComCliente = [];
    
    for (const processo of processosRegistreSe) {
      if (!processo.cliente) {
        processosSemCliente.push({
          id: processo.id,
          numero: processo.numero,
          procuradorNome: processo.procurador?.nome || "N/A"
        });
      } else {
        processosComCliente.push(processo);
      }
    }
    
    console.log(`📊 Processos sem cliente: ${processosSemCliente.length}`);
    console.log(`📊 Processos com cliente: ${processosComCliente.length}`);
    
    // PASSO 3: Identificar clientes sem identificador
    const clientesSemIdentificador = new Map<number, ClienteSemIdentificador>();
    
    for (const processo of processosComCliente) {
      if (processo.cliente) {
        const identificador = processo.cliente.identificador;
        
        // Verificar se o identificador é inválido (null, undefined, string vazia, ou string "null")
        const identificadorInvalido = !identificador || 
                                    identificador.trim() === "" || 
                                    identificador.toLowerCase() === "null" ||
                                    identificador === "0000000000";
        
        if (identificadorInvalido) {
          const clienteId = processo.cliente.id;
          
          if (!clientesSemIdentificador.has(clienteId)) {
            clientesSemIdentificador.set(clienteId, {
              clienteId: clienteId,
              nome: processo.cliente.nome || "N/A",
              crmId: processo.cliente.crmId,
              numeroDocumento: processo.cliente.numeroDocumento,
              processos: []
            });
          }
          
          clientesSemIdentificador.get(clienteId)!.processos.push(processo.numero);
          
          // Log para debug
          console.log(`🔍 Cliente sem identificador encontrado: ID ${clienteId}, Nome: ${processo.cliente.nome}, Identificador: ${JSON.stringify(identificador)}`);
        }
      }
    }
    
    const clientesSemIdentificadorArray = Array.from(clientesSemIdentificador.values());
    
    console.log(`📊 Clientes sem identificador: ${clientesSemIdentificadorArray.length}`);
    
    // PASSO 4: Gerar relatórios
    const timestamp = new Date().toISOString().slice(0, 10);
    
    // Relatório resumo
    const resumo = [
      'ANÁLISE DE CLIENTES REGISTRE-SE LTDA',
      `Data: ${new Date().toLocaleDateString('pt-BR')}`,
      '',
      'RESUMO:',
      `Total de processos REGISTRE-SE LTDA: ${processosRegistreSe.length}`,
      `Processos sem cliente vinculado: ${processosSemCliente.length}`,
      `Processos com cliente vinculado: ${processosComCliente.length}`,
      `Clientes únicos sem identificador: ${clientesSemIdentificadorArray.length}`,
      '',
      'PERCENTUAIS:',
      `Processos sem cliente: ${((processosSemCliente.length / processosRegistreSe.length) * 100).toFixed(1)}%`,
      `Clientes sem identificador: ${((clientesSemIdentificadorArray.length / processosComCliente.length) * 100).toFixed(1)}%`,
      ''
    ].join('\n');
    
    fs.writeFileSync(`analise-registre-se-${timestamp}.txt`, resumo, 'utf8');
    
    // CSV 1: Processos sem cliente
    if (processosSemCliente.length > 0) {
      const csvProcessosSemCliente = [
        'processo_id,numero_processo,procurador_nome',
        ...processosSemCliente.map(p => [
          `"${p.id}"`,
          `"${p.numero}"`,
          `"${p.procuradorNome}"`
        ].join(','))
      ].join('\n');
      
      fs.writeFileSync(`processos-registre-se-sem-cliente-${timestamp}.csv`, csvProcessosSemCliente, 'utf8');
    }
    
    // CSV 2: Clientes sem identificador
    if (clientesSemIdentificadorArray.length > 0) {
      const csvClientesSemIdentificador = [
        'cliente_id,nome_cliente,crm_id,numero_documento,quantidade_processos,numeros_processos',
        ...clientesSemIdentificadorArray.map(c => [
          `"${c.clienteId}"`,
          `"${c.nome}"`,
          `"${c.crmId || 'N/A'}"`,
          `"${c.numeroDocumento || 'N/A'}"`,
          `"${c.processos.length}"`,
          `"${c.processos.join('; ')}"`
        ].join(','))
      ].join('\n');
      
      fs.writeFileSync(`clientes-registre-se-sem-identificador-${timestamp}.csv`, csvClientesSemIdentificador, 'utf8');
    }
    
    // PASSO 5: Relatório final
    const endTime = Date.now();
    const executionTime = (endTime - startTime) / 1000;
    
    console.log("\n🎯 RESULTADOS:");
    console.log(`   📋 Total de processos REGISTRE-SE LTDA: ${processosRegistreSe.length}`);
    console.log(`   ❌ Processos sem cliente: ${processosSemCliente.length}`);
    console.log(`   ⚠️  Clientes sem identificador: ${clientesSemIdentificadorArray.length}`);
    console.log(`   ⏱️ Tempo de execução: ${executionTime} segundos`);
    
    console.log("\n📁 ARQUIVOS GERADOS:");
    console.log(`   📄 Relatório resumo: analise-registre-se-${timestamp}.txt`);
    
    if (processosSemCliente.length > 0) {
      console.log(`   📊 Processos sem cliente: processos-registre-se-sem-cliente-${timestamp}.csv`);
    }
    
    if (clientesSemIdentificadorArray.length > 0) {
      console.log(`   👤 Clientes sem identificador: clientes-registre-se-sem-identificador-${timestamp}.csv`);
    }
    
    console.log(`\n📂 Localização: ${process.cwd()}/`);
    
    return {
      totalProcessos: processosRegistreSe.length,
      processosSemCliente: processosSemCliente.length,
      clientesSemIdentificador: clientesSemIdentificadorArray.length
    };
    
  } catch (error: any) {
    console.error("❌ Erro durante a análise:", error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Função principal
async function main() {
  console.log("🚀 Análise de Clientes REGISTRE-SE LTDA\n");
  
  try {
    await analisarClientesRegistreSe();
    console.log("\n✅ Análise concluída com sucesso!");
    
  } catch (error: any) {
    console.error("❌ Erro durante a execução:", error.message);
    process.exit(1);
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  main();
}

export { analisarClientesRegistreSe }; 