import { PrismaClient } from "@prisma/client";
import axios from "axios";
import dotenv from "dotenv";
import { Logger } from "../services/logger.service";
import readline from "readline";

dotenv.config();

const prisma = new PrismaClient();

// Reutilizar funções do processamentoProtocoloService.ts
function extrairShortCodeDaUrl(url: string): string {
  if (!url) return '';
  
  // Extrair apenas o código após a última barra
  // Ex: "https://cliente.registre.se/Kk0jjB" → "Kk0jjB"
  const shortCode = url.split('/').pop() || '';
  console.log(`🔗 Extraindo short code: "${url}" → "${shortCode}"`);
  return shortCode;
}

function obterListaTelefones(contatos: any[]): string[] {
  const telefones: string[] = [];
  
  for (const contato of contatos) {
    if (contato.telefone) {
      telefones.push(contato.telefone);
    }
    if (contato.telefoneSegundario) {
      telefones.push(contato.telefoneSegundario);
    }
  }
  
  // Remover duplicatas e números inválidos
  return telefones.filter((telefone, index, arr) => 
    arr.indexOf(telefone) === index && telefone.replace(/\D/g, '').length >= 10
  );
}

// Função para gerar todas as variações possíveis de um telefone
function gerarVariacoesTelefone(telefone: string): string[] {
  const apenasNumeros = telefone.replace(/\D/g, '');
  const variacoes = new Set<string>();
  
  // Se tem menos de 10 dígitos, é inválido
  if (apenasNumeros.length < 10) {
    return [];
  }
  
  console.log(`📞 Gerando variações para: ${apenasNumeros} (${apenasNumeros.length} dígitos)`);
  
  // Casos baseados no comprimento
  if (apenasNumeros.length === 13) {
    // Formato: 5519991131354 (código país + DDD + 9 dígitos)
    variacoes.add(apenasNumeros); // Original
    variacoes.add(apenasNumeros.substring(2)); // Sem código país: 19991131354
    
    // Se começa com 555 (duplo 55), testar sem o primeiro 5
    if (apenasNumeros.startsWith('555')) {
      variacoes.add(apenasNumeros.substring(1)); // 519991131354
    }
    
    // Versão sem o 9: 551991131354
    const semNove = apenasNumeros.substring(0, 4) + apenasNumeros.substring(5);
    if (semNove.length === 12) {
      variacoes.add(semNove);
      variacoes.add(semNove.substring(2)); // Sem código país: 1991131354
    }
    
  } else if (apenasNumeros.length === 12) {
    // Formato: 551991131354 (código país + DDD + 8 dígitos)
    variacoes.add(apenasNumeros); // Original
    variacoes.add(apenasNumeros.substring(2)); // Sem código país: 1991131354
    
    // Versão com 9: 5519991131354
    const comNove = apenasNumeros.substring(0, 4) + '9' + apenasNumeros.substring(4);
    if (comNove.length === 13) {
      variacoes.add(comNove);
      variacoes.add(comNove.substring(2)); // Sem código país: 19991131354
    }
    
  } else if (apenasNumeros.length === 11) {
    // Formato: 19991131354 (DDD + 9 dígitos)
    variacoes.add(apenasNumeros); // Original
    variacoes.add('55' + apenasNumeros); // Com código país: 5519991131354
    
    // Versão sem o 9: 1991131354
    const semNove = apenasNumeros.substring(0, 2) + apenasNumeros.substring(3);
    if (semNove.length === 10) {
      variacoes.add(semNove);
      variacoes.add('55' + semNove); // Com código país: 551991131354
    }
    
  } else if (apenasNumeros.length === 10) {
    // Formato: 1991131354 (DDD + 8 dígitos)
    variacoes.add(apenasNumeros); // Original
    variacoes.add('55' + apenasNumeros); // Com código país: 551991131354
    
    // Versão com 9: 19991131354
    const comNove = apenasNumeros.substring(0, 2) + '9' + apenasNumeros.substring(2);
    if (comNove.length === 11) {
      variacoes.add(comNove);
      variacoes.add('55' + comNove); // Com código país: 5519991131354
    }
  }
  
  const resultado = Array.from(variacoes).filter(v => v.length >= 10);
  console.log(`📱 Variações geradas: [${resultado.join(', ')}]`);
  
  return resultado;
}

// Função para buscar persons por uma variação específica de telefone
async function buscarPersonsPorVariacao(telefoneVariacao: string): Promise<number[]> {
  try {
    const crmToken = process.env.CRM_TOKEN;
    if (!crmToken) {
      throw new Error("CRM_TOKEN não configurada");
    }

    const url = `https://api.pipe.run/v1/contactPhones?show=200&phone=${telefoneVariacao}&cursor=1`;
    
    const response = await axios.get(url, {
      headers: {
        token: crmToken,
      },
      timeout: 15000,
    });

    if (!response.data.success) {
      return [];
    }

    // Extrair person_ids únicos
    const personIds = new Set<number>();
    
    for (const contact of response.data.data) {
      if (contact.person_id) {
        personIds.add(contact.person_id);
      }
    }

    const resultado = Array.from(personIds);
    
    if (resultado.length > 0) {
      console.log(`   🎯 Variação ${telefoneVariacao}: ${resultado.length} persons [${resultado.join(', ')}]`);
    }

    return resultado;

  } catch (error: any) {
    console.error(`❌ Erro ao buscar variação ${telefoneVariacao}:`, error.message);
    return [];
  }
}

// Função para buscar persons por telefone no CRM (versão exaustiva)
async function buscarPersonsPorTelefone(telefone: string): Promise<number[]> {
  try {
    console.log(`🔍 Busca exaustiva para telefone: ${telefone}`);
    
    // 1. Gerar todas as variações possíveis
    const variacoes = gerarVariacoesTelefone(telefone);
    
    if (variacoes.length === 0) {
      console.log(`❌ Nenhuma variação válida gerada para: ${telefone}`);
      return [];
    }
    
    // 2. Buscar por cada variação
    const todasPersonsEncontradas = new Set<number>();
    
    for (let i = 0; i < variacoes.length; i++) {
      const variacao = variacoes[i];
      console.log(`   📞 Testando variação ${i + 1}/${variacoes.length}: ${variacao}`);
      
      const personsVariacao = await buscarPersonsPorVariacao(variacao);
      personsVariacao.forEach(id => todasPersonsEncontradas.add(id));
      
      // Pausa entre variações para não sobrecarregar API
      if (i < variacoes.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 300));
      }
    }
    
    const resultado = Array.from(todasPersonsEncontradas);
    console.log(`📊 Total de persons encontradas para ${telefone}: ${resultado.length} [${resultado.join(', ')}]`);
    
    return resultado;

  } catch (error: any) {
    console.error(`❌ Erro geral ao buscar persons para telefone ${telefone}:`, error.message);
    return [];
  }
}

// Função para atualizar campo personalizado de uma person
async function atualizarCampoPersonalizado(personId: number, linkEncurtado: string): Promise<boolean> {
  try {
    const crmToken = process.env.CRM_TOKEN;
    if (!crmToken) {
      throw new Error("CRM_TOKEN não configurada");
    }

    const url = `https://api.pipe.run/v1/persons/${personId}`;
    
    const payload = {
      custom_fields: [
        {
          id: 715633,
          value: linkEncurtado
        }
      ]
    };

    const response = await axios.put(url, payload, {
      headers: {
        'accept': 'application/json',
        'content-type': 'application/json',
        'token': crmToken,
      },
      timeout: 15000,
    });

    if (response.status === 200 || response.status === 201) {
      return true;
    } else {
      console.log(`⚠️ Person ${personId}: resposta inesperada (${response.status})`);
      return false;
    }

  } catch (error: any) {
    console.error(`❌ Erro ao atualizar person ${personId}:`, error.message);
    return false;
  }
}

// Função principal para atualizar todas as persons relacionadas aos telefones do cliente
async function atualizarPersonsCliente(cliente: any): Promise<{
  sucesso: boolean;
  telefonesProcesados: number;
  personsAtualizadas: number;
  personsComErro: number;
}> {
  try {
    // 1. Obter telefones do cliente
    const contatos = await prisma.contatoCliente.findMany({
      where: { clienteId: cliente.id }
    });
    
    const telefones = obterListaTelefones(contatos);
    
    if (telefones.length === 0) {
      return { sucesso: true, telefonesProcesados: 0, personsAtualizadas: 0, personsComErro: 0 };
    }

    // 2. Extrair link encurtado
    const linkEncurtado = cliente.autoLoginUrl || '';
    
    if (!linkEncurtado) {
      return { sucesso: true, telefonesProcesados: 0, personsAtualizadas: 0, personsComErro: 0 };
    }

    console.log(`🔗 Cliente ${cliente.id}: Link "${linkEncurtado}" para ${telefones.length} telefones`);

    // 3. Coletar todas as persons únicas de todos os telefones
    const todasPersonsUnicas = new Set<number>();
    let telefonesProcesados = 0;

    for (const telefone of telefones) {
      const personIds = await buscarPersonsPorTelefone(telefone);
      
      personIds.forEach(id => todasPersonsUnicas.add(id));
      telefonesProcesados++;

      // Pequena pausa entre buscas para não sobrecarregar API
      if (telefonesProcesados < telefones.length) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    const personsParaAtualizar = Array.from(todasPersonsUnicas);

    if (personsParaAtualizar.length === 0) {
      return { sucesso: true, telefonesProcesados, personsAtualizadas: 0, personsComErro: 0 };
    }

    console.log(`👥 Cliente ${cliente.id}: ${personsParaAtualizar.length} persons encontradas [${personsParaAtualizar.slice(0, 5).join(', ')}${personsParaAtualizar.length > 5 ? '...' : ''}]`);

    // 4. Atualizar cada person
    let personsAtualizadas = 0;
    let personsComErro = 0;

    for (const personId of personsParaAtualizar) {
      const sucesso = await atualizarCampoPersonalizado(personId, linkEncurtado);
      
      if (sucesso) {
        personsAtualizadas++;
      } else {
        personsComErro++;
      }

      // Pausa entre atualizações para não sobrecarregar API
      await new Promise(resolve => setTimeout(resolve, 800));
    }

    return {
      sucesso: true,
      telefonesProcesados,
      personsAtualizadas,
      personsComErro
    };

  } catch (error: any) {
    console.error(`❌ Erro geral na atualização do cliente ${cliente.id}:`, error.message);
    return { sucesso: false, telefonesProcesados: 0, personsAtualizadas: 0, personsComErro: 0 };
  }
}

// Interface para estatísticas
interface EstatisticasGerais {
  totalClientes: number;
  clientesComTelefone: number;
  clientesSemTelefone: number;
  clientesProcessados: number;
  clientesComErro: number;
  totalPersonsAtualizadas: number;
  totalPersonsComErro: number;
  totalTelefonesProcesados: number;
}

// Função para confirmar execução
function confirmarExecucao(): Promise<boolean> {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  return new Promise((resolve) => {
    console.log('\n⚠️  ATENÇÃO: Este script irá atualizar persons no CRM para TODOS os clientes existentes.');
    console.log('⚠️  Esta é uma operação que pode demorar bastante e fará muitas requisições à API.');
    console.log('⚠️  Certifique-se de que o ambiente está configurado corretamente.\n');
    
    rl.question('Você tem certeza que deseja continuar? (digite "SIM" para confirmar): ', (resposta) => {
      rl.close();
      resolve(resposta.toUpperCase() === 'SIM');
    });
  });
}

// Função principal
async function main() {
  try {
    Logger.section("🔄 ATUALIZAÇÃO RETROATIVA DE PERSONS NO CRM");
    
    // 1. Confirmar execução
    const confirmado = await confirmarExecucao();
    if (!confirmado) {
      console.log('❌ Operação cancelada pelo usuário.');
      return;
    }
    
    // 2. Buscar apenas clientes com autoLoginUrl (otimização)
    Logger.info("📊 Buscando clientes com autoLoginUrl no banco de dados...");
    
    const todosClientes = await prisma.cliente.findMany({
      where: {
        AND: [
          { autoLoginUrl: { not: null } },
          { autoLoginUrl: { not: '' } }
        ]
      },
      include: {
        contatos: true
      },
      orderBy: { id: 'asc' }
    });
    
    if (todosClientes.length === 0) {
      Logger.warn("❌ Nenhum cliente encontrado no banco de dados.");
      return;
    }
    
    // 3. Análise prévia (todos já têm autoLoginUrl devido ao filtro)
    const clientesComTelefone = todosClientes.filter(c => c.contatos.some(contato => 
      (contato.telefone && contato.telefone.replace(/\D/g, '').length >= 10) ||
      (contato.telefoneSegundario && contato.telefoneSegundario.replace(/\D/g, '').length >= 10)
    ));
    const clientesSemTelefone = todosClientes.filter(c => !c.contatos.some(contato => 
      (contato.telefone && contato.telefone.replace(/\D/g, '').length >= 10) ||
      (contato.telefoneSegundario && contato.telefoneSegundario.replace(/\D/g, '').length >= 10)
    ));
    
    // Clientes processáveis = todos com telefone (já têm autoLoginUrl)
    const clientesProcessaveis = clientesComTelefone;
    
    Logger.info("📊 ANÁLISE PRÉVIA:");
    Logger.info(`   • Clientes com autoLoginUrl: ${todosClientes.length}`);
    Logger.info(`   • Com telefone válido: ${clientesComTelefone.length}`);
    Logger.info(`   • Sem telefone válido: ${clientesSemTelefone.length}`);
    Logger.info(`   • 🎯 Processáveis (link + telefone): ${clientesProcessaveis.length}`);
    
    if (clientesProcessaveis.length === 0) {
      Logger.warn("❌ Nenhum cliente processável encontrado (precisam ter autoLoginUrl E telefone).");
      return;
    }
    
    // 4. Processar clientes
    Logger.section(`🚀 PROCESSANDO ${clientesProcessaveis.length} CLIENTES`);
    
    const estatisticas: EstatisticasGerais = {
      totalClientes: todosClientes.length,
      clientesComTelefone: clientesComTelefone.length,
      clientesSemTelefone: clientesSemTelefone.length,
      clientesProcessados: 0,
      clientesComErro: 0,
      totalPersonsAtualizadas: 0,
      totalPersonsComErro: 0,
      totalTelefonesProcesados: 0
    };
    
    for (let i = 0; i < clientesProcessaveis.length; i++) {
      const cliente = clientesProcessaveis[i];
      const progresso = `[${i + 1}/${clientesProcessaveis.length}]`;
      
      console.log(`\n${progresso} 🔄 Processando cliente ${cliente.id} (${cliente.nome})...`);
      
      try {
        const resultado = await atualizarPersonsCliente(cliente);
        
        estatisticas.clientesProcessados++;
        estatisticas.totalPersonsAtualizadas += resultado.personsAtualizadas;
        estatisticas.totalPersonsComErro += resultado.personsComErro;
        estatisticas.totalTelefonesProcesados += resultado.telefonesProcesados;
        
        if (resultado.personsAtualizadas > 0) {
          console.log(`${progresso} ✅ Cliente ${cliente.id}: ${resultado.personsAtualizadas} persons atualizadas`);
        } else {
          console.log(`${progresso} ⏭️ Cliente ${cliente.id}: nenhuma person para atualizar`);
        }
        
      } catch (error: any) {
        estatisticas.clientesComErro++;
        console.error(`${progresso} ❌ Cliente ${cliente.id}: ${error.message}`);
      }
      
      // Pausa entre clientes para não sobrecarregar APIs
      if (i < clientesProcessaveis.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
      // Log de progresso a cada 10 clientes
      if ((i + 1) % 10 === 0 || i === clientesProcessaveis.length - 1) {
        Logger.info(`📊 Progresso: ${i + 1}/${clientesProcessaveis.length} clientes processados`);
        Logger.info(`   • Persons atualizadas: ${estatisticas.totalPersonsAtualizadas}`);
        Logger.info(`   • Persons com erro: ${estatisticas.totalPersonsComErro}`);
      }
    }
    
    // 5. Relatório final
    Logger.section("📊 RELATÓRIO FINAL");
    Logger.success(`✅ Processamento concluído!`);
    Logger.info(`📈 Estatísticas:`);
    Logger.info(`   • Total de clientes no banco: ${estatisticas.totalClientes}`);
    Logger.info(`   • Clientes processáveis: ${clientesProcessaveis.length}`);
    Logger.info(`   • Clientes processados: ${estatisticas.clientesProcessados}`);
    Logger.info(`   • Clientes com erro: ${estatisticas.clientesComErro}`);
    Logger.info(`   • Total de telefones processados: ${estatisticas.totalTelefonesProcesados}`);
    Logger.info(`   • 🎯 Total de persons atualizadas: ${estatisticas.totalPersonsAtualizadas}`);
    Logger.info(`   • ❌ Total de persons com erro: ${estatisticas.totalPersonsComErro}`);
    
    const sucesso = (estatisticas.totalPersonsAtualizadas / (estatisticas.totalPersonsAtualizadas + estatisticas.totalPersonsComErro)) * 100;
    Logger.info(`   • Taxa de sucesso: ${sucesso.toFixed(1)}%`);
    
    console.log('\n🎉 Script concluído com sucesso!');
    
  } catch (error: any) {
    Logger.error("❌ Erro durante a execução:", error.message);
    if (error.stack) {
      Logger.error("Stack trace:", error.stack);
    }
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  main();
}

export { main as atualizarPersonsClientesExistentes }; 