#!/usr/bin/env python3
"""
Script para testar extração de dados dos titulares de PDFs
Coloque um PDF na mesma pasta e execute: python test_titulares.py nome_do_arquivo.pdf
"""

import sys
import os
import re
import fitz  # PyMuPDF

def extract_titulares_data(pdf_path: str) -> list:
    """
    Extrai dados dos titulares/requerentes do PDF
    """
    try:
        # Abrir PDF
        pdf_document = fitz.open(pdf_path)
        titulares = []
        
        print(f"📄 Processando PDF: {pdf_path}")
        print(f"📊 Total de páginas: {pdf_document.page_count}")
        
        # Buscar nas primeiras páginas onde geralmente estão os dados dos requerentes
        for page_num in range(min(5, pdf_document.page_count)):
            page = pdf_document[page_num]
            text = page.get_text()
            
            print(f"\n🔍 Processando página {page_num + 1}...")
            
            # Dividir texto em linhas
            lines = text.split('\n')
            
            # Procurar seção "Dados do(s) requerente(s)"
            section_found = False
            for i, line in enumerate(lines):
                line_clean = line.strip()
                
                if re.search(r"Dados\s+do\(s\)\s+requerente\(s\)", line_clean, re.IGNORECASE):
                    print(f"✅ Encontrou seção 'Dados do(s) requerente(s)' na linha {i}")
                    section_found = True
                    
                    # Processar a partir da próxima linha após a linha divisória
                    start_index = i + 1
                    
                    # Pular linha divisória se existir
                    while start_index < len(lines) and lines[start_index].strip() in ['', '----------', '─' * 10]:
                        start_index += 1
                    
                    print(f"🚀 Iniciando parsing a partir da linha {start_index}")
                    
                    # Processar dados dos titulares
                    titulares = parse_titulares_section(lines[start_index:])
                    break
            
            if section_found:
                break
        
        pdf_document.close()
        
        if not titulares:
            print("⚠️ Nenhum titular encontrado no PDF")
        else:
            print(f"🎉 Total de {len(titulares)} titular(es) encontrado(s)")
        
        return titulares
        
    except Exception as e:
        print(f"❌ Erro ao extrair dados dos titulares: {e}")
        return []

def parse_titulares_section(lines: list) -> list:
    """
    Analisa a seção de titulares e extrai dados de cada um
    Usa uma abordagem mais robusta para lidar com texto desordenado
    """
    titulares = []
    
    print(f"\n📋 Analisando {len(lines)} linhas da seção de titulares...")
    
    # Primeiro, vamos mapear todos os dados encontrados
    all_data = []
    
    try:
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Pular linhas vazias
            if not line:
                i += 1
                continue
            
            print(f"   Linha {i}: '{line}'")
            
            # Parar se chegou em seções definitivamente não-titulares
            if re.search(r"(Dados\s+da\s+Marca|Apresentação\s+da\s+Marca|Produto|Serviço)", line, re.IGNORECASE):
                print(f"🛑 Encontrou seção não-titular: '{line}' - parando parsing")
                break
            
            # Pular campos internos
            if re.search(r"(Natureza\s+Jurídica|Tipo\s+de\s+Pessoa|e-mail:)", line, re.IGNORECASE):
                print(f"   ⏭️ Pulando campo interno: '{line}'")
                i += 1
                continue
            
            # Mapear dados encontrados
            data_item = {'line_num': i, 'content': line, 'type': 'unknown'}
            
            if line.lower().startswith('nome:'):
                nome = line[5:].strip()
                if nome:
                    data_item['type'] = 'nome'
                    data_item['value'] = nome
                    print(f"   📝 Mapeado Nome: {nome}")
                
            elif re.search(r"CPF/CNPJ/N[úu]mero\s+INPI:", line, re.IGNORECASE):
                documento = re.sub(r"CPF/CNPJ/N[úu]mero\s+INPI:\s*", "", line, flags=re.IGNORECASE).strip()
                if documento:
                    data_item['type'] = 'documento'
                    data_item['value'] = documento
                    print(f"   📝 Mapeado Documento: {documento}")
                
            elif line.lower().startswith('endereço:'):
                endereco = line[9:].strip()
                if endereco:
                    data_item['type'] = 'endereco'
                    data_item['value'] = endereco
                    print(f"   📝 Mapeado Endereço: {endereco}")
                
            elif line.lower().startswith('cidade:'):
                cidade = line[7:].strip()
                if cidade:
                    data_item['type'] = 'cidade'
                    data_item['value'] = cidade
                    print(f"   📝 Mapeado Cidade: {cidade}")
                
            elif line.lower().startswith('estado:'):
                estado = line[7:].strip()
                if estado:
                    data_item['type'] = 'uf'
                    data_item['value'] = estado
                    print(f"   📝 Mapeado Estado: {estado}")
                
            elif line.lower().startswith('cep:'):
                cep = line[4:].strip()
                if cep:
                    data_item['type'] = 'cep'
                    data_item['value'] = cep
                    print(f"   📝 Mapeado CEP: {cep}")
                
            elif line.lower().startswith('pais:') or line.lower().startswith('país:'):
                pais = re.sub(r"Pa[íi]s:\s*", "", line, flags=re.IGNORECASE).strip()
                if pais:
                    data_item['type'] = 'pais'
                    data_item['value'] = pais
                    print(f"   📝 Mapeado País: {pais}")
            
            # Detectar valores soltos que podem ser de campos anteriores
            elif re.match(r'^[0-9]{11}$|^[0-9]{14}$|^[0-9]{8,15}$', line):
                # Possível CPF/CNPJ solto
                data_item['type'] = 'documento_solto'
                data_item['value'] = line
                print(f"   📝 Possível documento solto: {line}")
                
            elif re.match(r'^[0-9]{5}-?[0-9]{3}$', line):
                # Possível CEP solto
                data_item['type'] = 'cep_solto'
                data_item['value'] = line
                print(f"   📝 Possível CEP solto: {line}")
                
            elif line in ['Brasil', 'Argentina', 'Estados Unidos', 'Chile', 'Uruguai']:
                # Possível país solto
                data_item['type'] = 'pais_solto'
                data_item['value'] = line
                print(f"   📝 Possível país solto: {line}")
                
            elif line in ['SP', 'RJ', 'MG', 'RS', 'PR', 'SC', 'BA', 'GO', 'PE', 'CE', 'PA', 'MA', 'PB', 'ES', 'PI', 'AL', 'RN', 'MT', 'MS', 'DF', 'SE', 'RO', 'AC', 'AM', 'RR', 'AP', 'TO']:
                # Possível estado solto
                data_item['type'] = 'uf_solto'
                data_item['value'] = line
                print(f"   📝 Possível estado solto: {line}")
                
            elif line in ['São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Salvador', 'Brasília', 'Fortaleza', 'Manaus', 'Curitiba', 'Recife', 'Goiânia', 'Belém', 'Porto Alegre', 'Guarulhos', 'Campinas', 'São Luís', 'São Gonçalo', 'Maceió', 'Duque de Caxias', 'Campo Grande', 'Natal', 'Teresina', 'São Bernardo do Campo', 'Nova Iguaçu', 'João Pessoa', 'Santo André', 'Osasco']:
                # Possível cidade solto
                data_item['type'] = 'cidade_solto'
                data_item['value'] = line
                print(f"   📝 Possível cidade solto: {line}")
            
            all_data.append(data_item)
            i += 1
        
        # Agora vamos reconstruir os titulares a partir dos dados mapeados
        print(f"\n🔄 Reconstruindo titulares a partir de {len(all_data)} itens mapeados...")
        
        current_titular = {}
        
        for item in all_data:
            if item['type'] == 'nome' and 'value' in item:
                # Salvar titular anterior se existir
                if current_titular and 'nome' in current_titular:
                    titulares.append(current_titular.copy())
                    print(f"✅ Titular {len(titulares)} reconstruído: {current_titular['nome']}")
                
                # Iniciar novo titular
                current_titular = {'nome': item['value']}
                print(f"   🆕 Novo titular iniciado: {item['value']}")
                
            elif 'value' in item and current_titular:
                # Adicionar dados ao titular atual
                if item['type'] == 'documento':
                    current_titular['numeroDocumento'] = item['value']
                elif item['type'] == 'endereco':
                    current_titular['endereco'] = item['value']
                elif item['type'] == 'cidade':
                    current_titular['cidade'] = item['value']
                elif item['type'] == 'uf':
                    current_titular['uf'] = item['value']
                elif item['type'] == 'cep':
                    current_titular['cep'] = item['value']
                elif item['type'] == 'pais':
                    current_titular['pais'] = item['value']
                # Tentar preencher campos vazios com valores soltos
                elif item['type'] == 'documento_solto' and 'numeroDocumento' not in current_titular:
                    current_titular['numeroDocumento'] = item['value']
                    print(f"   🔗 Documento solto atribuído: {item['value']}")
                elif item['type'] == 'cep_solto' and 'cep' not in current_titular:
                    current_titular['cep'] = item['value']
                    print(f"   🔗 CEP solto atribuído: {item['value']}")
                elif item['type'] == 'pais_solto' and 'pais' not in current_titular:
                    current_titular['pais'] = item['value']
                    print(f"   🔗 País solto atribuído: {item['value']}")
                elif item['type'] == 'uf_solto' and 'uf' not in current_titular:
                    current_titular['uf'] = item['value']
                    print(f"   🔗 Estado solto atribuído: {item['value']}")
                elif item['type'] == 'cidade_solto' and 'cidade' not in current_titular:
                    current_titular['cidade'] = item['value']
                    print(f"   🔗 Cidade solto atribuído: {item['value']}")
        
        # Adicionar último titular se existir
        if current_titular and 'nome' in current_titular:
            titulares.append(current_titular.copy())
            print(f"✅ Último titular reconstruído: {current_titular['nome']}")
        
    except Exception as e:
        print(f"❌ Erro ao analisar seção de titulares: {e}")
    
    return titulares

def main():
    if len(sys.argv) != 2:
        print("❌ Uso: python test_titulares.py <arquivo.pdf>")
        print("📝 Exemplo: python test_titulares.py protocolo.pdf")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    
    if not os.path.exists(pdf_file):
        print(f"❌ Arquivo não encontrado: {pdf_file}")
        sys.exit(1)
    
    print("🚀 Iniciando teste de extração de titulares...")
    print("=" * 60)
    
    # Extrair titulares
    titulares = extract_titulares_data(pdf_file)
    
    print("\n" + "=" * 60)
    print("📊 RESULTADO FINAL:")
    print("=" * 60)
    
    if titulares:
        for i, titular in enumerate(titulares, 1):
            print(f"\n👤 TITULAR {i}:")
            print(f"   Nome: {titular.get('nome', 'N/A')}")
            print(f"   Documento: {titular.get('numeroDocumento', 'N/A')}")
            print(f"   Endereço: {titular.get('endereco', 'N/A')}")
            print(f"   Cidade: {titular.get('cidade', 'N/A')}")
            print(f"   Estado: {titular.get('uf', 'N/A')}")
            print(f"   CEP: {titular.get('cep', 'N/A')}")
            print(f"   País: {titular.get('pais', 'N/A')}")
    else:
        print("❌ Nenhum titular encontrado!")
    
    print(f"\n🎯 Total: {len(titulares)} titular(es) extraído(s)")

if __name__ == "__main__":
    main() 