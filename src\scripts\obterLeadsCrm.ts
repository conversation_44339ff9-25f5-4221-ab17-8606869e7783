import { PrismaClient } from "@prisma/client";
import dotenv from "dotenv";
import cliProgress from "cli-progress";
import { buscaProcessosMonitorados } from "../services/monitoradosService";
import axios from "axios";
import fs from "fs";

dotenv.config();

const prisma = new PrismaClient();

// Função auxiliar para extrair DDD + últimos 8 dígitos do telefone
function extrairIdentificadorTelefone(telefone: string): string {
  if (!telefone || telefone === "NoPhone") {
    return "0000000000"; // Retorna 10 dígitos zerados quando não há telefone
  }

  // Remove todos os caracteres não numéricos
  const numeroLimpo = telefone.replace(/\D/g, '');
  
  // Se o número estiver vazio após limpeza
  if (!numeroLimpo) {
    return "0000000000";
  }

  let numeroProcessado = numeroLimpo;

  // Lista de DDDs válidos no Brasil (11-99, excluindo alguns não utilizados)
  const dddsValidos = new Set([
    11, 12, 13, 14, 15, 16, 17, 18, 19, // SP
    21, 22, 24, // RJ/ES
    27, 28, // ES
    31, 32, 33, 34, 35, 37, 38, // MG
    41, 42, 43, 44, 45, 46, // PR
    47, 48, 49, // SC
    51, 53, 54, 55, // RS
    61, // DF/GO
    62, 64, // GO
    63, // TO
    65, 66, // MT
    67, // MS
    68, // AC
    69, // RO
    71, 73, 74, 75, 77, // BA
    79, // SE
    81, 87, // PE
    82, // AL
    83, // PB
    84, // RN
    85, 88, // CE
    86, 89, // PI
    91, 93, 94, // PA
    92, 97, // AM
    95, // RR
    96, // AP
    98, 99 // MA
  ]);

  // Lógica melhorada para detecção de prefixo do país
  if (numeroProcessado.startsWith('55') && numeroProcessado.length >= 12) {
    // Verifica se após remover 55, temos um número válido brasileiro
    const semPrefixo = numeroProcessado.substring(2);
    
    // Número brasileiro tem 10 ou 11 dígitos (DDD + 8 ou 9 dígitos)
    if (semPrefixo.length === 10 || semPrefixo.length === 11) {
      // Verifica se o DDD é válido
      const possibleDDD = parseInt(semPrefixo.substring(0, 2));
      if (dddsValidos.has(possibleDDD)) {
        numeroProcessado = semPrefixo;
      }
    }
  }

  // Se ainda não temos pelo menos 10 dígitos, preenche com zeros
  if (numeroProcessado.length < 10) {
    numeroProcessado = numeroProcessado.padStart(10, '0');
  }

  // Extrai DDD (primeiros 2 dígitos) e últimos 8 dígitos
  const ddd = numeroProcessado.substring(0, 2);
  const ultimosOitoDigitos = numeroProcessado.slice(-8);
  
  return ddd + ultimosOitoDigitos;
}

// Controle de taxa para API do CRM
class RateLimiter {
  private queue: (() => Promise<void>)[] = [];
  private running = false;
  private requestCount = 0;
  private resetTime = Date.now() + 30000; // 30 segundos
  private maxRequests: number;
  private timeWindow: number;

  constructor(maxRequests = 120, timeWindow = 30000) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindow;
  }

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await fn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      if (!this.running) {
        this.processQueue();
      }
    });
  }

  private async processQueue() {
    if (this.queue.length === 0) {
      this.running = false;
      return;
    }

    this.running = true;

    // Verificar se precisamos resetar o contador
    const now = Date.now();
    if (now >= this.resetTime) {
      this.requestCount = 0;
      this.resetTime = now + this.timeWindow;
    }

    // Verificar se atingimos o limite
    if (this.requestCount >= this.maxRequests) {
      const waitTime = this.resetTime - now;
      console.log(`Limite de requisições atingido. Aguardando ${waitTime / 1000} segundos...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      this.requestCount = 0;
      this.resetTime = Date.now() + this.timeWindow;
    }

    // Executar a próxima função na fila
    const nextFn = this.queue.shift();
    if (nextFn) {
      this.requestCount++;
      await nextFn();
    }

    // Processar o próximo item na fila
    this.processQueue();
  }
}

// Instância do limitador de taxa
const rateLimiter = new RateLimiter(120, 30000);

// Função para buscar leads por número de processo com retry automático
async function buscarLeadPorProcesso(numeroProcesso: string, maxRetries = 5): Promise<any[]> {
  const url_base = `https://api.pipe.run/v1/deals?`;
  const crmToken = process.env.CRM_TOKEN || "";
  const idCampoPersonalizadoNumProcesso = 194250;
  
  let tentativas = 0;
  
  while (tentativas <= maxRetries) {
    try {
      return await rateLimiter.execute(async () => {
        const { data } = await axios.get(
          `${url_base}token=${crmToken}&custom_fields[${idCampoPersonalizadoNumProcesso}]=${numeroProcesso}&with=person.contactPhones,person.contactEmails,customFields`,
          {
            headers: {
              token: crmToken,
            },
          }
        );
        
        if (data.success === false) {
          throw new Error(`Erro na API: ${data.message}`);
        }
        
        return data.data || [];
      });
    } catch (error: any) {
      tentativas++;
      
      // Se for erro 429 (Too Many Requests), 500 (Erro interno do servidor), 502 (Bad Gateway), 503 (Serviço indisponível) ou 504 (Gateway Timeout)
      if (error.response?.status === 429 || 
          error.response?.status === 500 ||  // Erro interno do servidor
          error.response?.status === 502 ||  // Bad Gateway
          error.response?.status === 503 ||  // Serviço indisponível
          error.response?.status === 504 ||  // Gateway Timeout
          error.message.includes("Too Many Attempts") || 
          error.message.includes("timeout") ||
          error.message.includes("network error")) {
        
        // Calcular tempo de espera com backoff exponencial
        const tempoEspera = Math.min(Math.pow(2, tentativas) * 1000, 60000); // Máximo de 1 minuto
        console.log(`Erro ao buscar lead para o processo ${numeroProcesso} (tentativa ${tentativas}/${maxRetries}): ${error.message}`);
        console.log(`Aguardando ${tempoEspera/1000} segundos antes de tentar novamente...`);
        
        // Esperar antes de tentar novamente
        await new Promise(resolve => setTimeout(resolve, tempoEspera));
      } else if (tentativas >= maxRetries) {
        // Se atingiu o número máximo de tentativas, registra o erro e retorna vazio
        console.error(`Erro ao buscar lead para o processo ${numeroProcesso} após ${maxRetries} tentativas: ${error.message}`);
        return [];
      } else {
        // Para outros erros, tenta novamente com menos espera
        const tempoEspera = 1000 * tentativas;
        console.log(`Erro ao buscar lead para o processo ${numeroProcesso} (tentativa ${tentativas}/${maxRetries}): ${error.message}`);
        console.log(`Aguardando ${tempoEspera/1000} segundos antes de tentar novamente...`);
        await new Promise(resolve => setTimeout(resolve, tempoEspera));
      }
    }
  }
  
  return [];
}

// Função principal para buscar dados do CRM
export const buscarDadosCrm = async () => {
  const BATCH_SIZE = 10; // Tamanho do batch reduzido para evitar sobrecarga
  const startTime = Date.now();

  try {
    console.log("Iniciando a busca de processos monitorados no banco de dados...");
    const processosMonitorados = (await buscaProcessosMonitorados()) || [];

    console.log(`Processos monitorados encontrados: ${processosMonitorados.length}`);

    const oportunidadesEncontradas: any[] = [];
    const processosMonitoradosNaoEncontrados: any[] = [];
    const clientesParaAtualizar: any[] = [];

    console.log("Inicializando processamento dos processos monitorados...");

    // Inicializar a barra de progresso
    const progressBar = new cliProgress.SingleBar({
      format: "Progresso |{bar}| {percentage}% || {value}/{total} Processos",
      barCompleteChar: "\u2588",
      barIncompleteChar: "\u2591",
      hideCursor: true,
    });

    progressBar.start(processosMonitorados.length, 0);

    // Processar processos monitorados em batches
    for (let i = 0; i < processosMonitorados.length; i += BATCH_SIZE) {
      const batch = processosMonitorados.slice(i, i + BATCH_SIZE);
      
      // Processar cada processo no batch
      const batchPromises = batch.map(async (processoMonitorado) => {
        const { numero, id } = processoMonitorado;
        
        // Buscar leads diretamente da API para este processo com retry
        const leads = await buscarLeadPorProcesso(numero);
        
        if (leads && leads.length > 0) {
          // Usar o primeiro lead encontrado
          const lead = leads[0];
          const { customFields, person } = lead;
          
          // Obter o telefone principal
          const telefone = person?.contactPhones?.find((p: { is_main: number; }) => p.is_main === 1)?.phone || 
                          person?.contactPhones?.[0]?.phone ||
                          "NoPhone";

          // Obter telefone secundário (se houver)
          const telefonesSecundarios = person?.contactPhones?.filter((p: { is_main: number; }) => p.is_main !== 1) || [];
          const telefoneSecundario = telefonesSecundarios[0]?.phone || null;

          // Obter e-mail principal (se houver)
          const emails = person?.contactEmails || [];
          const emailPrincipal = emails[0]?.email || null;

          // Verifica se o lead tem o número do processo e o número de telefone
          if (telefone) {
            oportunidadesEncontradas.push(lead);

            // Criar objeto cliente de acordo com o schema do Prisma
            const clienteData = {
              crmId: parseInt(lead.id),
              nome: person?.name || "Nome não informado",
              crmStageId: lead.stage_id.toString(),
              nomeDaMarca:
                customFields?.find((field: any) => field.id === 213657)
                  ?.value || "Marca não informada",
              identificador: extrairIdentificadorTelefone(telefone),
              numeroDocumento: person?.cpf || "CPF não informado",
              tipoDeDocumento: "CPF",
              // Relacionamento com o processo
              processos: {
                connect: { id }
              },
              // Criar contato do cliente
              contatos: {
                create: {
                  telefone: telefone,
                  telefoneSegundario: telefoneSecundario || "Não informado",
                  endereco: person?.address
                    ? `${person.address} ${person.address_number || ''}`
                    : "Endereço não informado",
                  cep: person?.address_postal_code || "CEP não informado",
                  email: emailPrincipal || "Email não informado",
                  cidade: person?.district || null,
                  estado: null // API não parece fornecer o estado diretamente
                }
              }
            };

            clientesParaAtualizar.push(clienteData);
          }
        } else {
          processosMonitoradosNaoEncontrados.push(processoMonitorado);
        }
        
        // Atualiza a barra de progresso
        progressBar.increment();
        
        // Log detalhado a cada 50 processos
        if ((i + batch.indexOf(processoMonitorado) + 1) % 50 === 0) {
          console.log(`\n📊 Progresso: ${i + batch.indexOf(processoMonitorado) + 1}/${processosMonitorados.length}`);
          console.log(`   • Oportunidades encontradas: ${oportunidadesEncontradas.length}`);
          console.log(`   • Processos sem lead: ${processosMonitoradosNaoEncontrados.length}`);
        }
      });

      // Executa as promessas do batch
      await Promise.all(batchPromises);
      console.log(`Batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(processosMonitorados.length / BATCH_SIZE)} processado.`);
    }
    
    // Finaliza a barra de progresso
    progressBar.stop();

    const endTime = Date.now();
    const executionTime = (endTime - startTime) / 1000;
    console.log(`Tempo de execução: ${executionTime} segundos`);
    console.log(`Total de oportunidades encontradas: ${oportunidadesEncontradas.length}`);
    console.log(`Total de processos não encontrados: ${processosMonitoradosNaoEncontrados.length}`);

    return { 
      oportunidadesEncontradas, 
      processosMonitoradosNaoEncontrados,
      clientesParaAtualizar,
      processosMonitorados
    };
  } catch (error) {
    console.error("Ocorreu um erro:", error);
    return {
      oportunidadesEncontradas: [],
      processosMonitoradosNaoEncontrados: [],
      clientesParaAtualizar: [],
      processosMonitorados: []
    };
  } finally {
    await prisma.$disconnect();
  }
};

// Função para atualizar clientes no banco de dados - melhorada
async function atualizarClientesNoBanco(clientesParaAtualizar: any[]) {
  console.log(`Iniciando atualização de ${clientesParaAtualizar.length} clientes no banco de dados...`);
  
  let atualizados = 0;
  let criados = 0;
  let erros = 0;
  
  const progressBar = new cliProgress.SingleBar({
    format: "Atualizando clientes |{bar}| {percentage}% || {value}/{total}",
    barCompleteChar: "\u2588",
    barIncompleteChar: "\u2591",
    hideCursor: true,
  });
  progressBar.start(clientesParaAtualizar.length, 0);
  
  for (const clienteDataInput of clientesParaAtualizar) {
    try {
      let clienteExistente = await prisma.cliente.findFirst({
        where: { identificador: clienteDataInput.identificador },
        include: { processos: true, contatos: true }
      });

      if (!clienteExistente && clienteDataInput.numeroDocumento && clienteDataInput.numeroDocumento !== "CPF não informado") {
        clienteExistente = await prisma.cliente.findFirst({
          where: { numeroDocumento: clienteDataInput.numeroDocumento },
          include: { processos: true, contatos: true }
        });
      }

      const taxaConcessaoPaga = clienteDataInput.crmStageId === "280246";
      
      if (clienteExistente) {
        // Cliente encontrado - Atualizar e/ou conectar novo processo/contato
        const contatoJaExiste = clienteExistente.contatos.find(
          c => c.telefone === clienteDataInput.contatos.create.telefone
        );
        
        await prisma.cliente.update({
          where: { id: clienteExistente.id },
          data: {
            nome: clienteDataInput.nome,
            crmStageId: clienteDataInput.crmStageId, // Pode ser útil manter o stage do último lead processado
            nomeDaMarca: clienteDataInput.nomeDaMarca,
            // Atualizar documento se o novo for mais completo ou diferente
            numeroDocumento: (clienteDataInput.numeroDocumento && clienteDataInput.numeroDocumento !== "CPF não informado") 
                             ? clienteDataInput.numeroDocumento 
                             : clienteExistente.numeroDocumento,
            tipoDeDocumento: (clienteDataInput.numeroDocumento && clienteDataInput.numeroDocumento !== "CPF não informado") 
                             ? clienteDataInput.tipoDeDocumento 
                             : clienteExistente.tipoDeDocumento,
            // Conectar o processo do lead atual
            processos: {
              connect: { id: clienteDataInput.processos.connect.id }
            },
            // Adicionar novo contato se não existir um com o mesmo telefone principal
            ...(contatoJaExiste ? {} : {
              contatos: {
                create: clienteDataInput.contatos.create
              }
            })
          }
        });

        // Atualizar o status do processo específico que está sendo processado
        await prisma.processo.update({
          where: { id: clienteDataInput.processos.connect.id },
          data: { taxaConcessaoPaga }
        });

        atualizados++;
      } else {
        // Cliente não encontrado - Criar novo cliente
        await prisma.cliente.create({
          data: {
            crmId: clienteDataInput.crmId, // Usar o crmId do lead atual
            nome: clienteDataInput.nome,
            crmStageId: clienteDataInput.crmStageId,
            nomeDaMarca: clienteDataInput.nomeDaMarca,
            identificador: clienteDataInput.identificador,
            numeroDocumento: clienteDataInput.numeroDocumento,
            tipoDeDocumento: clienteDataInput.tipoDeDocumento,
            contatos: clienteDataInput.contatos, // Já está formatado como { create: { ... } }
            processos: {
              connect: { id: clienteDataInput.processos.connect.id }
            }
          }
        });

        // Atualizar o status do processo recém-conectado
        await prisma.processo.update({
          where: { id: clienteDataInput.processos.connect.id },
          data: { taxaConcessaoPaga }
        });
        criados++;
      }
    } catch (error: any) {
      console.error(`\nErro ao atualizar/criar cliente (crmId: ${clienteDataInput.crmId}, identificador: ${clienteDataInput.identificador}):`, error.message);
      // Loggar mais detalhes do erro se for um erro do Prisma
      if (error.code) { // Erros do Prisma geralmente têm um código
        console.error(`   Prisma Error Code: ${error.code}`);
        if (error.meta) console.error(`   Meta: ${JSON.stringify(error.meta)}`);
      }
      erros++;
    }
    
    progressBar.increment();
  }
  
  progressBar.stop();
  console.log(`\n✅ Atualização concluída:`);
  console.log(`   • ${atualizados} clientes atualizados/consolidados`);
  console.log(`   • ${criados} novos clientes criados`);
  console.log(`   • ${erros} erros encontrados`);
}

// Função para gerar CSV com os dados processados
function gerarCsvSaida(clientesParaAtualizar: any[], processosMonitoradosGeral: any[]): string {
  console.log(`📊 Gerando arquivo CSV simplificado com ${clientesParaAtualizar.length} registros (pares cliente-processo desta execução)...`);
  
  // Cabeçalho do CSV
  const cabecalho = [
    'identificador',
    'numero_documento',
    'quantidade_processos_nesta_execucao' // Nome da coluna ajustado para clareza
  ];

  // Gerar linhas do CSV
  // Cada item em clientesParaAtualizar representa um cliente (novo ou existente) 
  // ao qual um processo específico (desta execução) foi conectado.
  // Portanto, a quantidade de processos aqui será sempre 1 por linha, 
  // refletindo a associação feita nesta execução do script.
  const linhas = clientesParaAtualizar.map(clienteData => {
    return [
      `"${clienteData.identificador || ''}"`,
      `"${(clienteData.numeroDocumento && clienteData.numeroDocumento !== "CPF não informado") ? clienteData.numeroDocumento : ''}"`,
      1 // Representa o processo associado nesta execução específica
    ].join(',');
  });

  // Combinar cabeçalho e dados
  const conteudoCsv = [cabecalho.join(','), ...linhas].join('\n');
  
  const nomeArquivo = `leads-crm-output-${new Date().toISOString().slice(0, 10)}-${Date.now()}.csv`;
  
  fs.writeFileSync(nomeArquivo, conteudoCsv, 'utf8');
  
  console.log(`✅ Arquivo CSV de saída gerado: ${nomeArquivo}`);
  console.log(`📂 Localização: ${process.cwd()}/${nomeArquivo}`);
  
  return nomeArquivo;
}

// Função principal para executar a busca de dados do CRM
async function main() {
  console.log("Iniciando busca de dados do CRM...");
  
  const resultado = await buscarDadosCrm();
  
  console.log("\n=== RESUMO DOS RESULTADOS ===");
  console.log(`Total de oportunidades encontradas: ${resultado.oportunidadesEncontradas.length}`);
  console.log(`Total de processos não encontrados: ${resultado.processosMonitoradosNaoEncontrados.length}`);
  console.log(`Total de clientes/processos para atualizar/criar: ${resultado.clientesParaAtualizar.length}`);
  
  if (resultado.clientesParaAtualizar.length > 0) {
    await atualizarClientesNoBanco(resultado.clientesParaAtualizar);
  }

  let nomeArquivoCsv = '';
  if (resultado.clientesParaAtualizar.length > 0) {
    // Passamos resultado.processosMonitorados para consistência, embora não seja usado diretamente no CSV simplificado.
    nomeArquivoCsv = gerarCsvSaida(resultado.clientesParaAtualizar, resultado.processosMonitorados);
    
    console.log("\n=== ARQUIVO CSV GERADO ===");
    console.log(`📊 Total de linhas (pares cliente-processo processados): ${resultado.clientesParaAtualizar.length}`);
    console.log(`📁 Arquivo: ${nomeArquivoCsv}`);
  }
}

// Executar a função principal se este arquivo for executado diretamente
if (require.main === module) {
  main().catch(error => {
    console.error("Erro ao executar a função principal:", error);
    process.exit(1);
  });
}
