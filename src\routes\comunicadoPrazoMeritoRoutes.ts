import { Router } from 'express';
import * as comunicadoController from '../controllers/comunicadoPrazoMeritoController';

const router = Router();

/**
 * @route   POST /api/crm/webhook/elegibilidade
 * @desc    Webhook para atualizar elegibilidade de um processo para comunicados quando um lead muda de etapa no CRM
 * @access  Privado
 */
router.post('/elegibilidade', comunicadoController.webhookEtapaCRM);

/**
 * @route   POST /api/comunicados/verificar
 * @desc    Verifica e envia comunicados pendentes manualmente
 * @access  Privado
 */
router.post('/verificar', comunicadoController.verificarEEnviarComunicadosController);

/**
 * @route   POST /api/comunicados/agendar
 * @desc    Inicializa o agendamento para verificação diária de comunicados
 * @access  Privado
 */
router.post('/agendar', comunicadoController.iniciarVerificacaoDiaria);

export default router; 