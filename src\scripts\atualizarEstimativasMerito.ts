import { PrismaClient } from '@prisma/client';
import { calcularEstimativasMeritoSimplificado } from '../services/meritoService';

const prisma = new PrismaClient();

async function atualizarEstimativasMerito() {
  try {
    console.log('Iniciando atualização de estimativas de mérito...');
    
    // Busca todos os processos onde somos procuradores
    const processos = await prisma.processo.findMany({
      where: {
        procurador: {
          nome: {
            contains: "REGISTRE-SE",
            mode: "insensitive"
          }
        }
      },
      include: {
        despachos: {
          include: {
            rpi: true
          },
          orderBy: {
            rpi: {
              dataPublicacao: 'asc'
            }
          }
        }
      }
    });

    console.log(`Encontrados ${processos.length} processos para atualização`);

    // Calcula as estimativas atuais
    const { estimativaMerito } = await calcularEstimativasMeritoSimplificado();

    let processosAtualizados = 0;
    let processosComSobrestamento = 0;
    let processosComOposicao = 0;
    let processosComExigencia = 0;
    let processosSemIntervencao = 0;
    let processosComDataDepositoEstimada = 0;
    let processosSemDataDeposito = 0;
    let processosSemIntervencaoSemDataDeposito = 0;

    for (const processo of processos) {
      // Se não tem data de depósito, tenta estimar baseado no despacho de publicação
      let dataDeposito = processo.dataDeposito;
      if (!dataDeposito) {
        const despachoPublicacao = processo.despachos.find(d => 
          d.nome?.toLowerCase().includes('publicação de pedido de registro para oposição')
        );
        
        if (despachoPublicacao?.rpi?.dataPublicacao) {
          dataDeposito = new Date(despachoPublicacao.rpi.dataPublicacao);
          dataDeposito.setDate(dataDeposito.getDate() - 23); // 23 dias antes da publicação
          processosComDataDepositoEstimada++;
        } else {
          processosSemDataDeposito++;
          
          // Log detalhado para processos sem data de depósito
          console.log(`\n========== Processo sem data de depósito: ${processo.numero} ==========`);
          console.log(`Total de despachos: ${processo.despachos.length}`);
          
          if (processo.despachos.length > 0) {
            console.log('Lista de despachos:');
            processo.despachos.forEach((despacho, index) => {
              console.log(`${index + 1}. Nome: ${despacho.nome || 'N/A'}`);
              console.log(`   Código: ${despacho.codigo}`);
              console.log(`   Data: ${despacho.rpi?.dataPublicacao?.toISOString() || 'N/A'}`);
            });
          } else {
            console.log('Esse processo não possui despachos.');
          }
          console.log('=============================================================');
        }
      }

      // Verifica sobrestamento
      const sobrestamento = processo.despachos.find(d => 
        d.nome?.toLowerCase().includes('sobrestamento do exame de mérito')
      );

      if (sobrestamento) {
        await prisma.processo.update({
          where: { id: processo.id },
          data: {
            sobrestamento: true,
            dataSobrestamento: sobrestamento.rpi.dataPublicacao,
            dataMeritoEstimada: null,
            diasAteMeritoEstimada: null
          }
        });
        processosComSobrestamento++;
        continue;
      }

      // Verifica oposição e exigência
      const oposicao = processo.despachos.find(d => 
        d.nome?.toLowerCase().includes('notificação de oposição')
      );
      const exigencia = processo.despachos.find(d => 
        d.nome?.toLowerCase().includes('exigência de mérito')
      );

      if (oposicao || exigencia) {
        let diasEstimados: number;
        if (oposicao) {
          diasEstimados = estimativaMerito.comOposicao.mediaEmDias;
          processosComOposicao++;
        } else {
          diasEstimados = estimativaMerito.comExigencia.mediaEmDias;
          processosComExigencia++;
        }

        const dataMeritoEstimada = dataDeposito ? 
          new Date(dataDeposito.getTime() + (diasEstimados * 24 * 60 * 60 * 1000)) : 
          null;

        await prisma.processo.update({
          where: { id: processo.id },
          data: {
            oposicao: !!oposicao,
            dataOposicao: oposicao?.rpi.dataPublicacao || null,
            exigencia: !!exigencia,
            dataExigencia: exigencia?.rpi.dataPublicacao || null,
            dataMeritoEstimada,
            diasAteMeritoEstimada: diasEstimados
          }
        });

        processosAtualizados++;
      } else if (dataDeposito) {
        // Se não tem intervenções e tem data de depósito, atualiza com estimativa sem intervenções
        const diasEstimados = estimativaMerito.semIntervencoes.mediaEmDias;
        const dataMeritoEstimada = new Date(dataDeposito.getTime() + (diasEstimados * 24 * 60 * 60 * 1000));

        await prisma.processo.update({
          where: { id: processo.id },
          data: {
            oposicao: false,
            dataOposicao: null,
            exigencia: false,
            dataExigencia: null,
            dataMeritoEstimada,
            diasAteMeritoEstimada: diasEstimados
          }
        });

        processosSemIntervencao++;
        processosAtualizados++;
      } else {
        processosSemIntervencaoSemDataDeposito++;
      }
    }

    console.log('\nResumo da atualização:');
    console.log(`- Total de processos processados: ${processos.length}`);
    console.log(`- Processos com data de depósito estimada: ${processosComDataDepositoEstimada}`);
    console.log(`- Processos sem data de depósito: ${processosSemDataDeposito}`);
    console.log(`- Processos sem intervenções e sem data de depósito: ${processosSemIntervencaoSemDataDeposito}`);
    console.log(`- Processos com sobrestamento: ${processosComSobrestamento}`);
    console.log(`- Processos com oposição: ${processosComOposicao}`);
    console.log(`- Processos com exigência: ${processosComExigencia}`);
    console.log(`- Processos sem intervenções: ${processosSemIntervencao}`);
    console.log(`- Processos atualizados com estimativas: ${processosAtualizados}`);

  } catch (error) {
    console.error('Erro ao atualizar estimativas:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executa o script
atualizarEstimativasMerito(); 