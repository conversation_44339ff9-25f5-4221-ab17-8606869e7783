module.exports = {
  apps: [
    {
      name: "api-v3-rgsys",
      script: "src/server.ts",
      interpreter: "npx",
      interpreter_args: "ts-node",
      watch: ["src"],
      ignore_watch: ["node_modules", "logs", "dist", "uploads"],
      max_memory_restart: "4G",
      env: {
        NODE_ENV: "development",
        PORT: 3321,
        NODE_OPTIONS: "--max-old-space-size=4096",
      },
      env_production: {
        NODE_ENV: "production",
        PORT: 3321,
        NODE_OPTIONS: "--max-old-space-size=4096",
      },
      max_restarts: 5,
      restart_delay: 5000,
      log_date_format: "YYYY-MM-DD HH:mm:ss",
      error_file: "./logs/pm2-errors.log",
      out_file: "./logs/pm2-out.log",
      merge_logs: true,
    },
    {
      // 🐍 MICROSERVIÇO PYTHON FASTAPI
      name: "pdf-processor",
      script: "main.py",
      cwd: "./pdf-processor-service",
      interpreter: "python3",
      instances: 1,
      max_memory_restart: "512M",
      max_restarts: 5,
      restart_delay: 4000,
      env: {
        HOST: "0.0.0.0",
        PORT: "8000"
      },
      error_file: "./logs/python-errors.log",
      out_file: "./logs/python-out.log",
      merge_logs: true,
    },
  ],
};
