import { PrismaClient } from '@prisma/client';
import { calcularEstimativasMeritoSimplificado } from './meritoService';

const prisma = new PrismaClient();

interface ProcessoAtualizado {
  id: string;
  numero: string;
  oposicao: boolean;
  dataOposicao: Date | null;
  sobrestamento: boolean;
  dataSobrestamento: Date | null;
  exigencia: boolean;
  dataExigencia: Date | null;
  dataMeritoEstimada: Date | null;
  diasAteMerito: number | null;
}

export async function atualizarEstimativasMerito(): Promise<{
  totalProcessos: number;
  processosAtualizados: number;
  processosSobrestados: number;
  processosComIntervencao: number;
}> {
  // Obtém as estimativas atuais
  const { estimativaMerito } = await calcularEstimativasMeritoSimplificado();

  // Busca processos onde somos procuradores e não têm data de mérito
  const processos = await prisma.processo.findMany({
    where: {
      procurador: {
        nome: {
          contains: "REGISTRE-SE",
          mode: "insensitive"
        }
      },
    },
    include: {
      despachos: {
        include: {
          rpi: true
        },
        orderBy: {
          rpi: {
            dataPublicacao: 'asc'
          }
        }
      }
    }
  });

  console.log(`Total de processos encontrados: ${processos.length}`);

  let processosAtualizados = 0;
  let processosSobrestados = 0;
  let processosComIntervencao = 0;

  for (const processo of processos) {
    // Verifica se há sobrestamento
    const sobrestamento = processo.despachos.find(d => 
      d.nome?.toLowerCase().includes('sobrestamento do exame de mérito')
    );

    if (sobrestamento) {
      await prisma.processo.update({
        where: { id: processo.id },
        data: {
          sobrestamento: true,
          dataSobrestamento: sobrestamento.rpi.dataPublicacao
        }
      });
      processosSobrestados++;
      continue;
    }

    // Verifica intervenções
    const oposicao = processo.despachos.find(d => 
      d.nome?.toLowerCase().includes('notificação de oposição')
    );
    const exigencia = processo.despachos.find(d => 
      d.nome?.toLowerCase().includes('exigência')
    );

    // Calcula data estimada de mérito
    let diasEstimados: number;
    if (oposicao) {
      diasEstimados = estimativaMerito.comOposicao.mediaEmDias;
    } else if (exigencia) {
      diasEstimados = estimativaMerito.comExigencia.mediaEmDias;
    } else {
      diasEstimados = estimativaMerito.semIntervencoes.mediaEmDias;
    }

    // Calcula data estimada
    const dataMeritoEstimada = processo.dataDeposito ? 
      new Date(processo.dataDeposito.getTime() + (diasEstimados * 24 * 60 * 60 * 1000)) : 
      null;

    // Atualiza o processo
    await prisma.processo.update({
      where: { id: processo.id },
      data: {
        oposicao: !!oposicao,
        dataOposicao: oposicao?.rpi.dataPublicacao || null,
        exigencia: !!exigencia,
        dataExigencia: exigencia?.rpi.dataPublicacao || null,
        dataMeritoEstimada,
        diasAteMeritoEstimada: diasEstimados
      }
    });

    processosAtualizados++;
    if (oposicao || exigencia) {
      processosComIntervencao++;
    }
  }

  console.log('Resumo da atualização:');
  console.log(`- Total de processos: ${processos.length}`);
  console.log(`- Processos atualizados: ${processosAtualizados}`);
  console.log(`- Processos sobrestados: ${processosSobrestados}`);
  console.log(`- Processos com intervenção: ${processosComIntervencao}`);

  return {
    totalProcessos: processos.length,
    processosAtualizados,
    processosSobrestados,
    processosComIntervencao
  };
} 