import { dialogoMapping } from '../../utils/chatguru.utils';
import { chatGuruLogger } from '../../utils/logger';
import { 
  AtualizacaoElegibilidadeResult, 
  DialogoInfo, 
  ElegibilidadeDespachoResult, 
  ElegibilidadeResult, 
  Processo, 
  prisma 
} from './types';

/**
 * Obtém as informações do diálogo correspondente ao prazo em meses
 */
export function getDialogoInfo(meses: number): DialogoInfo | null {
  const key = meses === 1 ? "falta cerca de 1 mês" : `faltam ${meses} meses`;
  
  if (dialogoMapping[key]) {
    return {
      dialogId: dialogoMapping[key].dialogoChatguru,
      estagioProcesso: dialogoMapping[key].estagioProcesso,
      idEtapa: dialogoMapping[key].idEtapa
    };
  }
  
  return null;
}

/**
 * Converte dias para meses (baseado em múltiplos exatos de 30 dias)
 */
export function diasParaMeses(dias: number): number {
  // Verificamos se o número de dias é múltiplo de 30
  return Math.floor(dias / 30);
}

/**
 * Verifica a elegibilidade de um processo com base nos despachos de publicação para oposição
 * Se o processo tiver um despacho de publicação para oposição com mais de 67 dias, é elegível
 */
export async function verificarElegibilidadePorDespacho(processoId: string): Promise<ElegibilidadeDespachoResult> {
  try {
    // Busca despachos com o texto específico
    const despacho = await prisma.despacho.findFirst({
      where: {
        processoId,
        nome: {
          contains: "Publicação de pedido de registro para oposição"
        }
      },
      include: {
        rpi: {
          select: {
            dataPublicacao: true
          }
        }
      },
      orderBy: {
        rpi: {
          dataPublicacao: 'desc'
        }
      }
    });

    // Se não encontrou despacho, não é elegível
    if (!despacho) {
      return {
        elegivel: false,
        motivo: 'Processo sem despacho de publicação para oposição'
      };
    }

    // Verifica se o despacho ocorreu há mais de 67 dias (60 dias + 7 dias)
    const dataPublicacao = despacho.rpi.dataPublicacao;
    const hoje = new Date();
    const diasDesdePublicacao = Math.floor(
      (hoje.getTime() - dataPublicacao.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (diasDesdePublicacao < 67) {
      return {
        elegivel: false,
        motivo: `Despacho de publicação para oposição ocorreu há apenas ${diasDesdePublicacao} dias (mínimo de 67 dias)`
      };
    }

    // Se passou por todas as verificações, é elegível
    return {
      elegivel: true
    };
  } catch (error) {
    chatGuruLogger.error(`Erro ao verificar elegibilidade por despacho: ${error}`);
    return {
      elegivel: false,
      motivo: 'Erro ao verificar despachos de publicação'
    };
  }
}

/**
 * Verifica se um processo deve receber um novo comunicado
 */
export async function verificarElegibilidadeComunicado(processo: Processo): Promise<ElegibilidadeResult> {
  // Se o processo tem sobrestamento, não é elegível
  if (processo.sobrestamento) {
    return {
      elegivel: false,
      prazoMeses: 0,
      motivoInelegibilidade: "Processo com sobrestamento",
    };
  }

  // Se não tem data estimada, não é elegível
  if (!processo.dataMeritoEstimada) {
    return {
      elegivel: false,
      prazoMeses: 0,
      motivoInelegibilidade: "Processo sem data estimada de mérito",
    };
  }

  // Se o processo não tem cliente associado, não é elegível
  if (!processo.clienteId) {
    return {
      elegivel: false,
      prazoMeses: 0,
      motivoInelegibilidade: "Processo sem cliente associado",
    };
  }

  // NOVA REGRA 1: Verifica se o processo foi marcado como elegível para comunicados
  // Este campo será definido quando o lead for movido para "Com Oposição" ou "Sem Oposição"
  if (!processo.elegivelParaComunicados) {
    // NOVA REGRA 2: Para processos antigos, verifica se tem um despacho de publicação para oposição
    // e se ele ocorreu há mais de 67 dias
    const elegibilidadeDespacho = await verificarElegibilidadePorDespacho(
      processo.id
    );

    if (!elegibilidadeDespacho.elegivel) {
      return {
        elegivel: false,
        prazoMeses: 0,
        motivoInelegibilidade:
          elegibilidadeDespacho.motivo ||
          "Processo não elegível para comunicados",
      };
    }

    // Se for elegível pelo despacho, atualiza o processo para marcar como elegível
    await prisma.processo.update({
      where: { id: processo.id },
      data: {
        elegivelParaComunicados: true,
        dataElegibilidade: new Date(),
      },
    });

    chatGuruLogger.info(
      `Processo ${processo.id} marcado como elegível por despacho de publicação`
    );
  }

  // Calcula dias restantes até a data estimada
  const hoje = new Date();
  const dataEstimada = new Date(processo.dataMeritoEstimada); //aqui podemos utilizar o campo "diasAteMeritoEstimada" que ja é o numero calculado.
  const diasRestantes = Math.ceil(
    (dataEstimada.getTime() - hoje.getTime()) / (1000 * 60 * 60 * 24)
  );

  // Se a data já passou, não é elegível
  if (diasRestantes <= 0) {
    return {
      elegivel: false,
      prazoMeses: 0,
      motivoInelegibilidade: "Data estimada já passou",
    };
  }

  // Verifica se os dias restantes são múltiplos de 30
  if (diasRestantes % 30 !== 0) {
    return {
      elegivel: false,
      prazoMeses: diasParaMeses(diasRestantes),
      motivoInelegibilidade: "Dias restantes não são múltiplos de 30",
    };
  }

  // Converte para meses (baseado em múltiplos de 30 dias)
  const mesesRestantes = diasParaMeses(diasRestantes);

  // Obtém informações do diálogo
  const dialogoInfo = getDialogoInfo(mesesRestantes);

  // Verifica se temos um diálogo para este prazo
  if (!dialogoInfo) {
    return {
      elegivel: false,
      prazoMeses: mesesRestantes,
      motivoInelegibilidade: "Não há diálogo mapeado para este prazo",
    };
  }

  // Busca o último comunicado enviado com sucesso para este processo
  const ultimoComunicado = await prisma.comunicadoPrazoMerito.findFirst({
    where: {
      processoId: processo.id,
      success: true,
      status: "ENVIADO",
    },
    orderBy: {
      dataEnvio: "desc",
    },
  });

  // Se nunca enviou comunicado, é elegível
  if (!ultimoComunicado) {
    return {
      elegivel: true,
      prazoMeses: mesesRestantes,
      dialogoInfo,
    };
  }

  // Se o último comunicado foi com prazo maior, é elegível
  // Isso garante que só enviamos comunicados com prazos menores
  if (
    ultimoComunicado.prazoEmMeses &&
    ultimoComunicado.prazoEmMeses > mesesRestantes
  ) {
    return {
      elegivel: true,
      prazoMeses: mesesRestantes,
      dialogoInfo,
    };
  }

  // Verifica se já existe um comunicado pendente ou em processamento para este diálogo
  const comunicadoPendente = await prisma.comunicadoPrazoMerito.findFirst({
    where: {
      processoId: processo.id,
      dialogId: dialogoInfo.dialogId,
      status: {
        in: ["PENDENTE", "EM_PROCESSAMENTO"],
      },
    },
  });

  if (comunicadoPendente) {
    return {
      elegivel: false,
      prazoMeses: mesesRestantes,
      motivoInelegibilidade:
        "Já existe comunicado pendente ou em processamento para este prazo",
    };
  }

  // Verifica se já existe um comunicado com falha para este diálogo que pode ser retentado
  const comunicadoFalha = await prisma.comunicadoPrazoMerito.findFirst({
    where: {
      processoId: processo.id,
      dialogId: dialogoInfo.dialogId,
      status: "FALHA",
      tentativas: {
        lt: 3, // Limita a 3 tentativas
      },
    },
  });

  if (comunicadoFalha) {
    return {
      elegivel: true,
      prazoMeses: mesesRestantes,
      dialogoInfo,
      motivoInelegibilidade:
        "Retentativa de comunicado que falhou anteriormente",
    };
  }

  // Se chegou aqui, não é elegível (já enviou comunicado para este prazo ou menor)
  return {
    elegivel: false,
    prazoMeses: mesesRestantes,
    motivoInelegibilidade: "Já enviou comunicado para este prazo ou menor",
  };
}

/**
 * Atualiza a elegibilidade de processos quando há mudança de etapa no CRM
 * Marcará como elegível quando o lead for movido para "Com Oposição" ou "Sem Oposição"
 */
export async function atualizarElegibilidadePorEtapaCRM(
  crmId: number, 
  etapa: string
): Promise<AtualizacaoElegibilidadeResult> {
  try {
    chatGuruLogger.info(`Atualizando elegibilidade por etapa CRM: ID ${crmId}, Etapa "${etapa}"`);
    
    // Verifica se a etapa é elegível para comunicados
    const etapaElegivel = ['Com Oposição', 'Sem Oposição'].includes(etapa);
    
    if (!etapaElegivel) {
      return {
        success: false,
        processosAtualizados: 0,
        mensagem: `Etapa "${etapa}" não torna processos elegíveis para comunicados`
      };
    }
    
    // Busca o cliente pelo crmId
    const cliente = await prisma.cliente.findUnique({
      where: { crmId }
    });
    
    if (!cliente) {
      return {
        success: false,
        processosAtualizados: 0,
        mensagem: `Cliente com crmId ${crmId} não encontrado`
      };
    }
    
    // Busca processos do cliente que ainda não são elegíveis
    const processos = await prisma.processo.findMany({
      where: {
        clienteId: cliente.id,
        elegivelParaComunicados: false,
        dataMeritoEstimada: { not: null },
        sobrestamento: false
      }
    });
    
    if (processos.length === 0) {
      return {
        success: true,
        processosAtualizados: 0,
        mensagem: `Nenhum processo não-elegível encontrado para o cliente ${cliente.id}`
      };
    }
    
    // Atualiza cada processo para torná-lo elegível
    const atualizacoes = await Promise.all(
      processos.map(processo => 
        prisma.processo.update({
          where: { id: processo.id },
          data: {
            elegivelParaComunicados: true,
            dataElegibilidade: new Date(),
            oposicao: etapa === 'Com Oposição' // Atualiza o campo oposicao se for "Com Oposição"
          }
        })
      )
    );
    
    chatGuruLogger.info(`${atualizacoes.length} processos atualizados para elegibilidade por etapa CRM`);
    
    return {
      success: true,
      processosAtualizados: atualizacoes.length,
      mensagem: `${atualizacoes.length} processos foram marcados como elegíveis para comunicados`
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    chatGuruLogger.error(`Erro ao atualizar elegibilidade por etapa CRM: ${errorMessage}`, { error });
    
    return {
      success: false,
      processosAtualizados: 0,
      mensagem: `Erro ao atualizar elegibilidade: ${errorMessage}`
    };
  }
} 