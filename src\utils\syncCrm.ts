import { Logger, formatNumber } from "../services/logger.service";
import { fetchDeals, fetchStages, fetchPipelines, fetchPersons, fetchCompanies } from "./fetchCrmData.utils";

export function formatPhoneNumber(phoneNumber: string): string {
  // Verificar se o número de telefone é válido e se é uma string
  if (!phoneNumber || typeof phoneNumber !== "string") {
    return `Número de telefone inválido [${phoneNumber}]`;
  }

  // Remover o código do país (55) e o zero inicial, se existir
  let cleanedNumber = phoneNumber.replace(/^55/, "").replace(/^0/, "");

  // Verificar se o DDD é válido (dois primeiros dígitos após remoção do código do país)
  const ddd = cleanedNumber.substring(0, 2);
  if (!/^[1-9]{2}$/.test(ddd)) {
    return `Número de telefone inválido [${phoneNumber}]`;
  }

  // Extrair o número de telefone sem o DDD
  const numberWithoutDdd = cleanedNumber.substring(2);
  // Formatar o número final
  let formattedNumber;
  if (/^9\d{8}$/.test(numberWithoutDdd)) {
    // Quando o número começa com 9 e tem 9 dígitos
    formattedNumber = `(${ddd}) ${numberWithoutDdd.slice(
      0,
      5
    )}-${numberWithoutDdd.slice(5)}`;
  } else if (/^\d{8}$/.test(numberWithoutDdd)) {
    // Quando o número tem 8 dígitos
    formattedNumber = `(${ddd}) ${numberWithoutDdd.slice(
      0,
      4
    )}-${numberWithoutDdd.slice(4)}`;
  } else {
    return `Número de telefone inválido [${phoneNumber}]`;
  }

  return formattedNumber;
}
// Funções auxiliares
function processarDadosContato(person: any) {
  // Extrair Telefone
  const leadContactPhone =
    Array.isArray(person?.contactPhones) && person.contactPhones.length > 0
      ? person.contactPhones[0]?.phone
      : null;

  // Extrair Email Principal
  let emailPrincipal: string | null = null;

  if (person?.contactEmails && Array.isArray(person.contactEmails) && person.contactEmails.length > 0) {
    // Tenta encontrar o email marcado como principal (is_main === 1, '1', ou true)
    const mainEmail = person.contactEmails.find(
      (email: any) => email.is_main === 1 || email.is_main === '1' || email.is_main === true
    );

    // Se encontrar, usa o email dele. Senão, usa o primeiro email da lista como fallback.
    emailPrincipal = mainEmail ? mainEmail.email : person.contactEmails[0].email;
  }

  return {
    telefone: leadContactPhone
      ? formatPhoneNumber(leadContactPhone)
      : null,
    email: emailPrincipal, // Retorna o email extraído
  };
}

export const enrichOpportunities = async (
): Promise<any[]> => {
  try {
    const startTime = Date.now();
    Logger.section("🔄 ENRIQUECENDO OPORTUNIDADES");

    // Buscando oportunidades com o parâmetro de última sincronização
    Logger.info("Buscando oportunidades...");
    const opportunitiesStartTime = Date.now();
    const opportunities = await fetchDeals();
    Logger.timer(`Oportunidades obtidas: ${formatNumber(opportunities.length)}`, (Date.now() - opportunitiesStartTime) / 1000);

    if (!opportunities.length) {
      Logger.warn("Nenhuma oportunidade encontrada para o período solicitado");
      return [];
    }

    // Busca de dados complementares para enriquecer as oportunidades
    Logger.info("Buscando dados complementares...");

    // Dados estáticos (não precisam de filtro de data)
    const stagesStartTime = Date.now();
    Logger.info("Buscando stages...");
    const stages = await fetchStages();
    Logger.timer(`Stages obtidos: ${formatNumber(stages.length)}`, (Date.now() - stagesStartTime) / 1000);

    const pipelinesStartTime = Date.now();
    Logger.info("Buscando pipelines...");
    const pipelines = await fetchPipelines();
    Logger.timer(`Pipelines obtidos: ${formatNumber(pipelines.length)}`, (Date.now() - pipelinesStartTime) / 1000);

    // Dados que podem ser filtrados por data de atualização
    const personsStartTime = Date.now();
    Logger.info("Buscando persons...");
    const persons = await fetchPersons();
    Logger.timer(`Persons obtidos: ${formatNumber(persons.length)}`, (Date.now() - personsStartTime) / 1000);

    const companiesStartTime = Date.now();
    Logger.info("Buscando companies...");
    const companies = await fetchCompanies();
    Logger.timer(`Companies obtidos: ${formatNumber(companies.length)}`, (Date.now() - companiesStartTime) / 1000);

    // Criando mapas para acesso rápido
    Logger.info("Criando mapas para enriquecimento de dados...");
    const stageMap = new Map(stages.map((stage) => [stage.id, stage]));
    const pipelineMap = new Map(pipelines.map((pipeline) => [pipeline.id, pipeline]));
    const personMap = new Map(persons.map((person) => [person.id, person]));
    const companyMap = new Map(companies.map((company) => [company.id, company]));



    // Realizando o enriquecimento das oportunidades
    Logger.info(`Enriquecendo ${formatNumber(opportunities.length)} oportunidades...`);
    const enrichStartTime = Date.now();
    
    const enrichedOpportunities = opportunities.map((opportunity) => {
      const pipeline = pipelineMap.get(opportunity.pipeline_id) || null;
      const stage = stageMap.get(opportunity.stage_id) || null;
      const person = personMap.get(opportunity.person_id) || null;
      const company = companyMap.get(opportunity.company_id) || null;

      // Log específico para crmId 47014644 na fase de enriquecimento
      if (opportunity.id === 47014644) {
        Logger.info(`[LOG ESPECIAL ENRICH] Processando opportunity.id: ${opportunity.id}`);
        Logger.info(`[LOG ESPECIAL ENRICH] Opportunity pipeline_id: ${opportunity.pipeline_id}`);
        Logger.info(`[LOG ESPECIAL ENRICH] Pipeline encontrado no map: ${JSON.stringify(pipeline, null, 2)}`);
        Logger.info(`[LOG ESPECIAL ENRICH] Nome do pipeline (para funil): ${pipeline?.name}`);
      }

      return {
        ...opportunity,
        pipeline: pipeline,
        stage: stage,
        origin: origin,
        person: person,
        company: company,
      };
    });
    
    Logger.timer(`Enriquecimento concluído`, (Date.now() - enrichStartTime) / 1000);
    Logger.success(`Total de oportunidades enriquecidas: ${formatNumber(enrichedOpportunities.length)}`);
    Logger.timer(`Tempo total de enriquecimento`, (Date.now() - startTime) / 1000);
    
    return enrichedOpportunities;
  } catch (error: any) {
    Logger.error("Erro ao enriquecer oportunidades", error);
    return [];
  }
};
