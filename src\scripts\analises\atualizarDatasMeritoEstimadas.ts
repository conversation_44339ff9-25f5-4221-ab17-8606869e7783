import { PrismaClient } from '@prisma/client';
import cliProgress from 'cli-progress';

const prisma = new PrismaClient();

// Estimativas de mérito fornecidas
const estimativasMerito = {
  estimativaMerito: {
    semIntervencoes: {
      mediaEmDias: 569,
      desvioPadrao: 4,
      quantidade: 10727,
      minimo: 561,
      maximo: 581
    },
    comOposicao: {
      mediaEmDias: 897,
      desvioPadrao: 3,
      quantidade: 189,
      minimo: 893,
      maximo: 903
    },
    comSobrestamento: {
      mediaEmDias: 843,
      desvioPadrao: 213,
      quantidade: 400,
      minimo: 579,
      maximo: 1484
    },
    comExigencia: {
      mediaEmDias: 644,
      desvioPadrao: 17,
      quantidade: 174,
      minimo: 600,
      maximo: 693
    }
  }
};

/**
 * Atualiza as datas de mérito estimadas para os processos
 * usando as estimativas fornecidas
 */
async function atualizarDatasMeritoEstimadas() {
  try {
    console.log('Iniciando atualização de datas de mérito estimadas para processos de teste...');
    
    // Busca apenas processos de teste (prefixo "TESTE-") onde o procurador é REGISTRE-SE
    const processos = await prisma.processo.findMany({
      where: {
        numero: {
          startsWith: "TESTE-"
        },
        procurador: {
          nome: {
            contains: "REGISTRE-SE",
            mode: "insensitive"
          }
        }
      },
      include: {
        despachos: {
          include: {
            rpi: true
          },
          orderBy: {
            rpi: {
              dataPublicacao: 'asc'
            }
          }
        }
      }
    });

    console.log(`Encontrados ${processos.length} processos de teste para atualização`);

    // Configurar barra de progresso
    const barraProgresso = new cliProgress.SingleBar({
      format: 'Atualizando estimativas |{bar}| {percentage}% | {value}/{total} Processos de Teste',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });
    barraProgresso.start(processos.length, 0);

    // Contadores para o relatório
    let processosAtualizados = 0;
    let processosComSobrestamento = 0;
    let processosComOposicao = 0;
    let processosComExigencia = 0;
    let processosSemIntervencao = 0;
    let processosComDataDepositoEstimada = 0;

    for (const processo of processos) {
      // Se não tem data de depósito, tenta estimar baseado no despacho de publicação
      let dataDeposito = processo.dataDeposito;
      if (!dataDeposito) {
        const despachoPublicacao = processo.despachos.find(d => 
          d.nome?.toLowerCase().includes('publicação de pedido de registro para oposição')
        );
        
        if (despachoPublicacao?.rpi?.dataPublicacao) {
          dataDeposito = new Date(despachoPublicacao.rpi.dataPublicacao);
          dataDeposito.setDate(dataDeposito.getDate() - 23); // 23 dias antes da publicação
          processosComDataDepositoEstimada++;
        }
      }

      // Se não tem data de depósito, pula este processo
      if (!dataDeposito) {
        barraProgresso.increment();
        continue;
      }

      // Verifica sobrestamento
      const sobrestamento = processo.despachos.find(d => 
        d.nome?.toLowerCase().includes('sobrestamento do exame de mérito')
      );

      if (sobrestamento) {
        await prisma.processo.update({
          where: { id: processo.id },
          data: {
            sobrestamento: true,
            dataSobrestamento: sobrestamento.rpi.dataPublicacao,
            dataMeritoEstimada: null,
            diasAteMeritoEstimada: null
          }
        });
        processosComSobrestamento++;
        barraProgresso.increment();
        continue;
      }

      // Verifica oposição e exigência
      const oposicao = processo.despachos.find(d => 
        d.nome?.toLowerCase().includes('notificação de oposição')
      );
      const exigencia = processo.despachos.find(d => 
        d.nome?.toLowerCase().includes('exigência de mérito')
      );

      // Calcula dias estimados e data estimada
      let diasEstimados: number;
      if (oposicao) {
        diasEstimados = estimativasMerito.estimativaMerito.comOposicao.mediaEmDias;
        processosComOposicao++;
      } else if (exigencia) {
        diasEstimados = estimativasMerito.estimativaMerito.comExigencia.mediaEmDias;
        processosComExigencia++;
      } else {
        diasEstimados = estimativasMerito.estimativaMerito.semIntervencoes.mediaEmDias;
        processosSemIntervencao++;
      }

      const dataMeritoEstimada = new Date(dataDeposito.getTime() + (diasEstimados * 24 * 60 * 60 * 1000));
      
      // Calcular dias corridos até o mérito (se já tiver uma data de mérito)
      let diasCorridosMerito = null;
      if (processo.dataMerito) {
        const diffTime = Math.abs(processo.dataMerito.getTime() - dataDeposito.getTime());
        diasCorridosMerito = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      }

      // Atualizar o processo com as estimativas
      await prisma.processo.update({
        where: { id: processo.id },
        data: {
          oposicao: !!oposicao,
          dataOposicao: oposicao?.rpi.dataPublicacao || null,
          exigencia: !!exigencia,
          dataExigencia: exigencia?.rpi.dataPublicacao || null,
          dataMeritoEstimada: dataMeritoEstimada,
          diasAteMeritoEstimada: diasEstimados,
          diasCorridosMerito: diasCorridosMerito
        }
      });

      processosAtualizados++;
      barraProgresso.increment();
    }

    barraProgresso.stop();

    console.log('\nResumo da atualização:');
    console.log(`- Total de processos de teste processados: ${processos.length}`);
    console.log(`- Processos com data de depósito estimada: ${processosComDataDepositoEstimada}`);
    console.log(`- Processos com sobrestamento: ${processosComSobrestamento}`);
    console.log(`- Processos com oposição: ${processosComOposicao}`);
    console.log(`- Processos com exigência: ${processosComExigencia}`);
    console.log(`- Processos sem intervenções: ${processosSemIntervencao}`);
    console.log(`- Processos atualizados com estimativas: ${processosAtualizados}`);

    console.log('Atualização de datas de mérito estimadas para processos de teste concluída com sucesso!');

  } catch (error) {
    console.error('Erro ao atualizar datas de mérito estimadas:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar a função
atualizarDatasMeritoEstimadas(); 