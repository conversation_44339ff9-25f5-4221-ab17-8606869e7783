// Sistema de logger com cores e formatação avançada
const consoleColors = {
  reset: "\x1b[0m",
  bright: "\x1b[1m",
  dim: "\x1b[2m",
  black: "\x1b[30m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  magenta: "\x1b[35m",
  cyan: "\x1b[36m",
  white: "\x1b[37m",
  bgRed: "\x1b[41m",
  bgGreen: "\x1b[42m",
  bgYellow: "\x1b[43m",
  bgBlue: "\x1b[44m",
};

// Formatar números com separadores de milhares
const formatNumber = (num: number): string => {
  return num.toLocaleString("pt-BR");
};

// Formatar tempo em formato legível (hh:mm:ss)
const formatTime = (seconds: number): string => {
  const hrs = Math.floor(seconds / 3600);
  const mins = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  return [
    hrs > 0 ? String(hrs).padStart(2, "0") : null,
    String(mins).padStart(2, "0"),
    String(secs).padStart(2, "0"),
  ]
    .filter(Boolean)
    .join(":");
};

// Estimar tempo restante
const estimateTimeRemaining = (
  processedItems: number,
  totalItems: number,
  elapsedSeconds: number
): string => {
  if (processedItems === 0) return "calculando...";
  
  const itemsPerSecond = processedItems / elapsedSeconds;
  const remainingItems = totalItems - processedItems;
  const remainingSeconds = remainingItems / itemsPerSecond;
  
  return formatTime(remainingSeconds);
};

// Logger centralizado
export class Logger {
  static timestamp(): string {
    return new Date().toLocaleString("pt-BR", {
      timeZone: "America/Sao_Paulo",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    });
  }

  static info(message: string): void {
    console.log(
      `${consoleColors.blue}[${this.timestamp()}] ℹ️ INFO:${consoleColors.reset} ${message}`
    );
  }

  static success(message: string): void {
    console.log(
      `${consoleColors.green}[${this.timestamp()}] ✅ SUCESSO:${consoleColors.reset} ${message}`
    );
  }

  static warn(message: string): void {
    console.log(
      `${consoleColors.yellow}[${this.timestamp()}] ⚠️ ALERTA:${consoleColors.reset} ${message}`
    );
  }

  static error(message: string, error?: any): void {
    console.log(
      `${consoleColors.red}[${this.timestamp()}] ❌ ERRO:${consoleColors.reset} ${message}`
    );
    if (error) {
      console.error(`${consoleColors.dim}${error}${consoleColors.reset}`);
    }
  }

  static timer(message: string, seconds: number): void {
    console.log(
      `${consoleColors.cyan}[${this.timestamp()}] ⏱️ TEMPO:${consoleColors.reset} ${message} (${formatTime(seconds)})`
    );
  }

  static progress(
    current: number,
    total: number,
    message: string,
    startTime: number
  ): void {
    const elapsedSeconds = (Date.now() - startTime) / 1000;
    const percentage = Math.round((current / total) * 100);
    const remaining = estimateTimeRemaining(current, total, elapsedSeconds);
    
    console.log(
      `${consoleColors.magenta}[${this.timestamp()}] 🔄 PROGRESSO:${consoleColors.reset} ${message} - ${formatNumber(current)}/${formatNumber(total)} (${percentage}%) - Tempo restante estimado: ${remaining}`
    );
  }

  static section(title: string): void {
    const line = "═".repeat(80);
    console.log(`\n${consoleColors.bright}${consoleColors.blue}${line}${consoleColors.reset}`);
    console.log(`${consoleColors.bright}${consoleColors.white}${title}${consoleColors.reset}`);
    console.log(`${consoleColors.bright}${consoleColors.blue}${line}${consoleColors.reset}\n`);
  }
}

export { formatNumber, formatTime }; 