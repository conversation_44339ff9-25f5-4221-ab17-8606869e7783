-- CreateEnum
CREATE TYPE "StatusProcessamento" AS ENUM ('PENDENTE', 'PROCESSANDO', 'SUCESSO', 'FA<PERSON>HA_CRM', '<PERSON>LHA_CLIENTE', '<PERSON><PERSON><PERSON>_PROCESSO', '<PERSON><PERSON><PERSON>_LINK', '<PERSON><PERSON><PERSON>_CHATGURU', '<PERSON><PERSON><PERSON>_GERAL');

-- CreateTable
CREATE TABLE "ProcessamentoProtocolo" (
    "id" TEXT NOT NULL,
    "numeroProcesso" TEXT NOT NULL,
    "elementoNominativo" TEXT,
    "nomeArquivoPdf" TEXT,
    "status" "StatusProcessamento" NOT NULL DEFAULT 'PENDENTE',
    "crmDealId" INTEGER,
    "crmPersonId" INTEGER,
    "crmCompanyId" INTEGER,
    "dadosCrm" JSONB,
    "clienteId" INTEGER,
    "clienteCriado" BOOLEAN NOT NULL DEFAULT false,
    "clienteAtualizado" BOOLEAN NOT NULL DEFAULT false,
    "processoId" TEXT,
    "processoVinculado" BOOLEAN NOT NULL DEFAULT false,
    "linkGerado" BOOLEAN NOT NULL DEFAULT false,
    "autoLoginUrl" TEXT,
    "chatguruAtualizado" BOOLEAN NOT NULL DEFAULT false,
    "telefoneChatguru" TEXT,
    "tentativas" INTEGER NOT NULL DEFAULT 0,
    "ultimoErro" TEXT,
    "proximaTentativa" TIMESTAMP(3),
    "criadoEm" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processadoEm" TIMESTAMP(3),
    "atualizadoEm" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProcessamentoProtocolo_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ProcessamentoProtocolo_numeroProcesso_key" ON "ProcessamentoProtocolo"("numeroProcesso");

-- CreateIndex
CREATE INDEX "ProcessamentoProtocolo_status_idx" ON "ProcessamentoProtocolo"("status");

-- CreateIndex
CREATE INDEX "ProcessamentoProtocolo_numeroProcesso_idx" ON "ProcessamentoProtocolo"("numeroProcesso");

-- CreateIndex
CREATE INDEX "ProcessamentoProtocolo_proximaTentativa_idx" ON "ProcessamentoProtocolo"("proximaTentativa");

-- CreateIndex
CREATE INDEX "ProcessamentoProtocolo_criadoEm_idx" ON "ProcessamentoProtocolo"("criadoEm");

-- AddForeignKey
ALTER TABLE "ProcessamentoProtocolo" ADD CONSTRAINT "ProcessamentoProtocolo_clienteId_fkey" FOREIGN KEY ("clienteId") REFERENCES "Cliente"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProcessamentoProtocolo" ADD CONSTRAINT "ProcessamentoProtocolo_processoId_fkey" FOREIGN KEY ("processoId") REFERENCES "Processo"("id") ON DELETE SET NULL ON UPDATE CASCADE;
