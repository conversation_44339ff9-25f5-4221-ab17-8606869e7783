import { Logger } from "../services/logger.service";

// Função de teste da correção do telefone
function extrairIdentificadorTelefone(telefone: string): string {
  if (!telefone || telefone === "NoPhone") {
    return "0000000000";
  }

  const numeroLimpo = telefone.replace(/\D/g, '');
  
  if (!numeroLimpo) {
    return "0000000000";
  }

  let numeroProcessado = numeroLimpo;

  // Lista de DDDs válidos no Brasil
  const dddsValidos = new Set([
    11, 12, 13, 14, 15, 16, 17, 18, 19, // SP
    21, 22, 24, // RJ/ES
    27, 28, // ES
    31, 32, 33, 34, 35, 37, 38, // MG
    41, 42, 43, 44, 45, 46, // PR
    47, 48, 49, // SC
    51, 53, 54, 55, // RS
    61, // DF/GO
    62, 64, // GO
    63, // TO
    65, 66, // MT
    67, // MS
    68, // AC
    69, // RO
    71, 73, 74, 75, 77, // BA
    79, // SE
    81, 87, // PE
    82, // AL
    83, // PB
    84, // RN
    85, 88, // CE
    86, 89, // PI
    91, 93, 94, // PA
    92, 97, // AM
    95, // RR
    96, // AP
    98, 99 // MA
  ]);

  // CORREÇÃO: Detectar duplo "55" (país + DDD)
  if (numeroProcessado.startsWith('5555') && numeroProcessado.length >= 12) {
    // Remove primeiro "55" (código do país), mantém segundo "55" (DDD)
    numeroProcessado = numeroProcessado.substring(2);
    Logger.info(`🔧 Duplo 55 detectado: removido primeiro "55"`);
  } else if (numeroProcessado.startsWith('55') && numeroProcessado.length >= 12) {
    // Verifica se após remover 55, temos um número válido brasileiro
    const semPrefixo = numeroProcessado.substring(2);
    
    if (semPrefixo.length === 10 || semPrefixo.length === 11) {
      const possibleDDD = parseInt(semPrefixo.substring(0, 2));
      if (dddsValidos.has(possibleDDD)) {
        numeroProcessado = semPrefixo;
        Logger.info(`🔧 Código país 55 removido: DDD ${possibleDDD} válido`);
      }
    }
  }

  // Se ainda não temos pelo menos 10 dígitos, preenche com zeros à direita
  if (numeroProcessado.length < 10) {
    numeroProcessado = numeroProcessado.padEnd(10, '0');
    Logger.warn(`⚠️ Número muito curto: preenchido com zeros`);
  }

  // Extrai DDD (primeiros 2 dígitos) e últimos 8 dígitos
  const ddd = numeroProcessado.substring(0, 2);
  const ultimosOitoDigitos = numeroProcessado.slice(-8);
  
  return ddd + ultimosOitoDigitos;
}

// Casos de teste
const casosTeste = [
  // Casos normais
  { input: "11987654321", expected: "1187654321", description: "Número normal brasileiro" },
  { input: "(11) 98765-4321", expected: "1187654321", description: "Número formatado brasileiro" },
  
  // Casos com código do país simples
  { input: "5511987654321", expected: "1187654321", description: "55 + número brasileiro normal" },
  { input: "+55 11 98765-4321", expected: "1187654321", description: "55 formatado brasileiro" },
  
  // CASOS PROBLEMÁTICOS: Duplo 55
  { input: "555598765432", expected: "5598765432", description: "55(país) + 55(DDD) + 8 dígitos" },
  { input: "55551987654321", expected: "5587654321", description: "55(país) + 55(DDD) + 9 dígitos" },
  { input: "5555987654321", expected: "5587654321", description: "55(país) + 55(DDD) + 9 dígitos" },
  
  // Casos extremos
  { input: "123456", expected: "0012345600", description: "Número muito curto" },
  { input: "", expected: "0000000000", description: "String vazia" },
  { input: "NoPhone", expected: "0000000000", description: "NoPhone especial" },
  
  // Outros DDDs com 55
  { input: "5527987654321", expected: "2787654321", description: "55(país) + 27(DDD ES) + 9 dígitos" },
  { input: "5561987654321", expected: "6187654321", description: "55(país) + 61(DDD DF) + 9 dígitos" },
];

function executarTestes(): void {
  Logger.section("🧪 TESTE DA FUNÇÃO extrairIdentificadorTelefone()");
  
  let testesPassaram = 0;
  let testesFalharam = 0;
  
  for (const caso of casosTeste) {
    const resultado = extrairIdentificadorTelefone(caso.input);
    const passou = resultado === caso.expected;
    
    if (passou) {
      testesPassaram++;
      Logger.success(`✅ PASSOU: ${caso.description}`);
      Logger.info(`   Input: "${caso.input}" → Output: "${resultado}"`);
    } else {
      testesFalharam++;
      Logger.error(`❌ FALHOU: ${caso.description}`);
      Logger.error(`   Input: "${caso.input}"`);
      Logger.error(`   Esperado: "${caso.expected}"`);
      Logger.error(`   Obtido: "${resultado}"`);
    }
    
    console.log(''); // Linha em branco entre testes
  }
  
  Logger.section("📊 RESULTADO DOS TESTES");
  Logger.info(`✅ Testes que passaram: ${testesPassaram}`);
  Logger.info(`❌ Testes que falharam: ${testesFalharam}`);
  Logger.info(`📊 Total de testes: ${casosTeste.length}`);
  
  if (testesFalharam === 0) {
    Logger.success("🎉 TODOS OS TESTES PASSARAM!");
  } else {
    Logger.warn(`⚠️ ${testesFalharam} teste(s) falharam. Necessário ajustar a função.`);
  }
}

// Executar se este arquivo for chamado diretamente
if (require.main === module) {
  executarTestes();
}

export { extrairIdentificadorTelefone, executarTestes }; 