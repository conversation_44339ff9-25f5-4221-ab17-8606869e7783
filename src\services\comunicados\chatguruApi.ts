import { chatGuruLogger } from '../../utils/logger';
import { ExecutarDialogoResult, prisma } from './types';

/**
 * Atualiza campo personalizado no ChatGuru
 * @param crmId ID do lead no CRM
 * @param estagioProcesso Valor a ser definido no campo EstagioProcesso
 * @returns Sucesso ou falha da operação
 */
export async function atualizarCampoChatGuru(crmId: number, estagioProcesso: string): Promise<boolean> {
  try {
    chatGuruLogger.debug(`Iniciando atualização de campo EstagioProcesso para crmId: ${crmId}`, { crmId, estagioProcesso });
    
    // Aqui você implementará a lógica para atualizar o campo field__EstagioProcesso
    // Esta é uma função mockada, substitua pela integração real
    
    // Simulando chamada à API do ChatGuru
    // Na implementação real, aqui você faria uma chamada HTTP para a API do ChatGuru
    
    // Simulando sucesso (em ambiente de produção, isso seria o resultado da chamada à API)
    const sucesso = Math.random() > 0.1; // 90% de chance de sucesso para simulação
    
    // Registrar no log
    chatGuruLogger.logCampoAtualizado(crmId, 'EstagioProcesso', estagioProcesso, sucesso);
    
    return sucesso;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    chatGuruLogger.error(`Erro ao atualizar campo no ChatGuru: ${errorMessage}`, { error, crmId, estagioProcesso });
    return false;
  }
}

/**
 * Executa um diálogo no ChatGuru para um lead
 * @param crmId ID do lead no CRM
 * @param dialogId ID do diálogo a ser executado
 * @param processoId ID do processo relacionado
 * @param prazoMeses Prazo em meses para mérito
 * @param estagioProcesso Estágio atual do processo
 * @returns Resultado da execução do diálogo
 */
export async function executarDialogo(
  crmId: number, 
  dialogId: string, 
  processoId: string, 
  prazoMeses: number, 
  estagioProcesso: string
): Promise<ExecutarDialogoResult> {
  try {
    chatGuruLogger.debug(`Iniciando execução de diálogo para crmId: ${crmId}`, { 
      crmId, 
      dialogId, 
      processoId, 
      prazoMeses, 
      estagioProcesso 
    });
    
    // Criar ou obter comunicado existente
    let comunicado = await prisma.comunicadoPrazoMerito.findFirst({
      where: {
        crmId,
        processoId,
        dialogId
      }
    });

    // Se não existe, criar um novo registro com status PENDENTE
    if (!comunicado) {
      chatGuruLogger.debug(`Criando novo registro de comunicado para crmId: ${crmId}`, { crmId, processoId, dialogId });
      comunicado = await prisma.comunicadoPrazoMerito.create({
        data: {
          id: `${crmId}-${processoId}-${dialogId}-${Date.now()}`,
          crmId,
          processoId,
          dialogId,
          prazoEmMeses: prazoMeses,
          status: 'PENDENTE',
          dataEnvio: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        }
      });
    }

    // Atualizar status para EM_PROCESSAMENTO e incrementar tentativas
    chatGuruLogger.debug(`Atualizando status do comunicado para EM_PROCESSAMENTO: ${comunicado.id}`);
    await prisma.comunicadoPrazoMerito.update({
      where: { id: comunicado.id },
      data: {
        status: 'EM_PROCESSAMENTO',
        tentativas: { increment: 1 },
        ultimaTentativa: new Date(),
        updatedAt: new Date()
      }
    });

    // Aqui você implementará a lógica para executar o diálogo no ChatGuru
    // Esta é uma função mockada, substitua pela integração real
    
    // Simulando chamada à API do ChatGuru
    // Na implementação real, aqui você faria uma chamada HTTP para a API do ChatGuru
    
    // Simulando execução (aqui você faria a chamada real para a API do ChatGuru)
    const sucessoExecucao = Math.random() > 0.2; // 80% de chance de sucesso para simulação
    const mensagemErro = sucessoExecucao ? null : 'Erro de conexão com o ChatGuru';
    const detalhesErroJson = mensagemErro ? { 
      tipo: 'ERRO_API', 
      detalhes: 'Detalhes técnicos do erro',
      codigoHttp: 500,
      timestamp: new Date().toISOString()
    } : undefined;
    
    // Registrar no log
    chatGuruLogger.logDialogoExecutado(
      crmId, 
      dialogId, 
      processoId, 
      estagioProcesso, 
      sucessoExecucao, 
      mensagemErro || undefined
    );
    
    // Registrar o resultado no histórico
    await prisma.historicoComunicadoPrazoMerito.create({
      data: {
        comunicadoPrazoMeritoId: comunicado.id,
        dataTentativa: new Date(),
        status: sucessoExecucao ? 'ENVIADO' : 'FALHA',
        success: sucessoExecucao,
        errorMessage: mensagemErro,
        detalhesErro: detalhesErroJson,
        createdAt: new Date()
      }
    });

    // Atualizar o comunicado com o resultado
    await prisma.comunicadoPrazoMerito.update({
      where: { id: comunicado.id },
      data: {
        status: sucessoExecucao ? 'ENVIADO' : 'FALHA',
        success: sucessoExecucao,
        errorMessage: mensagemErro,
        detalhesErro: detalhesErroJson,
        updatedAt: new Date()
      }
    });
    
    return { 
      success: sucessoExecucao, 
      errorMessage: mensagemErro || undefined 
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    chatGuruLogger.error(`Erro ao executar diálogo no ChatGuru: ${errorMessage}`, { 
      error, 
      crmId, 
      dialogId, 
      processoId 
    });
    
    // Tenta registrar a falha no histórico e atualizar o comunicado
    try {
      const comunicado = await prisma.comunicadoPrazoMerito.findFirst({
        where: {
          crmId,
          processoId,
          dialogId
        }
      });

      if (comunicado) {
        const errorMsg = error instanceof Error ? error.message : 'Erro desconhecido';
        const detalhesErroJson = { 
          tipo: 'ERRO_INTERNO', 
          stack: error instanceof Error ? error.stack : null 
        };
        
        await prisma.historicoComunicadoPrazoMerito.create({
          data: {
            comunicadoPrazoMeritoId: comunicado.id,
            dataTentativa: new Date(),
            status: 'FALHA',
            success: false,
            errorMessage: errorMsg,
            detalhesErro: detalhesErroJson,
            createdAt: new Date()
          }
        });

        await prisma.comunicadoPrazoMerito.update({
          where: { id: comunicado.id },
          data: {
            status: 'FALHA',
            success: false,
            errorMessage: errorMsg,
            detalhesErro: detalhesErroJson,
            updatedAt: new Date()
          }
        });
      }
    } catch (dbError) {
      chatGuruLogger.error('Erro ao registrar falha no banco de dados:', dbError);
    }
    
    return { 
      success: false, 
      errorMessage: error instanceof Error ? error.message : 'Erro desconhecido' 
    };
  }
} 