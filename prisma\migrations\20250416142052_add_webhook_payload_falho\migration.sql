-- CreateTable
CREATE TABLE "WebhookPayloadFalho" (
    "id" TEXT NOT NULL,
    "tipoWebhook" TEXT NOT NULL,
    "payload" JSONB NOT NULL,
    "mensagem<PERSON>rro" TEXT NOT NULL,
    "dataFalha" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processadoManualmente" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "WebhookPayloadFalho_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "WebhookPayloadFalho_dataFalha_idx" ON "WebhookPayloadFalho"("dataFalha");

-- CreateIndex
CREATE INDEX "WebhookPayloadFalho_tipoWebhook_idx" ON "WebhookPayloadFalho"("tipoWebhook");

-- CreateIndex
CREATE INDEX "WebhookPayloadFalho_processadoManualmente_idx" ON "WebhookPayloadFalho"("processadoManualmente");
