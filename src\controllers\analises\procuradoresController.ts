import { Request, Response } from 'express';
import prisma from '../../dbClient';

// Endpoint para listar processos com despacho de publicação
export const listarProcessosPublicados = async (req: Request, res: Response): Promise<any> => {
  try {
    const { ano, dataInicio, dataFim, page = 1, limit = 200 } = req.query;
    const skip = (Number(page) - 1) * Number(limit);
    
    let dataFilter: any = {};
    
    if (ano) {
      const anoNum = Number(ano);
      dataFilter = {
        gte: new Date(`${anoNum}-01-01T00:00:00.000Z`),
        lt: new Date(`${anoNum + 1}-01-01T00:00:00.000Z`)
      };
    } else if (dataInicio && dataFim) {
      dataFilter = {
        gte: new Date(dataInicio as string),
        lte: new Date(dataFim as string)
      };
    } else {
      return res.status(400).json({
        error: 'É necessário fornecer o ano ou o período (dataInicio e dataFim)'
      });
    }

    const whereClause = {
      nome: {
        contains: 'Publicação de pedido de registro para oposição',
        mode: 'insensitive' as const
      },
      rpi: {
        dataPublicacao: dataFilter,
        numero: {
          lte: 9999 // Excluir RPIs de teste
        }
      }
    };

    const processos = await prisma.despacho.findMany({
      where: whereClause,
      select: {
        id: true,
        codigo: true,
        nome: true,
        processo: {
          select: {
            id: true,
            numero: true,
            procurador: {
              select: {
                id: true,
                nome: true
              }
            },
            marca: {
              select: {
                id: true,
                nome: true,
                ncl: {
                  select: {
                    codigo: true
                  }
                }
              }
            }
          }
        },
        rpi: {
          select: {
            id: true,
            numero: true,
            dataPublicacao: true
          }
        }
      },
      take: Number(limit),
      skip: skip,
      orderBy: {
        rpi: {
          dataPublicacao: 'desc'
        }
      }
    });

    const totalRegistros = await prisma.despacho.count({
      where: whereClause // Reutiliza a mesma cláusula where
    });

    const resultado = processos.map(despacho => ({
      numeroProcesso: despacho.processo.numero,
      nomeDespacho: despacho.nome,
      dataDespacho: despacho.rpi.dataPublicacao,
      numeroRPI: despacho.rpi.numero,
      procurador: despacho.processo.procurador?.nome || 'Não informado',
      nomeMarca: despacho.processo.marca?.nome || 'Não informado',
      classe: despacho.processo.marca?.ncl[0]?.codigo || 'Não informado'
    }));

    return res.status(200).json({
      data: resultado,
      pagination: {
        total: totalRegistros,
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(totalRegistros / Number(limit))
      }
    });
  } catch (error) {
    console.error('Erro ao listar processos publicados:', error);
    return res.status(500).json({ error: 'Erro ao processar a requisição' });
  }
};

// Endpoint para ranking de procuradores por data de depósito
export const rankingProcuradores = async (req: Request, res: Response): Promise<any> => {
  try { 
    const { ano, dataInicio, dataFim, excluirSemProcurador, page = 1, limit = 50 } = req.query;
    const deveExcluirSemProcurador = String(excluirSemProcurador).toLowerCase() === 'true';

    let dataFilter: any = {};
    
    if (ano) {
      const anoNum = Number(ano);
      dataFilter = {
        gte: new Date(`${anoNum}-01-01T00:00:00.000Z`),
        lt: new Date(`${anoNum + 1}-01-01T00:00:00.000Z`)
      };
    } else if (dataInicio && dataFim) {
      dataFilter = {
        gte: new Date(dataInicio as string),
        lte: new Date(dataFim as string)
      };
    } else {
      return res.status(400).json({
        error: 'É necessário fornecer o ano ou o período (dataInicio e dataFim)'
      });
    }

    const whereClauseProcesso: any = {
      dataDeposito: dataFilter,
      despachos: {
        some: {
          nome: {
            contains: 'Publicação',
            mode: 'insensitive' as const
          }
        }
      }
    };

    if (deveExcluirSemProcurador) {
      whereClauseProcesso.procuradorId = { not: null };
    }

    // OTIMIZAÇÃO 1: Fazer paginação direto no banco com LIMIT/OFFSET
    const skip = (Number(page) - 1) * Number(limit);
    
    // OTIMIZAÇÃO 2: Buscar apenas a página necessária + total em paralelo
    const [rankingPaginado, totalProcuradores, totalProcessosDepositados] = await Promise.all([
      prisma.processo.groupBy({
        by: ['procuradorId'],
        where: whereClauseProcesso,
        _count: { _all: true },
        orderBy: { _count: { id: 'desc' } },
        take: Number(limit),
        skip: skip
      }),
      
      prisma.processo.groupBy({
        by: ['procuradorId'],
        where: whereClauseProcesso,
        _count: { _all: true }
      }).then(result => result.length),
      
      prisma.processo.count({ where: whereClauseProcesso })
    ]);

    const procuradorIds = rankingPaginado
      .map(item => item.procuradorId)
      .filter(id => id !== null) as string[];

    // DEBUG: Verificar se estamos analisando apenas os procuradores da página
    console.log(`🔍 DEBUG - Página ${page}: Analisando ${procuradorIds.length} procuradores`);
    console.log(`📋 DEBUG - IDs dos procuradores da página:`, procuradorIds.slice(0, 5)); // Primeiros 5 IDs

    if (procuradorIds.length === 0) {
      return res.status(200).json({
        data: [],
        pagination: {
          total: totalProcuradores,
          page: Number(page),
          limit: Number(limit),
          pages: Math.ceil(totalProcuradores / Number(limit))
        },
        totalProcessosDepositados: totalProcessosDepositados,
        resumoMeritoPagina: {
          totalProcessosComMerito: 0,
          totalDeferimentos: 0,
          totalIndeferimentos: 0,
          observacao: 'Nenhum procurador encontrado na página solicitada.'
        }
      });
    }

    // OTIMIZAÇÃO 3: Buscar nomes dos procuradores e despachos de mérito em paralelo
    const [procuradoresInfo, despachosMerito] = await Promise.all([
      prisma.procurador.findMany({
        where: { id: { in: procuradorIds } },
        select: { id: true, nome: true }
      }),
      
      // OTIMIZAÇÃO 4: Query mais eficiente para despachos de mérito
      prisma.despacho.findMany({
        where: {
          rpi: { dataPublicacao: dataFilter },
          processo: {
            procuradorId: { in: procuradorIds }, // ← ESTE filtro garante apenas os 50 da página
            ...(deveExcluirSemProcurador ? { procuradorId: { not: null } } : {})
          },
          OR: [
            { nome: { contains: 'Deferimento do pedido', mode: 'insensitive' as const } },
            { nome: { contains: 'Recurso provido (decisão reformada para: Deferimento)', mode: 'insensitive' as const } },
            { nome: { contains: 'Indeferimento do pedido', mode: 'insensitive' as const } }
          ]
        },
        select: {
          nome: true,
          processo: { select: { id: true, procuradorId: true } },
          rpi: { select: { dataPublicacao: true } }
        },
        // OTIMIZAÇÃO 5: Ordenar no banco, não em memória
        orderBy: [
          { processo: { id: 'asc' } },
          { rpi: { dataPublicacao: 'desc' } }
        ]
      })
    ]);

    // DEBUG: Verificar quantos despachos de mérito foram encontrados
    console.log(`⚖️ DEBUG - Encontrados ${despachosMerito.length} despachos de mérito para os ${procuradorIds.length} procuradores da página`);
    
    // DEBUG: Verificar se os despachos pertencem realmente aos procuradores da página
    const procuradoresComMerito = new Set(despachosMerito.map(d => d.processo.procuradorId));
    console.log(`👥 DEBUG - ${procuradoresComMerito.size} procuradores da página têm despachos de mérito`);

    const procuradorMap = new Map(procuradoresInfo.map(p => [p.id, p.nome]));

    // OTIMIZAÇÃO 6: Processamento mais eficiente - uma só passada
    const estatisticasMeritoPorProcurador = new Map<string | null, {
      totalMerito: number;
      deferimentos: number;
      indeferimentos: number;
      taxaAssertividade: number;
    }>();

    // OTIMIZAÇÃO 7: Agrupar e processar em uma única passada
    const processosVistos = new Set<string>();
    let totalDeferimentos = 0;
    let totalIndeferimentos = 0;
    
    for (const despacho of despachosMerito) {
      const processoId = despacho.processo.id;
      const procuradorId = despacho.processo.procuradorId;
      
      // Se já vimos este processo, pular (pega apenas o primeiro que é o mais recente devido ao ORDER BY)
      if (processosVistos.has(processoId)) continue;
      processosVistos.add(processoId);
      
      // Inicializar estatísticas se necessário
      if (!estatisticasMeritoPorProcurador.has(procuradorId)) {
        estatisticasMeritoPorProcurador.set(procuradorId, {
          totalMerito: 0,
          deferimentos: 0,
          indeferimentos: 0,
          taxaAssertividade: 0
        });
      }

      const stats = estatisticasMeritoPorProcurador.get(procuradorId)!;
      stats.totalMerito++;

      const nomeDespacho = despacho.nome?.toLowerCase() || '';
      
      if (nomeDespacho.includes('indeferimento do pedido')) {
        stats.indeferimentos++;
        totalIndeferimentos++;
      } else if (nomeDespacho.includes('deferimento do pedido') || 
                 nomeDespacho.includes('recurso provido (decisão reformada para: deferimento)')) {
        stats.deferimentos++;
        totalDeferimentos++;
      }

      // Recalcular taxa de assertividade
      stats.taxaAssertividade = stats.totalMerito > 0 
        ? Number((stats.deferimentos / stats.totalMerito * 100).toFixed(2))
        : 0;
    }

    // OTIMIZAÇÃO 8: Construir resultado final mais eficientemente
    const resultado = rankingPaginado
      .filter(item => !deveExcluirSemProcurador || item.procuradorId !== null)
      .map((item, index) => {
        const nomeProcurador = procuradorMap.get(item.procuradorId as string) || 
                              (item.procuradorId === null ? 'Sem procurador' : 'Procurador não encontrado');
        const quantidadeProcessos = item._count._all;
        const porcentagem = totalProcessosDepositados > 0
          ? Number(((quantidadeProcessos / totalProcessosDepositados) * 100).toFixed(2))
          : 0;
        
        const estatisticasMerito = estatisticasMeritoPorProcurador.get(item.procuradorId) || {
          totalMerito: 0,
          deferimentos: 0,
          indeferimentos: 0,
          taxaAssertividade: 0
        };
        
        return {
          posicao: skip + index + 1,
          procuradorNome: nomeProcurador,
          quantidadeProcessos: quantidadeProcessos,
          porcentagem: porcentagem,
          merito: {
            totalProcessosComMerito: estatisticasMerito.totalMerito,
            deferimentos: estatisticasMerito.deferimentos,
            indeferimentos: estatisticasMerito.indeferimentos,
            taxaAssertividade: estatisticasMerito.taxaAssertividade
          }
        };
      });

    return res.status(200).json({
      data: resultado,
      pagination: {
        total: totalProcuradores,
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(totalProcuradores / Number(limit))
      },
      totalProcessosDepositados: totalProcessosDepositados,
      resumoMeritoPagina: {
        totalProcessosComMerito: processosVistos.size,
        totalDeferimentos: totalDeferimentos,
        totalIndeferimentos: totalIndeferimentos,
        observacao: 'Ranking baseado em processos DEPOSITADOS no período. Mérito baseado em despachos PUBLICADOS no período. Contabiliza apenas a decisão final de cada processo (evita contagem dupla de recursos).'
      }
    });
  } catch (error) {
    console.error('Erro ao gerar ranking de procuradores por depósito:', error);
    return res.status(500).json({ error: 'Erro ao processar a requisição' });
  }
};

// Endpoint para listar processos com despachos de mérito
export const listarProcessosComMerito = async (req: Request, res: Response): Promise<any> => {
  try {
    const { ano, dataInicio, dataFim, page = 1, limit = 200, tipoMerito, procuradorId } = req.query;
    const skip = (Number(page) - 1) * Number(limit);
    
    let dataFilter: any = {};
    
    if (ano) {
      const anoNum = Number(ano);
      dataFilter = {
        gte: new Date(`${anoNum}-01-01T00:00:00.000Z`),
        lt: new Date(`${anoNum + 1}-01-01T00:00:00.000Z`)
      };
    } else if (dataInicio && dataFim) {
      dataFilter = {
        gte: new Date(dataInicio as string),
        lte: new Date(dataFim as string)
      };
    } else {
      return res.status(400).json({
        error: 'É necessário fornecer o ano ou o período (dataInicio e dataFim)'
      });
    }

    // Construir filtros para os tipos de mérito
    let despachoFilter: any = {
      OR: [
        {
          nome: {
            contains: 'Deferimento do pedido',
            mode: 'insensitive' as const
          }
        },
        {
          nome: {
            contains: 'Recurso provido (decisão reformada para: Deferimento)',
            mode: 'insensitive' as const
          }
        },
        {
          nome: {
            contains: 'Indeferimento do pedido',
            mode: 'insensitive' as const
          }
        }
      ]
    };

    // Filtrar por tipo específico de mérito se fornecido
    if (tipoMerito) {
      const tipo = String(tipoMerito).toLowerCase();
      if (tipo === 'deferimento') {
        despachoFilter = {
          OR: [
            {
              nome: {
                contains: 'Deferimento do pedido',
                mode: 'insensitive' as const
              }
            },
            {
              nome: {
                contains: 'Recurso provido (decisão reformada para: Deferimento)',
                mode: 'insensitive' as const
              }
            }
          ]
        };
      } else if (tipo === 'indeferimento') {
        despachoFilter = {
          nome: {
            contains: 'Indeferimento do pedido',
            mode: 'insensitive' as const
          }
        };
      }
    }

    // Construir filtros do processo
    const processoFilter: any = {
      dataDeposito: dataFilter
    };

    // Filtrar por procurador específico se fornecido
    if (procuradorId) {
      processoFilter.procuradorId = String(procuradorId);
    }

    const whereClause = {
      processo: processoFilter,
      ...despachoFilter
    };

    // Query mais eficiente usando índices
    const despachosMerito = await prisma.despacho.findMany({
      where: whereClause,
      select: {
        id: true,
        codigo: true,
        nome: true,
        processo: {
          select: {
            id: true,
            numero: true,
            dataDeposito: true,
            procurador: {
              select: {
                id: true,
                nome: true
              }
            },
            marca: {
              select: {
                id: true,
                nome: true,
                ncl: {
                  select: {
                    codigo: true
                  },
                  take: 1 // Pegar apenas a primeira classe para performance
                }
              }
            }
          }
        },
        rpi: {
          select: {
            id: true,
            numero: true,
            dataPublicacao: true
          }
        }
      },
      take: Number(limit),
      skip: skip,
      orderBy: [
        {
          rpi: {
            dataPublicacao: 'desc'
          }
        },
        {
          processo: {
            numero: 'asc'
          }
        }
      ]
    });

    // Count separado para performance
    const totalRegistros = await prisma.despacho.count({
      where: whereClause
    });

    // Classificar cada despacho como favorável ou não
    const resultado = despachosMerito.map(despacho => {
      const nomeDespacho = despacho.nome?.toLowerCase() || '';
      // Verificar INDEFERIMENTO primeiro para evitar classificação incorreta
      const isIndeferimento = nomeDespacho.includes('indeferimento do pedido');
      const isFavoravel = !isIndeferimento && (
        nomeDespacho.includes('deferimento do pedido') || 
        nomeDespacho.includes('recurso provido (decisão reformada para: deferimento)')
      );
      
      return {
        numeroProcesso: despacho.processo.numero,
        nomeDespacho: despacho.nome,
        tipoMerito: isFavoravel ? 'Favorável' : 'Desfavorável',
        dataDespacho: despacho.rpi.dataPublicacao,
        numeroRPI: despacho.rpi.numero,
        procurador: despacho.processo.procurador?.nome || 'Não informado',
        procuradorId: despacho.processo.procurador?.id || null,
        nomeMarca: despacho.processo.marca?.nome || 'Não informado',
        classe: despacho.processo.marca?.ncl[0]?.codigo || 'Não informado',
        dataDeposito: despacho.processo.dataDeposito
      };
    });

    // Calcular estatísticas apenas dos registros retornados para performance
    const totalFavoraveis = resultado.filter(r => r.tipoMerito === 'Favorável').length;
    const totalDesfavoraveis = resultado.filter(r => r.tipoMerito === 'Desfavorável').length;
    
    // Taxa de assertividade calculada sobre o total de registros
    const taxaAssertividadeGeral = totalRegistros > 0 
      ? Number((totalFavoraveis / totalRegistros * 100).toFixed(2))
      : 0;

    return res.status(200).json({
      data: resultado,
      pagination: {
        total: totalRegistros,
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(totalRegistros / Number(limit))
      },
      estatisticas: {
        totalProcessosComMerito: totalRegistros,
        totalFavoraveisNaPagina: totalFavoraveis,
        totalDesfavoraveisNaPagina: totalDesfavoraveis,
        taxaAssertividadeGeral: taxaAssertividadeGeral,
        observacao: 'Contadores da página atual. Taxa geral calculada sobre o total de registros.'
      }
    });
  } catch (error) {
    console.error('Erro ao listar processos com mérito:', error);
    return res.status(500).json({ error: 'Erro ao processar a requisição' });
  }
};

// Endpoint para ranking de procuradores por despachos de mérito publicados no período
export const rankingProcuradoresPorMerito = async (req: Request, res: Response): Promise<any> => {
  try {
    const { ano, dataInicio, dataFim, excluirSemProcurador, page = 1, limit = 50 } = req.query;
    const deveExcluirSemProcurador = String(excluirSemProcurador).toLowerCase() === 'true';

    let dataFilter: any = {};
    
    if (ano) {
      const anoNum = Number(ano);
      dataFilter = {
        gte: new Date(`${anoNum}-01-01T00:00:00.000Z`),
        lt: new Date(`${anoNum + 1}-01-01T00:00:00.000Z`)
      };
    } else if (dataInicio && dataFim) {
      dataFilter = {
        gte: new Date(dataInicio as string),
        lte: new Date(dataFim as string)
      };
    } else {
      return res.status(400).json({
        error: 'É necessário fornecer o ano ou o período (dataInicio e dataFim)'
      });
    }

    // Buscar todos os despachos de mérito publicados no período
    const despachosMerito = await prisma.despacho.findMany({
      where: {
        rpi: {
          dataPublicacao: dataFilter
        },
        processo: {
          ...(deveExcluirSemProcurador ? { procuradorId: { not: null } } : {})
        },
        OR: [
          {
            nome: {
              contains: 'Deferimento do pedido',
              mode: 'insensitive' as const
            }
          },
          {
            nome: {
              contains: 'Recurso provido (decisão reformada para: Deferimento)',
              mode: 'insensitive' as const
            }
          },
          {
            nome: {
              contains: 'Indeferimento do pedido',
              mode: 'insensitive' as const
            }
          }
        ]
      },
      select: {
        id: true,
        nome: true,
        processo: {
          select: {
            id: true,
            numero: true,
            procuradorId: true,
            procurador: {
              select: {
                id: true,
                nome: true
              }
            }
          }
        },
        rpi: {
          select: {
            dataPublicacao: true
          }
        }
      }
    });

    // Calcular estatísticas por procurador
    const estatisticasPorProcurador = new Map<string | null, {
      totalMerito: number;
      deferimentos: number;
      indeferimentos: number;
      taxaAssertividade: number;
      nomeProcurador: string;
    }>();

    // Agrupar despachos por processo para identificar decisão final
    const despachosPorProcesso = new Map<string, typeof despachosMerito[0][]>();
    
    despachosMerito.forEach(despacho => {
      const processoId = despacho.processo.id;
      if (!despachosPorProcesso.has(processoId)) {
        despachosPorProcesso.set(processoId, []);
      }
      despachosPorProcesso.get(processoId)!.push(despacho);
    });

    // Para cada processo, considerar apenas a decisão final (mais recente)
    despachosPorProcesso.forEach(despachos => {
      // Ordenar por data de publicação (mais recente primeiro)
      const despachosOrdenados = despachos.sort((a, b) => 
        new Date(b.rpi.dataPublicacao).getTime() - new Date(a.rpi.dataPublicacao).getTime()
      );
      
      // Considerar apenas o despacho mais recente (decisão final)
      const decisaoFinal = despachosOrdenados[0];
      const procuradorId = decisaoFinal.processo.procuradorId;
      const nomeProcurador = decisaoFinal.processo.procurador?.nome || 'Sem procurador';
      
      if (!estatisticasPorProcurador.has(procuradorId)) {
        estatisticasPorProcurador.set(procuradorId, {
          totalMerito: 0,
          deferimentos: 0,
          indeferimentos: 0,
          taxaAssertividade: 0,
          nomeProcurador: nomeProcurador
        });
      }

      const stats = estatisticasPorProcurador.get(procuradorId)!;
      stats.totalMerito++; // Conta apenas 1 vez por processo

      const nomeDespacho = decisaoFinal.nome?.toLowerCase() || '';
      
      // Verificar INDEFERIMENTO primeiro para evitar classificação incorreta
      if (nomeDespacho.includes('indeferimento do pedido')) {
        stats.indeferimentos++;
      } else if (nomeDespacho.includes('deferimento do pedido') || 
                 nomeDespacho.includes('recurso provido (decisão reformada para: deferimento)')) {
        stats.deferimentos++;
      }

      // Calcular taxa de assertividade
      stats.taxaAssertividade = stats.totalMerito > 0 
        ? Number((stats.deferimentos / stats.totalMerito * 100).toFixed(2))
        : 0;
    });

    // Converter para array e ordenar por total de méritos
    const rankingCompleto = Array.from(estatisticasPorProcurador.values())
      .filter(item => !deveExcluirSemProcurador || item.nomeProcurador !== 'Sem procurador')
      .sort((a, b) => b.totalMerito - a.totalMerito);

    // Aplicar paginação
    const skip = (Number(page) - 1) * Number(limit);
    const totalProcuradores = rankingCompleto.length;
    const rankingPaginado = rankingCompleto.slice(skip, skip + Number(limit));

    const resultado = rankingPaginado.map((stats, index) => {
      const totalGeral = despachosMerito.length;
      const porcentagem = totalGeral > 0
        ? Number((stats.totalMerito / totalGeral * 100).toFixed(2))
        : 0;

      return {
        posicao: skip + index + 1, // Posição global no ranking
        procuradorNome: stats.nomeProcurador,
        totalProcessosComMerito: stats.totalMerito,
        porcentagemDoTotal: porcentagem,
        deferimentos: stats.deferimentos,
        indeferimentos: stats.indeferimentos,
        taxaAssertividade: stats.taxaAssertividade
      };
    });

    // Calcular resumo geral considerando apenas decisões finais para evitar contagem dupla
    const decisoesFinaisGeral = Array.from(despachosPorProcesso.values()).map(despachos => {
      return despachos.sort((a, b) => 
        new Date(b.rpi.dataPublicacao).getTime() - new Date(a.rpi.dataPublicacao).getTime()
      )[0]; // Decisão mais recente
    });

    const totalIndeferimentos = decisoesFinaisGeral.filter(d => 
      d.nome?.toLowerCase().includes('indeferimento do pedido')
    ).length;
    
    const totalDeferimentos = decisoesFinaisGeral.filter(d => {
      const nome = d.nome?.toLowerCase() || '';
      return !nome.includes('indeferimento do pedido') && (
        nome.includes('deferimento do pedido') || 
        nome.includes('recurso provido (decisão reformada para: deferimento)')
      );
    }).length;

    const taxaAssertividadeGeral = decisoesFinaisGeral.length > 0
      ? Number((totalDeferimentos / decisoesFinaisGeral.length * 100).toFixed(2))
      : 0;

    return res.status(200).json({
      data: resultado,
      pagination: {
        total: totalProcuradores,
        page: Number(page),
        limit: Number(limit),
        pages: Math.ceil(totalProcuradores / Number(limit))
      },
      resumoGeral: {
        totalProcessosComMerito: decisoesFinaisGeral.length, // Processos únicos, não despachos duplicados
        totalDeferimentos: totalDeferimentos,
        totalIndeferimentos: totalIndeferimentos,
        taxaAssertividadeGeral: taxaAssertividadeGeral,
        observacao: 'Ranking baseado exclusivamente em despachos de mérito PUBLICADOS no período filtrado. Contabiliza apenas a decisão final de cada processo (evita contagem dupla de recursos).'
      }
    });
  } catch (error) {
    console.error('Erro ao gerar ranking de procuradores por mérito:', error);
    return res.status(500).json({ error: 'Erro ao processar a requisição' });
  }
}; 