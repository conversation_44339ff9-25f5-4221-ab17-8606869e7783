# 🔗 Integração de Protocolos com CRM

## Visão Geral

Sistema de integração automática que conecta o upload de protocolos com o CRM Pipe Run, criando clientes, processos, links de auto-login e atualizando o ChatGuru.

## 🏗️ Arquitetura

### Fluxo de Processamento

```
Upload PDF → Extração Metadados → Fila de Processamento
                                        ↓
CRM Pipe → Cliente → Processo → Link → ChatGuru
```

### Componentes

- **Controller**: `protocoloController.ts` - Upload e extração
- **Serviço**: `processamentoProtocoloService.ts` - Processamento completo
- **Job**: `processamentoProtocoloJob.ts` - Execução a cada 2 minutos
- **Modelo**: `ProcessamentoProtocolo` - Controle de estado

## 📊 Modelo de Dados

### ProcessamentoProtocolo

```typescript
{
  id: string                    // UUID único
  numeroProcesso: string        // Número do processo extraído
  elementoNominativo: string    // Nome da marca
  nomeArquivoPdf: string        // Nome do arquivo PDF
  status: StatusProcessamento   // Estado atual
  
  // Dados do CRM
  crmDealId: number
  crmPersonId: number
  crmCompanyId: number
  dadosCrm: Json
  
  // Cliente
  clienteId: number
  clienteCriado: boolean
  clienteAtualizado: boolean
  
  // Processo
  processoId: string
  processoVinculado: boolean
  
  // Link
  linkGerado: boolean
  autoLoginUrl: string
  
  // ChatGuru
  chatguruAtualizado: boolean
  telefoneChatguru: string
  
  // Error handling
  tentativas: number
  ultimoErro: string
  proximaTentativa: DateTime
}
```

### Status de Processamento

- `PENDENTE` - Aguardando processamento
- `PROCESSANDO` - Em execução
- `SUCESSO` - Concluído com sucesso
- `FALHA_CRM` - Erro na busca do CRM
- `FALHA_CLIENTE` - Erro na criação/atualização do cliente
- `FALHA_PROCESSO` - Erro na criação do processo
- `FALHA_LINK` - Erro na geração do link
- `FALHA_CHATGURU` - Erro na atualização do ChatGuru
- `FALHA_GERAL` - Erro geral/desconhecido

## 🚀 Funcionamento

### 1. Upload de Protocolo

Quando um PDF é enviado:

1. Extração de metadados (número processo + elemento nominativo)
2. Renomeação do arquivo
3. Extração da logo
4. **Novo**: Criação de registro na fila se metadados extraídos

### 2. Processamento Automático

Job executa **a cada 2 minutos** e processa até **5 protocolos** por vez:

#### Etapa 1: Busca no CRM
- Consulta API Pipe: `custom_fields[194250]={numeroProcesso}`
- Extrai dados da pessoa e empresa
- Salva informações completas

#### Etapa 2: Cliente
- Gera identificadores de TODOS os telefones
- Busca cliente existente por QUALQUER identificador
- Se encontrar: atualiza; se não: cria novo
- Prioriza telefone principal (`is_main = 1`)

#### Etapa 3: Processo
- Verifica se processo já existe para o cliente
- Se não existe, cria novo processo vinculado

#### Etapa 4: Link de Auto-Login
- Gera senha (últimos 3 dígitos do documento/telefone)
- Criptografa dados com AES-256-CBC
- Cria código curto único (6 caracteres)
- Salva na tabela `ShortUrl`
- Atualiza campo `autoLoginUrl` do cliente

#### Etapa 5: ChatGuru
- Atualiza campos: `IDC`, `Senha_Cliente`, `Link_Cliente`
- Tenta todos os telefones até conseguir
- Salva telefone que funcionou

### 3. Error Handling

- **Máximo 3 tentativas** por protocolo
- **Delays progressivos**: 5min, 10min, 15min
- **Estados específicos** para cada tipo de falha
- **Logs detalhados** de todos os erros

## 📡 API de Monitoramento

### Endpoints Disponíveis

#### GET `/api/fila-protocolo/estatisticas`
Estatísticas gerais da fila:

```json
{
  "total": 150,
  "estatisticas": {
    "PENDENTE": 5,
    "PROCESSANDO": 1,
    "SUCESSO": 130,
    "FALHA_CRM": 8,
    "FALHA_CLIENTE": 3,
    "FALHA_CHATGURU": 3
  },
  "ultimosProcessados": [...],
  "falhasRecentes": [...]
}
```

#### GET `/api/fila-protocolo`
Lista protocolos com filtros:

**Parâmetros:**
- `status` - Filtrar por status
- `numeroProcesso` - Buscar por número
- `page` - Página (padrão: 1)
- `limit` - Itens por página (padrão: 20)

#### POST `/api/fila-protocolo/{id}/reprocessar`
Reprocessa um protocolo específico

#### DELETE `/api/fila-protocolo/{id}`
Remove protocolo da fila

## 🔧 Configuração

### Variáveis de Ambiente Necessárias

```env
# CRM Pipe Run
PIPE_TOKEN=seu_token_pipe

# Criptografia
JWT_SECRET=sua_chave_secreta

# ChatGuru
# (URL já configurada no código)
```

### Dependências

```json
{
  "node-cron": "^3.0.3",
  "axios": "^1.9.0",
  "crypto": "built-in"
}
```

## 🎯 Vantagens da Implementação

### 1. **Processamento Assíncrono**
- Upload não trava esperando integração
- Job processa em background
- Resiliência a falhas de APIs externas

### 2. **Error Handling Robusto**
- Estados específicos para cada etapa
- Sistema de retry inteligente
- Logs detalhados para debugging

### 3. **Evita Duplicatas**
- Busca por TODOS os identificadores possíveis
- Atualiza clientes existentes
- Verificação de processos duplicados

### 4. **Monitoramento Completo**
- API para visualizar estatísticas
- Controle de falhas
- Reprocessamento manual

### 5. **Performance**
- Processamento em lotes pequenos
- Rate limiting entre APIs
- Delays configuráveis

## 🚨 Monitoramento e Alertas

### Logs Importantes

```bash
# Sucesso
✅ Protocolo ********* processado com sucesso!

# Falhas
❌ Falha no protocolo *********: Protocolo não encontrado no CRM
❌ Erro no ChatGuru para (11)99999-9999: Request timeout
```

### Indicadores de Saúde

- **Taxa de Sucesso** > 90%
- **Tempo Médio** < 2 minutos por protocolo
- **Falhas Consecutivas** < 5

### Ações em Caso de Problemas

1. **Falhas no CRM**: Verificar token `PIPE_TOKEN`
2. **Falhas no Cliente**: Verificar dados no CRM (telefones)
3. **Falhas no ChatGuru**: Verificar conectividade
4. **Falhas Gerais**: Verificar logs do servidor

## 📈 Métricas de Desempenho

### Estimativas

- **Processamento**: ~30-60 segundos por protocolo
- **Throughput**: ~150 protocolos/hora
- **Retry Rate**: <10% em condições normais

### Limitações

- **CRM**: Rate limit da API Pipe
- **ChatGuru**: Timeout de 30 segundos
- **Banco**: Transações simultâneas

## 🔄 Operação

### Iniciação Automática

O job inicia automaticamente com o servidor:

```typescript
// src/server.ts
app.listen(PORT, async () => {
  iniciarJobProcessamentoProtocolos(); // ← Aqui
});
```

### Comando Manual

Para executar processamento pontual:

```bash
# Em desenvolvimento
npm run dev

# Em produção 
npm start
```

## 🧪 Testes

### Cenários de Teste

1. **Upload com metadados válidos**
2. **Protocolo existente no CRM**
3. **Cliente já cadastrado**
4. **Falha de conectividade**
5. **Dados incompletos no CRM**

### Validação

- Verificar criação do registro na fila
- Monitorar logs do job
- Conferir dados criados no banco
- Testar links gerados

---

## 📚 Próximos Passos

1. **Alertas**: Integração com sistemas de monitoramento
2. **Dashboard**: Interface visual para monitoramento
3. **Relatórios**: Análises de performance
4. **Webhook**: Notificações em tempo real
5. **Backup**: Estratégia de recuperação de falhas 