import { PrismaClient } from '@prisma/client';
import readline from 'readline';

const prisma = new PrismaClient();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function obterEstatisticasAtuais() {
  const [
    totalComunicados,
    totalComunicadosPrazoMerito,
    totalHistoricoComunicados
  ] = await Promise.all([
    prisma.comunicado.count(),
    prisma.comunicadoPrazoMerito.count(),
    prisma.historicoComunicadoPrazoMerito.count()
  ]);

  return {
    totalComunicados,
    totalComunicadosPrazoMerito,
    totalHistoricoComunicados,
    totalGeral: totalComunicados + totalComunicadosPrazoMerito + totalHistoricoComunicados
  };
}

async function limparTodosComunicados() {
  console.log('\n🔍 Analisando comunicados no banco de dados...\n');
  
  try {
    const stats = await obterEstatisticasAtuais();
    
    console.log('📊 Estatísticas atuais:');
    console.log(`   • Comunicados gerais: ${stats.totalComunicados}`);
    console.log(`   • Comunicados de prazo/mérito: ${stats.totalComunicadosPrazoMerito}`);
    console.log(`   • Histórico de comunicados: ${stats.totalHistoricoComunicados}`);
    console.log(`   • TOTAL: ${stats.totalGeral} registros\n`);
    
    if (stats.totalGeral === 0) {
      console.log('✅ Não há comunicados para deletar!');
      return;
    }

    console.warn('\x1b[31m%s\x1b[0m', '!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! ATENÇÃO !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');
    console.warn('\x1b[33m%s\x1b[0m', 'Este script irá DELETAR TODOS OS COMUNICADOS do banco de dados.');
    console.warn('\x1b[33m%s\x1b[0m', 'Isso inclui:');
    console.warn('\x1b[33m%s\x1b[0m', '  - Comunicados gerais (tabela Comunicado)');
    console.warn('\x1b[33m%s\x1b[0m', '  - Comunicados de prazo/mérito (tabela ComunicadoPrazoMerito)');
    console.warn('\x1b[33m%s\x1b[0m', '  - Histórico de comunicados (tabela HistoricoComunicadoPrazoMerito)');
    console.warn('\x1b[31m%s\x1b[0m', 'Esta ação é IRREVERSÍVEL.');
    console.warn('\x1b[33m%s\x1b[0m', 'Faça um BACKUP do seu banco de dados antes de prosseguir!');
    console.warn('\x1b[31m%s\x1b[0m', '!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');

    // Aguardar 3 segundos para o usuário ler
    await new Promise(resolve => setTimeout(resolve, 3000));

    return new Promise<void>((resolve) => {
      rl.question('\nVocê tem certeza que deseja continuar? Digite "SIM, DELETAR TODOS" para confirmar: ', async (answer) => {
        if (answer.trim() === 'SIM, DELETAR TODOS') {
          console.log('\n🔄 Iniciando a limpeza de comunicados...\n');
          
          try {
            // Usar transação para garantir atomicidade
            await prisma.$transaction(async (tx) => {
              console.log('1️⃣ Deletando histórico de comunicados de prazo/mérito...');
              const historicoResult = await tx.historicoComunicadoPrazoMerito.deleteMany({});
              console.log(`   ✅ ${historicoResult.count} registros de histórico deletados`);

              console.log('2️⃣ Deletando comunicados de prazo/mérito...');
              const prazoMeritoResult = await tx.comunicadoPrazoMerito.deleteMany({});
              console.log(`   ✅ ${prazoMeritoResult.count} comunicados de prazo/mérito deletados`);

              console.log('3️⃣ Deletando comunicados gerais...');
              const comunicadosResult = await tx.comunicado.deleteMany({});
              console.log(`   ✅ ${comunicadosResult.count} comunicados gerais deletados`);

              const totalDeletados = historicoResult.count + prazoMeritoResult.count + comunicadosResult.count;
              
              console.log('\n🎉 LIMPEZA CONCLUÍDA COM SUCESSO!');
              console.log('📊 Resumo da operação:');
              console.log(`   • Histórico de comunicados: ${historicoResult.count} deletados`);
              console.log(`   • Comunicados de prazo/mérito: ${prazoMeritoResult.count} deletados`);
              console.log(`   • Comunicados gerais: ${comunicadosResult.count} deletados`);
              console.log(`   • TOTAL: ${totalDeletados} registros removidos\n`);
            });

            // Verificar se realmente foi limpo
            const statsFinais = await obterEstatisticasAtuais();
            if (statsFinais.totalGeral === 0) {
              console.log('✅ Verificação: Todas as tabelas de comunicados estão vazias!');
            } else {
              console.log(`⚠️  Verificação: Ainda restam ${statsFinais.totalGeral} registros. Pode haver algum problema.`);
            }

          } catch (error) {
            console.error('\n❌ Erro durante a limpeza dos comunicados:', error);
            throw error;
          }
        } else {
          console.log('\n❌ Operação cancelada pelo usuário.');
        }
        
        resolve();
      });
    });

  } catch (error) {
    console.error('\n❌ Erro ao obter estatísticas:', error);
    throw error;
  }
}

// Função principal
async function main() {
  try {
    console.log('🚀 Iniciando script de limpeza de comunicados...');
    await limparTodosComunicados();
  } catch (error) {
    console.error('\n💥 Falha no script:', error);
    process.exit(1);
  } finally {
    rl.close();
    await prisma.$disconnect();
    console.log('\n👋 Script finalizado.');
    process.exit(0);
  }
}

// Executar o script
main(); 