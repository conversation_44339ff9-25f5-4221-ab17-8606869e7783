// scripts/listarClientesSemIdentificador.ts

import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import dotenv from 'dotenv';
import cliProgress from 'cli-progress';

dotenv.config();

const prisma = new PrismaClient();

// Função auxiliar para extrair DDD + últimos 8 dígitos do telefone (copiada de obterLeadsCrm.ts)
function extrairIdentificadorTelefone(telefone: string): string {
  if (!telefone || telefone === "NoPhone") {
    return "0000000000"; // Retorna 10 dígitos zerados quando não há telefone
  }

  // Remove todos os caracteres não numéricos
  const numeroLimpo = telefone.replace(/\D/g, '');
  
  // Se o número estiver vazio após limpeza
  if (!numeroLimpo) {
    return "0000000000";
  }

  let numeroProcessado = numeroLimpo;

  // Lista de DDDs válidos no Brasil (11-99, excluindo alguns não utilizados)
  const dddsValidos = new Set([
    11, 12, 13, 14, 15, 16, 17, 18, 19, // SP
    21, 22, 24, // RJ/ES
    27, 28, // ES
    31, 32, 33, 34, 35, 37, 38, // MG
    41, 42, 43, 44, 45, 46, // PR
    47, 48, 49, // SC
    51, 53, 54, 55, // RS
    61, // DF/GO
    62, 64, // GO
    63, // TO
    65, 66, // MT
    67, // MS
    68, // AC
    69, // RO
    71, 73, 74, 75, 77, // BA
    79, // SE
    81, 87, // PE
    82, // AL
    83, // PB
    84, // RN
    85, 88, // CE
    86, 89, // PI
    91, 93, 94, // PA
    92, 97, // AM
    95, // RR
    96, // AP
    98, 99 // MA
  ]);

  // Lógica melhorada para detecção de prefixo do país
  if (numeroProcessado.startsWith('55') && numeroProcessado.length >= 12) {
    // Verifica se após remover 55, temos um número válido brasileiro
    const semPrefixo = numeroProcessado.substring(2);
    
    // Número brasileiro tem 10 ou 11 dígitos (DDD + 8 ou 9 dígitos)
    if (semPrefixo.length === 10 || semPrefixo.length === 11) {
      // Verifica se o DDD é válido
      const possibleDDD = parseInt(semPrefixo.substring(0, 2));
      if (dddsValidos.has(possibleDDD)) {
        numeroProcessado = semPrefixo;
      }
    }
  }

  // Se ainda não temos pelo menos 10 dígitos, preenche com zeros
  if (numeroProcessado.length < 10) {
    numeroProcessado = numeroProcessado.padStart(10, '0');
  }

  // Extrai DDD (primeiros 2 dígitos) e últimos 8 dígitos
  const ddd = numeroProcessado.substring(0, 2);
  const ultimosOitoDigitos = numeroProcessado.slice(-8);
  
  return ddd + ultimosOitoDigitos;
}

// Controle de taxa para API do CRM
class RateLimiter {
  private queue: (() => Promise<void>)[] = [];
  private running = false;
  private requestCount = 0;
  private resetTime = Date.now() + 30000; // 30 segundos
  private maxRequests: number;
  private timeWindow: number;

  constructor(maxRequests = 120, timeWindow = 30000) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindow;
  }

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await fn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      if (!this.running) {
        this.processQueue();
      }
    });
  }

  private async processQueue() {
    if (this.queue.length === 0) {
      this.running = false;
      return;
    }

    this.running = true;

    // Verificar se precisamos resetar o contador
    const now = Date.now();
    if (now >= this.resetTime) {
      this.requestCount = 0;
      this.resetTime = now + this.timeWindow;
    }

    // Verificar se atingimos o limite
    if (this.requestCount >= this.maxRequests) {
      const waitTime = this.resetTime - now;
      console.log(`Limite de requisições atingido. Aguardando ${waitTime / 1000} segundos...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      this.requestCount = 0;
      this.resetTime = Date.now() + this.timeWindow;
    }

    // Executar a próxima função na fila
    const nextFn = this.queue.shift();
    if (nextFn) {
      this.requestCount++;
      await nextFn();
    }

    // Processar o próximo item na fila
    this.processQueue();
  }
}

const rateLimiter = new RateLimiter(120, 30000);

// Função para buscar dados do cliente no CRM
async function buscarDadosCrm(crmId: number, maxRetries = 3): Promise<any> {
  const crmToken = process.env.CRM_TOKEN || "";
  const url = `https://api.pipe.run/v1/deals/${crmId}?with=person.contactPhones,company.contactPhones`;
  
  let tentativas = 0;
  
  while (tentativas <= maxRetries) {
    try {
      return await rateLimiter.execute(async () => {
        const { data } = await axios.get(url, {
          headers: {
            token: crmToken,
          },
        });
        
        if (data.success === false) {
          throw new Error(`Erro na API: ${data.message}`);
        }
        
        return data.data;
      });
    } catch (error: any) {
      tentativas++;
      
      if (error.response?.status === 429 || 
          error.response?.status === 500 ||
          error.response?.status === 502 ||
          error.response?.status === 503 ||
          error.response?.status === 504 ||
          error.message.includes("Too Many Attempts") || 
          error.message.includes("timeout")) {
        
        const tempoEspera = Math.min(Math.pow(2, tentativas) * 1000, 60000);
        console.log(`Erro ao buscar dados CRM para ID ${crmId} (tentativa ${tentativas}/${maxRetries}): ${error.message}`);
        console.log(`Aguardando ${tempoEspera/1000} segundos antes de tentar novamente...`);
        
        await new Promise(resolve => setTimeout(resolve, tempoEspera));
      } else if (tentativas >= maxRetries) {
        console.error(`Erro ao buscar dados CRM para ID ${crmId} após ${maxRetries} tentativas: ${error.message}`);
        return null;
      } else {
        const tempoEspera = 1000 * tentativas;
        console.log(`Erro ao buscar dados CRM para ID ${crmId} (tentativa ${tentativas}/${maxRetries}): ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, tempoEspera));
      }
    }
  }
  
  return null;
}

async function listarClientesSemIdentificador() {
  try {
    console.log('🔍 Buscando clientes sem identificador...\n');

    const clientesSemIdentificador = await prisma.cliente.findMany({
      where: {
        OR: [
          { identificador: null },
          { identificador: '' },
          { identificador: 'null' },
          { identificador: '0000000000' }
        ]
      },
      select: {
        id: true,
        nome: true,
        identificador: true,
        tipoDeDocumento: true,
        numeroDocumento: true,
        nomeDaMarca: true,
        crmId: true,
        crmLeadIds: true,
        _count: {
          select: {
            processos: true,
            contatos: true
          }
        }
      },
      orderBy: {
        id: 'asc'
      }
    });

    console.log(`📊 Total de clientes sem identificador: ${clientesSemIdentificador.length}\n`);

    if (clientesSemIdentificador.length === 0) {
      console.log('✅ Todos os clientes possuem identificador!');
      return;
    }

    console.log('📋 Lista detalhada dos clientes sem identificador:');
    console.log('='.repeat(80));

    clientesSemIdentificador.forEach((cliente, index) => {
      console.log(`\n${index + 1}. Cliente ID: ${cliente.id}`);
      console.log(`   Nome: ${cliente.nome || 'Não informado'}`);
      console.log(`   Identificador: ${cliente.identificador || 'VAZIO/NULL'}`);
      console.log(`   Tipo Documento: ${cliente.tipoDeDocumento || 'Não informado'}`);
      console.log(`   Número Documento: ${cliente.numeroDocumento || 'Não informado'}`);
      console.log(`   Nome da Marca: ${cliente.nomeDaMarca || 'Não informado'}`);
      console.log(`   CRM ID: ${cliente.crmId || 'Não vinculado'}`);
      console.log(`   CRM Lead IDs: ${cliente.crmLeadIds.length > 0 ? cliente.crmLeadIds.join(', ') : 'Nenhum'}`);
      console.log(`   Processos: ${cliente._count.processos}`);
      console.log(`   Contatos: ${cliente._count.contatos}`);
      console.log('   ' + '-'.repeat(50));
    });

    console.log(`\n📈 Resumo:`);
    console.log(`   - Total de clientes sem identificador: ${clientesSemIdentificador.length}`);
    console.log(`   - Clientes com nome informado: ${clientesSemIdentificador.filter(c => c.nome).length}`);
    console.log(`   - Clientes vinculados ao CRM: ${clientesSemIdentificador.filter(c => c.crmId).length}`);
    console.log(`   - Clientes com processos: ${clientesSemIdentificador.filter(c => c._count.processos > 0).length}`);
    
    const totalProcessos = clientesSemIdentificador.reduce((acc, cliente) => acc + cliente._count.processos, 0);
    console.log(`   - Total de processos vinculados: ${totalProcessos}`);

    return clientesSemIdentificador;

  } catch (error) {
    console.error('❌ Erro ao buscar clientes sem identificador:', error);
    throw error;
  }
}

async function atualizarIdentificadoresViaApi() {
  try {
    console.log('\n🔄 Iniciando atualização de identificadores via API do CRM...\n');

    const clientesSemIdentificador = await listarClientesSemIdentificador();

    if (!clientesSemIdentificador || clientesSemIdentificador.length === 0) {
      console.log('✅ Nenhum cliente precisa de atualização de identificador!');
      return;
    }

    // Filtrar apenas clientes que têm CRM ID
    const clientesComCrmId = clientesSemIdentificador.filter(cliente => cliente.crmId);

    console.log(`\n🎯 ${clientesComCrmId.length} clientes com CRM ID serão processados para atualização de identificador.\n`);

    if (clientesComCrmId.length === 0) {
      console.log('⚠️ Nenhum cliente sem identificador possui CRM ID. Não é possível buscar dados na API.');
      return;
    }

    const progressBar = new cliProgress.SingleBar({
      format: "Atualizando identificadores |{bar}| {percentage}% || {value}/{total}",
      barCompleteChar: "\u2588",
      barIncompleteChar: "\u2591",
      hideCursor: true,
    });

    progressBar.start(clientesComCrmId.length, 0);

    let atualizados = 0;
    let erros = 0;
    let semTelefone = 0;

    for (const cliente of clientesComCrmId) {
      try {
        // Buscar dados do CRM
        const dadosCrm = await buscarDadosCrm(cliente.crmId!);

        if (dadosCrm) {
          let telefone = null;
          let fonte = '';

          // Primeiro, tentar obter telefone de person
          if (dadosCrm.person && dadosCrm.person.contactPhones && dadosCrm.person.contactPhones.length > 0) {
            telefone = dadosCrm.person.contactPhones.find((p: { is_main: number; }) => p.is_main === 1)?.phone || 
                      dadosCrm.person.contactPhones[0]?.phone;
            fonte = 'person';
          }

          // Se não encontrou telefone em person, tentar em company
          if (!telefone && dadosCrm.company && dadosCrm.company.contactPhones && dadosCrm.company.contactPhones.length > 0) {
            telefone = dadosCrm.company.contactPhones.find((p: { is_main: number; }) => p.is_main === 1)?.phone || 
                      dadosCrm.company.contactPhones[0]?.phone;
            fonte = 'company';
          }

          if (telefone && telefone !== "NoPhone") {
            // Gerar identificador usando a função extraída
            const novoIdentificador = extrairIdentificadorTelefone(telefone);

            // Atualizar o cliente no banco
            await prisma.cliente.update({
              where: { id: cliente.id },
              data: { identificador: novoIdentificador }
            });

            atualizados++;
            console.log(`\n✅ Cliente ID ${cliente.id} (CRM ID: ${cliente.crmId}) atualizado com identificador: ${novoIdentificador} [${fonte}]`);
          } else {
            semTelefone++;
            console.log(`\n⚠️ Cliente ID ${cliente.id} (CRM ID: ${cliente.crmId}) não possui telefone válido no CRM (person/company)`);
          }
        } else {
          erros++;
          console.log(`\n❌ Não foi possível obter dados do CRM para o cliente ID ${cliente.id} (CRM ID: ${cliente.crmId})`);
        }
      } catch (error: any) {
        erros++;
        console.error(`\n❌ Erro ao processar cliente ID ${cliente.id} (CRM ID: ${cliente.crmId}): ${error.message}`);
      }

      progressBar.increment();
    }

    progressBar.stop();

    console.log(`\n📊 Resumo da atualização:`);
    console.log(`   ✅ Clientes atualizados com sucesso: ${atualizados}`);
    console.log(`   ⚠️ Clientes sem telefone válido: ${semTelefone}`);
    console.log(`   ❌ Erros encontrados: ${erros}`);
    console.log(`   📱 Total processado: ${atualizados + semTelefone + erros}`);

  } catch (error) {
    console.error('❌ Erro durante a atualização de identificadores:', error);
    throw error;
  }
}

async function executar() {
  try {
    const args = process.argv.slice(2);
    const shouldUpdate = args.includes('--atualizar') || args.includes('-u');

    if (shouldUpdate) {
      await atualizarIdentificadoresViaApi();
    } else {
      await listarClientesSemIdentificador();
      console.log('\n💡 Para atualizar os identificadores automaticamente, execute:');
      console.log('   npm run listar-clientes-sem-identificador -- --atualizar');
      console.log('   ou');
      console.log('   ts-node scripts/listarClientesSemIdentificador.ts --atualizar');
    }
    
    console.log('\n✅ Execução concluída com sucesso!');
  } catch (error) {
    console.error('\n❌ Erro durante a execução:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executa o script apenas se for chamado diretamente
if (require.main === module) {
  executar();
}

export { listarClientesSemIdentificador, atualizarIdentificadoresViaApi }; 