import { PrismaClient } from '@prisma/client';
import { prisma } from './types';

/**
 * Gera um relatório dos comunicados enviados em um período específico
 * @param dataInicio Data de início do período
 * @param dataFim Data de fim do período
 * @returns Objeto com o relatório de comunicados
 */
export async function gerarRelatorioComunicados(
  dataInicio?: Date,
  dataFim?: Date
) {
  try {
    // Define período padrão (últimos 30 dias) se não especificado
    const inicio = dataInicio || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const fim = dataFim || new Date();
    
    console.log(`Gerando relatório de comunicados de ${inicio.toISOString()} até ${fim.toISOString()}`);
    
    const [
      totalComunicados,
      comunicadosPorDia,
      comunicadosPorPrazo,
      comunicadosPorStatus,
      detalhes
    ] = await Promise.all([
      // Total de comunicados no período
      prisma.comunicadoPrazoMerito.count({
        where: {
          dataEnvio: {
            gte: inicio,
            lte: fim
          }
        }
      }),
      
      // Comunicados agrupados por dia
      prisma.$queryRaw`
        SELECT DATE(dataEnvio) as data, COUNT(*) as total
        FROM ComunicadoPrazoMerito
        WHERE dataEnvio >= ${inicio} AND dataEnvio <= ${fim}
        GROUP BY DATE(dataEnvio)
        ORDER BY data
      `,
      
      // Comunicados agrupados por prazo em meses
      prisma.$queryRaw`
        SELECT prazoEmMeses, COUNT(*) as total
        FROM ComunicadoPrazoMerito
        WHERE dataEnvio >= ${inicio} AND dataEnvio <= ${fim}
        GROUP BY prazoEmMeses
        ORDER BY prazoEmMeses
      `,
      
      // Comunicados agrupados por status
      prisma.$queryRaw`
        SELECT success, COUNT(*) as total
        FROM ComunicadoPrazoMerito
        WHERE dataEnvio >= ${inicio} AND dataEnvio <= ${fim}
        GROUP BY success
      `,
      
      // Lista detalhada dos comunicados (limitada a 100)
      prisma.comunicadoPrazoMerito.findMany({
        where: {
          dataEnvio: {
            gte: inicio,
            lte: fim
          }
        },
        orderBy: { dataEnvio: 'desc' },
        take: 100,
        include: {
          processo: {
            select: {
              numero: true,
              dataMeritoEstimada: true,
              diasAteMeritoEstimada: true,
              cliente: {
                select: {
                  nome: true,
                  id: true
                }
              }
            }
          }
        }
      })
    ]);
    
    const relatorio = {
      periodo: {
        inicio,
        fim
      },
      resumo: {
        totalComunicados,
        comunicadosPorDia,
        comunicadosPorPrazo,
        comunicadosPorStatus
      },
      detalhes
    };
    
    return relatorio;
  } catch (error) {
    console.error('Erro ao gerar relatório de comunicados:', error);
    throw error;
  }
} 