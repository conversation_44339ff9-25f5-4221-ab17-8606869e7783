import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Obtém estatísticas da fila de processamento
 */
export const obterEstatisticasFila = async (req: Request, res: Response): Promise<any> => {
  try {
    // Contar registros por status
    const estatisticas = await prisma.processamentoProtocolo.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    });

    // Estatísticas gerais
    const total = await prisma.processamentoProtocolo.count();
    const ultimosProcessados = await prisma.processamentoProtocolo.findMany({
      where: { status: 'SUCESSO' },
      orderBy: { processadoEm: 'desc' },
      take: 5,
      select: {
        numeroProcesso: true,
        elementoNominativo: true,
        processadoEm: true,
        clienteId: true
      }
    });

    const falhasRecentes = await prisma.processamentoProtocolo.findMany({
      where: { 
        status: { in: ['FALHA_CRM', 'FALHA_CLIENTE', 'FALHA_PROCESSO', 'FALHA_LINK', 'FALHA_CHATGURU', 'FALHA_GERAL'] }
      },
      orderBy: { atualizadoEm: 'desc' },
      take: 10,
      select: {
        numeroProcesso: true,
        status: true,
        ultimoErro: true,
        tentativas: true,
        atualizadoEm: true
      }
    });

    // Transformar estatísticas em objeto mais legível
    const estatisticasFormatadas = estatisticas.reduce((acc, stat) => {
      acc[stat.status] = stat._count.status;
      return acc;
    }, {} as Record<string, number>);

    return res.json({
      success: true,
      data: {
        total,
        estatisticas: estatisticasFormatadas,
        ultimosProcessados,
        falhasRecentes
      }
    });

  } catch (error) {
    console.error('Erro ao obter estatísticas da fila:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro ao obter estatísticas da fila',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
};

/**
 * Lista protocolos na fila com filtros
 */
export const listarProtocolosFila = async (req: Request, res: Response): Promise<any> => {
  try {
    const { 
      status, 
      page = 1, 
      limit = 20,
      numeroProcesso 
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const take = Number(limit);

    // Construir filtros
    const where: any = {};
    
    if (status && typeof status === 'string') {
      where.status = status;
    }
    
    if (numeroProcesso && typeof numeroProcesso === 'string') {
      where.numeroProcesso = {
        contains: numeroProcesso,
        mode: 'insensitive'
      };
    }

    // Buscar registros
    const [protocolos, total] = await Promise.all([
      prisma.processamentoProtocolo.findMany({
        where,
        skip,
        take,
        orderBy: { criadoEm: 'desc' },
        include: {
          cliente: {
            select: {
              id: true,
              nome: true,
              autoLoginUrl: true,
              contatos: {
                select: {
                  telefone: true,
                  email: true
                }
              }
            }
          },
          processo: {
            select: {
              id: true,
              numero: true
            }
          }
        }
      }),
      prisma.processamentoProtocolo.count({ where })
    ]);

    return res.json({
      success: true,
      data: {
        protocolos,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit))
        }
      }
    });

  } catch (error) {
    console.error('Erro ao listar protocolos da fila:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro ao listar protocolos da fila',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
};

/**
 * Busca o status de processamento de um protocolo específico pelo número do processo
 */
export const buscarStatusProtocolo = async (req: Request, res: Response): Promise<any> => {
  try {
    const { numeroProcesso } = req.params;

    if (!numeroProcesso) {
      return res.status(400).json({
        success: false,
        message: 'Número do processo é obrigatório'
      });
    }

    // Buscar protocolo pelo número do processo
    const protocolo = await prisma.processamentoProtocolo.findFirst({
      where: { 
        numeroProcesso: numeroProcesso.toString()
      },
      include: {
        cliente: {
          select: {
            id: true,
            nome: true,
            autoLoginUrl: true,
            contatos: {
              select: {
                telefone: true,
                email: true
              }
            }
          }
        },
        processo: {
          select: {
            id: true,
            numero: true
          }
        }
      },
      orderBy: { criadoEm: 'desc' } // Pegar o mais recente se houver duplicatas
    });

    if (!protocolo) {
      return res.status(404).json({
        success: false,
        message: 'Protocolo não encontrado na fila de processamento'
      });
    }

    // Calcular progresso baseado no status
    const getProgresso = (status: string) => {
      const etapas: Record<string, number> = {
        'PENDENTE': 0,
        'PROCESSANDO': 20,
        'FALHA_CRM': 10,
        'FALHA_CLIENTE': 30,
        'FALHA_PROCESSO': 50,
        'FALHA_LINK': 70,
        'FALHA_CHATGURU': 90,
        'FALHA_GERAL': 0,
        'SUCESSO': 100
      };
      return etapas[status] || 0;
    };

    // Determinar próxima tentativa se aplicável
    const proximaTentativaFormatada = protocolo.proximaTentativa 
      ? protocolo.proximaTentativa.toISOString()
      : null;

    return res.json({
      success: true,
      data: {
        id: protocolo.id,
        numeroProcesso: protocolo.numeroProcesso,
        elementoNominativo: protocolo.elementoNominativo,
        status: protocolo.status,
        progresso: getProgresso(protocolo.status),
        tentativas: protocolo.tentativas,
        ultimoErro: protocolo.ultimoErro,
        proximaTentativa: proximaTentativaFormatada,
        criadoEm: protocolo.criadoEm,
        processadoEm: protocolo.processadoEm,
        cliente: protocolo.cliente,
        processo: protocolo.processo,
        // Dados específicos do processamento
        etapas: {
          crmEncontrado: !!protocolo.crmDealId,
          clienteCriado: protocolo.clienteCriado,
          clienteAtualizado: protocolo.clienteAtualizado,
          processoVinculado: protocolo.processoVinculado,
          linkGerado: protocolo.linkGerado,
          chatguruAtualizado: protocolo.chatguruAtualizado
        }
      }
    });

  } catch (error) {
    console.error('Erro ao buscar status do protocolo:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro ao buscar status do protocolo',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
};

/**
 * Lista todos os protocolos processados recentemente (últimas 24h) para feedback do usuário
 */
export const listarProtocolosRecentes = async (req: Request, res: Response): Promise<any> => {
  try {
    const { limit = 10 } = req.query;
    
    // Data de 24 horas atrás
    const ultimasDatasHoras = new Date();
    ultimasDatasHoras.setHours(ultimasDatasHoras.getHours() - 24);

    const protocolos = await prisma.processamentoProtocolo.findMany({
      where: {
        criadoEm: {
          gte: ultimasDatasHoras
        }
      },
      orderBy: { criadoEm: 'desc' },
      take: Number(limit),
      include: {
        cliente: {
          select: {
            id: true,
            nome: true,
            autoLoginUrl: true
          }
        },
        processo: {
          select: {
            id: true,
            numero: true
          }
        }
      }
    });

    return res.json({
      success: true,
      data: {
        protocolos: protocolos.map(protocolo => ({
          id: protocolo.id,
          numeroProcesso: protocolo.numeroProcesso,
          elementoNominativo: protocolo.elementoNominativo,
          status: protocolo.status,
          progresso: getProgressoStatus(protocolo.status),
          criadoEm: protocolo.criadoEm,
          processadoEm: protocolo.processadoEm,
          cliente: protocolo.cliente,
          processo: protocolo.processo
        })),
        total: protocolos.length
      }
    });

  } catch (error) {
    console.error('Erro ao listar protocolos recentes:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro ao listar protocolos recentes',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
};

/**
 * Verifica se há protocolos pendentes ou em processamento (para polling)
 */
export const verificarFilaAtiva = async (req: Request, res: Response): Promise<any> => {
  try {
    const protocolosAtivos = await prisma.processamentoProtocolo.count({
      where: {
        status: {
          in: ['PENDENTE', 'PROCESSANDO']
        }
      }
    });

    const proximoProcessamento = await prisma.processamentoProtocolo.findFirst({
      where: {
        status: 'PENDENTE'
      },
      orderBy: { criadoEm: 'asc' },
      select: {
        numeroProcesso: true,
        criadoEm: true
      }
    });

    return res.json({
      success: true,
      data: {
        filaAtiva: protocolosAtivos > 0,
        protocolosAtivos,
        proximoProtocolo: proximoProcessamento
      }
    });

  } catch (error) {
    console.error('Erro ao verificar fila ativa:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro ao verificar fila ativa',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
};

/**
 * Função auxiliar para calcular progresso baseado no status
 */
function getProgressoStatus(status: string): number {
  const etapas: Record<string, number> = {
    'PENDENTE': 0,
    'PROCESSANDO': 20,
    'FALHA_CRM': 10,
    'FALHA_CLIENTE': 30,
    'FALHA_PROCESSO': 50,
    'FALHA_LINK': 70,
    'FALHA_CHATGURU': 90,
    'FALHA_GERAL': 0,
    'SUCESSO': 100
  };
  return etapas[status] || 0;
}

/**
 * Reprocessa um protocolo específico
 */
export const reprocessarProtocolo = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;

    // Verificar se protocolo existe
    const protocolo = await prisma.processamentoProtocolo.findUnique({
      where: { id }
    });

    if (!protocolo) {
      return res.status(404).json({
        success: false,
        message: 'Protocolo não encontrado'
      });
    }

    // Resetar para reprocessamento
    await prisma.processamentoProtocolo.update({
      where: { id },
      data: {
        status: 'PENDENTE',
        tentativas: 0,
        ultimoErro: null,
        proximaTentativa: null,
        processadoEm: null
      }
    });

    return res.json({
      success: true,
      message: `Protocolo ${protocolo.numeroProcesso} marcado para reprocessamento`
    });

  } catch (error) {
    console.error('Erro ao reprocessar protocolo:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro ao reprocessar protocolo',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
};

/**
 * Remove um protocolo da fila
 */
export const removerProtocoloFila = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;

    // Verificar se protocolo existe
    const protocolo = await prisma.processamentoProtocolo.findUnique({
      where: { id }
    });

    if (!protocolo) {
      return res.status(404).json({
        success: false,
        message: 'Protocolo não encontrado'
      });
    }

    // Remover protocolo
    await prisma.processamentoProtocolo.delete({
      where: { id }
    });

    return res.json({
      success: true,
      message: `Protocolo ${protocolo.numeroProcesso} removido da fila`
    });

  } catch (error) {
    console.error('Erro ao remover protocolo da fila:', error);
    return res.status(500).json({
      success: false,
      message: 'Erro ao remover protocolo da fila',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
}; 