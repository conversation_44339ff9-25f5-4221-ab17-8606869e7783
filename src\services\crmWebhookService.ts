import { Prisma, ScrapingStage } from '@prisma/client';
import prisma from '../dbClient';
import { fieldMapping } from '../utils/crm.utils'; // Importa o mapeamento de campos

/**
 * Função auxiliar para extrair o valor de um campo personalizado do payload do webhook.
 * Procura em `customFields`, `forms[*].fields` (seja array ou objeto) e `fields` (nível superior).
 *
 * @param payload O objeto do payload do webhook do CRM.
 * @param fieldId O ID numérico do campo personalizado a ser buscado.
 * @returns O valor do campo (pode ser string, array, etc.) ou `null` se não encontrado.
 */
function getRawCustomFieldValueFromWebhook(payload: any, fieldId: number): any | null {
  // 1. Tenta encontrar em payload.customFields (estrutura comum)
  if (payload.customFields && Array.isArray(payload.customFields)) {
    const field = payload.customFields.find((f: any) => f.id === fieldId);
    if (field && field.value !== undefined && field.value !== null) {
      console.log(`Campo ID ${fieldId} encontrado em payload.customFields`);
      return field.value;
    }
  }

  // 2. Tenta encontrar em payload.forms[*].fields
  if (payload.forms && Array.isArray(payload.forms)) {
    for (const form of payload.forms) {
      const fields = form.fields;
      if (fields) {
        // Se fields for um ARRAY
        if (Array.isArray(fields)) {
          const field = fields.find((f: any) => f.id === fieldId);
          if (field && field.value !== undefined && field.value !== null) {
            console.log(`Campo ID ${fieldId} encontrado em payload.forms[${form.id}].fields (array)`);
            return field.value;
          }
        }
        // Se fields for um OBJETO (como no exemplo do log)
        else if (typeof fields === 'object') {
          for (const key in fields) {
            const field = fields[key];
            // Verifica se o campo no objeto tem a propriedade 'id' e se corresponde
            if (field && field.id === fieldId && field.valor !== undefined && field.valor !== null) {
              console.log(`Campo ID ${fieldId} encontrado em payload.forms[${form.id}].fields (objeto)`);
              return field.valor;
            }
          }
        }
      }
    }
  }

  // 3. Tenta encontrar em payload.fields (nível superior, visto no log)
  if (payload.fields && Array.isArray(payload.fields)) {
    const field = payload.fields.find((f: any) => f.id === fieldId);
    // No log, os campos neste nível usam 'valor', não 'value'
    if (field && field.valor !== undefined && field.valor !== null) {
      console.log(`Campo ID ${fieldId} encontrado em payload.fields (nível superior)`);
      return field.valor;
    }
  }

  // 4. Se não encontrado em nenhuma das estruturas comuns, retorna null
  console.warn(`Campo personalizado com ID ${fieldId} não encontrado em nenhuma estrutura conhecida do payload.`);
  return null;
}

/**
 * Processa os dados recebidos do webhook da etapa "Protocolado".
 * Cria ou atualiza registros de Processo e Cliente no banco de dados.
 *
 * @param payload O objeto do payload do webhook do CRM.
 */
export const processProtocoladoWebhook = async (payload: any): Promise<void> => {
  // --- 1. Extração de Dados Essenciais do Payload ---
  const crmDealId = parseInt(payload.id);
  if (isNaN(crmDealId)) {
    throw new Error('ID do Deal (payload.id) não encontrado ou inválido no payload do webhook.');
  }
  const numeroProcesso = String(getRawCustomFieldValueFromWebhook(payload, fieldMapping.numeroProcesso.id) || '');
  if (!numeroProcesso) {
    throw new Error(`Número do Processo (ID ${fieldMapping.numeroProcesso.id}) não encontrado no payload.`);
  }
  console.log(`🔄 Processando protocolo: ${numeroProcesso}`);
  
  const linkProtocolo = String(getRawCustomFieldValueFromWebhook(payload, fieldMapping.linkProtocolo.id) || '');
  const nomeMarca = String(getRawCustomFieldValueFromWebhook(payload, fieldMapping.nomeMarca.id) || '');

  let apresentacaoMarca = getRawCustomFieldValueFromWebhook(payload, fieldMapping.tipoMarca.id);
  if (typeof apresentacaoMarca === 'string' && apresentacaoMarca.startsWith('[') && apresentacaoMarca.endsWith(']')) {
    try {
      const parsed = JSON.parse(apresentacaoMarca);
      if (Array.isArray(parsed) && parsed.length > 0) {
        apresentacaoMarca = String(parsed[0]);
      }
    } catch (e) {
      apresentacaoMarca = String(apresentacaoMarca);
    }
  } else {
    apresentacaoMarca = String(apresentacaoMarca || '');
  }

  const estadoEspecificacoes = String(getRawCustomFieldValueFromWebhook(payload, fieldMapping.estadoEspecificacoes.id) || '');
  const nclClassesRaw = getRawCustomFieldValueFromWebhook(payload, fieldMapping.classes.id);
  let nclCodes: string[] = [];

  if (nclClassesRaw) {
    let potentialArray: any[] = [];
    if (typeof nclClassesRaw === 'string') {
      if (nclClassesRaw.startsWith('[') && nclClassesRaw.endsWith(']')) {
        try {
          const parsed = JSON.parse(nclClassesRaw);
          if (Array.isArray(parsed)) {
            potentialArray = parsed;
          }
        } catch (e) {
          potentialArray = nclClassesRaw.split(/[,\s]+/).map(code => code.trim());
        }
      } else {
        potentialArray = nclClassesRaw.split(/[,\s]+/).map(code => code.trim());
      }
    } else if (Array.isArray(nclClassesRaw)) {
      potentialArray = nclClassesRaw;
    }
    
    nclCodes = potentialArray.map(item => String(item).trim()).filter(code => /^[0-9]{1,2}$/.test(code));
  }

  // --- 2. Interação com o Banco de Dados (Prisma) ---
  try {
    await prisma.$transaction(async (tx) => {
      // 2.1. Cria ou Atualiza o Processo
      const processo = await tx.processo.upsert({
        where: { numero: numeroProcesso },
        create: {
          numero: numeroProcesso,
          linkProtocolo: linkProtocolo || null,
          monitorado: true,
          dataDeposito: new Date(new Date().toLocaleString("en-US", { timeZone: "America/Sao_Paulo" }))
        },
        update: { 
          linkProtocolo: linkProtocolo || null, 
          monitorado: true 
        },
        select: { id: true }
      });

      // 2.1.5. Cria ou Atualiza o Controle de Scraping
      const agora = new Date();
      await tx.processoScrapingControl.upsert({
        where: { processoId: processo.id },
        create: {
          processo: { connect: { id: processo.id } },
          processoNumero: numeroProcesso,
          stage: ScrapingStage.AGUARDANDO_PUBLICACAO,
          isActive: true,
          stageEnteredAt: agora,
          nextScrapeDueAt: null, // Scraping não é ativo nesta fase inicial
        },
        update: {
          isActive: true, // Garante que está ativo se o webhook rodar de novo
          // Não atualiza a stage aqui, pois a RPI é quem deve avançar
        },
      });

      // 2.2. Cria ou Atualiza a Marca
      const marca = await tx.marca.upsert({
        where: { processoId: processo.id },
        create: {
          processo: { connect: { id: processo.id } },
          nome: nomeMarca || null,
          apresentacao: apresentacaoMarca || null,
        },
        update: {
          nome: nomeMarca || null,
          apresentacao: apresentacaoMarca || null,
        },
        select: { id: true }
      });

      // 2.3. Adiciona as Classes NCL
       if (nclCodes.length > 0) {
        for (const code of nclCodes) {
          const existingNcl = await tx.nCL.findFirst({ where: { marcaId: marca.id, codigo: code } });
          if (!existingNcl) {
            await tx.nCL.create({ data: { marca: { connect: { id: marca.id } }, codigo: code, estadoDasEspecificacoes: estadoEspecificacoes || null } });
          }
        }
      }
    }); // Fim da transação bem-sucedida

    console.log(`✅ Protocolo ${numeroProcesso} processado com sucesso.`);

  } catch (error: any) {
    // --- Tratamento de Erro e Salvamento do Payload ---
    console.error('ERRO DURANTE A TRANSAÇÃO DO WEBHOOK \'protocolado\':', error);

    try {
      await prisma.webhookPayloadFalho.create({
        data: {
          tipoWebhook: 'protocolado',
          payload: payload as Prisma.JsonObject,
          mensagemErro: error.message || 'Erro desconhecido durante a transação',
        }
      });
    } catch (saveError: any) {
      console.error('ERRO CRÍTICO: Falha ao salvar o payload do webhook com erro no banco de dados:', saveError);
    }

    throw error;
  }
}; 