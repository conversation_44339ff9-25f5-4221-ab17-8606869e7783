import { Request, Response } from 'express';
import { processProtocoladoWebhook } from '../services/crmWebhookService';

/**
 * Controlador para o webhook da etapa "Protocolado" do CRM.
 *
 * @param req Objeto de requisição do Express.
 * @param res Objeto de resposta do Express.
 */
export const handleProtocoladoWebhook = async (req: Request, res: Response): Promise<any> => {

  // Verifica se o corpo da requisição existe e não está vazio
  if (!req.body || Object.keys(req.body).length === 0) {
    console.warn('Webhook /protocolado recebido sem corpo (payload).');
    return res.status(400).json({ message: 'Payload do webhook não recebido.' });
  }

  try {
    // Chama o serviço para processar os dados do webhook
    await processProtocoladoWebhook(req.body);

    return res.status(200).json({ message: 'Webhook recebido e processado com sucesso.' });

  } catch (error: any) {
    // Loga o erro ocorrido durante o processamento
    console.error('Erro ao processar o webhook /protocolado:', error);

    // Responde ao CRM com um erro interno do servidor
    return res.status(500).json({ 
      message: 'Erro interno ao processar o webhook.', 
      error: error.message || 'Erro desconhecido' 
    });
  }
}; 