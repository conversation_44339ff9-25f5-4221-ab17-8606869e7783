# 🧪 Roteiro de Teste - Integração de Protocolos

## 🎯 Objetivo
Testar o fluxo completo: Upload PDF → CRM → Cliente → Processo → Link → ChatGuru

## 📋 Pré-requisitos

### 1. Variáveis de Ambiente
```env
PIPE_TOKEN=seu_token_pipe_run
JWT_SECRET=sua_chave_secreta
DATABASE_URL=sua_url_postgres
```

### 2. Banco de Dados
```bash
npx prisma db push
```

## 🚀 Teste Passo a Passo

### **ETAPA 1: Subir Servidor**
```bash
npm run dev
```

**Logs esperados:**
```
Servidor rodando na porta 3321
🚀 Inicializando job de processamento de protocolos...
✅ Job de processamento de protocolos iniciado - executa a cada 2 minutos
```

### **ETAPA 2: Upload de PDF**

**Endpoint:** `POST http://localhost:3321/api/protocolo/upload`

**Teste via cURL:**
```bash
curl -X POST http://localhost:3321/api/protocolo/upload \
  -F "protocolo=@caminho/para/seu/arquivo.pdf"
```

**Teste via Postman/Insomnia:**
- Method: POST
- URL: `http://localhost:3321/api/protocolo/upload`
- Body: form-data
- Key: `protocolo` (file)
- Value: Selecionar arquivo PDF

**Resposta esperada:**
```json
{
  "success": true,
  "message": "Protocolo processado com sucesso!",
  "data": {
    "numeroProcesso": "BR102023123456",
    "elementoNominativo": "NOME DA MARCA",
    "nomeArquivo": "BR102023123456-NOME_DA_MARCA.pdf",
    "logoExtraida": true,
    "urls": {
      "downloadPdf": "http://localhost:3321/api/protocolo/download/BR102023123456",
      "logoImagem": "http://localhost:3321/api/protocolo/logo/BR102023123456"
    }
  }
}
```

**Log esperado no servidor:**
```
📋 Protocolo BR102023123456 adicionado à fila de processamento
```

### **ETAPA 3: Monitorar Processamento (2 minutos)**

**Aguardar até 2 minutos e observar logs:**

#### 3.1 - Início do Job
```
🔄 Iniciando job de processamento de protocolos...
📋 Encontrados 1 protocolos para processar
🚀 Iniciando processamento do protocolo BR102023123456
```

#### 3.2 - Busca no CRM
```
🔍 Buscando protocolo BR102023123456 no CRM...
```

**Possíveis resultados:**
- ✅ **Sucesso**: Protocolo encontrado no CRM
- ❌ **Falha**: `❌ Falha no protocolo: Protocolo não encontrado no CRM`

#### 3.3 - Criação/Atualização de Cliente
```
📱 Identificadores gerados: 1199999999, 1188888888
🔄 Atualizando cliente existente ID: 123
OU
➕ Criando novo cliente: Nome do Cliente
```

#### 3.4 - Criação de Processo
```
➕ Criando processo BR102023123456 para cliente 123
OU  
🔄 Processo BR102023123456 já existe para o cliente
```

#### 3.5 - Geração de Link
```
🔍 Criptografando: { identificador: '1199999999', senha: '1**' }
✅ Token gerado: eyJhbGciOiJIUzI1NiIsInR5cCI6...
🆕 Link criado: https://cliente.registre.se/AbC123
🔗 Link gerado: https://cliente.registre.se/AbC123
```

#### 3.6 - Atualização ChatGuru
```
📞 Tentando atualizar ChatGuru para telefone: (11)99999-9999
✅ ChatGuru atualizado com sucesso para (11)99999-9999
```

#### 3.7 - Finalização
```
✅ Protocolo BR102023123456 processado com sucesso!
✅ Job de processamento concluído
```

### **ETAPA 4: Verificar APIs de Monitoramento**

#### 4.1 - Estatísticas da Fila
```bash
curl http://localhost:3321/api/fila-protocolo/estatisticas
```

**Resposta esperada:**
```json
{
  "success": true,
  "data": {
    "total": 1,
    "estatisticas": {
      "SUCESSO": 1
    },
    "ultimosProcessados": [
      {
        "numeroProcesso": "BR102023123456",
        "elementoNominativo": "NOME DA MARCA",
        "processadoEm": "2024-01-15T10:30:00.000Z",
        "clienteId": 123
      }
    ],
    "falhasRecentes": []
  }
}
```

#### 4.2 - Listar Protocolos
```bash
curl http://localhost:3321/api/fila-protocolo
```

#### 4.3 - Verificar Banco de Dados
```sql
-- Verificar protocolo na fila
SELECT * FROM "ProcessamentoProtocolo" WHERE "numeroProcesso" = 'BR102023123456';

-- Verificar cliente criado
SELECT * FROM "Cliente" WHERE "identificador" = '1199999999';

-- Verificar processo vinculado  
SELECT * FROM "Processo" WHERE "numero" = 'BR102023123456';

-- Verificar link gerado
SELECT * FROM "ShortUrl" WHERE "clienteId" = 123;
```

## 🚨 Cenários de Erro e Soluções

### **Erro 1: "Protocolo não encontrado no CRM"**
- **Causa**: Número do processo não existe no Pipe Run
- **Solução**: Verificar se processo existe no CRM com custom_field[194250]

### **Erro 2: "Nenhum telefone válido encontrado no CRM"**  
- **Causa**: Deal no CRM não tem telefones em person/company
- **Solução**: Adicionar telefones no CRM

### **Erro 3: "JWT_SECRET não configurado"**
- **Causa**: Variável de ambiente não definida
- **Solução**: Definir JWT_SECRET no .env

### **Erro 4: "Falha no ChatGuru"**
- **Causa**: API do ChatGuru fora do ar ou timeout
- **Solução**: Verificar conectividade, protocolo ainda fica como sucesso

### **Erro 5: "Property 'processamentoProtocolo' does not exist"**
- **Causa**: Schema não foi aplicado no banco
- **Solução**: Executar `npx prisma db push`

## ✅ Critérios de Sucesso

### **Teste Considerado Bem-sucedido se:**
1. ✅ PDF uploadado com sucesso
2. ✅ Protocolo adicionado à fila  
3. ✅ Job processou em até 2 minutos
4. ✅ Cliente criado/atualizado no banco
5. ✅ Processo vinculado ao cliente
6. ✅ Link de auto-login gerado
7. ✅ API de estatísticas retorna dados corretos
8. ✅ ChatGuru atualizado (opcional - pode falhar sem afetar fluxo)

### **Métricas Esperadas:**
- **Tempo de processamento**: < 60 segundos por protocolo
- **Taxa de sucesso CRM**: > 90% (se protocolos existem no Pipe)
- **Taxa de sucesso ChatGuru**: > 70% (API externa)

## 🎯 Casos de Teste Adicionais

### **Teste 1: Cliente Já Existe**
1. Upload protocolo de cliente que já está no banco
2. Verificar se atualiza em vez de criar

### **Teste 2: Processo Já Existe**  
1. Upload protocolo que já foi processado
2. Verificar se não duplica processo

### **Teste 3: Múltiplos Protocolos**
1. Upload vários PDFs rapidamente
2. Verificar se job processa todos

### **Teste 4: Reprocessamento Manual**
```bash
# Marcar protocolo para reprocessamento
curl -X POST http://localhost:3321/api/fila-protocolo/{id}/reprocessar
```

## 📊 Monitoramento Contínuo

### **Logs Importantes para Acompanhar:**
```bash
# Filtrar apenas logs da integração
npm run dev | grep -E "(📋|🚀|✅|❌|🔍|📱|🔗|📞)"

# Ou com tail dos logs
tail -f logs/app.log | grep -E "processamento|protocolo"
```

### **Comandos Úteis:**
```bash
# Ver protocolos pendentes
curl http://localhost:3321/api/fila-protocolo?status=PENDENTE

# Ver protocolos com falha
curl http://localhost:3321/api/fila-protocolo?status=FALHA_CRM

# Buscar protocolo específico
curl http://localhost:3321/api/fila-protocolo?numeroProcesso=BR102023123456
```

---

## 🎉 Resultado Final Esperado

Após teste bem-sucedido, você deve ter:
- ✅ Cliente criado/atualizado no banco
- ✅ Processo vinculado ao cliente  
- ✅ Link `https://cliente.registre.se/AbC123` funcionando
- ✅ Campos atualizados no ChatGuru
- ✅ Protocolo com status `SUCESSO` na fila 