import * as cron from 'node-cron';
import { processarFilaProtocolos } from '../services/processamentoProtocoloService';

/**
 * Job que executa a cada 2 minutos para processar protocolos pendentes
 */
export function iniciarJobProcessamentoProtocolos(): void {
  console.log('🚀 Inicializando job de processamento de protocolos...');
  
  // Executa a cada 2 minutos
  const task = cron.schedule('* * * * *', async () => {
    try {
      await processarFilaProtocolos();
    } catch (error) {
      console.error('❌ Erro no job de processamento de protocolos:', error);
    }
  }, {
    scheduled: false,
    timezone: 'America/Sao_Paulo'
  });
  
  // Iniciar o job
  task.start();
  
  console.log('✅ Job de processamento de protocolos iniciado - executa a cada 2 minutos');
}

/**
 * Para o job de processamento
 */
export function pararJobProcessamentoProtocolos(): void {
  cron.getTasks().forEach((task) => {
    task.stop();
  });
  
  console.log('🛑 Job de processamento de protocolos parado');
} 