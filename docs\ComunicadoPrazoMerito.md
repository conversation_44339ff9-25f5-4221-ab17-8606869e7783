# Sistema de Comunicados de Prazo de Mérito

## Visão Geral

O sistema de comunicados de prazo de mérito é responsável por enviar notificações automáticas aos clientes sobre o tempo restante até a data estimada de mérito de seus processos de registro de marca no INPI. Ele é projetado para enviar comunicados em intervalos regulares (múltiplos de 30 dias) à medida que o processo avança em direção ao mérito.

## Fluxo do Processo

1. **Verificação Diária**: Todos os dias às 9h da manhã, o sistema verifica os processos com data estimada de mérito.
2. **Cálculo de Elegibilidade**: Para cada processo, o sistema:
   - Calcula dias restantes até a data de mérito estimada
   - Verifica se os dias restantes são múltiplos exatos de 30
   - Determina se o processo é elegível para receber um comunicado
3. **Execução de Diálogos**: Para processos elegíveis, o sistema:
   - Registra o comunicado no banco de dados
   - Atualiza campos no ChatGuru
   - Executa o diálogo apropriado de acordo com o prazo
4. **Controle de Retentativas**: Se o envio falhar:
   - Registra a falha no histórico
   - Permite até 3 tentativas de reenvio

## Modelo de Dados

### ComunicadoPrazoMerito
Armazena informações sobre cada comunicado:
- `id`: Identificador único do comunicado
- `crmId`: ID do cliente no ChatGuru
- `processoId`: ID do processo de registro de marca
- `dialogId`: ID do diálogo no ChatGuru
- `prazoEmMeses`: Meses restantes até a data de mérito
- `dataEnvio`: Data de envio do comunicado
- `status`: Estado do comunicado (PENDENTE, EM_PROCESSAMENTO, ENVIADO, FALHA, CANCELADO)
- `tentativas`: Número de tentativas de envio
- `ultimaTentativa`: Data da última tentativa
- `success`: Indicador de sucesso
- `errorMessage`: Mensagem de erro (se houver)
- `detalhesErro`: Detalhes técnicos do erro em formato JSON

### HistoricoComunicadoPrazoMerito
Registra histórico de tentativas para cada comunicado:
- `id`: Identificador único
- `comunicadoPrazoMeritoId`: ID do comunicado relacionado
- `dataTentativa`: Data e hora da tentativa
- `status`: Status resultante da tentativa
- `success`: Indicador de sucesso
- `errorMessage`: Mensagem de erro (se houver)
- `detalhesErro`: Detalhes técnicos do erro em formato JSON

## Estados do Comunicado

1. **PENDENTE**: Comunicado criado, aguardando processamento
2. **EM_PROCESSAMENTO**: Comunicado sendo processado/enviado
3. **ENVIADO**: Comunicado enviado com sucesso
4. **FALHA**: Falha no envio do comunicado
5. **CANCELADO**: Comunicado cancelado manualmente

## Regras de Negócio

1. **Critério de Múltiplo de 30**: Comunicados são enviados apenas quando os dias restantes são múltiplos exatos de 30 dias.
2. **Sequência Decrescente**: Só são enviados comunicados para prazos menores que o último comunicado enviado com sucesso.
3. **Limite de Tentativas**: Cada comunicado tem no máximo 3 tentativas de envio.
4. **Processos Sobrestados**: Processos com sobrestamento não recebem comunicados.
5. **Diálogos Específicos**: Cada prazo em meses corresponde a um diálogo específico no ChatGuru.

## Relatórios e Monitoramento

O sistema oferece relatórios que permitem visualizar:
- Total de comunicados enviados por período
- Agrupamento por dia, prazo e status
- Detalhes individuais de comunicados
- Métricas de sucesso e falha

## Implementação Técnica

### Componentes Principais
- `verificarEEnviarComunicados()`: Função principal que verifica e processa todos os processos
- `verificarElegibilidadeComunicado()`: Determina se um processo deve receber comunicado
- `executarDialogo()`: Executa o diálogo no ChatGuru e registra o resultado
- `gerarRelatorioComunicados()`: Gera relatórios detalhados

### Agendamento
O sistema é executado diariamente às 9h da manhã através da função `iniciarVerificacaoDiariaComunicados()` que utiliza o agendador cron.

## Integração com ChatGuru

O sistema integra-se com o ChatGuru para:
1. Atualizar o campo `EstagioProcesso` no perfil do cliente
2. Executar diálogos automáticos baseados no prazo restante

## Configuração e Manutenção

### Mapeamento de Diálogos
O mapeamento entre prazos e diálogos está definido no arquivo `src/utils/chatguru.utils.ts`.

### Migração de Banco de Dados
Para aplicar alterações no esquema do banco de dados, execute:
```bash
npx prisma migrate dev
``` 