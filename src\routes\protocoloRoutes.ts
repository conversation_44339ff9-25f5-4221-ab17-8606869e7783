import { Router } from 'express';
import { 
  upload<PERSON><PERSON><PERSON><PERSON> as uploadProtocoloController, 
  upload<PERSON>rotocoloBatch as uploadProtocoloBatchController,
  listarProtocolos,
  downloadProtocolo,
  downloadLogo
} from '../controllers/protocoloController';
import { uploadProtocolo, uploadProtocoloBatch } from '../middleware/protocoloUploadMiddleware';

const router = Router();

/**
 * @route POST /api/protocolo/upload
 * @desc Upload e extração de imagem de arquivo PDF de protocolo (ÚNICO)
 * @access Public (por enquanto)
 */
router.post('/upload', uploadProtocolo.single('protocolo'), uploadProtocoloController);

/**
 * 🆕 @route POST /api/protocolo/upload-batch
 * @desc Upload em lote e processamento otimizado de múltiplos PDFs de protocolo
 * @access Public (por enquanto)
 */
router.post('/upload-batch', uploadProtocoloBatch.array('protocolos', 20), uploadProtocoloBatchController);

/**
 * @route GET /api/protocolo/list
 * @desc Listar protocolos salvos
 * @access Public (por enquanto)
 */
router.get('/list', listarProtocolos);

/**
 * @route GET /api/protocolo/download/:numeroProcesso
 * @desc Download do protocolo PDF pelo número do processo
 * @access Public (por enquanto)
 */
router.get('/download/:numeroProcesso', downloadProtocolo);

/**
 * @route GET /api/protocolo/logo/:numeroProcesso
 * @desc Servir logo da marca para exibição (uso em <img src="">)
 * @access Public (por enquanto)
 */
router.get('/logo/:numeroProcesso', downloadLogo);

export default router; 